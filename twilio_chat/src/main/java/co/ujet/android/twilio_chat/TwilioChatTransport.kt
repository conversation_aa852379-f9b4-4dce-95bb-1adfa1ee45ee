package co.ujet.android.twilio_chat

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.ChatTransportUtil
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransport
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportListener
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportState
import co.ujet.android.modulemanager.entrypoints.chat.Constants.TWILIO_CONVERSATIONS
import com.twilio.conversations.BuildConfig
import com.twilio.conversations.extensions.prepareMessage
import com.twilio.conversations.*
import com.twilio.util.ErrorInfo
import org.json.JSONObject
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

private const val VISIBLE_LAST_MESSAGE_COUNT = 100

/**
 * Migrated <PERSON>wi<PERSON> Chat to Conversations and referred documents listed below.
 * https://www.twilio.com/docs/conversations/migrating-chat-conversations
 * https://www.twilio.com/docs/conversations/android/migrate-your-chat-android-sdk-conversations
 * https://www.twilio.com/docs/conversations/android/changelog
 * https://media.twiliocdn.com/sdk/android/conversations/releases/1.4.1/docs/index.html
 */
class TwilioChatTransport<T : ChatMessage>(
    private val context: Context,
    private val channelSid: String,
    private val chatTransportListener: ChatTransportListener<T>,
    private val chatAccessTokenFetcher: ChatAccessTokenFetcher,
    private val taskVaMessageFetcher: TaskVaMessageFetcher,
    private val logger: Logger
) : ChatTransport<T>, ConversationsClientListener, ConversationListener {

    private val queuedMessagesToSend = ConcurrentLinkedQueue<T>()

    private var conversationsClient: ConversationsClient? = null
    private var conversation: Conversation? = null
    private var shouldBeConnected = false
    private var isFetchingAuthToken = false

    init {
        ConversationsClient.setLogLevel(if (BuildConfig.DEBUG) ConversationsClient.LogLevel.DEBUG else ConversationsClient.LogLevel.ERROR)
    }

    override fun connect() {
        shouldBeConnected = true
        fetchTokenAndConnect()
    }

    private fun fetchTokenAndConnect() {
        if (!shouldBeConnected) {
            return
        }
        isFetchingAuthToken = true
        chatTransportListener.onTransportStateChanged(getState())
        chatAccessTokenFetcher.fetch(TWILIO_CONVERSATIONS,
            onSuccess = { accessToken ->
                isFetchingAuthToken = false
                if (!shouldBeConnected) {
                    logger.i("Twilio chat token fetched, but transport should not be connected anymore")
                    return@fetch
                }
                if (conversationsClient != null) {
                    updateAccessToken(accessToken)
                } else {
                    createConversationClient(accessToken)
                }
            },
            onFailure = {
                isFetchingAuthToken = false
                logger.w("Failed to fetch Twilio chat access token")
                disconnect()
            }
        )
    }

    private fun createConversationClient(accessToken: String) {
        ConversationsClient.create(
            context, accessToken,
            ConversationsClient.Properties.newBuilder().createProperties()
        ) { onConversationClientCreated(it) }
    }

    private fun onConversationClientCreated(conversationsClient: ConversationsClient) {
        if (shouldBeConnected) {
            logger.i("Created Twilio ConversationsClient")
            if (this.conversationsClient != null) {
                logger.d("Twilio ConversationsClient already exists so shutdown existing client")
                this.conversationsClient?.removeListener(this)
                this.conversationsClient?.shutdown()
            }
            this.conversationsClient = conversationsClient
            this.conversationsClient?.addListener(this)
        } else {
            logger.i("Created Twilio ConversationsClient while isNotFinished == false")
            conversationsClient.shutdown()
        }
    }

    override fun onTokenAboutToExpire() {
        logger.w("Twilio conversations access token is about to expire")
        fetchTokenAndConnect()
    }

    override fun onTokenExpired() {
        logger.w("Twilio conversations access token expired")
        fetchTokenAndConnect()
    }

    override fun onTypingStarted() {
        if (shouldBeConnected) {
            conversation?.typing()
        }
    }

    override fun onTypingStopped() {
        // Ignored. Not supported by Twilio Conversations:
        // "After approximately 5 seconds after the last typing call the SDK will emit ConversationListener.onTypingEnded signal."
    }

    override fun send(chatMessage: T) {
        if (shouldBeConnected) {
            if (conversation == null) {
                logger.w("Queued outbound message as the conversation is not initialized yet")
                queuedMessagesToSend.offer(chatMessage)
            } else if (conversationsClient?.connectionState == ConversationsClient.ConnectionState.CONNECTED
                && conversation?.synchronizationStatus == Conversation.SynchronizationStatus.ALL
            ) {
                conversation?.prepareMessage {
                    body = chatMessage.toJson().toString()
                }?.send(object : CallbackListener<Message> {
                    override fun onSuccess(message: Message) {
                        logger.v(
                            "Message sent successfully: sid: %s, timestamp: %s",
                            message.sid,
                            message.dateCreated
                        )
                        if (shouldBeConnected) {
                            chatTransportListener.onMessageSent(chatMessage)
                        }
                    }

                    override fun onError(errorInfo: ErrorInfo) {
                        logger.w(
                            "Failed to send a message with error %d %s",
                            errorInfo.code,
                            errorInfo.message
                        )
                        if (shouldBeConnected) {
                            chatTransportListener.onMessageSendFailed(chatMessage)
                        }
                    }
                })
            } else {
                queuedMessagesToSend.offer(chatMessage)
                logger.d("Queued outbound message as the Conversation is not synchronized yet")
            }
        }
    }

    override fun getState(): ChatTransportState {
        val connectionState = conversationsClient?.connectionState
        return if (connectionState == ConversationsClient.ConnectionState.CONNECTED
            && conversation?.synchronizationStatus == Conversation.SynchronizationStatus.ALL
            && conversation?.status == Conversation.ConversationStatus.JOINED
        ) {
            ChatTransportState.CONNECTED
        } else if (connectionState == ConversationsClient.ConnectionState.ERROR
            || (connectionState == ConversationsClient.ConnectionState.DISCONNECTED && !isFetchingAuthToken)
            || connectionState == ConversationsClient.ConnectionState.DENIED
            || connectionState == ConversationsClient.ConnectionState.FATAL_ERROR
            || !shouldBeConnected
        ) {
            ChatTransportState.DISCONNECTED
        } else {
            ChatTransportState.CONNECTING
        }
    }

    override fun disconnect() {
        if (shouldBeConnected.not()) {
            logger.d("Can't disconnect an already disconnected Twilio ConversationClient")
            return
        }

        logger.d("Disconnecting Twilio Conversations Client")
        cleanConversation()
        cleanConversationsClient()
        shouldBeConnected = false
        chatTransportListener.onTransportStateChanged(getState())
    }

    override fun sendMessagePreview(chatMessage: ChatMessage) {
        if (conversation?.synchronizationStatus != Conversation.SynchronizationStatus.ALL) {
            // Prevent participant identity fetch until synchronizationStatus == 'ALL'
            return
        }
        conversation
            ?.getParticipantByIdentity(conversationsClient?.myIdentity ?: return)
            ?.setAttributes(Attributes(chatMessage.toJson())) {
                logger.logStatus("Participant attributes were updated", "Failed to update participant attributes")
            }
    }

    private fun updateAccessToken(accessToken: String) {
        if (shouldBeConnected) {
            conversationsClient?.updateToken(
                accessToken, logger.logStatus(
                    "Updated Twilio conversations access token",
                    "Failed to update Twilio conversations access token: "
                )
            )
        }
    }

    private fun cleanConversation() {
        queuedMessagesToSend.clear()
        conversation?.apply {
            leave(
                logger.logStatus(
                    "Successfully left Twilio conversation",
                    "Failed to leave Twilio conversation: "
                )
            )
            removeAllListeners()
        }
        conversation = null
    }

    private fun cleanConversationsClient() {
        conversationsClient?.removeListener(this)
        conversationsClient?.shutdown()
        conversationsClient = null
    }

    override fun onMessageAdded(message: Message) {
        if (shouldBeConnected) {
            logger.d("onMessageAdded with sid: %s, timestamp: %s", message.sid, message.dateCreated)
            notifyMessageReceived(message)
        }
    }

    override fun onMessageUpdated(message: Message, updateReason: Message.UpdateReason) {
        if (shouldBeConnected) {
            logger.d("onMessageUpdated with sid: %s by %s", message.sid, updateReason)
        }
    }

    override fun onMessageDeleted(message: Message) {
        if (shouldBeConnected) {
            logger.d("onMessageDeleted %s", message.sid)
        }
    }

    override fun onConversationAdded(conversation: Conversation) {
        logger.v("onConversationAdded %s", conversation.sid)
    }

    override fun onConversationUpdated(
        conversation: Conversation,
        reason: Conversation.UpdateReason
    ) {
        logger.v("onConversationUpdated %s by %s", conversation.sid, reason)
    }

    override fun onConversationDeleted(conversation: Conversation) {
        logger.v("onConversationDeleted %s", conversation.sid)
    }

    override fun onConversationSynchronizationChange(conversation: Conversation) {
        if (shouldBeConnected) {
            logger.d(
                "Received onConversationSynchronizationChange callback " +
                        conversation.friendlyName + " : " + conversation.sid + " : " +
                        conversation.synchronizationStatus
            )
            if (conversation.sid == channelSid) {
                if (conversation.synchronizationStatus == Conversation.SynchronizationStatus.ALL) {
                    while (queuedMessagesToSend.peek() != null) {
                        queuedMessagesToSend.poll()?.let {
                            logger.d(
                                "Conversation is fully synchronized. Sending queued message: %s",
                                it
                            )
                            send(it)
                        }
                    }
                }
            }
        }
    }

    override fun onParticipantAdded(participant: Participant) {
        if (shouldBeConnected) {
            logger.d("onParticipantAdded %s", participant.identity)
            chatTransportListener.onMemberJoined(participant.identity ?: return)
        }
    }

    override fun onParticipantUpdated(participant: Participant, reason: Participant.UpdateReason) {
        if (shouldBeConnected) {
            logger.d("onParticipantUpdated %s by %s", participant.identity, reason)
        }
    }

    override fun onParticipantDeleted(participant: Participant) {
        if (shouldBeConnected) {
            logger.d("onParticipantDeleted %s", participant.identity)
            chatTransportListener.onMemberLeft(participant.identity ?: return)
        }
    }

    override fun onTypingStarted(conversation: Conversation, participant: Participant) {
        if (shouldBeConnected) {
            chatTransportListener.onTypingStarted(participant.identity ?: return)
        }
    }

    override fun onTypingEnded(conversation: Conversation, participant: Participant) {
        if (shouldBeConnected) {
            chatTransportListener.onTypingEnded(participant.identity ?: return)
        }
    }

    override fun onSynchronizationChanged(conversation: Conversation) {
        logger.v("onSynchronizationChanged")
    }

    override fun onError(errorInfo: ErrorInfo) {
        logger.w("onError [%d] %s", errorInfo.code, errorInfo.message)
    }

    override fun onUserUpdated(user: User, updateReason: User.UpdateReason) {
        logger.v("onUserUpdated %s by %s", user.identity, updateReason)
    }

    override fun onUserSubscribed(user: User) {
        logger.v("onUserSubscribed %s", user.identity)
    }

    override fun onUserUnsubscribed(user: User) {
        logger.v("onUserUnsubscribed %s", user.identity)
    }

    override fun onClientSynchronization(synchronizationStatus: ConversationsClient.SynchronizationStatus) {
        if (shouldBeConnected) {
            logger.d("Received onClientSynchronization callback with status $synchronizationStatus")
            if (synchronizationStatus == ConversationsClient.SynchronizationStatus.COMPLETED) {
                joinConversation(channelSid)
            }
        }
    }

    override fun onNewMessageNotification(
        channelSid: String,
        messageSid: String,
        messageIndex: Long
    ) {
        logger.v(
            "onNewMessageNotification channelSid: [%s] messageSid: [%s]",
            channelSid,
            messageSid
        )
    }

    override fun onAddedToConversationNotification(conversationSid: String) {
        logger.v("onAddedToConversationNotification channelSid: [%s]", channelSid)
    }

    override fun onRemovedFromConversationNotification(conversationSid: String) {
        logger.v("onRemovedFromConversationNotification channelSid: [%s]", channelSid)
    }

    private fun joinConversation(channelSid: String) {
        val conversation =
            conversationsClient?.myConversations?.firstOrNull { it.sid == channelSid }
        if (conversation != null) {
            setConversation(conversation)
        } else {
            logger.w("Can't join Twilio conversation. Conversation not found: %s", channelSid)
        }
    }

    private fun setConversation(conversation: Conversation) {
        if (shouldBeConnected) {
            this.conversation = conversation
            try {
                conversation.addListener(this)
                if (conversation.status == Conversation.ConversationStatus.JOINED) {
                    chatTransportListener.onTransportStateChanged(getState())
                    fetchLastMessages(conversation)
                } else {
                    joinConversation(conversation)
                }
            } catch (ex: IllegalStateException) {
                joinConversation(conversation)
            }
        }
    }

    private fun joinConversation(conversation: Conversation) {
        conversation.join(
            object : StatusListener {
                override fun onSuccess() {
                    chatTransportListener.onTransportStateChanged(getState())
                    fetchLastMessages(conversation)
                }

                override fun onError(errorInfo: ErrorInfo) {
                    logger.e("Failed to join conversation ${conversation.sid}: $errorInfo")
                    disconnect()
                }
            }
        )
    }

    private fun fetchLastMessages(conversation: Conversation) {
        if (shouldBeConnected && conversation.synchronizationStatus == Conversation.SynchronizationStatus.ALL) {
            conversation.getLastMessages(VISIBLE_LAST_MESSAGE_COUNT) { messages: List<Message> ->
                logger.d("Last messages: %d", messages.size)
                for (message in messages) {
                    notifyMessageReceived(message)
                }
            }
        }
    }

    private fun notifyMessageReceived(message: Message) {
        if (shouldBeConnected) {
            try {
                val messageJson = JSONObject(message.body)
                val type = messageJson.getString("type")
                // Check if message is from task VA
                // If yes then fetch the actual message hidden behind the task VA message API
                if (type == "server_message") {
                    val messageId = messageJson.getInt("message_id")
                    handleTaskVaMessage(message, messageId)
                } else {
                    notifyListener(message, message.body)
                }
            }catch (e: Exception) {
                logger.w("Error processing message: ${e.stackTrace}")
            }
        }
    }

    private fun handleTaskVaMessage(message: Message, messageId: Int) {
        ChatTransportUtil.handleTaskVaMessage(taskVaMessageFetcher, messageId,
            onSuccess = { content ->
                notifyListener(message, content)
            },
            onFailure = {
                logger.e("Error fetching task va message: $it")
            })
    }

    private fun notifyListener(message: Message, content: String) {
        chatTransportListener.onMessageReceived(
            message.sid,
            content,
            message.author,
            message.dateCreatedAsDate ?: Date(),
            message.messageIndex
        )
    }

    override fun onNotificationSubscribed() {
        logger.v("onNotificationSubscribed")
    }

    override fun onNotificationFailed(errorInfo: ErrorInfo) {
        logger.v("onNotificationFailed [%d] %s", errorInfo.code, errorInfo.message)
    }

    override fun onConnectionStateChange(connectionState: ConversationsClient.ConnectionState) {
        logger.d("onConnectionStateChange %s", connectionState.name)
        chatTransportListener.onTransportStateChanged(getState())
    }

    private fun Logger.logStatus(success: String, error: String) = object : StatusListener {
        override fun onSuccess() = d(success)
        override fun onError(errorInfo: ErrorInfo) = w(error + errorInfo.message)
    }
}
