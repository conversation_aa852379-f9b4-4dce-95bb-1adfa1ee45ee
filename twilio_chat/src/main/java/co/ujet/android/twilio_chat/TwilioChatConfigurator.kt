package co.ujet.android.twilio_chat

import androidx.annotation.Keep
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory

@Keep
class TwilioChatConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        EntryPointFactory.registerEntryPoint(
            ChatTransportFactory::class.java,
            TwilioChatTransportFactory
        )
        EntryPointFactory.registerEntryPoint(UjetModule::class.java, TwilioChatUjetModule)
    }
}
