package co.ujet.android.twilio_chat

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportListener
import co.ujet.android.modulemanager.entrypoints.chat.Constants.TWILIO_CONVERSATIONS
import com.twilio.conversations.ConversationsClient

object TwilioChatTransportFactory : ChatTransportFactory {
    override val transportType = TWILIO_CONVERSATIONS
    override val transportVersion: String = ConversationsClient.getSdkVersion()

    override fun <T : ChatMessage> createChatTransport(
        context: Context,
        channelId: String,
        region: String?,
        chatTransportListener: ChatTransportListener<T>,
        chatAccessTokenFetcher: Chat<PERSON>ccessTokenFetcher,
        taskVaMessageFetcher: TaskVaMessageFetcher,
        logger: Logger
    ) = TwilioChatTransport(
        context,
        channelId,
        chatTransportListener,
        chatAccessTokenFetcher,
        taskVaMessageFetcher,
        logger,
    )
}
