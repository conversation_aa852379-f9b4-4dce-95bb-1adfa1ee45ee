fastlane documentation
================
# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```
xcode-select --install
```

Install _fastlane_ using
```
[sudo] gem install fastlane -NV
```
or alternatively using `brew cask install fastlane`

# Available Actions
## Android
### android test
```
fastlane android test
```
Runs all the tests
### android deploy
```
fastlane android deploy
```
Deploy a new version to the Google Play
### android beta_qa
```
fastlane android beta_qa
```
Submit a new QA Beta Build to Crashlytics Beta
### android beta_demo
```
fastlane android beta_demo
```
Submit a new Demo Beta Build to Crashlytics Beta
### android beta_rc
```
fastlane android beta_rc
```
Submit a new RC Beta Build to Crashlytics Beta
### android beta_staging
```
fastlane android beta_staging
```
Submit a new Staging Beta Build to Crashlytics Beta
### android beta_production
```
fastlane android beta_production
```
Submit a new Production Beta Build to Crashlytics Beta

----

This README.md is auto-generated and will be re-generated every time [fastlane](https://fastlane.tools) is run.
More information about fastlane can be found on [fastlane.tools](https://fastlane.tools).
The documentation of fastlane can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
