module Fastlane
  module Actions
    class UploadAppAction < Action
      def self.run(params)
        name = get_app_file_name(params[:target])
        app_file = get_app_file_path(name)
        UI.user_error! "Couldn't find the app file: #{name}" if app_file.nil?

        upload(
          user: "#{params[:lt_user]}:#{params[:lt_api_key]}",
          name: name,
          app_file: app_file,
          target: params[:target]
        )
      end

      def self.get_app_file_path(file_name)
        Dir.glob("testApp/build/outputs/apk/**/#{file_name}").first
      end

      def self.get_app_file_name(target)
        "testApp-#{target}-basic-release.apk"
      end

      def self.upload(user:, name:, app_file:, target:)
        system "curl -sS -X POST -H 'Accept:application/json' \
            -u '#{user}' \
            https://manual-api.lambdatest.com/app/upload/realDevice \
            -F 'appFile=@#{app_file}' \
            -F 'name=#{name}'"
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        "Upload app to LambdaTest"
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(key: :lt_user,
                                       env_name: "LT_USER",
                                       description: "LambdaTest user",
                                       optional: false),
          FastlaneCore::ConfigItem.new(key: :lt_api_key,
                                       env_name: "LT_API_KEY",
                                       description: "LambdaTest api key",
                                       optional: false),
          FastlaneCore::ConfigItem.new(key: :target,
                                       env_name: "FL_UPLOAD_APP_TARGET",
                                       description: "target",
                                       optional: false,
                                       default_value: "qa")
        ]
      end

      def self.is_supported?(platform)
        platform == :android
      end
    end
  end
end
