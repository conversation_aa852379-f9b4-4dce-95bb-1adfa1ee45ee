# Customise this file, documentation can be found here:
# https://github.com/fastlane/fastlane/tree/master/fastlane/docs
# All available actions: https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Actions.md
# can also be listed using the `fastlane actions` command

# Change the syntax highlighting to Ruby
# All lines starting with a # are ignored when running `fastlane`

# If you want to automatically update fastlane if a new version is available:
# update_fastlane

# This is the minimum version number required.
# Update this, if you use features of a newer version
fastlane_version "2.140.0"

default_platform :android

platform :android do
    before_all do
        # ENV["SLACK_URL"] = "https://hooks.slack.com/services/..."
    end

    desc "Runs all the tests"
    lane :test do
        gradle(task: "test")
    end

    desc "Deploy a new version to the Google Play"
    lane :deploy do
        gradle(task: "assembleRelease")
        supply
    end

    # You can define as many lanes as you want

    after_all do |lane|
        # This block is called, only if the executed lane was successful

        # slack(
        #   message: "Successfully deployed new App Update."
        # )
    end

    error do |lane, exception|
        # slack(
        #   message: exception.message,
        #   success: false
        # )
    end

    desc "Release test app"
    lane :release_app do |options|
        target = options[:target]
        version_name = options[:version_name]
        modules_info = options[:modules_info]
        release_notes_message = options[:release_notes_message]

        # validate `target` param
        available_targets = [ "qa", "rc", "production" ]
        unless available_targets.include? target
          UI.user_error! "invalid target: #{target} must be one of #{available_targets.join(', ')}"
        end

        firebase_app_id = get_firebase_app_id(target: target)
        version_code = get_version_code(firebase_app_id).to_s
        build_and_deploy(target: target,
                        version_name: version_name,
                        version_code: version_code,
                        modules_info: modules_info,
                        release_notes_message: release_notes_message)
    end

    desc "Submit a new Custom Build to Firebase"
    lane :firebase_custom_build do |options|
        code = options[:version_code]
        name = options[:version_name]
        args = options[:modules_info]
        release_notes_message = options[:release_notes_message]

        gradle(task: "clean")

        # Include version name and code to task string, also include ujet modules
        task_string = "assembleQaBasicRelease -Pversion_code="+code+" -Pversion_name="+name+" -Pargs="+args
        puts("Building QA custom build with #{task_string}")
        # Trigger custom build with version name, code and ujet modules
        gradle(task: task_string)

        puts("Deploying QA custom build app into firebase")
        deploy_to_firebase("1:866048216587:android:c1a0ffccbc949488", release_notes_message,
        "#{project_path}/testApp/build/outputs/apk/qaBasic/release/testApp-qa-basic-release.apk")
    end

    desc "Submit a new QA/PR Beta Build to Firebase"
    lane :firebase_qa do |options|
        name = options[:version_name]
        code = options[:version_code]
        release_notes_message = options[:release_notes_message]

        gradle(task: "clean")

        # Include version name and code to task string
        task_string = "assembleQaBasicRelease -Pversion_code="+code+" -Pversion_name="+name
        puts("Building QA app with #{task_string}")
        # Trigger build with version name and code
        gradle(task: task_string)

        puts("Deploying QA app into firebase")
        deploy_to_firebase("1:866048216587:android:c1a0ffccbc949488", release_notes_message,
        "#{project_path}/testApp/build/outputs/apk/qaBasic/release/testApp-qa-basic-release.apk")
    end

    desc "Submit a new RC/Staging Beta Build to Firebase"
    lane :firebase_rc do |options|
        name = options[:version_name]
        code = options[:version_code]
        release_notes_message = options[:release_notes_message]

        gradle(task: "clean")

        # Include version name and code to task string
        task_string = "assembleRcBasicRelease -Pversion_code="+code+" -Pversion_name="+name
        puts("Building RC app with #{task_string}")
        # Trigger build with version name and code
        gradle(task: task_string)

        puts("Deploying RC app into firebase")
        deploy_to_firebase("1:866048216587:android:b1b91bffa703db95", release_notes_message,
        "#{project_path}/testApp/build/outputs/apk/rcBasic/release/testApp-rc-basic-release.apk")
    end

    desc "Submit a new Prod Beta Build to Firebase"
    lane :firebase_production do |options|
        name = options[:version_name]
        code = options[:version_code]
        release_notes_message = options[:release_notes_message]

        gradle(task: "clean")

        # Include version name and code to task string
        task_string = "assembleProdBasicRelease -Pversion_code="+code+" -Pversion_name="+name
        puts("Building production app with #{task_string}")
        # Trigger build with version name and code
        gradle(task: task_string)

        puts("Deploying production app into firebase")
        deploy_to_firebase("1:866048216587:android:30767f53bcb3968d", release_notes_message,
        "#{project_path}/testApp/build/outputs/apk/productionBasic/release/testApp-production-basic-release.apk")
    end
end

def project_path
    File.dirname(File.dirname(File.expand_path(__FILE__)))
end

# build gradle and deploy to firebase based on target
def build_and_deploy(target:, version_name:, version_code:, modules_info:, release_notes_message:)
    if target == 'qa'
        if modules_info.length > 0
            firebase_custom_build(version_name: version_name, version_code: version_code, modules_info: modules_info, release_notes_message: release_notes_message)
        else
            firebase_qa(version_name: version_name, version_code: version_code, release_notes_message: release_notes_message)
        end
    elsif target == 'rc'
        firebase_rc(version_name: version_name, version_code: version_code, release_notes_message: release_notes_message)
    elsif target == 'production'
        firebase_production(version_name: version_name, version_code: version_code, release_notes_message: release_notes_message)
    end
end

# App Distribution to Firebase
def deploy_to_firebase(firebase_app_id, release_notes_message, release_apk_path)
    firebase_app_distribution(
      app: firebase_app_id,
      firebase_cli_token: ENV['FIREBASE_TOKEN'],
      groups: "ujet-testers, tdl-testers, solazu",
      release_notes: release_notes_message,
      apk_path: release_apk_path,
    )
end

# Get firebase app id based on target to be used to get version code
def get_firebase_app_id(target:)
    if target == 'qa'
        return "1:866048216587:android:c1a0ffccbc949488"
    elsif target == 'rc'
        return "1:866048216587:android:b1b91bffa703db95"
    elsif target == 'production'
        return "1:866048216587:android:30767f53bcb3968d"
    else
       return UI.user_error!("Incorrect target version passed to get_firebase_app_id")
    end
end

# Get latest version code from firebase console and increment it to bump the version
def get_version_code(firebase_app_id)
    latest_release = firebase_app_distribution_get_latest_release(app: firebase_app_id)
    puts("latest firebase version:#{latest_release}")
    return latest_release[:buildVersion].to_i + 1
end

# More information about multiple platforms in fastlane: https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Platforms.md
# All available actions: https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Actions.md

# fastlane reports which actions are used
# No personal data is sent or shared. Learn more at https://github.com/fastlane/enhancer
