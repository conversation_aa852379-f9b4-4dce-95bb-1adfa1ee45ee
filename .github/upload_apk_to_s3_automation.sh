#!/bin/bash

# Get branch name and version name supplied to the script
branch=$branch_name
target_app=$target
version=$version_name

# Fetch apk file, parent folder name and version name to include in s3 path
if grep -q '^proj.*/base$' <<< "$branch"; then
  apk_file=qaBasic/release/testApp-qa-basic-release.apk
  parent_folder=proj
elif [ $target_app == 'qa' ]; then
  if [[ -z "$version" ]]; then
    echo "Target Version require to build QA app is missing"
    exit 1
  else
    apk_file=qaBasic/release/testApp-qa-basic-release.apk
    parent_folder=qa
  fi  
elif [ $target_app == 'rc' ]; then
  apk_file=rcBasic/release/testApp-rc-basic-release.apk
  parent_folder=rc
elif [ $target_app == 'production' ]; then
  apk_file=productionBasic/release/testApp-production-basic-release.apk
  parent_folder=production
else
  echo "wrong branch name or target passed to upload_apk_to_s3_automation"
  exit 1
fi

#Upload apk file into "automation--ujet-load--uswe2" S3 bucket 
echo "Uploading apk_file: $apk_file in parent folder: $parent_folder with version: $version"
aws s3 cp "testApp/build/outputs/apk/$apk_file" s3://automation--ujet-load--uswe2/apks/$parent_folder/$version/ --acl bucket-owner-full-control
