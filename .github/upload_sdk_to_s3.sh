#!/bin/bash

# Get branch name and target supplied to the script
branch=$branch_name
target_app=$target

# we will deprecate ujet-distros account after all customers are migrated to ujet-prod account.
# we need to upload to both ujet-distros and ujet-prod account until then

#Upload to AWS Distros account S3
if grep -q '^release/.*$' <<< "$branch"; then
  if [ $target_app == 'rc' ]; then
    distros_repo="ujet-android-sdk-rc"
    distros_access_key=$ujet_distros_rc_access_key
    distros_secret_key=$ujet_distros_rc_secret_key

    prod_repo="sdk.ujet.co/android-staging"
    prod_access_key=$ujet_prod_access_key
    prod_secret_key=$ujet_prod_secret_key
    parent_prod_dir="android-staging"
  elif [ $target_app == 'production' ]; then
    distros_repo="ujet-android-sdk"
    distros_access_key=$ujet_distros_prod_access_key
    distros_secret_key=$ujet_distros_prod_secret_key

    prod_repo="sdk.ujet.co/android"
    prod_access_key=$ujet_prod_access_key
    prod_secret_key=$ujet_prod_secret_key
    parent_prod_dir="android"
  else
    echo "wrong target passed so skipping uploading sdk into S3"
    exit 0
  fi
else
  echo "release branch is not selected so skipping uploading sdk into S3"
  exit 0
fi

echo "Build new SDK for $target_app packages"

./gradlew clean
./gradlew assembleRelease

echo "Uploading $target_app packages into AWS Distros account S3"
./gradlew publish -PbucketName=${distros_repo} -Paws.s3.access=${distros_access_key} -Paws.s3.secret=${distros_secret_key}

echo "Uploading $target_app packages into AWS Prod account S3"
./gradlew publish -PbucketName=${prod_repo} -Paws.s3.access=${prod_access_key} -Paws.s3.secret=${prod_secret_key}

#Make uploaded packages public in ujet-prod account
echo "make uploaded $target_app packages public in ujet-prod account"

makeContentPublic() {
  ujet_version=$(AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3 cp s3://sdk.ujet.co/$3/co/ujet/android/$4/maven-metadata.xml - | sed -n 's|<latest>\(.*\)</latest>|\1|p')
  ujet_version=`echo $ujet_version | sed -e 's/^[[:space:]]*//'`
  echo "UJET SDK $4 version: $ujet_version"

  AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3api put-object-acl --bucket sdk.ujet.co --key $3/co/ujet/android/$4/${ujet_version}/$4-${ujet_version}.aar --acl public-read
  AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3api put-object-acl --bucket sdk.ujet.co --key $3/co/ujet/android/$4/${ujet_version}/$4-${ujet_version}-sources.jar --acl public-read
  AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3api put-object-acl --bucket sdk.ujet.co --key $3/co/ujet/android/$4/${ujet_version}/$4-${ujet_version}.pom --acl public-read
}

makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "ujet-android"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "ujet-markdown-android"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "cobrowse"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "module_manager"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "chat_red"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "chat_blue"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "call_red"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "ui"
makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "commons"
#makeContentPublic ${prod_access_key} ${prod_secret_key} ${parent_prod_dir} "call_blue"
