#!/bin/bash

# Get version name
branch=$branch_name
target_app=$target

# project env build uses branch name
# 'qa' target uses sprint number taken from input
# 'rc', 'production' target uses sdk version
if grep -q '^proj.*/base$' <<< "$branch"; then
  version=$(echo $branch| cut -d'/' -f 1)
elif [ $target_app == 'qa' ]; then
  version="$(grep 'SPRINT_NUMBER' 'ujet/src/main/java/co/ujet/android/UjetVersion.kt' | cut -d'=' -f2 | sed 's/[^a-zA-Z0-9.]//g')"
elif grep -q '^release/.*$' <<< "$branch"; then
  if ([ $target_app == 'rc' ]) || ([ $target_app == 'production' ]); then
    version="$(grep 'BUILD' 'ujet/src/main/java/co/ujet/android/UjetVersion.kt' | cut -d'=' -f2 | sed 's/[^a-zA-Z0-9.]//g')"
  else
    echo "wrong target passed to get_version"
    exit 1
  fi
else
  echo "wrong branch name passed to get_version"
  exit 1
fi

echo "version name is: $version"

#Save version name to be used in remaining steps of workflow
echo "VERSION_NAME=$version" >> $GITHUB_ENV
