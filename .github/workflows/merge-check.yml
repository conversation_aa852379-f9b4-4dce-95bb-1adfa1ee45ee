name: PR Simple Merge Check
on: issue_comment
concurrency:
  group: merge-check-${{ github.event.issue.number }}
  cancel-in-progress: true

jobs:
  simple_merge_check:
    runs-on: ubuntu-latest
    if: ${{ github.event.issue.pull_request && github.event.comment.body == '/merge-check' }}
    steps:
      - name: Post comment with link to run
        uses: octokit/request-action@v2.x
        with:
          route: POST /repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments
          body: ${{ toJSON(env.COMMENT_BODY) }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COMMENT_BODY: |
            Starting merge verification with source branch `develop`
            https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}

      - name: Request PR data from Github API
        uses: octokit/request-action@v2.x
        id: pr_data
        with:
          route: ${{ github.event.issue.pull_request.url }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Add PR branch names to workflow env variables
        run: |
          echo "base_branch=${{ fromJson(steps.pr_data.outputs.data).base.ref }}" >> $GITHUB_ENV
          echo "merge_branch=${{ fromJson(steps.pr_data.outputs.data).head.ref }}" >> $GITHUB_ENV

      - name: Checkout the PR base branch
        uses: actions/checkout@v2
        with:
          ref: ${{ env.base_branch }}

      # need to set up git user config to create a merge commit, even if not pushing it
      - name: Merge the upstream branch into the local base branch
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git config pull.rebase false
          git fetch --unshallow origin "${{ env.base_branch }}"
          git pull origin develop

      - name: Ensure no differences between local branch and PR head branch
        run: |
          git fetch origin "${{ env.merge_branch }}"
          git diff --quiet "origin/${{ env.merge_branch }}"

      - name: Approve the PR
        if: success()
        uses: octokit/request-action@v2.x
        with:
          route: POST /repos/${{ github.repository }}/pulls/${{ github.event.issue.number }}/reviews
          body: ${{ toJSON(env.COMMENT_BODY) }}
          event: APPROVE
          commit_id: ${{ fromJson(steps.pr_data.outputs.data).head.sha }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COMMENT_BODY: >
            Verified that `${{ env.merge_branch }}` branch is a clean merge of `develop`
            into `${{ env.base_branch }}`

      - name: Post verification failure comment
        if: failure()
        uses: octokit/request-action@v2.x
        with:
          route: POST /repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments
          body: ${{ toJSON(env.COMMENT_BODY) }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COMMENT_BODY: Could not verify merge
