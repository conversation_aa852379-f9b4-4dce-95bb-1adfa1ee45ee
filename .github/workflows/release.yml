name: Release
on:
  workflow_dispatch:
    inputs:
      target:
        description: 'Target'
        required: true
        default: 'qa'
        type: choice
        options:
        - qa
        - rc
        - production
      
      modules_info:
        description: 'Modules info to build, required only for custom builds targeting QA, ignore otherwise. Example, "ujet,cobrowse" (double quotes are must) for including Co-browse module.'
        required: false
        type: string

      release_notes_message:
        description: 'Release notes, must include for custom builds targeting QA to list modules that are included in build, otherwise optional. Example, "This build does not contain Co-browse module" (double quotes are must)'
        required: false
        type: string

      skip_sdk_upload:
        description: 'Skip SDK Upload to S3'
        required: false
        type: boolean
        default: false

jobs:
  job:
    runs-on: macos-14-xlarge
    steps:
      - name: 'Checkout sources'
        uses: actions/checkout@v3

      - name: Set up Java
        uses: actions/setup-java@v2
        with:
          distribution: "temurin"
          java-version: 17

      - name: 'Install gems'
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true

      - name: Get version name
        env:
          branch_name: ${{ github.ref_name }}
          target: ${{ github.event.inputs.target }}
        run: source ./.github/get_version.sh
        shell: bash

      - name: 'Release test app'
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
            bundle exec fastlane release_app \
            target:${{ github.event.inputs.target }} \
            version_name:${{ env.VERSION_NAME }} \
            modules_info:${{ github.event.inputs.modules_info }} \
            release_notes_message:${{ github.event.inputs.release_notes_message }} \

      - name: 'Configure AWS credentials for uploading apk for automation'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.UJET_PROD_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.UJET_PROD_SECRET_KEY }}
          aws-region: us-west-2

      - name: Upload apk into S3 for automation
        env:
          branch_name: ${{ github.ref_name }}
          target: ${{ github.event.inputs.target }}
          version_name: ${{ env.VERSION_NAME }}
        run: source ./.github/upload_apk_to_s3_automation.sh
        shell: bash

      - name: Upload App to LambdaTest for E2E testings
        if: ${{ github.ref_name == 'develop' || startsWith(github.ref_name, 'release/') }}
        env:
          LT_USER: ${{ vars.LT_USER }}
          LT_API_KEY: ${{ secrets.LT_API_KEY }}
        run: |
          bundle exec fastlane run upload_app \
            target:${{ github.event.inputs.target }}

      - name: 'Configure AWS credentials for uploading SDK packages'
        if: ${{ (github.event.inputs.skip_sdk_upload == 'false') &&
          (github.event.inputs.target == 'rc' ||
          github.event.inputs.target == 'production') }}
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.UJET_DISTROS_PROD_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.UJET_DISTROS_PROD_SECRET_KEY }}
          aws-region: us-west-2

      - name: 'Upload SDK into S3'
        if: ${{ (github.event.inputs.skip_sdk_upload == 'false') &&
          (github.event.inputs.target == 'rc' ||
          github.event.inputs.target == 'production') }}
        env:
          target: ${{ github.event.inputs.target }}
          branch_name: ${{ github.ref_name }}
          ujet_distros_rc_access_key: ${{ secrets.UJET_DISTROS_RC_ACCESS_KEY }}
          ujet_distros_rc_secret_key: ${{ secrets.UJET_DISTROS_RC_SECRET_KEY }}
          ujet_distros_prod_access_key: ${{ secrets.UJET_DISTROS_PROD_ACCESS_KEY }}
          ujet_distros_prod_secret_key: ${{ secrets.UJET_DISTROS_PROD_SECRET_KEY }}
          ujet_prod_access_key: ${{ secrets.UJET_PROD_ACCESS_KEY }}
          ujet_prod_secret_key: ${{ secrets.UJET_PROD_SECRET_KEY }}
        run: source ./.github/upload_sdk_to_s3.sh
        shell: bash
