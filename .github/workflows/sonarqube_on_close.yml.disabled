name: SonarQube develop merge

on:
  pull_request:
    branches: [ develop ]
    types: [ closed ]

jobs:
  run_develop_analysis:
    if: github.event.pull_request.merged == true
    runs-on: load
    steps:
      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: '12'
          always-auth: true
          registry-url: 'https://npm.pkg.github.com'
          scope: '@ujet'
      - name: 'Checkout repository on branch: develop'
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: develop
          clean: true
      - name: Cache node modules
        uses: actions/cache@v2
        id: node-cache-develop
        env:
          cache-name: cache-node-modules
        with:
          path: |
            node_modules
            /home/<USER>/.cache/Cypress
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Install dependencies
        if: steps.node-cache-develop.outputs.cache-hit != 'true'
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Set up JDK 11
        uses: actions/setup-java@v1
        with:
          java-version: 11
      - name: Setup Android SDK
        uses: android-actions/setup-android@v2
      - name: Clean project
        run: ./gradlew :ujet:clean 
      - name: Build project
        run: ./gradlew :ujet:assembleRelease
      - name: Run an analysis of the PR
        run: /home/<USER>/sonar-scanner/bin/sonar-scanner
          -Dsonar.projectKey=${{ github.event.repository.name }}
          -Dsonar.host.url=https://sonarqube.lod.ujet.xyz/
          -Dsonar.login=${{ secrets.SONARQUBE_TOKEN }}
          -Dsonar.scm.provider=git
          -Dsonar.java.binaries=ujet/build
  delete_temp_project:
    runs-on: load
    steps:
      - name: Delete temp project
        run: curl -X POST -u "${{ secrets.SONARQUBE_TOKEN }}:" "https://sonarqube.lod.ujet.xyz/api/projects/delete?project=${{ github.event.repository.name }}-${{ github.event.pull_request.number }}"
