name: <PERSON><PERSON> YAML

on:
  pull_request:
    branches: [ develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: 'Checkout repository on branch: ${{ github.REF }}'
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.HEAD_REF }}
          clean: true
      - name: Lint YAML files
        run: npm run lint:yaml
