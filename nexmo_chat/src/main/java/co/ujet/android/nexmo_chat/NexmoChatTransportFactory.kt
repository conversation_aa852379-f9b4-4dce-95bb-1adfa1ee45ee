package co.ujet.android.nexmo_chat

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportListener
import co.ujet.android.modulemanager.entrypoints.chat.Constants.NEXMO_CONVERSATIONS
import com.nexmo.client.NexmoClient

object NexmoChatTransportFactory : ChatTransportFactory {
    ******** val transportType = NEXMO_CONVERSATIONS
    ******** val transportVersion: String
        get() = NexmoClient.get()?.sdkVersion ?: "unknown"

    ******** fun <T : ChatMessage> createChatTransport(
        context: Context,
        channelId: String,
        region: String?,
        chatTransportListener: ChatTransportListener<T>,
        chatAccessTokenFetcher: ChatAccessTokenFetcher,
        taskVaMessageFetcher: TaskVaMessageFetcher,
        logger: Logger
    ) = NexmoChatTransport(
        context,
        channelId,
        region,
        chatTransportListener,
        chatAccessTokenFetcher,
        taskVaMessageFetcher,
        logger
    )
}
