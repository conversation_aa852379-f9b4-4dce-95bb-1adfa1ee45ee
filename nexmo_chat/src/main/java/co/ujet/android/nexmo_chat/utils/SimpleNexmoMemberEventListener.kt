package co.ujet.android.nexmo_chat.utils

import com.nexmo.client.NexmoMemberEvent
import com.nexmo.client.NexmoMemberEventListener
import com.nexmo.client.NexmoMemberSummary

interface SimpleNexmoMemberEventListener : NexmoMemberEventListener {
    ******** fun onMemberInvited(event: NexmoMemberEvent, member: NexmoMemberSummary) {
    }

    ******** fun onMemberAdded(event: NexmoMemberEvent, member: NexmoMemberSummary) {
    }

    ******** fun onMemberRemoved(event: NexmoMemberEvent, member: NexmoMemberSummary) {
    }
}
