package co.ujet.android.nexmo_chat.utils

import com.nexmo.client.NexmoAttachmentEvent
import com.nexmo.client.NexmoDeletedEvent
import com.nexmo.client.NexmoDeliveredEvent
import com.nexmo.client.NexmoMessageEvent
import com.nexmo.client.NexmoMessageEventListener
import com.nexmo.client.NexmoRejectedEvent
import com.nexmo.client.NexmoSeenEvent
import com.nexmo.client.NexmoSubmittedEvent
import com.nexmo.client.NexmoTextEvent
import com.nexmo.client.NexmoTypingEvent
import com.nexmo.client.NexmoUndeliverableEvent

interface SimpleNexmoMessageEventListener: NexmoMessageEventListener {
    ******** fun onTextEvent(textEvent: NexmoTextEvent) {
    }

    ******** fun onMessageEvent(messageEvent: NexmoMessageEvent) {
    }

    ******** fun onAttachmentEvent(attachmentEvent: NexmoAttachmentEvent) {
    }

    ******** fun onEventDeleted(deletedEvent: NexmoDeletedEvent) {
    }

    ******** fun onSeenReceipt(seenEvent: NexmoSeenEvent) {
    }

    ******** fun onDeliveredReceipt(deliveredEvent: NexmoDeliveredEvent) {
    }

    ******** fun onSubmittedReceipt(submittedEvent: NexmoSubmittedEvent) {
    }

    ******** fun onRejectedReceipt(rejectedEvent: NexmoRejectedEvent) {
    }

    ******** fun onUndeliverableReceipt(undeliverableEvent: NexmoUndeliverableEvent) {
    }

    ******** fun onTypingEvent(typingEvent: NexmoTypingEvent) {
    }
}
