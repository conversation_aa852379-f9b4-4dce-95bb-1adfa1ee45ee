package co.ujet.android.nexmo_chat

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.ChatTransportUtil
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransport
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportListener
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportState
import co.ujet.android.modulemanager.entrypoints.chat.Constants.NEXMO_CONVERSATIONS
import co.ujet.android.nexmo_chat.ui.NexmoPermissionRequestActivity
import co.ujet.android.nexmo_chat.utils.MainHandlerImmediate.runOnMain
import co.ujet.android.nexmo_chat.utils.SimpleNexmoMemberEventListener
import co.ujet.android.nexmo_chat.utils.SimpleNexmoMessageEventListener
import com.nexmo.client.*
import com.nexmo.client.request_listener.NexmoApiError
import com.nexmo.client.request_listener.NexmoConnectionListener
import com.nexmo.client.request_listener.NexmoConnectionListener.ConnectionStatus
import com.nexmo.client.request_listener.NexmoConnectionListener.ConnectionStatusReason
import com.nexmo.client.request_listener.NexmoRequestListener
import com.nexmo.utils.logger.ILogger
import org.json.JSONException
import org.json.JSONObject
import java.util.concurrent.ConcurrentLinkedQueue

private const val VISIBLE_LAST_MESSAGE_COUNT = 100

class NexmoChatTransport<T : ChatMessage>(
    private val context: Context,
    private val conversationId: String,
    private val region: String?,
    private val chatTransportListener: ChatTransportListener<T>,
    private val chatAccessTokenFetcher: ChatAccessTokenFetcher,
    private val taskVaMessageFetcher: TaskVaMessageFetcher,
    private val logger: Logger
) : ChatTransport<T>, NexmoConnectionListener, SimpleNexmoMemberEventListener, SimpleNexmoMessageEventListener, NexmoTypingEventListener {

    private val queuedMessagesToSend = ConcurrentLinkedQueue<T>()
    private var nexmoClient: NexmoClient? = null
    private var conversation: NexmoConversation? = null
    private var shouldBeConnected = false
    private var permissionDenied = false

    ******** fun connect() {
        shouldBeConnected = true
        permissionDenied = false

        if (!hasReadPhoneStatePermission()) {
            requestPhoneStatePermission()
            return
        }
        chatTransportListener.onTransportStateChanged(getState())
        chatAccessTokenFetcher.fetch(
            NEXMO_CONVERSATIONS,
            onSuccess = { accessToken ->
                if (!shouldBeConnected) {
                    logger.i("Nexmo chat access token fetched, but transport should not be connected anymore")
                    return@fetch
                }
                nexmoClient = getOrCreateClient().apply {
                    setConnectionListener(this@NexmoChatTransport)
                    login(accessToken)
                }
            },
            onFailure = {
                logger.w("Failed to fetch Nexmo chat access token")
                disconnect()
            }
        )
    }

    private fun getOrCreateClient(): NexmoClient {
        return NexmoClient.get()
            ?: run {
                val domain = if (vonageRegions.contains(region)) {
                    "vonage.com"
                } else {
                    "nexmo.com"
                }
                logger.d("Creating a new Nexmo client")
                NexmoClient
                    .Builder()
                    .logLevel(if (BuildConfig.DEBUG) ILogger.eLogLevel.DEBUG else ILogger.eLogLevel.CRITICAL)
                    .environmentHost("https://ws-$region.$domain/")
                    .restEnvironmentHost("https://api-$region.$domain/")
                    .imageProcessingServiceUrl("https://api-$region.$domain/v1/image/")
                    .build(context)
            }
    }

    private fun requestPhoneStatePermission() {
        logger.d("Missing required READ_PHONE_STATE permission for Nexmo SDK, requesting it")
        val localBroadcastManager = LocalBroadcastManager.getInstance(context)
        localBroadcastManager.registerReceiver(object : BroadcastReceiver() {
            ******** fun onReceive(context: Context?, intent: Intent?) {
                localBroadcastManager.unregisterReceiver(this)
                if (shouldBeConnected && hasReadPhoneStatePermission()) {
                    connect()
                } else if (!hasReadPhoneStatePermission()) {
                    logger.w("Required READ_PHONE_STATE permission not granted. The Nexmo SDK can not be started.")
                    permissionDenied = true
                    disconnect()
                }
            }
        }, IntentFilter(NexmoPermissionRequestActivity.INTENT_PERMISSION_RESULT))
        context.startActivity(Intent(context, NexmoPermissionRequestActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        })
    }

    private fun hasReadPhoneStatePermission() = ActivityCompat.checkSelfPermission(
        context,
        Manifest.permission.READ_PHONE_STATE
    ) == PackageManager.PERMISSION_GRANTED

    ******** fun getState(): ChatTransportState {
        return when {
            permissionDenied -> {
                // Reset permissionDenied to false to break infinite loop of adding chat end message
                // in Android 5 device
                permissionDenied = false
                ChatTransportState.ERROR
            }

            shouldBeConnected.not() -> ChatTransportState.DISCONNECTED
            nexmoClient?.isConnected == true && conversation != null -> ChatTransportState.CONNECTED
            else -> ChatTransportState.CONNECTING
        }
    }

    ******** fun send(chatMessage: T) {
        if (!shouldBeConnected) {
            return
        }

        if (conversation == null) {
            logger.w("Queued outbound message as the conversation is not initialized yet")
            queuedMessagesToSend.offer(chatMessage)
        } else {
            val messageJsonString = chatMessage.toJson().toString()
            logger.v("Sending message to Nexmo conversation: message length: ${messageJsonString.length}")
            conversation?.sendMessage(
                NexmoMessage.fromText(messageJsonString),
                object : NexmoRequestListener<Void> {
                    ******** fun onSuccess(result: Void?) {
                        logger.v("Message sent to Nexmo conversation: ${messageJsonString.length}")
                        if (shouldBeConnected) {
                            chatTransportListener.onMessageSent(chatMessage)
                        }
                    }

                    ******** fun onError(error: NexmoApiError) {
                        logger.w("Failed to send Nexmo message: ${error.toLoggerMessage()}")
                        if (shouldBeConnected) {
                            chatTransportListener.onMessageSendFailed(chatMessage)
                        }
                    }
                })
        }
    }

    ******** fun onTypingStarted() {
        if (!shouldBeConnected) {
            return
        }
        logger.v("Sending typing started event to Nexmo conversation")
        conversation?.startTyping()
    }

    ******** fun onTypingStopped() {
        if (nexmoClient == null) {
            return
        }
        logger.v("Sending typing stopped event to Nexmo conversation")
        conversation?.stopTyping()
    }

    ******** fun sendMessagePreview(chatMessage: ChatMessage) {
        if (!shouldBeConnected) {
            return
        }
        logger.v("Sending custom event to Nexmo chat")
        conversation?.sendCustomEvent(
            "ujet_preview",
            chatMessage.toMap(),
            object : NexmoRequestListener<Void> {
                ******** fun onSuccess(result: Void?) {
                    logger.i("Message preview sent")
                }

                ******** fun onError(error: NexmoApiError) {
                    logger.w("Message preview failed to send ${error.toLoggerMessage()}")
                }
            })
    }

    ******** fun disconnect() {
        shouldBeConnected = false
        nexmoClient?.logout()
        nexmoClient = null
        chatTransportListener.onTransportStateChanged(getState())
    }

    ******** fun onConnectionStatusChange(
        status: ConnectionStatus,
        reason: ConnectionStatusReason
    ) = runOnMain {
        logger.d("Nexmo connection status changed: $status ($reason)")
        if (status == ConnectionStatus.CONNECTED && conversation == null) {
            joinConversation(conversationId)
        }
        chatTransportListener.onTransportStateChanged(getState())
    }

    private fun joinConversation(conversationId: String) {
        logger.v("Getting conversation for conversation ID $conversationId")
        nexmoClient?.getConversation(conversationId, object :
            NexmoRequestListener<NexmoConversation> {
            ******** fun onSuccess(conversation: NexmoConversation?) {
                logger.v("Got conversation for conversation ID $conversationId: $conversation")
                if (conversation == null) {
                    logger.w("Can not get Nexmo conversation with id: $conversationId")
                    disconnect()
                } else {
                    joinConversation(conversation)
                }
            }

            ******** fun onError(error: NexmoApiError) {
                logger.w("Can not get Nexmo conversation with id: $conversationId, error: ${error.toLoggerMessage()}")
                disconnect()
            }
        })
    }

    private fun joinConversation(conversation: NexmoConversation) {
        logger.v("Joining Nexmo conversation: $conversation")
        if (!shouldBeConnected) {
            return
        }
        conversation.join(object : NexmoRequestListener<Void> {
            ******** fun onSuccess(result: Void?) {
                logger.v("Joined Nexmo conversation: $conversation")
                <EMAIL> = conversation
                conversation.addMemberEventListener(this@NexmoChatTransport)
                conversation.addMessageEventListener(this@NexmoChatTransport)
                conversation.addTypingEventListener(this@NexmoChatTransport)
                fetchLastMessages(conversation)
            }

            ******** fun onError(error: NexmoApiError) {
                logger.w("Can not join Nexmo conversation with id: $conversationId, error: ${error.toLoggerMessage()}")
                disconnect()
            }
        })
    }

    private fun fetchLastMessages(conversation: NexmoConversation) {
        if (!shouldBeConnected) {
            return
        }
        logger.v("Nexmo chat ******** last messages for conversation: $conversation")
        conversation.getEvents(
            VISIBLE_LAST_MESSAGE_COUNT,
            NexmoPageOrder.NexmoMPageOrderAsc,
            null,
            object : NexmoRequestListener<NexmoEventsPage> {
                ******** fun onSuccess(result: NexmoEventsPage?) {
                    if (result == null) {
                        logger.w("Failed to get recent Nexmo chat messages")
                        return
                    }
                    for (message in result.pageResponse.data) {
                        notifyEventReceived(message)
                    }
                }

                ******** fun onError(error: NexmoApiError) {
                    logger.w("Failed to get recent Nexmo chat messages: ${error.toLoggerMessage()}")
                }
            }
        )
    }

    private fun notifyEventReceived(event: NexmoEvent) {
        logger.v("Nexmo chat event received: $event")
        if (!shouldBeConnected || event !is NexmoMessageEvent) {
            return
        }
        try {
            val messageJson = JSONObject(event.message.text)
            val type = messageJson.getString("type")
            // Check if message is from task VA
            // If yes then fetch the actual message hidden behind the task VA message API
            if (type == "server_message") {
                val messageId = messageJson.getInt("message_id")
                handleTaskVaMessage(messageId, event)
            } else {
                notifyListener(event, event.message.text)
            }
        } catch (e: Exception) {
            logger.w("Error parsing server message: ${e.stackTrace}")
        }
    }

    private fun notifyListener(event: NexmoMessageEvent, content: String) {
        chatTransportListener.onMessageReceived(
            event.id.toString(),
            content,
            event.embeddedInfo.user.name,
            event.creationDate,
            event.id.toLong()
        )
    }

    private fun handleTaskVaMessage(messageId: Int, event: NexmoMessageEvent) {
        ChatTransportUtil.handleTaskVaMessage(taskVaMessageFetcher, messageId,
            onSuccess = { content ->
                notifyListener(event, content)
            },
            onFailure = {
                logger.e("Error ******** task va message: $it")
            })
    }

    private fun NexmoApiError.toLoggerMessage() =
        "NexmoApiError(type=$type, message='$message', tid='$tid', conversationId='$conversationId')"

    ******** fun onMemberAdded(event: NexmoMemberEvent, member: NexmoMemberSummary) {
        logger.v("Nexmo chat member added: $event, $member")
        if (!shouldBeConnected) {
            return
        }
        chatTransportListener.onMemberJoined(member.memberId)
    }

    ******** fun onMemberRemoved(event: NexmoMemberEvent, member: NexmoMemberSummary) {
        logger.v("Nexmo chat member removed: $event, $member")
        if (!shouldBeConnected) {
            return
        }
        chatTransportListener.onMemberLeft(member.memberId)
    }

    ******** fun onMemberInvited(event: NexmoMemberEvent, member: NexmoMemberSummary) {
        logger.v("Nexmo chat member invited: $event, $member")
    }

    ******** fun onMessageEvent(messageEvent: NexmoMessageEvent) {
        notifyEventReceived(messageEvent)
    }

    ******** fun onTyping(typingEvent: NexmoTypingEvent) {
        logger.v("Nexmo chat onTyping: $typingEvent")
        if (!shouldBeConnected) {
            return
        }
        if (typingEvent.state == NexmoTypingState.ON) {
            chatTransportListener.onTypingStarted(typingEvent.embeddedInfo.user.name)
        } else {
            chatTransportListener.onTypingEnded(typingEvent.embeddedInfo.user.name)
        }
    }

    companion object {
        private val vonageRegions = listOf("us-3", "us-4", "eu-3", "eu-4", "ap-3", "ap-4")
    }
}
