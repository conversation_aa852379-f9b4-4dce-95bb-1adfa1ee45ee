package co.ujet.android.nexmo_chat.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ujet.android.nexmo_chat.R

class NexmoPermissionRequestActivity : AppCompatActivity() {
    companion object {
        private const val READ_PHONE_STATE_PERMISSION_REQUEST_CODE = 1
        const val INTENT_PERMISSION_RESULT = "INTENT_PERMISSION_RESULT"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_PHONE_STATE),
            READ_PHONE_STATE_PERMISSION_REQUEST_CODE
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == READ_PHONE_STATE_PERMISSION_REQUEST_CODE && grantResults.firstOrNull() == PackageManager.PERMISSION_DENIED) {

            Toast.makeText(
                this,
                // Workaround for accessing a string resource defined in the :ujet module
                resources.getIdentifier("ujet_permission_read_phone_state_missing", "string", packageName),
                Toast.LENGTH_LONG
            ).show()
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(INTENT_PERMISSION_RESULT))
        finish()
    }
}