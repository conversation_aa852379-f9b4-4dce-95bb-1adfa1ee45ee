package co.ujet.android.nexmo_chat

import androidx.annotation.Keep
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory

@Keep
class NexmoChatConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        EntryPointFactory.registerEntryPoint(
            ChatTransportFactory::class.java,
            NexmoChatTransportFactory
        )
        EntryPointFactory.registerEntryPoint(UjetModule::class.java, NexmoChatUjetModule)
    }
}
