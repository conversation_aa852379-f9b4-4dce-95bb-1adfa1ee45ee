#!/bin/bash

#Get bucket name, aws credentials from commandline arguments
bucket_name=$1
access_key=$2
secret_key=$3

echo "prepareNexmoDependencies bucketName: $bucket_name"

CLIENT_SDK_PKG="client-sdk"
CLIENT_INFRA_PKG="clientInfrastructures"
MINI_RTC_PKG="MiniRTCRelease"
BASE_URL="https://artifactory.ess-dev.com/artifactory/gradle-dev-local/com/nexmo/android"

#Get latest version for each nexmo transitive dependencies
getPackageVersion() {
  #Arguments passed - package name
  latest_version=$(curl $BASE_URL/"$1"/maven-metadata.xml | sed -n 's|<latest>\(.*\)</latest>|\1|p')
  # shellcheck disable=SC2001
  latest_version=$(echo "$latest_version" | sed -e 's/^[[:space:]]*//')
  echo "$latest_version"
}

# download nexmo dependencies
downloadPackages() {
  #Arguments passed - package name, package version
  mkdir "$1"
  # shellcheck disable=SC2164
  cd "$1"
  { curl -O "$BASE_URL/$1/$2/$1-$2.pom"; }
  { curl -O "$BASE_URL/$1/$2/$1-$2.aar"; }

  if [ "$1" == $CLIENT_SDK_PKG ]
  then
    { curl -O "$BASE_URL/$1/$2/$1-$2-sources.jar"; }
  fi
  # shellcheck disable=SC2164
  # shellcheck disable=SC2103
  cd -
}

#Upload packages into aws S3
uploadToS3() {
  #Arguments passed - access key, secret key, bucket name, package name, package version
  #We make content public in ujet-prod account but not in ujet-distros account
  if [ "$3" == "ujet-android-sdk-rc" ] || [ "$3" == "ujet-android-sdk" ]
  then
    AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3 cp /tmp/"$4"/ s3://"$3"/com/nexmo/android/"$4"/"$5" --recursive
  else
    AWS_ACCESS_KEY_ID=$1 AWS_SECRET_ACCESS_KEY=$2 aws s3 cp /tmp/"$4"/ s3://"$3"/com/nexmo/android/"$4"/"$5" --acl public-read --recursive
  fi
}

client_sdk_version=$(getPackageVersion $CLIENT_SDK_PKG)
echo "client sdk latest version: $client_sdk_version"
# shellcheck disable=SC2164
cd /tmp/
downloadPackages "$CLIENT_SDK_PKG" "$client_sdk_version"

client_infra_version=$(getPackageVersion $CLIENT_INFRA_PKG)
echo "clientInfrastructures latest version: $client_infra_version"
# shellcheck disable=SC2164
cd /tmp/
downloadPackages "$CLIENT_INFRA_PKG" "$client_infra_version"

mini_rtc_version=$(getPackageVersion $MINI_RTC_PKG)
echo "MiniRTCRelease latest version: $mini_rtc_version"
# shellcheck disable=SC2164
cd /tmp/
downloadPackages "$MINI_RTC_PKG" "$mini_rtc_version"

#Upload packages into aws S3
# shellcheck disable=SC2154
echo "uploading nexmo dependencies into S3"
uploadToS3 "$access_key" "$secret_key" "$bucket_name" "$CLIENT_SDK_PKG" "$client_sdk_version"
uploadToS3 "$access_key" "$secret_key" "$bucket_name" "$CLIENT_INFRA_PKG" "$client_infra_version"
uploadToS3 "$access_key" "$secret_key" "$bucket_name" "$MINI_RTC_PKG" "$mini_rtc_version"

#Clear downloaded packages in /tmp/
# shellcheck disable=SC2164
cd /tmp/
# shellcheck disable=SC2115
rm -rf "$CLIENT_SDK_PKG/"
# shellcheck disable=SC2115
rm -rf "$CLIENT_INFRA_PKG/"
# shellcheck disable=SC2115
rm -rf "$MINI_RTC_PKG/"




