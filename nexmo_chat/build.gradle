plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

repositories {
    google()
    mavenCentral()
    maven {
        url "https://artifactory.ess-dev.com/artifactory/gradle-dev-local"
    }
}

android {
    namespace = "co.ujet.android.nexmo_chat"
    defaultConfig {
        buildToolsVersion = rootProject.ext.buildToolsVersion
        compileSdk rootProject.ext.compileSdkVersion
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        buildConfig = true
    }
}

build.dependsOn rootProject.setUjetVersion

dependencies {
    api project(":module_manager")
    implementation project(":ui")
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    implementation "androidx.annotation:annotation:$androidXAnnotationVersion"
    implementation "androidx.appcompat:appcompat:$androidXAppCompatVersion"
    implementation "androidx.localbroadcastmanager:localbroadcastmanager:$androidXLocalBroadcastManagerVersion"

    implementation("com.nexmo.android:client-sdk:$nexmoClientVersion")

    testImplementation "junit:junit:$junitVersion"
}

apply from: 'publish.gradle'
