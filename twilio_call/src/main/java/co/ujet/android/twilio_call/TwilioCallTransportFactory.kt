package co.ujet.android.twilio_call

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher
import co.ujet.android.modulemanager.entrypoints.call.CallTransport
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import co.ujet.android.modulemanager.entrypoints.call.CallTransportListener
import co.ujet.android.modulemanager.entrypoints.call.Constants.VOIP_PROVIDER_TWILIO
import com.twilio.voice.Voice

internal object TwilioCallTransportFactory : CallTransportFactory {

    override val transportType = VOIP_PROVIDER_TWILIO
    override val transportVersion: String = Voice.getVersion()

    override fun createCallTransport(
        context: Context,
        region: String?, //Only used in nexmo call
        callId: Int,
        participantId: Int, //Only used in nexmo call
        callTransportListener: CallTransportListener,
        callAccessTokenFetcher: CallAccessTokenFetcher,
        logger: Logger
    ): CallTransport =
        TwilioCallTransport(context, callId, callTransportListener, callAccessTokenFetcher, logger)
}