package co.ujet.android.twilio_call

import android.content.Context
import co.ujet.android.modulemanager.BuildConfig
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher
import co.ujet.android.modulemanager.entrypoints.call.CallTransport
import co.ujet.android.modulemanager.entrypoints.call.CallTransportListener
import co.ujet.android.modulemanager.entrypoints.call.CallTransportState
import co.ujet.android.modulemanager.entrypoints.call.FailureReason
import com.twilio.voice.*

internal class TwilioCallTransport(
    private val context: Context,
    private val callId: Int,
    private val callTransportListener: CallTransportListener,
    private val callAccessTokenFetcher: CallAccessTokenFetcher,
    private val logger: Logger
) : CallTransport, Call.Listener {

    private var twilioCall: Call? = null
    private var shouldBeConnected = false
    private var isFetchingAuthToken = false
    private var isMuted = false
    private var errorCode: Int? = null

    init {
        Voice.setLogLevel(if (BuildConfig.DEBUG) LogLevel.DEBUG else LogLevel.WARNING)
    }

    override fun connect() {
        if (shouldBeConnected) {
            logger.w("TwilioCallTransport.connect() called before previous call was disconnected")
            return
        }
        shouldBeConnected = true
        fetchAccessToken(
            onSuccess = { accessToken -> createTwilioCall(accessToken) },
            onFailure = { disconnect() }
        )
    }

    private fun fetchAccessToken(onSuccess: (String) -> Unit, onFailure: () -> Unit) {
        callTransportListener.onTransportStateChanged(getState())
        isFetchingAuthToken = true
        callAccessTokenFetcher.fetch("",
            onSuccess = { accessToken ->
                isFetchingAuthToken = false
                if (!shouldBeConnected) {
                    logger.i("Twilio call token fetched, but transport should not be connected anymore")
                    return@fetch
                }
                onSuccess(accessToken)
            },
            onFailure = {
                isFetchingAuthToken = false
                logger.w("Failed to fetch Twilio call access token")
                onFailure()
            }
        )
    }

    private fun createTwilioCall(accessToken: String) {
        val connectOptions = ConnectOptions.Builder(accessToken)
            .params(mapOf("call_id" to callId.toString(), "from" to "end_user"))
            .build()
        logger.d("Connecting Twilio call for Ujet call ID $callId")
        twilioCall = Voice.connect(context, connectOptions, this).apply {
            mute(isMuted)
        }
    }

    override fun getState(): CallTransportState {
        val errorCodeCopy = errorCode
        return when {
            !shouldBeConnected -> CallTransportState.Disconnected
            twilioCall?.state == Call.State.CONNECTED -> CallTransportState.Connected
            errorCodeCopy != null -> CallTransportState.Failed(errorCodeCopy.toFailureReason())
            else -> CallTransportState.Connecting
        }
    }

    override fun mute() {
        logger.i("Muting Twilio call: ${twilioCall?.sid} (Ujet call ID $callId)")
        isMuted = true
        twilioCall?.mute(true)
    }

    override fun unMute() {
        logger.i("Un-muting Twilio call: ${twilioCall?.sid} (Ujet call ID $callId)")
        isMuted = false
        twilioCall?.mute(false)
    }

    override fun disconnect() {
        if (!shouldBeConnected) {
            logger.w("Can't disconnect an already disconnected Twilio call")
            return
        }
        logger.i("Disconnecting Twilio call: ${twilioCall?.sid} (Ujet call ID $callId)")
        shouldBeConnected = false
        isFetchingAuthToken = false
        isMuted = false
        twilioCall?.disconnect()
        twilioCall = null
        callTransportListener.onTransportStateChanged(getState())
    }

    override fun onConnectFailure(call: Call, callException: CallException) {
        logger.i("Twilio call failed to connect: ${call.sid} (Ujet call ID $callId), exception message: ${callException.message}")
        errorCode = callException.errorCode
        disconnect()
    }

    override fun onRinging(call: Call) {
        logger.i("Twilio call is ringing: ${call.sid} (Ujet call ID $callId)")
    }

    override fun onConnected(call: Call) {
        logger.i("Twilio call is connected: ${call.sid} (Ujet call ID $callId)")
        callTransportListener.onTransportStateChanged(getState())
    }

    override fun onReconnecting(call: Call, callException: CallException) {
        logger.i("Twilio call is reconnecting: ${call.sid} (Ujet call ID $callId), exception message: ${callException.message}")
        callTransportListener.onTransportStateChanged(getState())
    }

    override fun onReconnected(call: Call) {
        logger.i("Twilio call reconnected: ${call.sid} (Ujet call ID $callId)")
        callTransportListener.onTransportStateChanged(getState())
    }

    override fun onDisconnected(call: Call, callException: CallException?) {
        logger.i("Twilio call disconnected: ${call.sid} (Ujet call ID $callId), exception message: ${callException?.message}")
        disconnect()
    }
}

private fun Int.toFailureReason(): FailureReason {
    return when (this) {
        // Copied hardcoded values from [UjetCallService]. Not sure these are in use anymore at all by Twilio as they are not defined in [CallException].
        // Leaving them here for now, but we should remove them when we are sure that Twilio isn't using them anymore.
        31000, 31001, 31002, 31003 -> FailureReason.GENERIC_FAILURE
        CallException.EXCEPTION_CONNECTION_ERROR -> FailureReason.CONNECTION_FAILURE
        CallException.EXCEPTION_TRANSPORT_ERROR -> FailureReason.TRANSPORT_FAILURE
        else -> FailureReason.UNKNOWN_FAILURE
    }
}
