package co.ujet.android.twilio_call

import androidx.annotation.Keep
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory

@Keep
internal class TwilioCallConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        EntryPointFactory.registerEntryPoint(
            CallTransportFactory::class.java,
            TwilioCallTransportFactory
        )
        EntryPointFactory.registerEntryPoint(UjetModule::class.java, TwilioCallUjetModule)
    }
}
