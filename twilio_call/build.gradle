plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace = "co.ujet.android.twilio_call"
    defaultConfig {
        buildToolsVersion = rootProject.ext.buildToolsVersion
        compileSdk rootProject.ext.compileSdkVersion
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        buildConfig = true
    }
}

build.dependsOn rootProject.setUjetVersion

dependencies {
    api project(":module_manager")
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    implementation "androidx.annotation:annotation:$androidXAnnotationVersion"

    implementation "com.twilio:voice-android:$twilioVoiceVersion"

    testImplementation "junit:junit:$junitVersion"
}

apply from: 'publish.gradle'
