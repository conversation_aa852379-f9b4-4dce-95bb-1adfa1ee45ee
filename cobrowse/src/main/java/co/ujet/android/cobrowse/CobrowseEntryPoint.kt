package co.ujet.android.cobrowse

import android.app.Activity
import android.app.Application
import android.util.Log
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.cobrowse.Cobrowse
import io.cobrowse.CobrowseIO
import io.cobrowse.Session

internal class CobrowseEntryPoint : Cobrowse {

    companion object CobrowseUjetModule: UjetModule {
        override val moduleName = "cobrowse"
    }

    override fun createSession(
        application: Application,
        customData: Map<String, String>,
        sessionStateListener: Cobrowse.SessionStateListener,
        onSessionActivationRequest: () -> Unit,
        onSessionRemoteControlRequest: () -> Unit,
        onSessionCreated: (String) -> Unit,
        onSessionCreationError: (Error) -> Unit,
        onSessionFullDeviceRequest: () -> Unit,
    ) {
        CobrowseIO.instance().apply {
            setDelegate(object : CobrowseIO.SessionLoadDelegate,
                CobrowseIO.SessionRequestDelegate, CobrowseIO.SessionControlsDelegate,
                CobrowseIO.RemoteControlRequestDelegate, CobrowseIO.FullDeviceRequestDelegate {
                override fun sessionDidUpdate(session: Session) {
                    Log.d(
                        "CobrowseConfigurator",
                        "sessionDidUpdate() state: ${session.state()}"
                    )
                    sessionStateListener.onSessionStateUpdated(getSessionState(session))
                }

                override fun sessionDidEnd(session: Session) {
                    Log.d(
                        "CobrowseConfigurator",
                        "sessionDidEnd() state: ${session.state()}"
                    )
                    CobrowseIO.instance().setDelegate(null)
                }

                // When session is activated, cobrowse draws red horizontal bar at the top of the screen to
                // indicate session is active but it interferes hiding status bar logic so using CobrowseIO.SessionControlsDelegate
                // to override default cobrowse library behavior and define our own accoridng to
                // https://docs.cobrowse.io/sdk-features/customize-the-interface/customize-session-controls.
                // Meaning we need to draw red notification bar when session is active and hide it when it is inactive.
                // Since we are updating co-browse button in UI in setCobrowseSessionActive() already, used it to show/hide
                // red notification bar there.
                override fun showSessionControls(activity: Activity?, session: Session) {}

                override fun hideSessionControls(activity: Activity?, session: Session) {}

                override fun sessionDidLoad(session: Session) {
                    Log.d(
                        "CobrowseConfigurator",
                        "sessionDidLoad() state: ${session.state()}"
                    )
                }

                override fun handleSessionRequest(activity: Activity, session: Session) {
                    onSessionActivationRequest()
                }

                override fun handleRemoteControlRequest(activity: Activity, session: Session) {
                    onSessionRemoteControlRequest()
                }

                override fun handleFullDeviceRequest(activity: Activity, session: Session) {
                    onSessionFullDeviceRequest()
                }
            })
            start(application)
            customData(customData)
            createSession { error, session ->
                val sessionCode = session?.code()
                when {
                    error != null -> onSessionCreationError(error)
                    sessionCode != null -> onSessionCreated(sessionCode)
                    else -> onSessionCreationError(Error("Cobrowse session created but session code is missing"))
                }
            }
        }
    }

    override fun stopSession(callback: (Error?) -> Unit) {
        CobrowseIO.instance().currentSession()?.end { error, _ ->
            callback(error)
            CobrowseIO.instance().stop()
            CobrowseIO.instance().setDelegate(null)
        }
    }

    override fun isEnabled() = true

    override fun getSessionState() = getSessionState(CobrowseIO.instance().currentSession())

    override fun grantActivateSessionRequest(requestGranted: Boolean) {
        if (requestGranted) {
            CobrowseIO.instance().currentSession()?.activate(null)
        } else {
            CobrowseIO.instance().currentSession()?.end(null)
        }
    }

    override fun grantRemoteControlRequest(requestGranted: Boolean) {
        if (requestGranted) {
            CobrowseIO.instance().currentSession()?.setRemoteControl(Session.RemoteControlState.On, null)
        } else {
            CobrowseIO.instance().currentSession()?.setRemoteControl(Session.RemoteControlState.Rejected, null)
        }
    }

    override fun grantFullDeviceRequest(requestGranted: Boolean) {
        if (requestGranted) {
            CobrowseIO.instance().currentSession()?.setFullDeviceState(Session.FullDeviceState.On, null)
        } else {
            CobrowseIO.instance().currentSession()?.setFullDeviceState(Session.FullDeviceState.Rejected, null)
        }
    }

    private fun getSessionState(session: Session?) = when {
        session == null || session.isEnded -> Cobrowse.State.INACTIVE
        session.isActive -> Cobrowse.State.ACTIVE
        else -> Cobrowse.State.PENDING
    }
}
