package co.ujet.android.cobrowse

import androidx.annotation.Keep
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.ConfigurationParameters
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.cobrowse.Cobrowse
import co.ujet.android.modulemanager.entrypoints.log.Logger
import io.cobrowse.CobrowseIO

@Keep
class CobrowseConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        // Extract parameters from the configurations map
        configurationsMap.forEach { (key, value) ->
            when (key) {
                ConfigurationParameters.COBROWSE_API_KEY.name -> {
                    (value as? String)?.let {
                        CobrowseIO.instance().license(it)
                    } ?: run {
                        Logger.w("Missing Cobrowse API Key")
                    }
                }
                ConfigurationParameters.COBROWSE_CUSTOM_URL.name -> {
                    (value as? String)?.let {
                        CobrowseIO.instance().api(it)
                    } ?: run {
                        Logger.w("Missing Cobrowse API Url")
                    }
                }
            }
        }

        // Register the cobrowse module entry point
        EntryPointFactory.registerEntryPoint(Cobrowse::class.java, CobrowseEntryPoint())
        EntryPointFactory.registerEntryPoint(UjetModule::class.java, CobrowseEntryPoint.CobrowseUjetModule)
    }
}
