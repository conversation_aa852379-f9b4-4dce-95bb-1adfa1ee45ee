package co.ujet.android.activity

import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.data.constant.CallCreateType

internal interface UjetActivityContract {
    interface View : BaseView {
        fun isEmpty(): Boolean
        fun showEntry()
        fun showMenus(fetchFaqDetails: <PERSON>olean, fetchAudibleMessages: Boolean)
        fun showChannels(fetchRootMenus: Boolean)
        fun showCsat()
        fun showSurveyScreen()
        fun showPhoneNumberInput(callCreateType: CallCreateType)
        fun showInAppIvrCall()
        fun showInChat()
        fun startNewChatSession(isDapWithPreferredChannelEnabled: Boolean)
        fun showInCallScreen()
        fun showInCallScreen(callId: Int)
        fun startNewCallSession(isDapWithPreferredChannelEnabled: Boolean)
        fun showRescheduleCall()
        fun showLoadingState(channelType: ChannelType, fetchRootMenus: Boolean)
        fun showPsaScreen()
        fun shouldDisplayFirstUi(): Boolean
        fun showInCall(menuId: Int, voiceMailReason: String?)
        fun close()
        fun isCallInProgress(): Boolean
        fun isChatInProgress(): Boolean
        fun stopInAppIvrCallInProgress()
        fun isInAppIvrCallInProgress(): Boolean
        fun authenticate(callId: Int, nonce: String?)
        fun showFaq()
        fun checkCallPermissionsGranted(): Boolean
        fun requestCallPermissions()
    }

    interface Presenter : BasePresenter {
        fun onInAppIvrCallArgsUpdated(callId: Int, isFromLink: Boolean)
        fun onIvrModeUpdated(isIvrMode: Boolean)
        fun forceStartPresenter()
        fun onCallPermissionsGranted()
    }
}
