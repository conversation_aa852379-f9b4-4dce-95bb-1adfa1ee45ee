package co.ujet.android.activity

import co.ujet.android.BuildConfig
import co.ujet.android.UjetPreferredChannel
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.inappivrcall.usecase.InvalidateInAppIvrCallArgs
import co.ujet.android.clean.domain.inappivrcall.usecase.SaveInAppIvrCallArgs
import co.ujet.android.clean.domain.menu.filter.MenuFilter
import co.ujet.android.clean.domain.menu.usecase.GetMenus
import co.ujet.android.clean.entity.company.Company
import co.ujet.android.clean.entity.inappivrcall.InAppIvrCallArgs
import co.ujet.android.clean.entity.menu.MenuContactOption
import co.ujet.android.commons.util.Preconditions
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.data.constant.CallStatus
import co.ujet.android.data.constant.CallType
import co.ujet.android.data.model.Call
import co.ujet.android.libs.logger.Logger.d
import co.ujet.android.libs.logger.Logger.v
import co.ujet.android.libs.logger.Logger.w

/**
 * Presenter for [UjetActivity]
 *
 *
 * This class determines which a screen should be displayed right after SDK launched
 * and display determined screen. Then clear expired session data and prepare data for starting.
 */
class UjetActivityPresenter internal constructor(
    view: UjetActivityContract.View,
    private val ujetContext: UjetContext,
    private val localRepository: LocalRepository,
    private val useCaseHandler: UseCaseHandler,
    private val saveInAppIvrCallArgs: SaveInAppIvrCallArgs,
    private val invalidateInAppIvrCallArgs: InvalidateInAppIvrCallArgs,
    private val getMenus: GetMenus,
    inAppIvrCallId: Int?,
    isStartedFromLink: Boolean?,
    isIvrMode: Boolean?,
    isNewCommInProgress: Boolean?
) : UjetActivityContract.Presenter {
    private val view: UjetActivityContract.View
    private var inAppIvrCallId: Int? = null
    private var isIvrMode: Boolean?
    private var isNewCommInProgress: Boolean?

    init {
        this.view = Preconditions.checkNotNull(view)
        if (inAppIvrCallId != null && isStartedFromLink != null) {
            updateInAppIvrCallArgs(inAppIvrCallId, isStartedFromLink)
        }
        this.isIvrMode = isIvrMode
        this.isNewCommInProgress = isNewCommInProgress
    }

    override fun start() {
        if (view.isActive && view.isEmpty()) {
            startInitialUi()
        }
    }

    override fun forceStartPresenter() {
        startInitialUi()
    }

    override fun onCallPermissionsGranted() {
        if (ujetContext.preferredChannel == UjetPreferredChannel.UjetPreferredChannelCall) {
            view.startNewCallSession(isDapWithPreferredChannelEnabled = true)
        }
    }

    private fun startInitialUi() {
        when {
            localRepository.isOngoingPsa -> {
                view.showPsaScreen()
            }
            view.isCallInProgress() -> {
                view.showInCallScreen()
            }
            isNewCommInProgress != null && isNewCommInProgress == true -> {
                view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                isNewCommInProgress = false
            }
            view.isChatInProgress() -> {
                view.showInChat()
            }
            view.isInAppIvrCallInProgress() -> {
                val call = localRepository.call
                resumeInAppIvrCall(call)
            }
            inAppIvrCallId != null -> {
                view.showInAppIvrCall()
            }
            resumeRating() -> {
                // Rating was resumed
            }
            isCallScheduled() -> {
                resumeScheduledCall(localRepository.call)
            }
            view.shouldDisplayFirstUi() -> {
                startNewSession()
            }
        }
    }

    private fun resumeInAppIvrCall(call: Call?) {
        when {
            call?.getType()?.isPstn == true && call.isResumable -> {
                d("Resume in-app ivr call")
                if (view.isActive) {
                    view.showInAppIvrCall()
                }
            }
            resumeRating() -> {
                // Resumed rating
            }
            else -> {
                if (view.isActive) {
                    view.authenticate(0, null)
                    view.stopInAppIvrCallInProgress()
                }
                startNewSession()
            }
        }
    }

    private fun isCallScheduled(): Boolean {
        val call = localRepository.call
        return call != null && call.getType() == CallType.Scheduled
    }

    private fun resumeScheduledCall(scheduledCall: Call?) {
        if (scheduledCall != null && scheduledCall.isResumable) {
            restoreCallInProgress(scheduledCall)
            d("Resume the call %d", scheduledCall.id)
            return
        }
        if (resumeRating()) {
            return
        }
        startNewSession()
    }

    /**
     * Resume the rating of the last communication
     *
     * @return true if resumed
     */
    private fun resumeRating(): Boolean {
        val rateRepository = localRepository.rateRepository
        return when {
            rateRepository.isSurveyUnanswered -> {
                if (view.isActive) {
                    view.showSurveyScreen()
                }
                v("Resume survey screen")
                true
            }
            rateRepository.csatEnabled() -> {
                if (view.isActive) {
                    view.showCsat()
                }
                v("Resume rating")
                true
            }
            else -> {
                rateRepository.clear()
                false
            }
        }
    }

    private fun restoreCallInProgress(call: Call) {
        if (!view.isActive) {
            return
        }
        var restored = false
        val type = call.getType()
            ?: if (BuildConfig.DEBUG) {
                throw RuntimeException("Unexpected call type $call")
            } else {
                w("Can't restore the communication due to the unexpected call type $call")
                startNewSession()
                return
            }
        when (type) {
            CallType.Scheduled -> {
                if (call.getStatus() == CallStatus.Connecting || call.getStatus() == CallStatus.Connected ||
                    call.getStatus() == CallStatus.VaAssigned || call.getStatus() == CallStatus.VaConnected) {
                    view.showInCallScreen(call.id)
                } else {
                    view.showRescheduleCall()
                }
                restored = true
            }
            CallType.Incoming -> {
                view.showInCallScreen(call.id)
                restored = true
                if (type.isPstn) {
                    view.showInAppIvrCall()
                }
            }
            else -> {
                if (type.isPstn) {
                    view.showInAppIvrCall()
                    restored = true
                }
            }
        }
        if (!restored) {
            w("Can't restore the communication so start new session")
            startNewSession()
        }
    }

    private fun startNewSession() {
        v("Start a new session")
        val isFaqEnabled = shouldShowFaq(localRepository.company)
        localRepository.clearSessionData()
        useCaseHandler.execute(
            invalidateInAppIvrCallArgs,
            InvalidateInAppIvrCallArgs.RequestValues()
        )
        startFirstUI(isFaqEnabled)
    }

    private fun startFirstUI(isFaqEnabled: Boolean) {
        if (!view.isActive) {
            return
        }
        when {
            ujetContext.directAccessKey != null -> {
                getCachedRootMenus()
            }
            isIvrMode != null && isIvrMode == true -> {
                view.showPhoneNumberInput(CallCreateType.InAppIvrCall)
            }
            ujetContext.isSkipSplash -> {
                if (isFaqEnabled) {
                    view.showFaq() //Close and proceed to menu screen if faq is disabled in web page fragment
                } else {
                    view.showMenus(fetchFaqDetails = true, fetchAudibleMessages = true) //Check and show faq only if it is enabled
                }
            }
            else -> {
                view.showEntry()
            }
        }
    }

    private fun getCachedRootMenus() {
        if (!view.isActive) {
            return
        }
        useCaseHandler.executeImmediate(
            getMenus,
            GetMenus.RequestValues.createCached(ujetContext.directAccessKey, MenuFilter.VISIBLE),
            object : UseCaseCallback<GetMenus.ResponseValue?> {
                override fun onSuccess(response: GetMenus.ResponseValue?) {
                    if (response?.isDirectAccess == true) {
                        val menus = response.menus
                        if (menus.isNotEmpty()) {
                            val dapMenu = menus[0]
                            when {
                                dapMenu.redirect != null -> {
                                    view.showLoadingState(getChannelType(dapMenu.redirect), true)
                                }
                                dapMenu.isLeafMenu -> {
                                    if (dapMenu.channels.getEnabledChannels().size > 0) {
                                        view.showChannels(true)
                                    } else {
                                        view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                                    }
                                }
                                else -> {
                                    view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                                }
                            }
                        } else {
                            view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                        }
                    } else {
                        view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                    }
                }

                override fun onError() {
                    when (ujetContext.preferredChannel) {
                        UjetPreferredChannel.UjetPreferredChannelChat -> {
                            view.startNewChatSession(isDapWithPreferredChannelEnabled = true)
                        }
                        UjetPreferredChannel.UjetPreferredChannelCall -> {
                            // We need microphone, bluetooth permissions to start call service
                            if (view.checkCallPermissionsGranted()) {
                                view.startNewCallSession(isDapWithPreferredChannelEnabled = true)
                            } else {
                                view.requestCallPermissions()
                            }
                        }
                        else -> {
                            view.showMenus(fetchFaqDetails = false, fetchAudibleMessages = true)
                        }
                    }
                }
            })
    }

    private fun getChannelType(redirection: MenuContactOption): ChannelType {
        return if (redirection.isURL) {
            ChannelType.ChannelRedirectionWebsite
        } else if (redirection.isPhone) {
            ChannelType.ChannelRedirectionPhoneCall
        } else if (redirection.isMessage) {
            ChannelType.ChannelRedirectionMessage
        } else if (redirection.isVoicemail) {
            ChannelType.ChannelRedirectionVoiceMail
        } else {
            ChannelType.ChannelNone
        }
    }

    override fun onInAppIvrCallArgsUpdated(callId: Int, isFromLink: Boolean) {
        updateInAppIvrCallArgs(callId, isFromLink)
        if (view.isActive && !view.isInAppIvrCallInProgress()) {
            view.showInAppIvrCall()
        }
    }

    override fun onIvrModeUpdated(isIvrMode: Boolean) {
        this.isIvrMode = isIvrMode
    }

    private fun updateInAppIvrCallArgs(callId: Int, isFromLink: Boolean) {
        inAppIvrCallId = callId
        useCaseHandler.execute(saveInAppIvrCallArgs, SaveInAppIvrCallArgs.RequestValues(
            InAppIvrCallArgs(callId, isFromLink)))
    }

    private fun shouldShowFaq(company: Company?): Boolean {
        return company != null && company.faqSetting != null && company.faqSetting?.isEnabled == true &&
                ujetContext.directAccessKey.isNullOrEmpty()
    }
}
