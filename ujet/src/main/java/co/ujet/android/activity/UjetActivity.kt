package co.ujet.android.activity

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.KeyEvent
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import co.ujet.android.R
import co.ujet.android.api.lib.Authenticator
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.inappivr.incall.UjetInAppIvrActivity
import co.ujet.android.app.call.incall.UjetCallActivity
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment.Companion.newInstance
import co.ujet.android.app.call.scheduled.call.UjetScheduledCallActivity.Companion.start
import co.ujet.android.app.channel.ChannelFragment
import co.ujet.android.app.channel.ChannelFragment.Companion.newInstance
import co.ujet.android.app.chat.UjetChatActivity
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.confirmation.ConfirmationDialogFragment
import co.ujet.android.app.confirmation.ConfirmationDialogFragment.Companion.newInstance
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.app.loadingstate.LoadingStateFragment
import co.ujet.android.app.loadingstate.LoadingStateFragment.Companion.newInstance
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.clean.presentation.entry.EntryFragment
import co.ujet.android.clean.presentation.language.LanguageFragment
import co.ujet.android.clean.presentation.menu.MenuFragment.Companion.getTag
import co.ujet.android.clean.presentation.menu.MenuFragment.Companion.newInstance
import co.ujet.android.clean.presentation.psa.PsaFragment
import co.ujet.android.clean.presentation.webpage.WebPageFragment
import co.ujet.android.common.util.NotificationPermissionUtil.NOTIFICATION_PERMISSION_DENIED
import co.ujet.android.common.util.NotificationPermissionUtil.getCachedNotificationPermission
import co.ujet.android.common.util.NotificationPermissionUtil.saveNotificationPermissionStatus
import co.ujet.android.common.util.PermissionUtil
import co.ujet.android.common.util.PermissionUtil.REQUEST_CODE_NOTIFICATIONS
import co.ujet.android.common.util.PermissionUtil.isPermissionsForNotificationsGranted
import co.ujet.android.common.util.PermissionUtil.requestPermissionsForNotifications
import co.ujet.android.common.util.ServiceUtil
import co.ujet.android.common.util.SessionManagerUtil
import co.ujet.android.common.util.VoipAvailability
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.commons.util.AccessibilityUtil.ignoreNextButtonFocus
import co.ujet.android.commons.util.AccessibilityUtil.isBackButtonClicked
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.extensions.registerOnBackPressedCallback
import co.ujet.android.internal.Injection
import co.ujet.android.service.UjetCallService
import co.ujet.android.service.UjetCallService.Companion.startInstantCall
import co.ujet.android.service.UjetChatService
import co.ujet.android.service.UjetChatService.Companion.isChatEnded
import co.ujet.android.service.UjetInAppIvrCallService
import java.util.Locale

/**
 * The activity contains Ujet dialogs
 */
class UjetActivity : UjetBaseActivity(), UjetActivityContract.View {
    private var presenter: UjetActivityContract.Presenter? = null
    private var authenticator: Authenticator? = null
    private var inAppIvrNonce: String? = null
    private var inAppIvrCallId: Int? = null
    private var isStartedFromLink: Boolean? = null
    private var isIvrMode: Boolean? = null
    private var isNewCommInProgress: Boolean? = null
    var voipAvailability: VoipAvailability? = null
        private set
    private var hasRespondedToNotificationPermission = false

    override fun checkWebViewAvailability() {
        //Implemented in other activity classes which extends UjetBaseActivity.
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.ujet_activity_main)
        setToolBar()
        window.statusBarColor = ujetStyle().colorPrimaryDark
        authenticator = Injection.provideAuthenticator(this)
        if (parseIntent(intent)) {
            inAppIvrCallId?.let { inAppIvrCallId ->
                authenticator?.invalidate()
                inAppIvrNonce?.let { authenticator?.setIvrAuthInfo(inAppIvrCallId, it) }
                authenticator?.authenticate()
            }
        }
        presenter = UjetActivityPresenter(
            this,
            Injection.provideUjetContext(this),
            Injection.provideLocalRepository(this),
            Injection.provideUseCaseHandler(),
            Injection.provideSaveInAppIvrCallArgs(this),
            Injection.provideInvalidateInAppIvrCallArgs(this),
            Injection.provideGetMenus(this),
            inAppIvrCallId,
            isStartedFromLink,
            isIvrMode,
            isNewCommInProgress
        )
        resetSdkParams()
        voipAvailability = VoipAvailability(
            this,
            Injection.provideApiManager(this),
            Injection.provideAppExecutors().networkIO()
        )
        voipAvailability?.startMeasuring()
        isBackButtonClicked = false //reset when app is restarted
        if (shouldRequestNotificationPermission()) {
            hasRespondedToNotificationPermission = false
            requestPermissionsForNotifications(this)
        } else {
            hasRespondedToNotificationPermission = true
        }
        val activityResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            result: ActivityResult? -> }
        registerActivityForResult(activityResultLauncher)
        registerOnBackPressedCallback { back() }
    }

    private fun shouldRequestNotificationPermission(): Boolean {
        // Request notification permission only when permission is not granted and user did not select to ignore
        // (did not select no thanks button) in push notification confirmation dialog
        return (!isPermissionsForNotificationsGranted(this)
                && getCachedNotificationPermission(this) != NOTIFICATION_PERMISSION_DENIED)
    }

    //Reset on every SDK start.
    private fun resetSdkParams() {
        WebViewUtil.setIsHandled(false)
        SessionManagerUtil.setResponseCallback(null)
    }

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        toolbar.setBackgroundColor(ujetStyle().colorPrimary)
        setSupportActionBar(toolbar)
        supportActionBar?.title = getString(R.string.ujet_greeting_navigation_title).uppercase(Locale.getDefault())
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        if (parseIntent(intent)) {
            inAppIvrCallId?.let { inAppIvrCallId ->
                authenticator?.invalidate()
                inAppIvrNonce?.let { authenticator?.setIvrAuthInfo(inAppIvrCallId, it) }
                authenticator?.authenticate()
                presenter?.onInAppIvrCallArgsUpdated(inAppIvrCallId, false)
                isStartedFromLink?.let { isStartedFromLink ->
                    presenter?.onInAppIvrCallArgsUpdated(inAppIvrCallId, isStartedFromLink)
                }
            }
            isIvrMode?.let { presenter?.onIvrModeUpdated(it) }
        }
    }

    override fun onResume() {
        super.onResume()
        if (hasRespondedToNotificationPermission) {
            presenter?.start()
        }
    }

    override fun onDestroy() {
        voipAvailability?.clear()
        voipAvailability = null
        super.onDestroy()
    }

    override fun onSupportNavigateUp(): Boolean {
        back()
        return true
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            isBackButtonClicked = true
            val fragmentManager = supportFragmentManager
            if (fragmentManager.findFragmentByTag(LanguageFragment.TAG) != null) {
                ignoreNextButtonFocus = true
            }
            val emailFragment = fragmentManager.findFragmentByTag(EmailFragment.TAG)
            val psaFragment = fragmentManager.findFragmentByTag(PsaFragment.TAG)
            when {
                fragmentManager.findFragmentByTag(LoadingStateFragment.TAG) != null -> {
                    finish()
                }
                emailFragment != null -> {
                    (emailFragment as EmailFragment).onKeyDown(keyCode)
                }
                psaFragment != null -> {
                    (psaFragment as PsaFragment).onKeyDown(keyCode)
                }
                else -> {
                    back()
                }
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun back() {
        val fragmentManager = supportFragmentManager
        if (fragmentManager.backStackEntryCount > 1) {
            isBackButtonClicked = true
            if (fragmentManager.findFragmentByTag(LanguageFragment.TAG) != null) {
                ignoreNextButtonFocus = true
            }
            fragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    private fun parseIntent(intent: Intent?): Boolean {
        inAppIvrNonce = null
        inAppIvrCallId = null
        isStartedFromLink = null
        isNewCommInProgress = null
        if (intent == null) {
            return false
        }
        if (intent.hasExtra(EXTRA_IVR_MODE)) {
            isIvrMode = intent.getBooleanExtra(EXTRA_IVR_MODE, false)
        }
        if (intent.hasExtra(EXTRA_NEW_COMM_STATE)) {
            isNewCommInProgress = intent.getBooleanExtra(EXTRA_NEW_COMM_STATE, false)
        }
        return parseFromSmsLink(intent) || parseIntentFromNotification(intent)
    }

    private fun parseFromSmsLink(intent: Intent): Boolean {
        if (intent.data == null) return false
        isStartedFromLink = true
        val uri = intent.data
        val nonce = uri?.getQueryParameter("nonce")
        val callId: Int = try {
            val callIdStr = uri?.getQueryParameter("call_id")
            callIdStr?.toInt() ?: 0
        } catch (ignore: NumberFormatException) {
            0
        }
        if (callId > 0 && nonce != null) {
            inAppIvrCallId = callId
            inAppIvrNonce = nonce
            return true
        }
        return false
    }

    /**
     * Started from [UjetPushHandlerInternal.startInAppIvrCall]
     *
     * @param intent Intent
     * @return true if parsed
     */
    private fun parseIntentFromNotification(intent: Intent): Boolean {
        val extras = intent.extras ?: return false
        val callId = extras.getInt(EXTRA_PSTN_CALL_ID, 0)
        if (callId > 0) {
            inAppIvrCallId = callId
            isStartedFromLink = false
            return true
        }
        return false
    }

    private fun requestPhoneStatePermission(): Boolean {
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) !=
            PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.READ_PHONE_STATE),
                READ_PHONE_STATE_PERMISSION_REQUEST_CODE)
            return true
        }
        return false
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            READ_PHONE_STATE_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    showInAppIvrCall()
                }
            }
            CALL_PERMISSIONS_REQUEST_CODE -> {
                if (PermissionUtil.isPermissionsForCallGranted(this)) {
                    presenter?.onCallPermissionsGranted()
                } else {
                    onPermissionNotGranted()
                }
            }
            REQUEST_CODE_NOTIFICATIONS -> {
                if (grantResults.isNotEmpty()) {
                    when {
                        grantResults[0] == PackageManager.PERMISSION_DENIED -> {
                            // If notification permission is denied, show push notification confirmation dialog
                            showPushNotificationConfirmation()
                        }
                        grantResults[0] == PackageManager.PERMISSION_GRANTED -> {
                            hasRespondedToNotificationPermission = true // Reset to continue the app
                        }
                    }
                }
            }
        }
    }

    private fun registerActivityForResult(activityResultLauncher: ActivityResultLauncher<Intent>) {
        supportFragmentManager.setFragmentResultListener(TAG, this) { requestKey: String, result: Bundle ->
            if (TAG == requestKey) {
                val requestCode = result.getInt(BaseFragment.REQUEST_CODE)
                val resultCode = result.getInt(BaseFragment.RESULT_CODE)
                if (requestCode == REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE) {
                    if (resultCode == RESULT_OK) {
                        when {
                            result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false) -> {
                                showAppNotificationSettingsPage(activityResultLauncher)
                            }
                            result.getBoolean(ConfirmationDialogFragment.EXTRAS_SECOND_BUTTON_CLICKED, false) -> {
                                // continue the app without asking permission again and save this selection
                                val context = this.applicationContext
                                if (context != null) {
                                    saveNotificationPermissionStatus(context, NOTIFICATION_PERMISSION_DENIED)
                                }
                            }
                        }
                    }
                    // User responded in confirmation dialog and onResume() will not be called again,
                    // so explicitly call presenter.startInitialUi() to continue the app
                    hasRespondedToNotificationPermission = true
                    presenter?.forceStartPresenter()
                }
            }
        }
    }

    private fun showAppNotificationSettingsPage(activityResultLauncher: ActivityResultLauncher<Intent>) {
        val intent = Intent()
        intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS")
        intent.putExtra("android.provider.extra.APP_PACKAGE", packageName)
        try {
            activityResultLauncher.launch(intent)
        } catch (e: ActivityNotFoundException) {
            // Fallback to Settings page if app notifications intent not found
            activityResultLauncher.launch(Intent(Settings.ACTION_SETTINGS))
        }
    }

    private fun showPushNotificationConfirmation() {
        val activity: Activity = this
        newInstance(
            TAG,
            REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE,
            activity.getString(R.string.ujet_permission_push_title),
            activity.getString(R.string.ujet_permission_push_message),
            activity.getString(R.string.ujet_support_permission_settings),
            activity.getString(R.string.ujet_support_permission_proceed)
        ).show(supportFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun changeLanguage(languageCode: String?): Boolean {
        val isChanged = super.changeLanguage(languageCode)
        if (isChanged) {
            supportActionBar?.title = getString(R.string.ujet_greeting_navigation_title).uppercase(Locale.getDefault())
        }
        return isChanged
    }

    override fun isActive(): Boolean {
        return !isFinishing && !isDestroyed
    }

    override fun isCallInProgress(): Boolean {
        return ServiceUtil.isServiceRunning(this, UjetCallService::class.java)
    }

    override fun showInCallScreen() {
        finish()
        UjetCallActivity.start(this)
    }

    override fun isChatInProgress(): Boolean {
        return ServiceUtil.isServiceRunning(this, UjetChatService::class.java) &&
                !isChatEnded
    }

    override fun stopInAppIvrCallInProgress() {
        stopService(Intent(this, UjetInAppIvrCallService::class.java))
    }

    override fun isInAppIvrCallInProgress(): Boolean {
        return ServiceUtil.isServiceRunning(this, UjetInAppIvrCallService::class.java)
    }

    override fun showCsat() {
        finish()
        UjetCsatActivity.start(this)
    }

    override fun showSurveyScreen() {
        finish()
        UjetSurveyActivity.start(this)
    }

    override fun authenticate(callId: Int, nonce: String?) {
        authenticator?.setIvrAuthInfo(callId, nonce)
    }

    override fun showMenus(fetchFaqDetails: Boolean, fetchAudibleMessages: Boolean) {
        FragmentHelper.showAsTop(
            this,
            newInstance(0, fetchFaqDetails, fetchAudibleMessages),
            getTag(0)
        )
    }

    override fun showChannels(fetchRootMenus: Boolean) {
        FragmentHelper.showAsTop(
            this,
            newInstance(fetchRootMenus),
            ChannelFragment.TAG
        )
    }

    override fun showPhoneNumberInput(callCreateType: CallCreateType) {
        FragmentHelper.showAsTop(
            this,
            newInstance(callCreateType, null, null),
            PhoneNumberInputFragment.TAG
        )
    }

    override fun isEmpty(): Boolean {
        return FragmentHelper.isEmpty(this)
    }

    override fun showEntry() {
        FragmentHelper.showAsTop(
            this,
            EntryFragment.newInstance(),
            EntryFragment.TAG
        )
    }

    override fun showInAppIvrCall() {
        // The READ_PHONE_STATE permission is only needed to be requested in Android 12+ and when
        //UjetOption#ignoreReadPhoneStatePermission is set to false. Customers can ignore this permission
        //request by setting config flag to true.
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S || Injection.provideConfiguration().ignoreReadPhoneStatePermission ||
            !requestPhoneStatePermission()) {
            UjetInAppIvrActivity.start(this)
            finish()
        }
    }

    override fun showInChat() {
        finish()
        UjetChatActivity.start(this)
    }

    override fun startNewChatSession(isDapWithPreferredChannelEnabled: Boolean) {
        finish()
        UjetChatService.startChat(this, isDapWithPreferredChannelEnabled)
    }

    override fun showInCallScreen(callId: Int) {
        finish()
        startInstantCall(this, callId)
    }

    override fun startNewCallSession(isDapWithPreferredChannelEnabled: Boolean) {
        finish()
        startInstantCall(this, isDapWithPreferredChannelEnabled)
    }

    override fun showRescheduleCall() {
        finish()
        start(this)
    }

    override fun showLoadingState(channelType: ChannelType, fetchRootMenus: Boolean) {
        FragmentHelper.showAsTop(
            this,
            newInstance(channelType, fetchRootMenus),
            LoadingStateFragment.TAG
        )
    }

    override fun showPsaScreen() {
        FragmentHelper.showAsTop(
            this,
            PsaFragment.newInstance(),
            PsaFragment.TAG
        )
    }

    override fun shouldDisplayFirstUi(): Boolean {
        return supportFragmentManager.backStackEntryCount == 0
    }

    override fun showInCall(menuId: Int, voiceMailReason: String?) {
        finish()
        startInstantCall(this, menuId, voiceMailReason, false)
    }

    override fun close() {
        finish()
    }

    override fun onPause() {
        super.onPause()
        overridePendingTransition(0, 0)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun showFaq() {
        FragmentHelper.showAsTop(
            this,
            WebPageFragment.newFaqWebPage(),
            WebPageFragment.getTag(0)
        )
    }

    override fun checkCallPermissionsGranted(): Boolean {
        return PermissionUtil.isPermissionsForCallGranted(this)
    }

    override fun requestCallPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(arrayOf(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.BLUETOOTH_CONNECT))
        } else {
            requestPermissions(arrayOf(Manifest.permission.RECORD_AUDIO))
        }
    }

    private fun requestPermissions(permissions: Array<String>) {
        ActivityCompat.requestPermissions(this, permissions,
            CALL_PERMISSIONS_REQUEST_CODE)
    }

    private fun onPermissionNotGranted() {
        Toast.makeText(this, R.string.ujet_error_ujet_audio_permission_denied_android, Toast.LENGTH_LONG).show()
        finish()
    }

    companion object {
        private const val EXTRA_IVR_MODE = "ivr_mode"
        private const val EXTRA_PSTN_CALL_ID = "pstn_call_id"
        private const val EXTRA_NEW_COMM_STATE = "new_comm_state"
        private const val READ_PHONE_STATE_PERMISSION_REQUEST_CODE = 1
        private const val REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE = 1008
        private const val CALL_PERMISSIONS_REQUEST_CODE = 10010
        private const val TAG = "UjetActivity"

        fun start(context: Context, extras: Bundle?) {
            if (extras != null) {
                start(context, extras.getBoolean(EXTRA_IVR_MODE, false))
            } else {
                start(context, false)
            }
        }

        fun start(context: Context, ivrMode: Boolean) {
            val intent = Intent(context, UjetActivity::class.java)
            intent.putExtra(EXTRA_IVR_MODE, ivrMode)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(intent)
        }

        fun start(context: Context, pstnCallId: Int) {
            val intent = Intent(context, UjetActivity::class.java)
            intent.putExtra(EXTRA_PSTN_CALL_ID, pstnCallId)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(intent)
        }

        fun startNewComm(context: Context, isNewCommInProgress: Boolean) {
            val intent = Intent(context, UjetActivity::class.java)
            intent.putExtra(EXTRA_NEW_COMM_STATE, isNewCommInProgress)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(intent)
        }
    }
}
