package co.ujet.android.api.lib

import android.net.Uri
import co.ujet.android.api.constant.HttpMethod
import java.io.File
import java.util.Collections
import java.util.HashMap

class HttpRequest {
    var url: String
        private set
    var method: HttpMethod
        private set
    var headers: HashMap<String, String>? = null
        private set
    var body: String? = null
        private set
    var multipartContents: List<MultipartContent>? = null
    var timeout = 0
        private set
    var cacheDirectory: File? = null
        private set

    private constructor(url: String, method: HttpMethod) {
        this.url = url
        this.method = method
    }

    private constructor(url: String, method: HttpMethod, body: String) {
        this.url = url
        this.method = method
        this.body = body
    }

    private constructor(url: String, method: HttpMethod, multipartContents: List<MultipartContent>) {
        this.url = url
        this.method = method
        this.multipartContents = multipartContents
    }

    fun replaceUrl(oldValue: String, newValue: String) {
        url = url.replace(oldValue.toRegex(), newValue)
    }

    fun replaceBody(oldValue: String, newValue: String) {
        body = body?.replace(oldValue.toRegex(), newValue)
    }

    fun putHeader(key: String, value: String) {
        headers = (headers ?: HashMap()).apply {
            put(key, value)
        }
    }

    override fun toString(): String {
        return "[$method] $url"
    }

    class Builder {
        private var baseUrl: String?
        private var path: String? = null
        private var method: HttpMethod
        private var paths: HashMap<String, Any>? = null
        private var queries: HashMap<String, Any?>? = null
        private var headers: HashMap<String, String>? = null
        private var body: String? = null
        private var multipartContents: MutableList<MultipartContent>? = null
        private var timeout = 0
        private var cacheDirectory: File? = null

        constructor(url: String?, method: HttpMethod) {
            baseUrl = url
            this.method = method
        }

        constructor(baseUrl: String?, path: String?, method: HttpMethod) {
            this.baseUrl = baseUrl
            this.path = path
            this.method = method
        }

        fun path(key: String, value: Any): Builder {
            paths = (paths ?: HashMap()).apply {
                put(key, value)
            }
            return this
        }

        fun query(key: String, value: Any?): Builder {
            queries = (queries ?: HashMap()).apply {
                put(key, value)
            }
            return this
        }

        fun json(body: String?): Builder {
            this.body = body
            header("Content-Type", "application/json; charset=UTF-8")
            return this
        }

        fun text(body: String?): Builder {
            this.body = body
            header("Content-Type", "text/plain; charset=UTF-8")
            return this
        }

        fun multipart(vararg multipartContents: MultipartContent): Builder {
            this.multipartContents = (this.multipartContents ?: mutableListOf()).apply {
                Collections.addAll(this, *multipartContents)
            }
            return this
        }

        fun header(key: String, value: String): Builder {
            headers = (headers ?: HashMap()).apply {
                put(key, value)
            }
            return this
        }

        fun timeout(timeout: Int): Builder {
            this.timeout = timeout
            return this
        }

        fun cacheDirectory(cacheDirectory: File): Builder {
            this.cacheDirectory = cacheDirectory
            return this
        }

        fun build(): HttpRequest {
            val url = getUrl()
            val httpRequest = HttpRequest(url, method)
            httpRequest.multipartContents = multipartContents?.toList()
            if (body != null) {
                httpRequest.body = body
            }
            if (headers != null) {
                httpRequest.headers = headers
            }
            if (timeout > 0) {
                httpRequest.timeout = timeout
            }
            if (cacheDirectory != null) {
                httpRequest.cacheDirectory = cacheDirectory
            }
            return httpRequest
        }

        private fun getUrl(): String {
            paths?.let { paths ->
                for ((key, value) in paths) {
                    baseUrl = baseUrl?.replace(String.format("\\{%s\\}", key).toRegex(), value.toString())
                    path = path?.replace(String.format("\\{%s\\}", key).toRegex(), value.toString())
                }
            }
            val url = StringBuilder()
            url.append(baseUrl)
            if (path != null) {
                url.append(path)
            }
            queries
                ?.map {
                    "${Uri.encode(it.key)}=${Uri.encode(it.value.toString())}"
                }
                ?.joinToString("&")
                ?.let {
                    url.append("?$it")
                }
            return url.toString()
        }
    }
}
