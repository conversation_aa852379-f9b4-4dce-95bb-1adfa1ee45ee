package co.ujet.android.api.lib

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ujet.android.UjetPayloadType
import co.ujet.android.UjetTokenCallback
import co.ujet.android.UjetVersion
import co.ujet.android.api.UjetBroadcast
import co.ujet.android.api.constant.HttpMethod.Post
import co.ujet.android.api.lib.HttpRequest.Builder
import co.ujet.android.api.lib.exception.AuthorizationException
import co.ujet.android.api.lib.exception.JwtSigningException
import co.ujet.android.api.response.MessageResponse
import co.ujet.android.clean.data.auth.AuthTokenResponse
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.device.usecase.GetPhoneNumber
import co.ujet.android.clean.domain.device.usecase.GetPhoneNumber.ResponseValue
import co.ujet.android.clean.domain.enduser.usecase.SaveEndUser
import co.ujet.android.clean.domain.enduser.usecase.SaveEndUser.RequestValues
import co.ujet.android.clean.entity.auth.AuthTokenRequestPayload
import co.ujet.android.clean.entity.enduser.EndUser
import co.ujet.android.clean.util.AppExecutors
import co.ujet.android.commons.util.IOUtil
import co.ujet.android.common.util.JwtUtil
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.extensions.let
import co.ujet.android.internal.Configuration
import co.ujet.android.internal.Injection
import co.ujet.android.internal.UjetInternal
import co.ujet.android.libs.logger.Logger
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.util.ArrayList
import java.util.HashMap
import java.util.UUID
import java.util.concurrent.locks.ReadWriteLock
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.math.max

class Authenticator private constructor(
    private val configuration: Configuration,
    context: Context,
    private val appExecutors: AppExecutors,
    private val serializer: co.ujet.android.commons.libs.uson.Serializer,
    private val useCaseHandler: UseCaseHandler,
    private val saveEndUser: SaveEndUser,
    private val getPhoneNumber: GetPhoneNumber
) {
    interface OnAuthenticationListener {
        fun onAuthenticationSucceeded(authToken: AuthToken)
        fun onAuthenticationFailed(throwable: Throwable)
    }

    private val preferences = context.getSharedPreferences(SHARED_PREFERENCES_NAME, Context.MODE_PRIVATE)
    private var authToken: AuthToken?
    private var logProxyUrl: String?
    private var logProxyAuthToken: String?
    private val authTokenLock: ReadWriteLock
    private var currentAuthenticatingId = 0
    private var finishedAuthenticatingId = 0
    private val authenticationListeners: MutableList<OnAuthenticationListener>
    private val localBroadcastManager = LocalBroadcastManager.getInstance(context)

    /**
     * Authenticating parameters. Last parameters are kept until invalidated.
     * Parameters are invalidated when start a new session or in-app ivr ended.
     */
    private var inAppIvrCallId = 0
    private var inAppIvrAuthNonce: String? = null
    private val uson = co.ujet.android.commons.libs.uson.Uson()

    init {
        authTokenLock = ReentrantReadWriteLock()
        authenticationListeners = ArrayList()
        val authTokenJson = preferences.getString(KEY_AUTH_TOKEN, null)
        authToken = uson.deserialize(authTokenJson, AuthToken::class.java)
        logProxyUrl = preferences.getString(KEY_LOG_PROXY_URL, null)
        logProxyAuthToken = preferences.getString(KEY_LOG_PROXY_AUTH_TOKEN, null)
    }

    @AnyThread
    @Synchronized
    fun setIvrAuthInfo(inAppIvrCallId: Int, inAppIvrAuthNonce: String?) {
        this.inAppIvrAuthNonce = if (TextUtils.isEmpty(inAppIvrAuthNonce)) {
            null
        } else {
            inAppIvrAuthNonce
        }
        this.inAppIvrCallId = inAppIvrCallId
    }

    @AnyThread
    @Synchronized
    fun authenticate() {
        if (isAuthenticating) {
            return
        }
        val authenticatingId = ++currentAuthenticatingId
        appExecutors.mainThread().execute { requestSignedPayload(authenticatingId) }
        if (TextUtils.isEmpty(inAppIvrAuthNonce) || inAppIvrCallId == 0) {
            Logger.d("Begin to authenticate")
        } else {
            Logger.d("Begin to authenticate with call id: %d, nonce: %s", inAppIvrCallId, inAppIvrAuthNonce)
        }
    }

    @AnyThread
    fun getAuthToken(): AuthToken? {
        authTokenLock.readLock().lock()
        return try {
            authToken
        } finally {
            authTokenLock.readLock().unlock()
        }
    }

    @AnyThread
    fun getLogProxyAuthToken(): String? {
        authTokenLock.readLock().lock()
        return try {
            logProxyAuthToken
        } finally {
            authTokenLock.readLock().unlock()
        }
    }

    @AnyThread
    fun getLogProxyUrl(): String? {
        authTokenLock.readLock().lock()
        return try {
            logProxyUrl
        } finally {
            authTokenLock.readLock().unlock()
        }
    }

    @AnyThread
    fun invalidate() {
        authTokenLock.writeLock().lock()
        try {
            authToken = null
            preferences.edit()
                ?.remove(KEY_AUTH_TOKEN)
                ?.remove(KEY_LOG_PROXY_URL)
                ?.remove(KEY_LOG_PROXY_AUTH_TOKEN)
                ?.apply()
        } finally {
            authTokenLock.writeLock().unlock()
        }
        synchronized(this) {
            inAppIvrCallId = 0
            inAppIvrAuthNonce = null
        }
        Logger.d("Invalidated auth token")
    }

    @AnyThread
    fun clearUserData() {
        preferences.edit().remove(KEY_USER_NAME).apply()
    }

    @AnyThread
    @Synchronized
    fun addAuthenticationListener(listener: OnAuthenticationListener) {
        authenticationListeners.add(listener)
    }

    @AnyThread
    @Synchronized
    fun removeAuthenticationListener(listener: OnAuthenticationListener) {
        authenticationListeners.remove(listener)
    }

    @MainThread
    private fun requestSignedPayload(authenticatingId: Int) {
        val payload = HashMap<String, Any>()
        payload["name"] = getUserName()
        UjetInternal.getUjetRequestListener()
            .onSignPayloadRequest(payload, UjetPayloadType.AuthToken, object : UjetTokenCallback {
                override fun onToken(requestToken: String?) {
                    onRequestToken(authenticatingId, requestToken)
                }

                override fun onError() {
                    finishedAuthenticatingId = max(finishedAuthenticatingId, authenticatingId)
                    localBroadcastManager.sendBroadcast(Intent(UjetBroadcast.CLOSE_SDK))
                }
            })
    }

    @AnyThread
    private fun getUserName(): String {
        var endUserName = preferences.getString(KEY_USER_NAME, null)
        if (endUserName == null) {
            val timestamp = TimeUtil.getNowString("MM/dd HH:mm")
            endUserName = String.format("Mobile User - %s", timestamp)
            preferences.edit().putString(KEY_USER_NAME, endUserName).apply()
        }
        return endUserName
    }

    @MainThread
    private fun onRequestToken(authenticatingId: Int, requestToken: String?) {
        if (isPastAuthenticatingId(authenticatingId)) {
            return
        }
        if (requestToken.isNullOrEmpty() || !JwtUtil.hasPayload(requestToken)) {
            Logger.w("JWT of auth token is invalid")
            notifyFailed(authenticatingId, JwtSigningException("JWT doesn't have a payload"))
            return
        }
        val tokenPayload = JwtUtil.getPayloadString(requestToken)
        val payload = serializer.deserialize(tokenPayload, AuthTokenRequestPayload::class.java)
        if (payload == null) {
            Logger.w("Auth token request payload doesn't exist")
            notifyFailed(authenticatingId, JwtSigningException("JWT doesn't have a payload"))
            return
        }
        val userName = payload.name ?: getUserName()
        val endUser = EndUser(payload.identifier, userName, payload.email, payload.phoneNumber)
        useCaseHandler.execute(saveEndUser, RequestValues(endUser))

        // Default user name is only used for anonymous user
        if (TextUtils.isEmpty(endUser.identifier)) {
            preferences.edit().remove(KEY_USER_NAME).apply()
        }
        useCaseHandler.execute(
            getPhoneNumber, GetPhoneNumber.RequestValues(),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    val phoneNumber = response.phoneNumber
                    requestAuthToken(
                        authenticatingId, requestToken, endUser,
                        phoneNumber.formattedPhoneNumber
                    )
                }

                override fun onError() {
                    requestAuthToken(authenticatingId, requestToken, endUser, null)
                }
            })
    }

    @MainThread
    private fun requestAuthToken(authenticatingId: Int, requestToken: String, endUser: EndUser, phoneNumber: String?) {
        if (isPastAuthenticatingId(authenticatingId)) {
            return
        }
        val authTokenRequest = createAuthTokenRequest(
            configuration.companyKey,
            requestToken,
            UjetInternal.getUjetRequestListener().onRequestPushToken(),
            getDeviceUUID(),
            Injection.provideConfiguration().appIdentifier,
            phoneNumber,
            inAppIvrCallId,
            inAppIvrAuthNonce
        )
        val httpRequest = Builder(configuration.endpointUrl, "auth/token", Post)
            .header("Ujet-Sdk-Version", UjetVersion.BUILD)
            .json(authTokenRequest)
            .build()
        appExecutors.networkIO().execute(HttpProcessor(httpRequest, object : HttpCallback {
            override fun onResponse(httpRequest: HttpRequest, response: HttpResponse) {
                val responseCode = response.code
                val responseBody = IOUtil.readString(response.inputStream) ?: return
                MainLooper.post {
                    onResponseAuthToken(
                        authenticatingId, responseCode, responseBody, endUser,
                        requestToken, phoneNumber
                    )
                }
            }

            override fun onFailure(httpRequest: HttpRequest, exception: IOException) {
                notifyFailed(authenticatingId, exception)
                Logger.w(exception, "Failed to authenticate")
            }
        }))
    }

    @MainThread
    fun getDeviceUUID(): String {
        var uuid = preferences.getString(KEY_UUID, null)
        if (uuid == null) {
            uuid = UUID.randomUUID().toString()
            preferences.edit().putString(KEY_UUID, uuid).apply()
        }
        return uuid
    }

    @AnyThread
    fun clearDeviceUUID() {
        preferences.edit().remove(KEY_UUID).apply()
    }

    @AnyThread
    private fun createAuthTokenRequest(
        companyKey: String,
        requestToken: String,
        deviceToken: String?,
        deviceUUID: String,
        appIdentifier: String,
        phoneNumber: String?,
        callId: Int?,
        nonce: String?
    ): String? {
        val modules = JSONArray()
        EntryPointFactory.provideEntryPoints(UjetModule::class.java).forEach {
            modules.put(it.moduleName)
        }
        try {
            val authTokenRequest = JSONObject()
                .put(
                    "end_user", JSONObject()
                        .put("company_id", companyKey)
                        .put("token", requestToken)
                        .put(
                            "device", JSONObject()
                                .put("device_type", "android")
                                .put("device_token", deviceToken)
                                .put("device_uuid", deviceUUID)
                                .put("app_identifier", appIdentifier)
                                .putOpt("phone_number", phoneNumber)
                                .put("modules", modules)
                        )
                )
            if (nonce != null && callId != null) {
                authTokenRequest
                    .putOpt("nonce", nonce)
                    .putOpt("call_id", callId)
            }
            return authTokenRequest.toString()
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return null
    }

    @MainThread
    private fun onResponseAuthToken(
        authenticatingId: Int,
        responseCode: Int,
        responseBody: String,
        endUser: EndUser,
        requestToken: String,
        phoneNumber: String?
    ) {
        if (isPastAuthenticatingId(authenticatingId)) {
            return
        }
        var success = false
        var errorMessage: String? = null
        if (responseCode < 400) {
            val authTokenResponse = serializer.deserialize(responseBody, AuthTokenResponse::class.java)
            success = updateAuthTokenAndLogProxyConfigurations(authTokenResponse)
        } else {
            // Disable logging as authentication failed
            Logger.setLoggingEnabled(false)

            errorMessage = serializer.deserialize(responseBody, MessageResponse::class.java)?.message ?: "unknown reason"
            Logger.w("Failed to authenticate by %s", errorMessage)
        }
        Logger.authenticationFinished()
        val authToken = authToken
        when {
            success && authToken != null -> {
                val updatedEndUser = EndUser(
                    authToken.payload?.endUserId ?: 0,
                    endUser.identifier,
                    endUser.name,
                    endUser.email,
                    endUser.phoneNumber
                )
                useCaseHandler.execute(saveEndUser, RequestValues(updatedEndUser))
                notifySucceeded(authenticatingId, authToken)
                Logger.d("Authenticated")
            }
            inAppIvrAuthNonce.isNullOrEmpty().not() -> {
                // This case is when the nonce was expired
                inAppIvrCallId = 0
                inAppIvrAuthNonce = null
                requestAuthToken(authenticatingId, requestToken, endUser, phoneNumber)
                Logger.d("Failed to authenticate may be caused by InAppIvr nonce expiration")
            }
            errorMessage?.contains("token is invalid") == true -> notifyFailed(authenticatingId, JwtSigningException(errorMessage))
            else -> notifyFailed(authenticatingId, AuthorizationException(errorMessage))
        }
    }

    @AnyThread
    private fun updateAuthTokenAndLogProxyConfigurations(authTokenResponse: AuthTokenResponse?): Boolean {
        val authTokenStr = authTokenResponse?.authToken
        if (authTokenStr.isNullOrEmpty() || !JwtUtil.hasPayload(authTokenStr)) {
            return false
        }
        val payloadString = JwtUtil.getPayloadString(authTokenStr)
        val payload = serializer.deserialize(payloadString, AuthTokenPayload::class.java) ?: return false
        authTokenLock.writeLock().lock()
        try {
            authToken = AuthToken(authTokenStr, payload)
            // Update Logger proxy configuration based on the Auth token response proxyUrl and proxyAuthToken
            let(authTokenResponse.logProxyUrl, authTokenResponse.logProxyAuthToken) { proxyUrl, proxyAuthToken ->
                Logger.updateProxyConfiguration(proxyUrl, proxyAuthToken)
                logProxyUrl = proxyUrl
                logProxyAuthToken = proxyAuthToken
            } ?: run {
                // Disable logging as authentication response does not contain logProxyUrl/logProxyAuthToken
                Logger.setLoggingEnabled(false)
            }
            preferences.edit()
                ?.putString(KEY_AUTH_TOKEN, uson.serialize(authToken))
                ?.putString(KEY_LOG_PROXY_URL, logProxyUrl)
                ?.putString(KEY_LOG_PROXY_AUTH_TOKEN, logProxyAuthToken)
                ?.apply()
        } finally {
            authTokenLock.writeLock().unlock()
        }
        return true
    }

    @AnyThread
    private fun notifySucceeded(authenticatingId: Int, authToken: AuthToken) {
        val payload = authToken.payload ?: return
        val userId = payload.endUserId ?: 0
        if (userId != 0) {
            Logger.setEndUserId(userId)
        }
        if (payload.subdomain != null) {
            Logger.setSubdomain(payload.subdomain)
        }
        appExecutors.mainThread().execute(Runnable {
            finishedAuthenticatingId = max(finishedAuthenticatingId, authenticatingId)
            if (finishedAuthenticatingId < currentAuthenticatingId) {
                return@Runnable
            }
            for (listener in authenticationListeners) {
                listener.onAuthenticationSucceeded(authToken)
            }
        })
    }

    @AnyThread
    private fun notifyFailed(authenticatingId: Int, throwable: Throwable) {
        appExecutors.mainThread().execute(Runnable {
            finishedAuthenticatingId = max(finishedAuthenticatingId, authenticatingId)
            if (finishedAuthenticatingId < currentAuthenticatingId) {
                return@Runnable
            }
            for (listener in authenticationListeners) {
                listener.onAuthenticationFailed(throwable)
            }
        })
    }

    @get:Synchronized
    private val isAuthenticating: Boolean
        get() = finishedAuthenticatingId < currentAuthenticatingId

    @Synchronized
    private fun isPastAuthenticatingId(authenticatingId: Int): Boolean {
        return authenticatingId < currentAuthenticatingId
    }

    companion object {
        const val SHARED_PREFERENCES_NAME = "co.ujet.android.data.auth"
        private var instance: Authenticator? = null

        @JvmStatic
        fun getInstance(
            configuration: Configuration,
            context: Context,
            appExecutors: AppExecutors,
            serializer: co.ujet.android.commons.libs.uson.Serializer,
            useCaseHandler: UseCaseHandler,
            saveEndUser: SaveEndUser,
            getPhoneNumber: GetPhoneNumber
        ): Authenticator {
            return instance ?: Authenticator(
                configuration,
                context,
                appExecutors,
                serializer,
                useCaseHandler,
                saveEndUser,
                getPhoneNumber
            ).apply {
                instance = this
            }
        }

        @VisibleForTesting
        const val KEY_USER_NAME = "user_name"
        const val KEY_AUTH_TOKEN = "auth_token"
        const val KEY_LOG_PROXY_URL = "log_proxy_url"
        const val KEY_LOG_PROXY_AUTH_TOKEN = "log_proxy_auth_token"
        private const val KEY_UUID = "uuid"
    }
}
