package co.ujet.android.api.lib

import androidx.annotation.Keep
import co.ujet.android.commons.libs.uson.SerializedName

class AuthToken {
    @co.ujet.android.commons.libs.uson.SerializedName("authToken")
    var authToken: String = ""
        private set

    @co.ujet.android.commons.libs.uson.SerializedName("payload")
    var payload: AuthTokenPayload? = null
        private set

    @Keep
    constructor() {
    }

    constructor(authToken: String, payload: AuthTokenPayload) {
        this.authToken = authToken
        this.payload = payload
    }
}
