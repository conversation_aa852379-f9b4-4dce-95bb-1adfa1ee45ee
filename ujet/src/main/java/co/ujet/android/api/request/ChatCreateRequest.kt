package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

class ChatCreateRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("chat")
    private var chat: Chat? = null

    @co.ujet.android.commons.libs.uson.SerializedName("verifiable")
    private var verifiable = false

    @co.ujet.android.commons.libs.uson.SerializedName("cobrowsable")
    private var cobrowsable = false

    override fun toString() = "ChatCreateRequest{chat=$chat, verifiable=$verifiable}"

    class Chat {
        @co.ujet.android.commons.libs.uson.SerializedName("menu_id")
        var menuId = 0

        @co.ujet.android.commons.libs.uson.SerializedName("lang")
        var language: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("ticket_id")
        var ticketId: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("greeting")
        var greeting: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("custom_data")
        var customData = CustomData()

        override fun toString() = "Chat{menuId=$menuId, language='$language', ticketId='$ticketId'}"
    }

    class CustomData {
        @co.ujet.android.commons.libs.uson.SerializedName("signed")
        var signedCustomData: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("unsigned")
        var unsignedCustomData: Map<String, Map<String, Any>>? = null
    }

    companion object {
        fun create(
            menuId: Int,
            verifiable: Boolean,
            cobrowsable: Boolean,
            language: String,
            ticketId: String?,
            greeting: String?,
            signedCustomData: String?,
            unsignedCustomData: Map<String, Map<String, Any>>?
        ): ChatCreateRequest {
            return ChatCreateRequest().apply {
                this.verifiable = verifiable
                this.cobrowsable = cobrowsable
                chat = Chat().apply {
                    this.menuId = menuId
                    this.language = language
                    this.ticketId = ticketId
                    this.greeting = greeting
                    this.customData.signedCustomData = signedCustomData
                    this.customData.unsignedCustomData = unsignedCustomData
                }
            }
        }
    }
}
