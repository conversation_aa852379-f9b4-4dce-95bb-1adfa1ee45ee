package co.ujet.android.api.lib

import co.ujet.android.common.TaskCallback
import java.net.HttpURLConnection

class TaskCallbackWrapper<T>(private val callback: TaskCallback<T>) : ApiCallback<T> {
    override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<T>) {
        if (response.code() == HttpURLConnection.HTTP_OK || response.code() == HttpURLConnection.HTTP_CREATED) {
            callback.onTaskSuccess(response.body())
        } else {
            callback.onTaskFailure()
        }
    }

    override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
        callback.onTaskFailure()
    }
}
