package co.ujet.android.api.request

import co.ujet.android.data.constant.CallFailReason
import co.ujet.android.data.constant.CallStatus
import co.ujet.android.commons.libs.uson.SerializedName

class CallUpdateRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("call")
    private var call: Call? = null

    constructor()
    constructor(status: CallStatus, failReason: CallFailReason?, failDetails: String?) {
        call = Call(status, failReason, failDetails)
    }

    class Call {
        @co.ujet.android.commons.libs.uson.SerializedName("status")
        private var status: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("fail_reason")
        private var failReason: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("fail_details")
        private var failDetails: String? = null

        constructor()
        constructor(status: CallStatus, failReason: CallFailReason?, failDetails: String?) {
            this.status = status.key
            this.failReason = failReason?.key
            this.failDetails = failDetails
        }
    }
}
