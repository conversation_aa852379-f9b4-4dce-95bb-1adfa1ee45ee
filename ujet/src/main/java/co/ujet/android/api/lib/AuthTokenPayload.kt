package co.ujet.android.api.lib

import co.ujet.android.commons.libs.uson.SerializedName

class AuthTokenPayload {
    @co.ujet.android.commons.libs.uson.SerializedName("end_user_id")
    val endUserId: Int? = null

    @co.ujet.android.commons.libs.uson.SerializedName("device_id")
    val deviceId: Int? = null

    @co.ujet.android.commons.libs.uson.SerializedName("company_id")
    val companyId: Int? = null

    @co.ujet.android.commons.libs.uson.SerializedName("company_name")
    val companyName: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("subdomain")
    val subdomain: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("exp")
    val expireTime: Int? = null
}
