package co.ujet.android.api.request

import androidx.annotation.IntRange
import co.ujet.android.commons.libs.uson.SerializedName

class InAppIvrRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("phone_number")
    private var phoneNumber: String = ""

    @IntRange(from = 1)
    @co.ujet.android.commons.libs.uson.SerializedName("menu_id")
    private var menuId = 1

    @co.ujet.android.commons.libs.uson.SerializedName("lang")
    private var language: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("custom_data_signed")
    private var signedCustomData: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("custom_data_unsigned")
    private var unsignedCustomData: Map<String, Map<String, Any>>? = null

    constructor()
    constructor(
        phoneNumber: String,
        @IntRange(from = 1) menuId: Int,
        language: String?,
        signedCustomData: String?,
        unsignedCustomData: Map<String, Map<String, Any>>?
    ) {
        this.phoneNumber = phoneNumber
        this.menuId = menuId
        this.language = language
        this.signedCustomData = signedCustomData
        this.unsignedCustomData = unsignedCustomData
    }
}
