package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

class CustomDataRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("signed")
    private var signed = false

    @co.ujet.android.commons.libs.uson.SerializedName("data")
    private var unsignedData: Map<String, Map<String, Any>>? = null

    @co.ujet.android.commons.libs.uson.SerializedName("signed_data")
    private var signedData: String? = null

    companion object {
        fun createSignedData(signedData: String): CustomDataRequest {
            val request = CustomDataRequest()
            request.signedData = signedData
            request.signed = true
            return request
        }

        fun createUnsignedData(unsignedData: Map<String, Map<String, Any>>): CustomDataRequest {
            val request = CustomDataRequest()
            request.unsignedData = unsignedData
            request.signed = false
            return request
        }
    }
}