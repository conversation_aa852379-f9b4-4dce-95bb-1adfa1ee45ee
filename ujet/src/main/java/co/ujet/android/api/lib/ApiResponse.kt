package co.ujet.android.api.lib

class ApiResponse<T> {
    private val code: Int
    private val rawBody: String
    private val body: Any?
    private val message: String?
    private val httpHeaders: HttpHeaders?

    /**
     * Response for success request (code < 400)
     *
     * @param code        HTTP code
     * @param rawBody     Raw body string (usually json)
     * @param body        Parsed object from raw body
     * @param httpHeaders Headers
     */
    constructor(code: Int, rawBody: String, body: Any?, httpHeaders: HttpHeaders?) {
        this.code = code
        this.rawBody = rawBody
        this.body = body
        message = null
        this.httpHeaders = httpHeaders
    }

    /**
     * Response for failure (400 <= code)
     *
     * @param code        HTTP code
     * @param rawBody     Raw body string (usually json)
     * @param message     Error message
     * @param httpHeaders Headers
     */
    constructor(code: Int, rawBody: String, message: String?, httpHeaders: HttpHeaders?) {
        this.code = code
        this.rawBody = rawBody
        body = null
        this.message = message
        this.httpHeaders = httpHeaders
    }

    fun code() = code

    /**
     * Check if the request is success with response.code() first.
     * Because if HTTP request was failed, body would be null.
     *
     * @return Parsed body object
     */
    @Suppress("UNCHECKED_CAST")
    fun body() = body as T?

    fun rawBody() = rawBody

    fun headers() = httpHeaders ?: HttpHeaders()

    fun message() = message
}
