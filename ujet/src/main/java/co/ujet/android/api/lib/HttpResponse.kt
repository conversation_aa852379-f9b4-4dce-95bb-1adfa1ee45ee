package co.ujet.android.api.lib

import java.io.InputStream

class HttpResponse {
    var code: Int
        private set
    var httpHeaders: HttpHeaders
        private set

    // Input stream should be closed after used
    var inputStream: InputStream

    constructor(code: Int, inputStream: InputStream) {
        this.code = code
        this.inputStream = inputStream
        httpHeaders = HttpHeaders()
    }

    constructor(code: Int, inputStream: InputStream, headers: Map<String, List<String>>) {
        this.code = code
        this.inputStream = inputStream
        httpHeaders = HttpHeaders(headers)
    }
}
