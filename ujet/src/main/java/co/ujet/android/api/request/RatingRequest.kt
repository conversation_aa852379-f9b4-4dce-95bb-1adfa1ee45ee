package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

class RatingRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("rating")
    private var rating: Int? = null

    @co.ujet.android.commons.libs.uson.SerializedName("feedback")
    private var feedback: String? = null

    constructor()
    constructor(rating: Int?, feedback: String?) {
        this.rating = rating
        this.feedback = feedback
    }
}
