package co.ujet.android.api.lib

import android.annotation.SuppressLint
import android.net.Uri
import co.ujet.android.UjetErrorCode.AUTHENTICATION_ERROR
import co.ujet.android.UjetErrorCode.AUTHENTICATION_JWT_ERROR
import co.ujet.android.UjetErrorCode.NETWORK_ERROR
import co.ujet.android.UjetVersion
import co.ujet.android.api.lib.Authenticator.OnAuthenticationListener
import co.ujet.android.api.lib.exception.AuthorizationException
import co.ujet.android.api.lib.exception.JwtSigningException
import co.ujet.android.api.lib.exception.NetworkException
import co.ujet.android.api.response.MessageResponse
import co.ujet.android.clean.presentation.general.UjetErrorHandler
import co.ujet.android.clean.util.AppExecutors
import co.ujet.android.commons.util.FileUtil.copyInputStreamToFile
import co.ujet.android.commons.util.IOUtil
import co.ujet.android.commons.util.IOUtil.closeQuietly
import co.ujet.android.data.model.ChatTranscriptResponse
import co.ujet.android.data.model.MediaContentResponse
import co.ujet.android.internal.Configuration
import co.ujet.android.libs.logger.Logger
import co.ujet.android.libs.logger.Logger.w
import java.io.File
import java.io.IOException
import java.net.ConnectException
import java.net.HttpURLConnection
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.ArrayDeque
import java.util.Deque
import java.util.concurrent.atomic.AtomicInteger

class ApiRunner private constructor(
    private val configuration: Configuration,
    private val serializer: co.ujet.android.commons.libs.uson.Serializer,
    private val appExecutors: AppExecutors,
    private val authenticator: Authenticator,
    private val ujetErrorHandler: UjetErrorHandler,
) : OnAuthenticationListener {
    /**
     * requestQueue, runningRequestCount are synchronized
     */
    private val requestQueue: Deque<RequestEntry<*>> = ArrayDeque()
    private val runningRequestCount = AtomicInteger()

    init {
        authenticator.addAuthenticationListener(this)
    }

    fun <T> enqueue(httpRequest: HttpRequest, cls: Class<T>, callback: ApiCallback<T>?) {
        synchronized(requestQueue) {
            val requestEntry = RequestEntry(httpRequest, cls, callback)
            requestQueue.add(requestEntry)
        }
        Logger.consoleOnly("Enqueue http request [%s] %s", httpRequest.method, httpRequest.url)
        processNextRequests()
    }

    inline fun <reified T> enqueue(httpRequest: HttpRequest, callback: ApiCallback<T>?) {
        enqueue(httpRequest, T::class.java, callback)
    }

    internal inline fun <reified T> enqueue(
        httpRequest: HttpRequest,
        crossinline onSuccess: (HttpRequest, ApiResponse<T>) -> Unit = { _, _ -> },
        crossinline onError: (HttpRequest, Throwable) -> Unit = { _, _ -> },
    ) {
        enqueue(httpRequest, T::class.java, object : ApiCallback<T> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<T>) {
                onSuccess(httpRequest, response)
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                onError(httpRequest, throwable)
            }
        })
    }

    private fun processNextRequests() {
        val authToken = authenticator.getAuthToken()
        if (authToken == null) {
            authenticator.authenticate()
            return
        }
        synchronized(requestQueue) {
            while (requestQueue.size > 0 && runningRequestCount.get() < MAX_CONCURRENT_REQUEST_COUNT) {
                requestQueue.poll()?.let {
                    process(authToken, it)
                }
            }
        }
    }

    private fun process(authToken: AuthToken, requestEntry: RequestEntry<*>) {
        val httpRequest = requestEntry.httpRequest
        transformRequest(authToken, httpRequest)
        val processor = HttpProcessor(httpRequest, object : HttpCallback {
            @SuppressLint("NewApi")
            override fun onResponse(httpRequest: HttpRequest, response: HttpResponse) {
                runningRequestCount.decrementAndGet()
                when (val responseCode = response.code) {
                    HttpURLConnection.HTTP_UNAUTHORIZED -> {
                        // will be processed at the first
                        synchronized(requestQueue) { requestQueue.addFirst(requestEntry) }
                        Logger.d("Unauthorized! This request will be ran after authentication")
                        if (requestEntry.increaseRetryCount() < RETRY_COUNT) {
                            // Retry authenticate when ever we receive 401 from server
                            authenticator.invalidate()
                            processNextRequests()
                        } else {
                            onAuthenticationFailed(AuthorizationException("Failed to authenticate"))
                        }
                    }
                    else -> {
                        val mediaFileName = response.httpHeaders.getString(HTTP_RESPONSE_HEADERS_FILE_NAME) ?: ""
                        // If media file exist, copy input stream to file and send the media response in callback
                        if (mediaFileName.isNotEmpty()) {
                            Logger.consoleOnly("%s %s\n => [%d]", httpRequest.method, httpRequest.url, responseCode)
                            val mediaInputStream = response.inputStream
                            val cacheDirectory = httpRequest.cacheDirectory
                            val cacheFile = File(cacheDirectory, mediaFileName)
                            try {
                                copyInputStreamToFile(mediaInputStream, cacheFile)
                                val mediaResponse = MediaContentResponse(mediaFileName, cacheFile)
                                notifyMediaSuccess(requestEntry, responseCode, mediaResponse, response.httpHeaders)
                            } catch (e: Exception) {
                                w(e, "Failed to write file %s", cacheFile)
                                notifyFailure(requestEntry, IOException())
                            } finally {
                                closeQuietly(mediaInputStream)
                            }
                        } else if (response.httpHeaders[HTTP_RESPONSE_HEADERS_CONTENT_TYPE]?.contains(HTTP_RESPONSE_HEADERS_PDF_TYPE) == true) {
                            Logger.consoleOnly("%s %s\n => [%d]", httpRequest.method, httpRequest.url, responseCode)
                            val mediaInputStream = response.inputStream
                            val cacheDirectory = httpRequest.cacheDirectory
                            val fileName =
                                "Chat Transcript with ${configuration.companyName} on ${
                                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                }"
                            val cacheFile = File(cacheDirectory, fileName)
                            try {
                                copyInputStreamToFile(mediaInputStream, cacheFile)
                                val chatTranscriptResponse = ChatTranscriptResponse(status = "finished").apply {
                                    pdfFile = cacheFile
                                    pdfFileName = fileName
                                }
                                notifyMediaSuccess(requestEntry, responseCode, chatTranscriptResponse, response.httpHeaders)
                            } catch (e: Exception) {
                                w(e, "Failed to write file %s", cacheFile)
                                notifyFailure(requestEntry, IOException())
                            } finally {
                                closeQuietly(mediaInputStream)
                            }
                        }else {
                            val body = IOUtil.readString(response.inputStream)
                            Logger.consoleOnly("%s %s\n => [%d] %s", httpRequest.method, httpRequest.url, responseCode, body)
                            notifySuccess(requestEntry, responseCode, body, response.httpHeaders)
                        }
                        processNextRequests()
                    }
                }
            }

            override fun onFailure(httpRequest: HttpRequest, exception: IOException) {
                runningRequestCount.decrementAndGet()
                if (requestEntry.increaseRetryCount() < RETRY_COUNT) {
                    Logger.consoleOnly("Retry request [%s] %s", httpRequest.method, httpRequest.url)
                    process(authToken, requestEntry)
                } else {
                    Logger.consoleOnly("Request failed [%s] %s", httpRequest.method, httpRequest.url)
                    notifyFailure(requestEntry, exception)
                    processNextRequests()
                }
            }
        })
        runningRequestCount.incrementAndGet()
        appExecutors.networkIO().execute(processor)
    }

    /**
     * We can't make sure that an auth token is valid when creating an HTTP request.
     * Because an auth token can be expired anytime. So we uses <device_id> as a placeholder
     * and replace <device_id> to real device id before request a HTTP.
     * It should be supported for the body of HTTP request later.
    </device_id></device_id> */
    private fun transformRequest(authToken: AuthToken, httpRequest: HttpRequest) {
        if (httpRequest.url.startsWith(configuration.endpointUrl)) {
            httpRequest.putHeader("Ujet-Sdk-Version", UjetVersion.BUILD)
            httpRequest.putHeader("Authorization", String.format("Bearer %s", authToken.authToken))
            val authTokenPayload = authToken.payload
            val encodedDeviceId = Uri.encode("<device_id>")
            httpRequest.replaceUrl(encodedDeviceId, authTokenPayload?.deviceId.toString())
            httpRequest.replaceBody(encodedDeviceId, authTokenPayload?.deviceId.toString())
        }
    }

    override fun onAuthenticationSucceeded(authToken: AuthToken) {
        processNextRequests()
    }

    override fun onAuthenticationFailed(throwable: Throwable) {
        val t = if (isNetworkError(throwable)) {
            NetworkException()
        } else {
            throwable
        }
        val errorCode = when (t) {
            is AuthorizationException -> AUTHENTICATION_ERROR.errorCode
            is JwtSigningException -> AUTHENTICATION_JWT_ERROR.errorCode
            is NetworkException -> NETWORK_ERROR.errorCode
            else -> 0
        }
        synchronized(requestQueue) {
            for (requestEntry in requestQueue) {
                val callback = requestEntry.callback
                val httpRequest = requestEntry.httpRequest
                when {
                    errorCode > 0 && httpRequest.url.contains("forms/verify") -> {
                        invokeOnFailure(requestEntry, throwable)
                    }

                    errorCode > 0 && !httpRequest.url.contains("rating") -> {
                        ujetErrorHandler.onError(errorCode)
                    }

                    callback != null -> {
                        invokeOnFailure(requestEntry, throwable)
                    }
                }
            }
            requestQueue.clear()
        }
    }

    private fun invokeOnFailure(requestEntry: RequestEntry<*>, throwable: Throwable) {
        appExecutors.mainThread().execute {
            requestEntry.callback?.onFailure(requestEntry.httpRequest, throwable)
            requestEntry.clearCallback()
        }
    }

    private fun <T> notifySuccess(
        requestEntry: RequestEntry<T>, responseCode: Int, body: String?,
        httpHeaders: HttpHeaders,
    ) {
        if (requestEntry.callback == null || body == null) {
            return
        }
        appExecutors.mainThread().execute {
            val apiResponse: ApiResponse<T>
            if (responseCode < 400) {
                val `object` = serializer.deserialize(body, requestEntry.type)
                apiResponse = ApiResponse(responseCode, body, `object`, httpHeaders)
            } else {
                val messageResponse = serializer.deserialize(body, MessageResponse::class.java)
                val message = messageResponse?.message
                apiResponse = ApiResponse(responseCode, body, message, httpHeaders)
                Logger.i("%s %s => %d %s", requestEntry.httpRequest.method, requestEntry.httpRequest.url, responseCode, message)
            }
            requestEntry.callback?.onSuccess(requestEntry.httpRequest, apiResponse)
            requestEntry.clearCallback()
        }
    }

    private fun <T> notifyMediaSuccess(
        requestEntry: RequestEntry<T>, responseCode: Int, mediaResponse: Any?,
        httpHeaders: HttpHeaders
    ) {
        if (requestEntry.callback == null || mediaResponse == null) {
            return
        }
        appExecutors.mainThread().execute {
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val apiResponse: ApiResponse<T> = ApiResponse(responseCode, "", mediaResponse, httpHeaders)
                requestEntry.callback?.onSuccess(requestEntry.httpRequest, apiResponse)
                requestEntry.clearCallback()
            } else {
                notifyFailure(requestEntry, IOException())
            }
        }
    }

    private fun <T> notifyFailure(requestEntry: RequestEntry<T>, t: Throwable) {
        appExecutors.mainThread().execute {
            val throwable = if (isNetworkError(t)) {
                NetworkException()
            } else {
                t
            }
            val errorCode = when (throwable) {
                is AuthorizationException -> AUTHENTICATION_ERROR.errorCode
                is JwtSigningException -> AUTHENTICATION_JWT_ERROR.errorCode
                is NetworkException -> {
                    val url = requestEntry.httpRequest.url
                    if (!url.contains("rating") && !url.contains("calls") && !url.contains("chats")) {
                        NETWORK_ERROR.errorCode
                    } else {
                        0
                    }
                }

                else -> 0
            }
            if (errorCode > 0) {
                ujetErrorHandler.onError(errorCode)
            } else {
                requestEntry.callback?.onFailure(requestEntry.httpRequest, throwable)
                requestEntry.clearCallback()
            }
        }
    }

    private fun isNetworkError(t: Throwable): Boolean {
        return t is ConnectException ||
                t is SocketTimeoutException ||
                t is UnknownHostException ||
                t.cause is ConnectException ||
                t.cause is SocketTimeoutException
    }

    fun clear() {
        synchronized(requestQueue) {
            requestQueue.clear()
        }
        authenticator.removeAuthenticationListener(this)
    }

    inner class RequestEntry<T>(val httpRequest: HttpRequest, val type: Class<T>, var callback: ApiCallback<T>?) {
        private var retryCount = 0

        fun clearCallback() {
            callback = null
        }

        @Synchronized
        fun increaseRetryCount(): Int {
            return ++retryCount
        }
    }

    companion object {
        private var instance: ApiRunner? = null

        @JvmStatic
        fun getInstance(
            configuration: Configuration,
            serializer: co.ujet.android.commons.libs.uson.Serializer,
            appExecutors: AppExecutors,
            authenticator: Authenticator,
            ujetErrorHandler: UjetErrorHandler,
        ): ApiRunner {
            return instance ?: ApiRunner(configuration, serializer, appExecutors, authenticator, ujetErrorHandler).apply {
                instance = this
            }
        }

        private const val RETRY_COUNT = 3
        private const val MAX_CONCURRENT_REQUEST_COUNT = 2
        private const val HTTP_RESPONSE_HEADERS_CONTENT_TYPE = "content-type"
        private const val HTTP_RESPONSE_HEADERS_PDF_TYPE = "application/pdf"
        private const val HTTP_RESPONSE_HEADERS_FILE_NAME = "X-File-Name"
    }
}
