package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

data class EventRequest(@SerializedName("event") val event: EventRequestData) {
    data class EventRequestData(
        @SerializedName("name")
        val name: String,

        @SerializedName("payload")
        val payload: EventRequestPayload
    )

    data class EventRequestPayload(
        @SerializedName("title")
        val title: String? = null,

        @SerializedName("button_title")
        val buttonTitle: String? = null,

        @SerializedName("name")
        val name: String? = null
    )

    companion object {
        fun build(name: String, payload: EventRequestPayload) = EventRequest(
            event = EventRequestData(
                name = name,
                payload = payload
            )
        )
    }
}
