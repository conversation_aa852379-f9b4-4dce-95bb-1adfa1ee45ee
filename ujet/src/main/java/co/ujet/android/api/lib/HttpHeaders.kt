package co.ujet.android.api.lib

import java.util.HashMap

class HttpHeaders {
    private var headers: Map<String, List<String>>

    constructor() {
        headers = HashMap()
    }

    constructor(headers: Map<String, List<String>>) {
        this.headers = headers
    }

    operator fun get(key: String) = headers[key]

    fun getString(key: String): String? {
        val stringList = get(key) ?: return null
        return stringList[0]
    }

    fun getBoolean(key: String): Boolean {
        val value = getString(key)
        return value != null && java.lang.Boolean.parseBoolean(value)
    }
}
