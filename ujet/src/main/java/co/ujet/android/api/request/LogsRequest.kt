package co.ujet.android.api.request

import co.ujet.android.common.log.UjetLogEntity
import co.ujet.android.commons.libs.uson.SerializedName
import co.ujet.android.internal.Injection
import java.io.Serializable

class LogsRequest(logsData: List<UjetLogEntity>) : Serializable {
    // Keep both logs and num_of_logs in json format for Observe
    @SerializedName("num_of_logs")
    val logsCount = logsData.size

    @SerializedName("logs")
    val logs: List<Map<String, Any>> = logsData.map { it.logData }

    fun toJson(): String {
        return serializer.serialize(this@LogsRequest)
    }

    companion object {
        private val serializer = Injection.provideSerializer()
    }
}
