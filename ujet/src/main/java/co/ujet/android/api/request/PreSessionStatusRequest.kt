package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

class PreSessionStatusRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("smart_action")
    private var preSessionData: Data? = null

    inner class Data(
        @co.ujet.android.commons.libs.uson.SerializedName("type") private var type: String,
        @co.ujet.android.commons.libs.uson.SerializedName("notified_at") private var notifiedAt: String,
        @co.ujet.android.commons.libs.uson.SerializedName("ended_at") private var endedAt: String?,
        @co.ujet.android.commons.libs.uson.SerializedName("mandatory") private var mandatory: Boolean,
        @co.ujet.android.commons.libs.uson.SerializedName("number_of_photos") private var photosCount: Int,
        @co.ujet.android.commons.libs.uson.SerializedName("status") private var status: String
    )

    companion object {
        fun loadData(type: String, notifiedAt: String, endedAt: String?, mandatory: Boolean,
                            photosCount: Int, status: String): PreSessionStatusRequest {
            val preSessionStatusRequest = PreSessionStatusRequest()
            preSessionStatusRequest.preSessionData = preSessionStatusRequest.Data(type, notifiedAt,
                    endedAt, mandatory, photosCount, status)

            return preSessionStatusRequest
        }
    }
}
