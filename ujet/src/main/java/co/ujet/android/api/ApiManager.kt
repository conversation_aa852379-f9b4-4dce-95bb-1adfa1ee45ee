package co.ujet.android.api

import androidx.annotation.IntRange
import co.ujet.android.api.constant.HttpMethod.*
import co.ujet.android.api.lib.*
import co.ujet.android.api.lib.HttpRequest.Builder
import co.ujet.android.api.request.*
import co.ujet.android.api.response.*
import co.ujet.android.clean.data.ApiClient
import co.ujet.android.clean.data.call.IceToken
import co.ujet.android.clean.entity.audiblemessages.AudibleMessages
import co.ujet.android.clean.entity.audiblemessages.QueueLevelAudibleMessages
import co.ujet.android.clean.entity.company.Company
import co.ujet.android.clean.entity.deflectedevent.DeflectedEventRequest
import co.ujet.android.clean.entity.email.EmailResponse
import co.ujet.android.clean.entity.menu.Menu
import co.ujet.android.clean.entity.survey.Survey
import co.ujet.android.clean.util.AppExecutors
import co.ujet.android.common.BulkTaskCallback
import co.ujet.android.common.TaskCallback
import co.ujet.android.common.util.CollectionUtil
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.chat.message.TaskVaChatMessage
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import co.ujet.android.commons.util.IOUtil
import co.ujet.android.data.constant.CallFailReason
import co.ujet.android.data.constant.CallFailReason.EndUserAbandoned
import co.ujet.android.data.constant.CallFailReason.EndUserCanceled
import co.ujet.android.data.constant.CallStatus
import co.ujet.android.data.constant.CallStatus.Failed
import co.ujet.android.data.constant.CallStatus.Finished
import co.ujet.android.data.model.*
import co.ujet.android.internal.Configuration
import co.ujet.android.libs.logger.Logger
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.net.HttpURLConnection
import kotlin.coroutines.resume

class ApiManager(
    configuration: Configuration,
    private val appExecutors: AppExecutors,
    private val serializer: co.ujet.android.commons.libs.uson.Serializer,
    private val apiRunner: ApiRunner
) : ApiClient {
    private val endpointUrl: String = configuration.endpointUrl

    init {
        Logger.setLoggingEnabled(endpointUrl)
    }

    override fun getCompany(callback: ApiCallback<Company>) {
        val httpRequest = Builder(endpointUrl, "company", Get).build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun getMenus(directAccessKey: String?, language: String, callback: ApiCallback<Array<Menu>>) {
        val httpRequest = Builder(endpointUrl, "menus", Get)
            .query("lang", language)
            .query("direct_access_key", directAccessKey)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getIceTokens(callTransportFactories: List<CallTransportFactory>, callback: TaskCallback<Array<IceToken>>) {
        val httpRequest = Builder(endpointUrl, "calls/ice_tokens", Post)
            .json(serializer.serialize(IceTokenRequest(callTransportFactories)))
            .build()
        apiRunner.enqueue(httpRequest, TaskCallbackWrapper(callback))
    }

    fun getWaitTimes(menuId: Int, language: String, callback: ApiCallback<WaitTimes>?) {
        val httpRequest = Builder(endpointUrl, "menus/{menuId}/wait_times", Get)
            .path("menuId", menuId)
            .query("lang", language)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getTimeSlots(menuId: Int, language: String, callback: ApiCallback<Array<String>>) {
        val httpRequest = Builder(endpointUrl, "menus/{menuId}/time_slots", Get)
            .path("menuId", menuId)
            .query("lang", language)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getVirtualAgentSettings(menuId: Int, language: String?, channelType: String, callback: ApiCallback<VirtualAgentSettings>) {
        val httpRequest = Builder(endpointUrl, "menus/{menuId}/virtual_agent_settings", Get)
            .path("menuId", menuId)
            .query("lang", language)
            .query("channel", channelType)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun escalateChat(chatId: Int, callback: ApiCallback<Escalation>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/escalations", Post)
            .path("chatId", chatId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun escalateCall(callId: Int, callback: ApiCallback<Escalation>) {
        val httpRequest = Builder(endpointUrl, "calls/{callId}/escalate", Post)
            .path("callId", callId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun deflectToVirtualAgent(chatId: Int, escalationId: Int, callback: ApiCallback<Escalation>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/escalations/{escalationId}", Put)
            .path("chatId", chatId)
            .path("escalationId", escalationId)
            .json(serializer.serialize(EscalationRequest.VIRTUAL_AGENT))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getCallDeflection(callId: Int, callback: ApiCallback<CallDeflection>) {
        val httpRequest = Builder(endpointUrl, "calls/{call_id}/deflections", Get)
            .path("call_id", callId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getCallEscalationStatus(callId: Int, callback: ApiCallback<Array<Escalation>>) {
        val httpRequest = Builder(endpointUrl, "calls/{call_id}/escalations", Get)
            .path("call_id", callId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun createCall(callCreateRequest: CallCreateRequest, callback: ApiCallback<Call>) {
        val httpRequest = Builder(endpointUrl, "calls", Post)
            .json(serializer.serialize(callCreateRequest))
            .build()
        apiRunner.enqueue(
            httpRequest,
            onSuccess = { request, response: ApiResponse<Call> ->
                /*
                  End user already has a call in progress but there is no call in the client.
                  Try to cancel/finish calls in progress and retry to create a call.
                 */
                if ("End user has a call in progress" == response.message()) {
                    Logger.i("End user has a call in progress")
                    cancelCallsInProgress(object : TaskCallback<Array<Call>> {
                        override fun onTaskSuccess(result: Array<Call>) {
                            createCallAgain()
                        }

                        override fun onTaskFailure() {
                            createCallAgain()
                        }

                        private fun createCallAgain() {
                            val callRequest = Builder(endpointUrl, "calls", Post)
                                .json(serializer.serialize(callCreateRequest))
                                .build()
                            apiRunner.enqueue(callRequest, callback)
                        }
                    })
                } else {
                    callback.onSuccess(request, response)
                }
            },
            onError = { request, throwable ->
                callback.onFailure(request, throwable)
            }
        )
    }

    fun getVoipProviders(callback: ApiCallback<Array<String>>) {
        val httpRequest = Builder(endpointUrl, "calls/voip_providers", Get).build()
        apiRunner.enqueue(httpRequest, callback)
    }

    /**
     * Call [TaskCallback.onTaskSuccess] if all calls in progress are canceled.
     * Otherwise, call [TaskCallback.onTaskFailure].
     *
     * @param callback Callback
     */
    private fun cancelCallsInProgress(callback: TaskCallback<Array<Call>>) {
        getCallsInProgress(object : ApiCallback<Array<Call>> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<Array<Call>>) {
                if (response.code() == HttpURLConnection.HTTP_OK) {
                    endCalls(response.body(), callback)
                } else {
                    callback.onTaskFailure()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                callback.onTaskFailure()
            }
        })
    }

    /**
     * Request to cancel a call if that call is not assigned yet.
     * Request to finish a call if that call is assigned or connected.
     *
     * @param calls    Calls which want to end
     * @param callback Callback for the result
     */
    private fun endCalls(calls: Array<Call>?, callback: TaskCallback<Array<Call>>) {
        if (calls.isNullOrEmpty()) {
            callback.onTaskSuccess(arrayOf())
            return
        }
        val waitCallback: BulkTaskCallback<Call> = object : BulkTaskCallback<Call>(Call::class.java, calls.size) {
            override fun onTaskSuccess(canceledCalls: Array<Call>) {
                callback.onTaskSuccess(canceledCalls)
            }

            override fun onTaskFailure() {
                callback.onTaskFailure()
            }
        }
        for (call in calls) {
            var failReason: CallFailReason? = null
            var failDetails: String? = null
            if (call.getStatus()?.isCancelable == true) {
                failReason = if (call.getStatus()?.isScheduled == true) {
                    EndUserCanceled
                } else {
                    EndUserAbandoned
                }
                failDetails = "Previous call doesn't exist in Android SDK"
            }
            updateCall(call.id, Failed, failReason, failDetails, waitCallback.taskCallback())
        }
    }

    fun createCallToken(callId: Int, callback: TaskCallback<VoipProviderToken>) {
        val httpRequest = Builder(endpointUrl, "calls/{callId}/tokens", Post)
            .path("callId", callId)
            .build()
        apiRunner.enqueue(httpRequest, TaskCallbackWrapper(callback))
    }

    fun getCall(callId: Int, callback: TaskCallback<Call>) {
        getCall(callId, TaskCallbackWrapper(callback))
    }

    fun getCall(callId: Int, callback: ApiCallback<Call>) {
        val httpRequest = Builder(endpointUrl, "calls/{callId}", Get)
            .path("callId", callId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    private fun getCallsInProgress(callback: ApiCallback<Array<Call>>) {
        val httpRequest = Builder(endpointUrl, "calls", Get)
            .query("in_progress", true)
            .query("device_id", "<device_id>")
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun endCall(callId: Int, callback: TaskCallback<Call>) {
        updateCall(callId, Finished, null, null, callback)
    }

    fun updateCall(callId: Int, callStatus: CallStatus, failReason: CallFailReason?, failDetails: String?, callback: TaskCallback<Call>) {
        updateCall(callId, CallUpdateRequest(callStatus, failReason, failDetails), TaskCallbackWrapper(callback))
    }

    fun updateCall(callId: Int, callUpdateRequest: CallUpdateRequest, callback: ApiCallback<Call>?) {
        val httpRequest = Builder(endpointUrl, "calls/{callId}", Patch)
            .path("callId", callId)
            .json(serializer.serialize(callUpdateRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun informInAppIvrCall(
        phoneNumber: String,
        @IntRange(from = 1) menuId: Int,
        language: String?,
        signedCustomData: String?,
        unsignedCustomData: Map<String, Map<String, Any>>?,
        callback: ApiCallback<String>
    ) {
        val inAppIvrRequest = InAppIvrRequest(
            phoneNumber, menuId, language, signedCustomData, unsignedCustomData
        )
        val httpRequest = Builder(endpointUrl, "calls/in_app_ivr", Post)
            .json(serializer.serialize(inAppIvrRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun getAudibleMessages(language: String, callback: ApiCallback<AudibleMessages>) {
        val httpRequest = Builder(endpointUrl, "audible_messages/{lang}", Get)
            .path("lang", language)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun getQueueLevelAudibleMessages(
        language: String,
        selectedMenuId: Int,
        callback: ApiCallback<QueueLevelAudibleMessages>
    ) {
        val httpRequest = Builder(endpointUrl, "audible_messages/queue/{lang}/{menuId}", Get)
            .path("lang", language)
            .path("menuId", selectedMenuId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun createChat(chatCreateRequest: ChatCreateRequest, callback: ApiCallback<Chat>) {
        val httpRequest = Builder(endpointUrl, "chats", Post)
            .json(serializer.serialize(chatCreateRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun setSmartActionEnabled(callId: Int, verifiableRequest: VerifiableRequest, callback: ApiCallback<String>) {
        val httpRequest = Builder(endpointUrl, "calls/{callId}/smart_actions", Post)
            .path("callId", callId)
            .json(serializer.serialize(verifiableRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getChat(chatId: Int, callback: TaskCallback<Chat>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}", Get)
            .path("chatId", chatId)
            .build()
        apiRunner.enqueue(httpRequest, TaskCallbackWrapper(callback))
    }

    fun getChat(chatId: Int, callback: ApiCallback<Chat>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}", Get)
            .path("chatId", chatId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun generateChatPdfTranscript(chatId: Int, callback: ApiCallback<ChatTranscriptResponse>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/generate_transcript_pdf", Post)
            .path("chatId", chatId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    suspend fun downloadChatPdfTranscript(cacheDirectory: File, chatTranscriptId: Int): Result<ChatTranscriptResponse?> {
        return suspendCancellableCoroutine { continuation ->
            val httpRequest = Builder(endpointUrl, "chat_transcript_pdfs/{chatTranscriptId}/download_transcript", Get)
                .path("chatTranscriptId", chatTranscriptId)
                .cacheDirectory(cacheDirectory)
                .build()
            apiRunner.enqueue<ChatTranscriptResponse>(
                httpRequest,
                object : ApiCallback<ChatTranscriptResponse> {
                    override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<ChatTranscriptResponse>) {
                        if (continuation.isActive) {
                            if (response.code() == HttpURLConnection.HTTP_OK) {
                                continuation.resume(Result.success(response.body()))
                            } else {
                                Logger.e("Chat Transcript PDF Download error ${response.code()}: ${response.message()}")
                                continuation.resume(Result.failure(Exception("Download transcript error: ${response.code()}")))
                            }
                        }
                    }

                    override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                        if (continuation.isActive) {
                            continuation.resume(Result.failure(throwable))
                            Logger.e("Chat Transcript PDF Download error: ${throwable.message}")
                        }
                    }
                })
        }
    }


    fun getTaskVaMessage(chatId: Int, messageId: Int, callback: ApiCallback<String>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/task_va_messages/{messageId}", Get)
            .path("chatId", chatId)
            .path("messageId", messageId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun sendMessageToTaskVa(chatId: Int, sendableChatMessage: SendableChatMessage, callback: ApiCallback<TaskVaChatMessage>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/task_va_messages", Post)
            .path("chatId", chatId)
            .json(JSONObject().apply {
                put("content", sendableChatMessage.toJson())
            }.toString())
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun requestMediaContents(cacheDirectory: File, mediaId: Int, mediaType: String, callback: ApiCallback<MediaContentResponse>) {
        val httpRequest = Builder(endpointUrl, "media/{mediaId}", Get)
            .path("mediaId", mediaId)
            .query("type", mediaType)
            .cacheDirectory(cacheDirectory)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun updateChatStatus(chatId: Int, chatStatus: String? = null, postSessionTransferStatus: String? = null, callback: TaskCallback<Chat>) {
        val chatUpdateRequest = ChatUpdateRequest(chatStatus, postSessionTransferStatus)
        val httpRequest = Builder(endpointUrl, "chats/{chatId}", Patch)
            .path("chatId", chatId)
            .json(serializer.serialize(chatUpdateRequest))
            .build()
        apiRunner.enqueue(httpRequest, TaskCallbackWrapper(callback))
    }

    fun createChatToken(chatId: Int, chatCreateTokenRequest: ChatCreateTokenRequest, callback: ApiCallback<ChatToken>) {
        val httpRequest = Builder(endpointUrl, "chats/{chatId}/tokens", Post)
            .path("chatId", chatId)
            .json(serializer.serialize(chatCreateTokenRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun getChatHistory(pageNumber: Int, perPage: Int, callback: ApiCallback<ChatHistory>) {
        val httpRequest = Builder(endpointUrl, "chats/histories", Get)
            .query("page", pageNumber)
            .query("per_page", perPage)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun verification(
        communicationType: String,
        communicationId: Int,
        verifyRequest: VerifyRequest,
        callback: ApiCallback<VerificationResponse>
    ) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/verification", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .json(serializer.serialize(verifyRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun rate(communicationType: String, communicationId: Int, ratingRequest: RatingRequest, callback: ApiCallback<RatingResponse>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/rating", Put)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .json(serializer.serialize(ratingRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun uploadPhotoToS3(communicationType: String, communicationId: Int, photoFile: MediaFile, callback: TaskCallback<String>) {
        getPreSignedUrl(communicationType, communicationId, "photos", object : TaskCallback<PresignedPost> {
            override fun onTaskSuccess(presignedPost: PresignedPost) {
                uploadToS3(presignedPost, MultipartContent.create(photoFile), callback)
            }

            override fun onTaskFailure() {
                callback.onTaskFailure()
            }
        })
    }

    fun uploadVideo(communicationType: String, communicationId: Int, video: MediaFile, smartActionId: Int, callback: TaskCallback<MediaFileUploadResponse>) {
        getPreSignedUrl(communicationType, communicationId, "videos", object : TaskCallback<PresignedPost> {
            override fun onTaskSuccess(presignedPost: PresignedPost) {
                uploadToS3(presignedPost, MultipartContent.create(video), object : TaskCallback<String> {
                    override fun onTaskSuccess(url: String) {
                        postUploadVideo(communicationType, communicationId, presignedPost.fields?.get("key") ?: return, smartActionId, callback)
                    }

                    override fun onTaskFailure() {
                        callback.onTaskFailure()
                    }
                })
            }

            override fun onTaskFailure() {
                callback.onTaskFailure()
            }
        })
    }

    private fun getPreSignedUrl(
        communicationType: String,
        communicationId: Int,
        mediaType: String,
        callback: TaskCallback<PresignedPost>
    ) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/{mediaType}/upload", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .path("mediaType", mediaType)
            .build()
        apiRunner.enqueue(httpRequest, TaskCallbackWrapper(callback))
    }

    private fun uploadToS3(presignedPost: PresignedPost, content: MultipartContent, callback: TaskCallback<String>) {
        val params = CollectionUtil.map(
            presignedPost.fields,
            { entry -> MultipartContent.create(entry.key, entry.value) },
            MultipartContent::class.java
        )
        val s3UploadHttpRequest = Builder(presignedPost.url, Post)
            .multipart(*params)
            .multipart(content)
            .timeout(5 * 60 * 1000)
            .build()
        appExecutors.networkIO().execute(HttpProcessor(s3UploadHttpRequest, object : HttpCallback {
            override fun onResponse(httpRequest: HttpRequest, response: HttpResponse) {
                val responseCode = response.code
                val body = IOUtil.readString(response.inputStream)
                if (response.code == HttpURLConnection.HTTP_CREATED) {
                    Logger.d("%s %s\n => [%d] %s", httpRequest.method, httpRequest.url, responseCode, body)
                    callback.onTaskSuccess(presignedPost.fields?.get("key"))
                } else {
                    Logger.w("%s %s\n => [%d] %s", httpRequest.method, httpRequest.url, responseCode, body)
                    callback.onTaskFailure()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, exception: IOException) {
                callback.onTaskFailure()
            }
        }))
    }

    fun postUploadedPhotos(
        communicationType: String,
        communicationId: Int,
        s3PathSet: Set<String>,
        photoType: String,
        smartActionId: Int,
        callback: TaskCallback<Array<MediaFileUploadResponse>>
    ) {
        val postUploadRequest: PostUploadRequest = PostUploadRequest.createPhoto(s3PathSet, photoType, smartActionId)
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/{mediaType}", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .path("mediaType", "photos")
            .json(serializer.serialize(postUploadRequest))
            .build()
        apiRunner.enqueue(httpRequest, object : ApiCallback<Array<MediaFileUploadResponse>> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<Array<MediaFileUploadResponse>>) {
                if (response.code() == HttpURLConnection.HTTP_OK) {
                    callback.onTaskSuccess(response.body())
                } else {
                    callback.onTaskFailure()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                callback.onTaskFailure()
            }
        })
    }

    override fun sendEmail(
        emailContentFields: HashMap<String, String>,
        mediaFiles: Array<MediaFile>,
        mediaFieldName: String?,
        callback: ApiCallback<EmailResponse>
    ) {
        val params = CollectionUtil.map(
            emailContentFields,
            { entry -> MultipartContent.create(entry.key, entry.value) },
            MultipartContent::class.java
        )
        val attachments: Array<MultipartContent> = MultipartContent.create(mediaFiles, mediaFieldName)
        val httpRequest = Builder(endpointUrl, "emails", Post)
            .multipart(*params)
            .multipart(*attachments)
            .timeout(5 * 60 * 1000)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    private fun postUploadVideo(
        communicationType: String,
        communicationId: Int,
        s3Path: String,
        smartActionId: Int,
        callback: TaskCallback<MediaFileUploadResponse>
    ) {
        val postUploadRequest: PostUploadRequest = PostUploadRequest.createVideo(s3Path, smartActionId)
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/{mediaType}", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .path("mediaType", "videos")
            .json(serializer.serialize(postUploadRequest))
            .build()
        apiRunner.enqueue(httpRequest, object : ApiCallback<MediaFileUploadResponse> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<MediaFileUploadResponse>) {
                if (response.code() == HttpURLConnection.HTTP_OK) {
                    callback.onTaskSuccess(response.body())
                } else {
                    callback.onTaskFailure()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                callback.onTaskFailure()
            }
        })
    }

    fun sendText(communicationType: String, communicationId: Int, sendTextRequest: SendTextRequest, callback: ApiCallback<Map<*, *>>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/text", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .json(serializer.serialize(sendTextRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun sendDeflectedEvents(deflectedEventRequest: DeflectedEventRequest, callback: ApiCallback<String>) {
        val httpRequest = Builder(endpointUrl, "client_events", Post)
            .json(serializer.serialize(deflectedEventRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun getSurveyData(communicationType: String, communicationId: Int,
                               callback: ApiCallback<Survey>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/survey", Get)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    override fun sendSurveyResponse(communicationType: String, communicationId: Int,
                                    request: SurveyRequest, callback: ApiCallback<String>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/survey", Put)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .json(serializer.serialize(request))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    //Smart action reporting
    fun sendSmartActionStatus(communicationType: String, communicationId: Int, smartActionId: Int,
                              inSessionStatusRequest: InSessionStatusRequest, callback: ApiCallback<Map<*, *>?>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/smart_actions/{smartActionId}", Put)
                .path("commType", communicationType)
                .path("commId", communicationId)
                .path("smartActionId", smartActionId)
                .json(serializer.serialize(inSessionStatusRequest))
                .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun notifySmartActionReceived(communicationType: String, communicationId: Int, smartActionId: Int,
                                  callback: ApiCallback<Map<*, *>?>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/smart_actions/{smartActionId}/notified", Put)
                .path("commType", communicationType)
                .path("commId", communicationId)
                .path("smartActionId", smartActionId)
                .build()

        apiRunner.enqueue(httpRequest, callback)
    }

    fun sendPreSessionStatus(communicationType: String, communicationId: Int,
                             preSessionStatusRequest: PreSessionStatusRequest, callback: ApiCallback<Map<*, *>?>) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/pre_session_smart_action", Post)
                .path("commType", communicationType)
                .path("commId", communicationId)
                .json(serializer.serialize(preSessionStatusRequest))
                .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun createCobrowseSession(
        communicationType: String,
        communicationId: Int,
        cobrowseCreateRequest: CobrowseCreateRequest,
        callback: ApiCallback<String>
    ) {
        val httpRequest = Builder(endpointUrl, "{communicationType}/{communicationId}/cobrowse", Post)
            .path("communicationType", communicationType)
            .path("communicationId", communicationId)
            .json(serializer.serialize(cobrowseCreateRequest))
            .build()
        apiRunner.enqueue(httpRequest, callback)
    }

    fun sendEvent(communicationType: String, communicationId: Int, eventRequest: EventRequest) {
        val httpRequest = Builder(endpointUrl, "{commType}/{commId}/end_user_event", Post)
            .path("commType", communicationType)
            .path("commId", communicationId)
            .json(serializer.serialize(eventRequest))
            .build()
        apiRunner.enqueue<String>(httpRequest, null)
    }

    suspend fun verifyWebFormUri(verifyApiRequestEvent: String): Result<String> {
        return suspendCancellableCoroutine { continuation ->
            val httpRequest = Builder(endpointUrl, "forms/verify", Post)
                .json(verifyApiRequestEvent)
                .build()

            apiRunner.enqueue<String>(httpRequest, object : ApiCallback<String> { // Assuming apiRunner returns a Call object
                override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<String>) {
                    if (continuation.isActive) {
                        if (response.code() == HttpURLConnection.HTTP_OK) {
                            continuation.resume(Result.success(response.body() ?: ""))
                        } else {
                            continuation.resume(Result.failure(Exception("API error: ${response.code()}")))
                        }
                    }
                }

                override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                    if (continuation.isActive) {
                        continuation.resume(Result.failure(throwable))
                    }
                }
            })
        }
    }

    companion object {
        private var instance: ApiManager? = null

        @JvmStatic
        fun getInstance(
            configuration: Configuration,
            appExecutors: AppExecutors,
            serializer: co.ujet.android.commons.libs.uson.Serializer,
            apiRunner: ApiRunner
        ): ApiManager {
            return instance ?: ApiManager(configuration, appExecutors, serializer, apiRunner).apply {
                instance = this
            }
        }
    }
}
