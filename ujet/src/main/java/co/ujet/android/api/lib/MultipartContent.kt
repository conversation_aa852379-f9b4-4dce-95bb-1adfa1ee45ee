package co.ujet.android.api.lib

import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.MediaFile.Type.Video
import java.lang.ref.WeakReference
import java.util.Locale

class MultipartContent private constructor(
    mediaFile: MediaFile? = null,
    fieldName: String? = null,
    val name: String? = null,
    val value: String? = null
) {
    val file = mediaFile?.file
    val fileName: String?
    val fieldName: String?
    val mimeType: String?

    init {
        when {
            Video === mediaFile?.type -> {
                mimeType = "video/mp4"
                fileName = String.format(Locale.US, "videos_%d.mp4", mediaFile.localId)
                this.fieldName = fieldName ?: "file"
            }
            mediaFile != null -> {
                mimeType = "image/jpeg"
                fileName = String.format(Locale.US, "photos_%d.jpg", mediaFile.localId)
                this.fieldName = fieldName ?: "file"
            }
            else -> {
                fileName = null
                this.fieldName = fieldName
                mimeType = null
            }
        }
    }

    val isFileContent = file != null

    companion object {
        fun create(mediaFile: MediaFile) = MultipartContent(mediaFile)

        fun create(mediaFiles: Array<MediaFile>, fieldName: String?) = mediaFiles
            .map { MultipartContent(it, fieldName) }
            .toTypedArray()

        fun create(uploadMedias: List<WeakReference<MediaFile>>) = uploadMedias
            .mapNotNull { it.get() }
            .map { MultipartContent(it) }
            .toTypedArray()

        fun create(name: String?, value: String?) = MultipartContent(name = name, value = value)
    }
}
