package co.ujet.android.api

import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager

/**
 * This class is for decoupling a sender and a receiver. We need fixed forms including information
 * for the broadcast such as Action, Extras. But if this information is included in a receiver or
 * a sender, they must be known by each other and it means coupled components. As a simple way,
 * we use this class to keep Actions for the broadcast and send methods to know which extras are
 * used for each broadcast. Also, it can be helpful to split components for each variant.
 * For example, [UjetBroadcast.sendChatPushNotification] is not required
 * for Lite SDK and this method is referenced only from the chat related classes which are included
 * only in Basic and Pro SDK. Even though Lite SDK also has this class, we can decouple
 * chat-related classes from Lite SDK.
 */
object UjetBroadcast {
    private const val PREFIX = "co.ujet.broadcast"
    const val CLOSE_SDK = "$PREFIX.close_sdk"
    const val END_COMMUNICATION = "$PREFIX.end_communication"

    /**
     * Close SDK by unexpected error
     *
     * @param context Context
     */
    @JvmStatic
    fun sendCloseSdkBroadcast(context: Context) {
        LocalBroadcastManager.getInstance(context).sendBroadcast(Intent(CLOSE_SDK))
    }

    /**
     * Disconnect UJET by ending in-progress communication.
     *
     * @param context Context
     */
    @JvmStatic
    fun sendEndCommunicationBroadcast(context: Context) {
        LocalBroadcastManager.getInstance(context).sendBroadcast(Intent(END_COMMUNICATION))
    }
}
