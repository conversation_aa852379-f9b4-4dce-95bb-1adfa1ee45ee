package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory

class IceTokenRequest(callTransportFactories: List<CallTransportFactory>) {
    @co.ujet.android.commons.libs.uson.SerializedName("voip_providers")
    private val voipProviders = callTransportFactories.map { it.transportType }.toTypedArray()
}
