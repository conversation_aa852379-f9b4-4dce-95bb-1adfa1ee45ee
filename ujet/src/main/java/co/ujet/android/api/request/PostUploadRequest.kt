package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName
import java.util.ArrayList

class PostUploadRequest {
    @co.ujet.android.commons.libs.uson.SerializedName("video")
    private var video: Data? = null

    @co.ujet.android.commons.libs.uson.SerializedName("photo")
    private var photo: ArrayList<Data>? = null

    @co.ujet.android.commons.libs.uson.SerializedName("multiple")
    private var multiple: Boolean? = null

    @co.ujet.android.commons.libs.uson.SerializedName("photo_type")
    private var photoType: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("smart_action_id")
    private var smartActionId: Int? = null

    class Data {
        @co.ujet.android.commons.libs.uson.SerializedName("s3_path")
        var path: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("photo_type")
        var photoType: String? = null
    }

    companion object {
        fun createPhoto(paths: Set<String>, photoType: String, smartActionId: Int): PostUploadRequest {
            val request = PostUploadRequest()
            if (smartActionId > 0) {
                request.smartActionId = smartActionId
            }
            request.photo = ArrayList()
            for (path in paths) {
                val data = Data()
                data.path = path
                data.photoType = photoType
                request.photo?.add(data)
            }
            request.multiple = true
            request.photoType = photoType
            return request
        }

        fun createVideo(path: String, smartActionId: Int): PostUploadRequest {
            val request = PostUploadRequest()
            if (smartActionId > 0) {
                request.smartActionId = smartActionId
            }
            request.video = Data().apply {
                this.path = path
            }
            return request
        }
    }
}
