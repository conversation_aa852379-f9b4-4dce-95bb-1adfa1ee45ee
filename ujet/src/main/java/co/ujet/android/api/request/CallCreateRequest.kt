package co.ujet.android.api.request

import androidx.annotation.IntRange
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.data.constant.CallCreateType.Instant
import co.ujet.android.data.constant.CallCreateType.Scheduled
import java.util.Date
import java.util.Locale

class CallCreateRequest private constructor() {
    @co.ujet.android.commons.libs.uson.SerializedName("call")
    private var call = Call()

    @co.ujet.android.commons.libs.uson.SerializedName("phone_number")
    private var phoneNumber: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("verifiable")
    private var verifiable = false

    @co.ujet.android.commons.libs.uson.SerializedName("cobrowsable")
    private var cobrowsable = false

    override fun toString(): String {
        return String.format(
            Locale.ENGLISH, "%s{verifiable=%s, phoneNumber=%s, cobrowsable=%s, call=%s}",
            javaClass.simpleName, verifiable, phoneNumber != null, cobrowsable, call
        )
    }

    class Call {
        @co.ujet.android.commons.libs.uson.SerializedName("type")
        var callType: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("menu_id")
        var menuId: Int? = null

        @co.ujet.android.commons.libs.uson.SerializedName("scheduled_at")
        var scheduledAt: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("voicemail_reason")
        var voicemailReason: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("voip_provider")
        var voipProvider: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("lang")
        var language: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("ticket_id")
        var ticketId: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("recording_permission")
        var recordingPermission: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("custom_data")
        var customData = CustomData()

        override fun toString(): String {
            return String.format(
                Locale.ENGLISH,
                "%s{menuId=%d, type=%s, voicemail=%s, voip=%s, lang=%s, scheduled=%s, ticketId=%s}",
                javaClass.simpleName, menuId, callType, voicemailReason, voipProvider,
                language, scheduledAt, ticketId
            )
        }
    }

    class CustomData {
        @co.ujet.android.commons.libs.uson.SerializedName("signed")
        var signedCustomData: String? = null

        @co.ujet.android.commons.libs.uson.SerializedName("unsigned")
        var unsignedCustomData: Map<String, Map<String, Any>>? = null
    }

    companion object {
        fun createInstantCall(
            @IntRange(from = 1) menuId: Int,
            voipProvider: String,
            language: String,
            ticketId: String?,
            verifiable: Boolean,
            recordingPermission: String,
            cobrowsable: Boolean,
            signedCustomData: String?,
            unsignedCustomData: Map<String, Map<String, Any>>?
        ): CallCreateRequest {
            val callCreateRequest = CallCreateRequest()
            callCreateRequest.call.voipProvider = voipProvider
            callCreateRequest.call.menuId = menuId
            callCreateRequest.call.language = language
            callCreateRequest.call.callType = Instant.key
            callCreateRequest.call.ticketId = ticketId
            callCreateRequest.call.recordingPermission = recordingPermission
            callCreateRequest.call.customData.signedCustomData = signedCustomData
            callCreateRequest.call.customData.unsignedCustomData = unsignedCustomData
            callCreateRequest.verifiable = verifiable
            callCreateRequest.cobrowsable = cobrowsable
            return callCreateRequest
        }

        fun createScheduledCall(
            @IntRange(from = 1) menuId: Int,
            voipProvider: String,
            scheduledAt: Date,
            ticketId: String?,
            verifiable: Boolean,
            language: String,
            phoneNumber: String?,
            recordingPermission: String,
            signedCustomData: String?,
            unsignedCustomData: Map<String, Map<String, Any>>?
        ): CallCreateRequest {
            val callCreateRequest = CallCreateRequest()
            callCreateRequest.call.menuId = menuId
            callCreateRequest.call.language = language
            callCreateRequest.call.voipProvider = voipProvider
            callCreateRequest.call.callType = Scheduled.key
            callCreateRequest.call.scheduledAt = TimeUtil.getUjetServerFormat(scheduledAt)
            callCreateRequest.call.ticketId = ticketId
            callCreateRequest.call.customData.signedCustomData = signedCustomData
            callCreateRequest.call.customData.unsignedCustomData = unsignedCustomData
            callCreateRequest.phoneNumber = phoneNumber
            callCreateRequest.call.recordingPermission = recordingPermission
            callCreateRequest.verifiable = verifiable
            return callCreateRequest
        }

        fun createVoicemail(
            @IntRange(from = 1) menuId: Int,
            reason: String,
            voipProvider: String,
            ticketId: String?,
            verifiable: Boolean,
            language: String,
            recordingPermission: String
        ): CallCreateRequest {
            val callCreateRequest = CallCreateRequest()
            callCreateRequest.call = Call()
            callCreateRequest.call.menuId = menuId
            callCreateRequest.call.language = language
            callCreateRequest.call.voicemailReason = reason
            callCreateRequest.call.voipProvider = voipProvider
            callCreateRequest.call.callType = Instant.key
            callCreateRequest.call.ticketId = ticketId
            callCreateRequest.call.recordingPermission = recordingPermission
            callCreateRequest.verifiable = verifiable
            return callCreateRequest
        }
    }
}
