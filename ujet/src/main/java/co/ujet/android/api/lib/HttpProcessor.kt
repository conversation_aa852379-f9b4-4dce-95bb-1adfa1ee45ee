package co.ujet.android.api.lib

import android.os.SystemClock
import android.text.TextUtils
import android.webkit.URLUtil
import androidx.annotation.VisibleForTesting
import co.ujet.android.api.constant.HttpMethod.Patch
import co.ujet.android.api.helper.MultipartWriter
import co.ujet.android.commons.util.IOUtil
import co.ujet.android.libs.logger.Logger
import java.io.IOException
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import java.nio.charset.StandardCharsets
import javax.net.ssl.HttpsURLConnection

class HttpProcessor : Runnable {
    @get:VisibleForTesting
    val httpRequest: HttpRequest

    @get:VisibleForTesting
    val httpCallback: HttpCallback?
    private val writeResultLog: Boolean

    companion object {
        private const val CONNECT_AND_READ_TIMEOUT = 60 * 1000
    }

    constructor(httpRequest: HttpRequest, httpCallback: HttpCallback?) : this(httpRequest, true, httpCallback)

    constructor(
        httpRequest: HttpRequest,
        writeResultLog: Boolean,
        onSuccess: (HttpRequest, HttpResponse) -> Unit,
        onFail: (HttpRequest, IOException) -> Unit
    ) : this(httpRequest, writeResultLog, object : HttpCallback {
        override fun onResponse(httpRequest: HttpRequest, response: HttpResponse) {
            onSuccess(httpRequest, response)
        }

        override fun onFailure(httpRequest: HttpRequest, exception: IOException) {
            onFail(httpRequest, exception)
        }
    })

    constructor(httpRequest: HttpRequest, writeResultLog: Boolean, httpCallback: HttpCallback?) {
        this.httpRequest = httpRequest
        this.httpCallback = httpCallback
        this.writeResultLog = writeResultLog
    }

    override fun run() {
        val startTime = SystemClock.elapsedRealtime()
        var inputStream: InputStream? = null
        var connection: HttpURLConnection? = null
        try {
            // Open connection
            val url = URL(httpRequest.url)
            connection = if (URLUtil.isHttpUrl(httpRequest.url)) {
                url.openConnection() as HttpURLConnection
            } else {
                url.openConnection() as HttpsURLConnection
            }
            connection.doInput = true
            if (httpRequest.timeout > 0) {
                connection.readTimeout = httpRequest.timeout
                connection.connectTimeout = httpRequest.timeout
            } else {
                connection.readTimeout = CONNECT_AND_READ_TIMEOUT
                connection.connectTimeout = CONNECT_AND_READ_TIMEOUT
            }
            connection.requestMethod = httpRequest.method.method
            httpRequest.headers?.let { headers ->
                for ((key, value) in headers) {
                    connection.setRequestProperty(key, value)
                }
            }

            if (httpRequest.method == Patch) {
                connection.setRequestProperty("X-HTTP-Method-Override", "PATCH")
            }

            // Set body content
            val multipartContent = httpRequest.multipartContents
            if (multipartContent != null && multipartContent.isNotEmpty()) {
                val write = MultipartWriter(connection)
                for (content in multipartContent) {
                    write.write(content)
                }
                write.end()
            } else if (!TextUtils.isEmpty(httpRequest.body)) {
                connection.doOutput = true
                val os = connection.outputStream
                os.write(httpRequest.body?.toByteArray(StandardCharsets.UTF_8))
                os.close()
            }

            // Read the response
            val responseCode = connection.responseCode
            inputStream = if (responseCode in 200..299) {
                connection.inputStream
            } else {
                connection.errorStream
            }
            if (writeResultLog) {
                Logger.d(
                    "%s %s => %d (%.2fs)",
                    httpRequest.method, httpRequest.url, responseCode,
                    (SystemClock.elapsedRealtime() - startTime) / 1000f
                )
            }
            httpCallback?.onResponse(httpRequest, HttpResponse(responseCode, inputStream, connection.headerFields))
        } catch (e: IOException) {
            e.printStackTrace()
            httpCallback?.onFailure(httpRequest, e)
            if (writeResultLog) {
                Logger.w(
                    "[Failed %s] %s %s (%.2fs)",
                    e.message, httpRequest.method, httpRequest.url,
                    (SystemClock.elapsedRealtime() - startTime) / 1000f
                )
            }
        } finally {
            if (inputStream != null) {
                IOUtil.closeQuietly(inputStream)
            }
            connection?.disconnect()
        }
    }
}
