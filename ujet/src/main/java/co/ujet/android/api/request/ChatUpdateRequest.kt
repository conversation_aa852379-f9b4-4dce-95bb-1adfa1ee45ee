package co.ujet.android.api.request

import co.ujet.android.commons.libs.uson.SerializedName

class ChatUpdateRequest(chatStatus: String? = null, postSessionTransferStatus: String? = null) {
    @SerializedName("chat")
    val chat = Chat(chatStatus, postSessionTransferStatus)

    class Chat(
        @SerializedName("status")
        val status: String?,

        @SerializedName("post_session_transfer_status")
        val postSessionTransferStatus: String?
    )
}
