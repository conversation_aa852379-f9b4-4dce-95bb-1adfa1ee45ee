package co.ujet.android.api.helper

import co.ujet.android.api.lib.MultipartContent
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.OutputStream
import java.io.OutputStreamWriter
import java.io.PrintWriter
import java.net.HttpURLConnection

class MultipartWriter @JvmOverloads constructor(connection: HttpURLConnection, charset: String = "UTF-8") {
    private val boundary: String = "===" + System.currentTimeMillis() + "==="
    private val outputStream: OutputStream
    private val writer: PrintWriter

    init {
        // creates a unique boundary based on time stamp
        connection.useCaches = false
        connection.doOutput = true // indicates POST method
        connection.doInput = true
        connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")

        outputStream = connection.outputStream
        writer = PrintWriter(OutputStreamWriter(outputStream, charset), true)
    }

    fun write(content: MultipartContent) {
        if (content.isFileContent) {
            writeFilePart(content.fieldName, content.fileName, content.mimeType, content.file)
        } else {
            writeFormField(content.name, content.value)
        }
    }

    /**
     * Adds a form field to the request
     *
     * @param name  field name
     * @param value field value
     */
    private fun writeFormField(name: String?, value: String?) {
        writer
            .append("--$boundary")
            .append(LINE_FEED)
            .append("Content-Disposition: form-data; name=\"$name\"")
            .append(LINE_FEED)
            .append(LINE_FEED)
            .append(value)
            .append(LINE_FEED)
            .flush()
    }

    /**
     * Adds a upload file section to the request
     *
     * @param fieldName  name attribute in <input type="file" name="..."></input>
     * @param uploadFile a File to be uploaded
     * @throws IOException
     */
    @Throws(IOException::class)
    private fun writeFilePart(fieldName: String?, fileName: String?, mimeType: String?, uploadFile: File?) {
        writer
            .append("--$boundary")
            .append(LINE_FEED)
            .append("Content-Disposition: form-data; name=\"$fieldName\"; filename=\"$fileName\"")
            .append(LINE_FEED)
            .append("Content-Type: $mimeType")
            .append(LINE_FEED)
            .append("Content-Transfer-Encoding: binary")
            .append(LINE_FEED)
            .append(LINE_FEED)
            .flush()
        FileInputStream(uploadFile).use {
            it.copyTo(outputStream, 4096)
        }
        outputStream.flush()
        writer
            .append(LINE_FEED)
            .flush()
    }

    /**
     * Completes write request
     */
    fun end() {
        writer
            .append("--$boundary--")
            .append(LINE_FEED)
            .close()
    }

    companion object {
        private const val LINE_FEED = "\r\n"
    }
}
