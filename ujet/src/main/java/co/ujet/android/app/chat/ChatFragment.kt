package co.ujet.android.app.chat

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.*
import android.content.Context.ACCESSIBILITY_SERVICE
import android.graphics.drawable.GradientDrawable
import android.graphics.Rect
import android.graphics.drawable.InsetDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.view.*
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.view.View.GONE
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_AUTO
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_YES
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.view.accessibility.AccessibilityManager
import android.view.animation.TranslateAnimation
import android.webkit.JavascriptInterface
import android.webkit.MimeTypeMap
import android.webkit.RenderProcessGoneDetail
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
import androidx.recyclerview.widget.RecyclerView.IMPORTANT_FOR_ACCESSIBILITY_NO
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import co.ujet.android.R
import co.ujet.android.activity.UjetActivity
import co.ujet.android.activity.UjetMediaPreviewActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.chat.ChatAdapter.ChatItemClickListener
import co.ujet.android.app.chat.ChatContract.Presenter
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_APPLICATION
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_AUDIO
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_DOCUMENT
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_DOCUMENT_TEXT
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_IMAGE
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_PHOTO
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_VIDEO
import co.ujet.android.app.chat.data.ChatMessageDataSource
import co.ujet.android.app.chat.view.*
import co.ujet.android.app.chat.viewholders.ChatEndedMessageViewHolder
import co.ujet.android.app.chat.view.ChatAfterHoursView
import co.ujet.android.app.chat.view.ChatDismissView
import co.ujet.android.app.chat.view.ChatStatusView
import co.ujet.android.app.chat.view.TypingIndicatorView
import co.ujet.android.app.chat.viewholders.HumanAgentDocumentChatMessageViewHolder
import co.ujet.android.app.chat.viewholders.HumanAgentPhotosChatMessageViewHolder
import co.ujet.android.app.chat.viewholders.HumanAgentVideosChatMessageViewHolder
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.common.BasicAlertDialogFragment
import co.ujet.android.app.common.EmailClient
import co.ujet.android.app.common.showUniqueNow
import co.ujet.android.app.confirmation.ConfirmationDialogFragment
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.error.AlertDialogFragment
import co.ujet.android.app.error.ConnectivityDialogFragment
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.clean.presentation.email.EmailFragment.Companion.newInstance
import co.ujet.android.common.util.ApplicationUtil.getApplicationName
import co.ujet.android.common.util.ServiceUtil
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.MediaFile.Type
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.ContentCardButton
import co.ujet.android.commons.domain.chat.WebForm
import co.ujet.android.commons.domain.chat.message.FormCompleteEvent
import co.ujet.android.commons.domain.chat.message.QuickReplyButton
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyButtonsChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyChatMessage
import co.ujet.android.commons.domain.chat.message.WebFormChatMessage
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.data.constant.CommunicationType
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled
import co.ujet.android.data.constant.ChatStatus
import co.ujet.android.data.constant.PostSessionTransferStatus
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.libs.materialcamera.util.Degrees.isPortrait
import co.ujet.android.service.UjetBinder
import co.ujet.android.service.UjetChatService
import co.ujet.android.smartaction.data.SmartActionType
import co.ujet.android.smartaction.data.SmartActionType.COBROWSE
import co.ujet.android.smartaction.manager.SmartActionManager
import co.ujet.android.smartaction.routers.PsaRouter
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI
import co.ujet.android.smartaction.ui.verification.BiometricsVerification
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.dpToPx
import co.ujet.android.ui.util.StyleUtil.RESOURCE_NOT_FOUND
import co.ujet.android.ui.util.StyleUtil.getColorResIdByName
import co.ujet.android.ui.widgets.ChatInputBarLayout
import co.ujet.android.ui.widgets.ChatInputBarLayout.ChatInputBarListener
import co.ujet.android.ui.widgets.UjetProgressBar
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_COLLAPSED
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_HIDDEN
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileInputStream
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

open class ChatFragment @Keep constructor() : BaseFragment(), ChatContract.View, ChatItemClickListener,
    ChatServiceInteractor {
    private var isSkippingVirtualAgentAllowed = false
    private var isMenuOptionVisible: Boolean = true
    private var cacheFile: File? = null
    private var headerContainerHeight: Int = 0
    private var webFormCloseDelayJob: Job? = null
    private var formLoadingErrorMessage: RelativeLayout? = null
    private var formWebView: WebView? = null
    private var formProgressBar: LinearLayout? = null
    private var postSessionOptInBannerContainer: LinearLayout? = null
    private var postSessionOptInBanner: LinearLayout? = null
    private var postSessionBannerProgressBar: UjetProgressBar? = null
    private var formBottomSheet: FrameLayout? = null
    private var chatListener: ChatListener? = null
    private var presenter: Presenter? = null
    private var emptyViewProgressBar: UjetProgressBar? = null
    private var chatMessageRecyclerView: RecyclerView? = null
    private var chatMessageAdapter: ChatAdapter? = null
    private var chatStatusView: ChatStatusView? = null
    private var chatInputBarLayout: ChatInputBarLayout? = null
    private var chatOptionsMenuView: ChatOptionsMenuView? = null
    private var chatActionsMenuView: ChatActionsMenuView? = null
    private var chatAfterHoursView: ChatAfterHoursView? = null
    private var chatDismissView: ChatDismissView? = null
    private var typingIndicatorView: TypingIndicatorView? = null
    private var isServiceBound = false
    private var isAfterHourMessage = false
    private var errorMessage: String? = null
    private var chatService: UjetChatService? = null
    private var sharedPreferences: SharedPreferences? = null
    private var isExternalDeflectionLinkClicked = false
    private var isTimerFinished = false
    private val handler = Handler(Looper.getMainLooper())
    private val configuration = Injection.provideConfiguration()
    private var loadingChatHistory = false
    private var topItemVisible = false
    private var isChatUIPaused = false
    private var isWebFormCloseBtnClicked = false
    private var formBottomSheetBehavior: BottomSheetBehavior<FrameLayout>? = null
    private var swipeRefreshLayout: SwipeRefreshLayout? = null
    private var accessibilityManager: AccessibilityManager? = null
    private var initialWebFormSlideOffset = 0f
    private val contentCardButtonClickedMap = linkedMapOf<Int, Boolean>()
    private var isWebFormInProgress = false
    private val uson = co.ujet.android.commons.libs.uson.Uson()
    private var webFormChatMessage: WebFormChatMessage? = null
    private var startNewWebFormChatMessage: WebFormChatMessage? = null
    private var currentFormAdapterPosition = -1
    private var newFormAdapterPosition = -1
    private var webFormErrorMsg = -1
    private var webFormUrl = ""
    private var isWebFormOrientationChanged = false
    private var webFormInitialState = STATE_EXPANDED
    private var previousHeightDiff = 0
    private var menuIdToResumeChat = -1
    private var isWebFormVisible = false
    private var isWebFormClosed = false
    private var bottomSheetContainer: CoordinatorLayout? = null
    private var isPostSessionChatInProgress = false
    private var isPostSessionOptInBannerVisible = false
    private var isPostSessionOptInBannerLoadingState = false
    private var isChatTranscriptDownloadInProgress = false
    private var isChatTranscriptDownloadInitiatedFromInlineButton = false
    private var chatEndedMessageViewHolder: ChatEndedMessageViewHolder? = null
    private var chatEndedMessageViewHolderAdapterPosition = -1
    private var chatTranscriptId = -1
    private var menu: View? = null
    private var title = ""

    // We can not include media file data while launching create document intent so caching it here
    // and will be used after user selected destination folder to save media (only for Android 9 and
    // below version devices) / document (for all android version devices)
    private var mediaFiles: ArrayList<MediaFile>? = null

    private var isCreateDocumentIntentLaunchedForMedia = false
    private var isDownloadSucceeded = true
    private var mediaType: String? = null
    private var downloadedMediaCount = 0
    private var resumeUpdateAttachmentView = false

    // Last visible message position when media is clicked to preview / download
    private var lastVisibleItemPosition: Int = RecyclerView.NO_POSITION
    private val launcherIntentMap = mutableMapOf<String, ActivityResultLauncher<String>>()

    // Message position at which media / documents is clicked to download
    private var attachmentsMessageAdapterPosition = -1

    private val keyboardScreenListener = ViewTreeObserver.OnGlobalLayoutListener {
        val rect = Rect()
        view?.let {
            it.getWindowVisibleDisplayFrame(rect)
            val screenHeight = resources.displayMetrics.heightPixels
            val heightDiff = it.rootView.height - (rect.bottom - rect.top)
            val threshold = screenHeight * 0.15
            val isKeyboardVisible = heightDiff > threshold
            if (isKeyboardVisible && heightDiff != previousHeightDiff) { // Check if heightDiff has changed
                scrollToBottom()
            }
            if (isSkippingVirtualAgentAllowed) {
                when {
                    isKeyboardVisible && chatInputBarLayout?.isEscalateButtonVisible() == true -> {
                        chatInputBarLayout?.setEscalateEnabled(false)
                    }

                    !isKeyboardVisible && chatInputBarLayout?.isEscalateButtonVisible() == false -> {
                        chatInputBarLayout?.setEscalateEnabled(true)
                    }
                }
            }
            previousHeightDiff = heightDiff
        }
    }

    private val networkDialog by lazy {
        ConnectivityDialogFragment
            .newDialog(getString(R.string.ujet_chat_internet_connection_lost), getString(R.string.ujet_chat_end)).apply {
                setDismissCallback { presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER) }
            }
    }

    private val createFileLauncher = registerForActivityResult(
        ActivityResultContracts.CreateDocument(MIME_TYPE_DOCUMENT_PDF)
    ) { uri: Uri? ->
        if (uri != null) {
            // User selected a location, proceed with copying the file.
            cacheFile?.let { cachedFile ->
                context?.let { context ->
                    copyFileToUri(context, cachedFile, uri)
                }
            }
            BottomSheetUtil.closeBottomSheet()
            isChatTranscriptDownloadInProgress = false
        } else {
            Logger.d("User did not save the chat transcript pdf file.")
            isChatTranscriptDownloadInProgress = false
        }
    }

    // Deflect the given escalation to the VA when UjetChatService is bound to
    private var deflectToVirtualAgentWhenServiceConnected = ESCALATION_ID_NONE

    private val bottomSheetListener = object : BottomSheetUtil.BottomSheetListener {
        override fun onBottomSheetExpanded() {
            onBottomSheetExpandedAccessibility()
        }

        override fun onBottomSheetCollapsed() {
            onBottomSheetCollapsedAccessibility()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        errorMessage = arguments?.getString(EXTRA_ERROR_MESSAGE) ?: ""
        isAfterHourMessage = arguments?.getBoolean(EXTRA_ERROR_TYPE) ?: false
        val activity = activity ?: return
        val chatPresenter = ChatPresenter(
            this,
            ujetContext(),
            Injection.provideBiometrics(activity, this),
            Injection.provideLocalRepository(activity),
            this,
            Injection.provideUseCaseHandler(),
            Injection.provideGetCompany(activity),
            Injection.provideGetMenu(activity),
            Injection.provideGetSelectedMenuId(activity),
            Injection.provideApiManager(activity),
            configuration
        )
        presenter = chatPresenter
        chatListener = chatPresenter
        registerActivityForResult()
        registerDocumentLauncher()
        accessibilityManager = activity.getSystemService(ACCESSIBILITY_SERVICE) as AccessibilityManager
        title = getString(R.string.ujet_chat_title).uppercase(Locale.ROOT)
        setActionBarTitle(title)
        invokeAgentWaitingAnnouncement()
    }

    private fun invokeAgentWaitingAnnouncement() {
        handler.postDelayed({
            val baseActivity = activity as? UjetBaseActivity ?: return@postDelayed
            // We need to check isAdded and baseActivity is not null to address app crash due to
            // IllegalStateException is thrown when ChatFragment not attached to a context.
            if (isAdded && baseActivity.supportActionBar?.title == getString(R.string.ujet_chat_title).uppercase(Locale.ROOT))
                accessibilityManager?.let {
                    AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                        it,
                        getString(R.string.ujet_chat_agent_assignment_announcement_talkback)
                    )
                }
        }, WAITING_FOR_AGENT_TIME_DURATION)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val activity = activity ?: return null
        sharedPreferences = activity.applicationContext.getSharedPreferences(SHARED_PREFERENCE_KEY, Context.MODE_PRIVATE)
        val view = inflater.inflate(R.layout.ujet_fragment_chat, container, false)
        view.setBackgroundColor(ujetStyle().primaryBackgroundColor)

        // header view
        val headerStyles = configuration.ujetStylesOptions?.chatStyles?.header
        val customHeaderTitle = configuration.customChatHeaderTitle
        chatStatusView = ChatStatusView(activity, ujetStyle(), headerStyles, customHeaderTitle, "")

        chatInputBarLayout = view.findViewById<ChatInputBarLayout>(R.id.chat_input_bar_layout).apply {
            setHint(getString(R.string.ujet_chat_input_placeholder))
            setImeActionLabel(getString(R.string.ujet_common_send))
            init(
                ujetStyle(),
                object : ChatInputBarListener {
                    override fun onQuickReplyClicked(lastMessage: VirtualAgentQuickReplyButtonsChatMessage, quickReply: QuickReplyButton) {
                        if (chatMessageRecyclerView?.paddingBottom?.compareTo(0) != 0) {
                            chatMessageRecyclerView?.updatePadding(bottom = 0)
                        }
                        <EMAIL>(lastMessage, quickReply)
                    }

                    override fun onSendChatMessagePreview(ongoingInputMessage: String) {
                        presenter?.onSendChatMessagePreview(ongoingInputMessage)
                    }

                    override fun onChatMessageSend(message: String) {
                        presenter?.onChatMessageSend(message)
                    }

                    override fun onSetChatInput(input: String) {
                        presenter?.setChatInput(input)
                    }

                    override fun isChatPreviewAvailable() = presenter?.isChatPreviewAvailable()

                    override fun onChatActionsMenuIconClicked() {
                        chatActionsMenuView?.let { BottomSheetUtil.addBottomSheet(context, bottomSheetContainer, it, bottomSheetListener) }
                    }

                    override fun showEscalateConfirmation() {
                        <EMAIL>()
                    }


                    override fun setActionIconAlignment(alignIconsVertically: Boolean) {
                        chatService?.alignIconsVertically = alignIconsVertically
                    }

                    override fun getActionIconAlignment(): Boolean {
                        return chatService?.alignIconsVertically ?: false
                    }

                    override fun setTextCountPerLine(textCountPerLine: Int) {
                        chatService?.textCountPerLine = textCountPerLine
                    }

                    override fun getTextCountPerLine(): Int {
                        return chatService?.textCountPerLine ?: 0
                    }
                },
                configuration.hideMediaAttachmentInChat,
                configuration.ujetStylesOptions,
                getString(R.string.ujet_escalation_to_human_agent)
            )
            setActionsMenuContentDescription(getString(R.string.ujet_chat_action_menu_accessibility))
        }

        // Empty view with loading indicator
        emptyViewProgressBar = view.findViewById<UjetProgressBar?>(R.id.empty_view_progress_bar)?.apply {
            val spinnerColor = ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_gray_lighter)
            setColorFilter(BlendModeColorFilterCompat.createBlendModeColorFilterCompat(spinnerColor, BlendModeCompat.SRC_IN))
            visibility = VISIBLE
        }
        // message list view
        chatMessageAdapter = ChatAdapter(activity, ujetStyle(), this)
        chatMessageAdapter?.addHeaderView(chatStatusView)
        chatMessageAdapter?.setChatItemClickListener(this)
        chatMessageRecyclerView = view.findViewById<RecyclerView>(R.id.message_recycler_view).apply {
            adapter = chatMessageAdapter
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    // means user finger is moving up but the list is going down
                    if (dy > 0) {
                        topItemVisible = false
                    } else if (!recyclerView.canScrollVertically(-1)) { // means user finger is moving down but the list is going up
                        // We reach top item in view when user scrolling down and view can not scroll vertically anymore
                        topItemVisible = true
                        // reset to scroll when current session messages are added
                        if (loadingChatHistory) {
                            loadingChatHistory = false
                        }
                    }
                }
            })
            importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_NO
            viewTreeObserver?.addOnGlobalLayoutListener(keyboardScreenListener)
        }
        chatMessageAdapter?.registerAdapterDataObserver(object : AdapterDataObserver() {
            override fun onChanged() {
                updateQuickReplyButtons()
                // Skip scrolling to current item / bottom position while loading chat history messages,
                // chat UI paused and when returning from media preview or download documents screen.
                if (!isChatUIPaused && !loadingChatHistory && !isWebFormClosed && !isWebFormVisible &&
                    chatService?.isMediaPreviewOrDownloadScreenVisible == false
                ) {
                    chatMessageRecyclerView?.smoothScrollToPosition(chatMessageAdapter?.itemCount ?: 0)
                }
                isWebFormClosed = false
                isWebFormVisible = false
                if (configuration.blockChatTerminationByEndUser && !isMenuOptionVisible) {
                    updateMenuOptionVisibility(chatService?.canDownloadChatTranscript() ?: false)
                }

                chatMessageRecyclerView?.viewTreeObserver?.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
                    override fun onPreDraw(): Boolean {
                        chatMessageRecyclerView?.viewTreeObserver?.removeOnPreDrawListener(this)
                        if (resumeUpdateAttachmentView) {
                            updateDownloadAttachmentView()
                            resumeUpdateAttachmentView = false // reset
                        }
                        return true
                    }
                })
            }
        })
        bottomSheetContainer = activity.findViewById(R.id.bottom_sheet_container)
        swipeRefreshLayout = view.findViewById<SwipeRefreshLayout>(R.id.swipe_refresh_layout)?.apply {
            setOnRefreshListener {
                this.isRefreshing = false // to stop the loading indicator
                if (topItemVisible && !loadingChatHistory) {
                    loadMoreItems()
                }
            }
        }
        initChatOptionsMenuView()
        initChatActionsMenuView()
        setChatInputVisible(false)
        setEndChatEnabled(false)
        setSendPhotoEnabled(false)
        // Post-session opt in banner
        initializePostSessionOptInBanner(view)
        setupNavigationBarMenu()
        handleKeyboardAccessibility(view)
        if (savedInstanceState != null) {
            isWebFormOrientationChanged = true
            //Passing `view` as a parameter so that swipeRefreshLayout can be accessed in real time because if we use fragment's view, it might be null
            restoredWebFormState(savedInstanceState, view)
            restorePostSessionState(savedInstanceState)
            restoreChatMenuBottomSheet(savedInstanceState)
            restoreMediaFiles(savedInstanceState)
        }
        return view
    }

    private fun restoreChatMenuBottomSheet(savedInstanceState: Bundle) {
        isChatTranscriptDownloadInProgress = savedInstanceState.getBoolean(STATE_CHAT_TRANSCRIPT_DOWNLOAD_IS_IN_PROGRESS, false)
        isChatTranscriptDownloadInitiatedFromInlineButton =
            savedInstanceState.getBoolean(STATE_CHAT_TRANSCRIPT_DOWNLOAD_INITIATED_FROM_INLINE_BUTTON, false)
        chatTranscriptId = savedInstanceState.getInt(STATE_CHAT_TRANSCRIPT_ID, -1)
        //Handle chat transcript download inline button state
        if (isChatTranscriptDownloadInitiatedFromInlineButton) {
            chatMessageRecyclerView?.post {
                chatEndedMessageViewHolderAdapterPosition =
                    savedInstanceState.getInt(STATE_CHAT_TRANSCRIPT_CHAT_ENDED_MESSAGE_VIEW_HOLDER_ADAPTER_POSITION, -1)
                chatEndedMessageViewHolder =
                    chatMessageRecyclerView?.findViewHolderForAdapterPosition(chatEndedMessageViewHolderAdapterPosition) as? ChatEndedMessageViewHolder
                if (isChatTranscriptDownloadInProgress) {
                    downloadChatTranscript(true, chatTranscriptId)
                }
                restoreChatTranscriptDownloadUI()
            }
        }
        //Proceed only if bottom sheet was previously opened
        if (!BottomSheetUtil.isBottomSheetOpened) return
        val context = context ?: return
        //Restore bottom sheet content
        val bottomSheetContent: View? = if (BottomSheetUtil.isLastViewChatOptionsMenu) chatOptionsMenuView else chatActionsMenuView
        bottomSheetContent?.let { BottomSheetUtil.addBottomSheet(context, bottomSheetContainer, it, bottomSheetListener) }
        if (isChatTranscriptDownloadInProgress) {
            downloadChatTranscript(false, chatTranscriptId)
        }
        restoreChatTranscriptDownloadUI()
    }

    private fun restoreChatTranscriptDownloadUI() {
        updateChatTranscriptDownloadButtonUI(
            isChatTranscriptDownloadInProgress,
            false,
            isChatTranscriptDownloadInitiatedFromInlineButton
        )
    }

    private fun restorePostSessionState(savedInstanceState: Bundle) {
        when {
            savedInstanceState.getBoolean(STATE_POST_SESSION_OPT_IN_BANNER_VISIBILITY, false) -> {
                showPostSessionOptInBanner(savedInstanceState.getBoolean(STATE_POST_SESSION_OPT_IN_BANNER_LOADING_VISIBILITY, false))
                isPostSessionOptInBannerVisible = true
            }

            savedInstanceState.getBoolean(STATE_POST_SESSION_CHAT_IN_PROGRESS, false) -> {
                showPostSessionChatUI(true)
            }
        }
    }

    private fun setupNavigationBarMenu() {
        chatOptionsMenuView?.updateEndChatButtonText(getString(R.string.ujet_chat_end))
        isMenuOptionVisible = !configuration.blockChatTerminationByEndUser
        registerNavigationBarMenuProvider(R.menu.ujet_menu_chat, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
            menuItem.isVisible = isMenuOptionVisible
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_more_options -> {
                    val context = context ?: return@registerNavigationBarMenuProvider true
                    chatOptionsMenuView?.apply {
                        BottomSheetUtil.addBottomSheet(context, bottomSheetContainer, this, bottomSheetListener)
                        canDownloadChatTranscript(chatService?.canDownloadChatTranscript() ?: false)
                    }
                    activity?.let { chatInputBarLayout?.hideKeyboard(it) }
                    true
                }

                else -> false
            }
        })
    }

    private fun handleKeyboardAccessibility(view: View) {
        view.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            menu = getMenuItemView(toolbar, R.id.ujet_menu_item_more_options)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                if (BottomSheetUtil.isBottomSheetOpened) {
                    chatOptionsMenuView?.requestFocus()
                } else {
                    chatMessageRecyclerView?.requestFocus()
                }
                true
            })
        }
    }

    private fun onBottomSheetExpandedAccessibility() {
        updateBottomSheetAccessibility(
            isViewFocusable = false,
            accessibilityImportanceValue = IMPORTANT_FOR_ACCESSIBILITY_NO
        )
    }

    private fun onBottomSheetCollapsedAccessibility() {
        updateBottomSheetAccessibility(
            isViewFocusable = true,
            accessibilityImportanceValue = IMPORTANT_FOR_ACCESSIBILITY_YES
        )
        if (BottomSheetUtil.isLastViewChatOptionsMenu) {
            menu?.requestFocus()
        } else {
            chatInputBarLayout?.requestFocusOfChatActionsMenu()
        }
    }

    private fun updateBottomSheetAccessibility(
        isViewFocusable: Boolean,
        accessibilityImportanceValue: Int,
        menuFocusable: Boolean = isViewFocusable,
        menuClickable: Boolean = isViewFocusable
    ) {
        menu?.apply {
            isFocusable = menuFocusable
            isFocusableInTouchMode = menuFocusable
            isClickable = menuClickable
            importantForAccessibility = accessibilityImportanceValue
        }
        findToolbarViewByText(title)?.apply {
            importantForAccessibility = if (isViewFocusable) IMPORTANT_FOR_ACCESSIBILITY_AUTO else IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        navigateUpView?.apply {
            isFocusable = isViewFocusable
            isFocusableInTouchMode = isViewFocusable
            isClickable = isViewFocusable
            importantForAccessibility = accessibilityImportanceValue
        }
        chatMessageRecyclerView?.apply {
            isFocusable = isViewFocusable
            importantForAccessibility = if (isViewFocusable) IMPORTANT_FOR_ACCESSIBILITY_YES else IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS
        }
        chatInputBarLayout?.setFocus(isViewFocusable)
    }

    private fun findToolbarViewByText(value: String): View? {
        var targetView: View? = null
        val foundViews = ArrayList<View>()
        val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar)
        toolbar?.findViewsWithText(foundViews, value, View.FIND_VIEWS_WITH_TEXT)
        if (foundViews.isNotEmpty()) {
            targetView = foundViews[0]
        }
        return targetView
    }

    private fun showEndChatConfirmationDialog() {
        val context = context ?: return
        BasicAlertDialogFragment.newInstance(
            title = context.getString(R.string.ujet_chat_end),
            message = context.getString(R.string.ujet_chat_end_dialog),
            positiveButton = context.getString(R.string.ujet_common_end),
            negativeButton = context.getString(R.string.ujet_common_cancel)
        ).showUniqueNow(
            childFragmentManager,
            TAG_END_CHAT_CONFIRMATION_DIALOG, this,
            onPositiveAction = {
                onEndChatConfirmation()
                BottomSheetUtil.closeBottomSheet()
            }
        )
    }

    private fun updateMenuOptionVisibility(isVisible: Boolean) {
        val activity = activity ?: return
        isMenuOptionVisible = isVisible
        activity.invalidateOptionsMenu()
    }

    private fun onEndChatConfirmation() {
        if (presenter?.isPostSessionRequired() == true && presenter?.getChatStatus() != ChatStatus.Queued) {
            val status = if (presenter?.isPostSessionOptInRequired() == true) {
                PostSessionTransferStatus.Ready
            } else {
                setPostSessionOptInBannerVisibility(isVisible = true, showLoadingBar = true)
                PostSessionTransferStatus.InProgress
            }
            chatService?.updatePostSessionTransferStatus(status, presenter?.isPostSessionOptInRequired() ?: false)
        } else {
            presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
        }
    }

    private fun initChatOptionsMenuView() {
        val activity = activity ?: return
        chatOptionsMenuView = ChatOptionsMenuView(
            activity,
            configuration.blockChatTerminationByEndUser,
            configuration.ujetStylesOptions?.chatStyles?.endChatButton,
            ujetStyle(),
            object : ChatOptionsMenuView.ChatMenuOptionsListener {
                override fun onCloseButtonClicked() {
                    BottomSheetUtil.closeBottomSheet()
                }

                override fun onEndChatButtonClicked() {
                    when (chatOptionsMenuView?.getEndChatButtonText()) {
                        getString(R.string.ujet_chat_exit) -> {
                            presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
                        }

                        else -> showEndChatConfirmationDialog()
                    }
                }

                override fun onDownloadChatTranscriptButtonClicked() {
                    downloadChatTranscript(false)
                }
            })
    }

    private fun downloadChatTranscript(isInlineButton: Boolean, chatTranscriptId: Int = -1) {
        val cacheDirectory = File(context?.cacheDir, "ChatTranscript")
        if (cacheDirectory.exists().not()) {
            cacheDirectory.mkdir()
        }
        isChatTranscriptDownloadInProgress = true
        isChatTranscriptDownloadInitiatedFromInlineButton = isInlineButton
        if (chatTranscriptId == -1) {
            presenter?.initiateChatTranscriptPdfGeneration(cacheDirectory, isInlineButton)
        } else {
            presenter?.downloadChatTranscriptPdf(cacheDirectory, chatTranscriptId, isInlineButton)
        }
    }

    private fun initChatActionsMenuView() {
        val activity = activity ?: return
        chatActionsMenuView = ChatActionsMenuView(
            activity,
            ujetStyle(),
            configuration.ujetStylesOptions,
            configuration.hideMediaAttachmentInChat,
            object : ChatActionsMenuView.ChatActionsMenuListener {
                override fun onCloseButtonClicked() {
                    BottomSheetUtil.closeBottomSheet()
                }

                override fun onCoBrowseButtonClicked() {
                    SmartActionManager.startUserInitiatedAction(<EMAIL> ?: return, COBROWSE, CommunicationType.Chat)
                }

                override fun onTakePhotoButtonClicked() {
                    SmartActionManager.startUserInitiatedAction(
                        <EMAIL> ?: return,
                        SmartActionType.TAKE_PHOTO,
                        CommunicationType.Chat
                    )
                }

                override fun onPhotoFromLibraryButtonClicked() {
                    SmartActionManager.startUserInitiatedAction(
                        <EMAIL> ?: return,
                        SmartActionType.SELECT_PHOTO_FROM_LIBRARY,
                        CommunicationType.Chat
                    )
                }
            })
    }

    private fun restoreMediaFiles(savedInstanceState: Bundle) {
        mediaFiles = savedInstanceState.getParcelableArrayList(STATE_MEDIA_FILES)
        isCreateDocumentIntentLaunchedForMedia = savedInstanceState.getBoolean(STATE_MEDIA_FILES_IS_INTENT_LAUNCHED)
        isDownloadSucceeded = savedInstanceState.getBoolean(STATE_IS_DOWNLOAD_SUCCEEDED)
        mediaType = savedInstanceState.getString(STATE_MEDIA_TYPE)
        downloadedMediaCount = savedInstanceState.getInt(STATE_MEDIA_FILES_DOWNLOAD_COUNT)
        attachmentsMessageAdapterPosition = savedInstanceState.getInt(STATE_ATTACHMENTS_ADAPTER_POSITION)
        lastVisibleItemPosition = savedInstanceState.getInt(STATE_LAST_VISIBLE_ITEM_POSITION)
    }

    private fun restoredWebFormState(savedInstanceState: Bundle, view: View) {
        isWebFormInProgress = savedInstanceState.getBoolean(STATE_WEB_FORM_IS_IN_PROGRESS, false)
        if (isWebFormInProgress) {
            webFormChatMessage = generateWebFormChatMessage(savedInstanceState)
            newFormAdapterPosition = savedInstanceState.getInt(STATE_NEW_FORM_ADAPTER_POSITION, -1)
            currentFormAdapterPosition = savedInstanceState.getInt(STATE_CURRENT_FORM_ADAPTER_POSITION, -1)
            webFormErrorMsg = savedInstanceState.getInt(STATE_BOTTOM_SHEET_ERROR_MSG, R.string.ujet_chat_form_loading_failed_message)
            webFormUrl = savedInstanceState.getString(STATE_WEB_FORM_URL, "")
            val swipeRefreshLayout = view.findViewById<SwipeRefreshLayout>(R.id.swipe_refresh_layout)
            webFormInitialState = savedInstanceState.getInt(STATE_BOTTOM_SHEET_STATE, STATE_EXPANDED)
            val restoredHeaderContainerHeight = savedInstanceState.getInt(STATE_HEADER_CONTAINER_HEIGHT, 0)
            val restoredInputContainerHeight = savedInstanceState.getInt(STATE_INPUT_CONTAINER_HEIGHT, 0)
            val webFormState = WebFormState.valueOf(savedInstanceState.getString(STATE_WEB_FORM_STATE, WebFormState.NONE.name))
            WebFormState.setState(webFormState)
            showWebForm()
            when {
                webFormState == WebFormState.ERROR || webFormUrl.isEmpty() -> {
                    showWebFormLoadingError(webFormErrorMsg)
                }

                else -> {
                    loadUrlInWebForm(webFormUrl)
                }
            }
            swipeRefreshLayout.post {
                chatInputBarLayout?.setQuickReplyButtonsBottomMargin(restoredHeaderContainerHeight)
                swipeRefreshLayout.updatePadding(bottom = restoredHeaderContainerHeight)
                if (webFormInitialState == STATE_COLLAPSED) {
                    formBottomSheet?.updateLayoutParams {
                        height = restoredHeaderContainerHeight
                    }
                    formBottomSheetBehavior?.peekHeight = restoredHeaderContainerHeight + restoredInputContainerHeight
                    formBottomSheet?.findViewById<TextView>(R.id.close_btn)?.apply {
                        visibility = GONE
                    }
                }
            }
            this.swipeRefreshLayout = swipeRefreshLayout
        }
    }

    private fun generateWebFormChatMessage(savedInstanceState: Bundle): WebFormChatMessage? {
        val webForm = savedInstanceState.getSerializable(STATE_WEB_FORM_CHAT_MESSAGE_WEB_FORM) as? WebForm
        val memberIdentity = savedInstanceState.getString(STATE_WEB_FORM_CHAT_MESSAGE_MEMBER_IDENTITY, "")
        val messageIndex = savedInstanceState.getLong(STATE_WEB_FORM_CHAT_MESSAGE_MESSAGE_INDEX, 0L)
        val localId = savedInstanceState.getInt(STATE_WEB_FORM_CHAT_MESSAGE_LOCAL_ID, 0)
        val sid = savedInstanceState.getString(STATE_WEB_FORM_CHAT_MESSAGE_SID, "")
        return webForm?.let { WebFormChatMessage.createSent(localId, sid, Date(), it, memberIdentity, messageIndex) }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(STATE_BOTTOM_SHEET_STATE, formBottomSheetBehavior?.state ?: STATE_EXPANDED)
        outState.putInt(STATE_BOTTOM_SHEET_ERROR_MSG, webFormErrorMsg)
        outState.putBoolean(STATE_WEB_FORM_IS_IN_PROGRESS, isWebFormInProgress)
        outState.putInt(STATE_NEW_FORM_ADAPTER_POSITION, newFormAdapterPosition)
        outState.putInt(STATE_CURRENT_FORM_ADAPTER_POSITION, currentFormAdapterPosition)
        outState.putString(STATE_WEB_FORM_STATE, WebFormState.activeState.name)
        outState.putString(STATE_WEB_FORM_URL, webFormUrl)
        outState.putInt(STATE_HEADER_CONTAINER_HEIGHT, headerContainerHeight)
        outState.putInt(STATE_INPUT_CONTAINER_HEIGHT, chatInputBarLayout?.getMainInputContainerHeight() ?: 80)
        outState.putInt(STATE_WEB_FORM_CHAT_MESSAGE_LOCAL_ID, webFormChatMessage?.localId ?: 0)
        outState.putString(STATE_WEB_FORM_CHAT_MESSAGE_SID, webFormChatMessage?.sid)
        outState.putSerializable(STATE_WEB_FORM_CHAT_MESSAGE_WEB_FORM, webFormChatMessage?.webForm)
        outState.putString(STATE_WEB_FORM_CHAT_MESSAGE_MEMBER_IDENTITY, webFormChatMessage?.memberIdentity ?: "")
        outState.putLong(STATE_WEB_FORM_CHAT_MESSAGE_MESSAGE_INDEX, webFormChatMessage?.messageIndex ?: 0L)
        outState.putBoolean(STATE_POST_SESSION_OPT_IN_BANNER_VISIBILITY, isPostSessionOptInBannerVisible)
        outState.putBoolean(STATE_POST_SESSION_OPT_IN_BANNER_LOADING_VISIBILITY, isPostSessionOptInBannerLoadingState)
        outState.putBoolean(STATE_POST_SESSION_CHAT_IN_PROGRESS, isPostSessionChatInProgress)
        outState.putBoolean(STATE_CHAT_TRANSCRIPT_DOWNLOAD_IS_IN_PROGRESS, isChatTranscriptDownloadInProgress)
        outState.putBoolean(STATE_CHAT_TRANSCRIPT_DOWNLOAD_INITIATED_FROM_INLINE_BUTTON, isChatTranscriptDownloadInitiatedFromInlineButton)
        outState.putInt(STATE_CHAT_TRANSCRIPT_CHAT_ENDED_MESSAGE_VIEW_HOLDER_ADAPTER_POSITION, chatEndedMessageViewHolderAdapterPosition)
        outState.putInt(STATE_CHAT_TRANSCRIPT_ID, chatTranscriptId)
        outState.putParcelableArrayList(STATE_MEDIA_FILES, mediaFiles)
        outState.putBoolean(STATE_MEDIA_FILES_IS_INTENT_LAUNCHED, isCreateDocumentIntentLaunchedForMedia)
        outState.putBoolean(STATE_IS_DOWNLOAD_SUCCEEDED, isDownloadSucceeded)
        outState.putString(STATE_MEDIA_TYPE, mediaType)
        outState.putInt(STATE_MEDIA_FILES_DOWNLOAD_COUNT, downloadedMediaCount)
        outState.putInt(STATE_ATTACHMENTS_ADAPTER_POSITION, attachmentsMessageAdapterPosition)
        outState.putInt(STATE_LAST_VISIBLE_ITEM_POSITION, lastVisibleItemPosition)
    }

    override fun setPostSessionOptInBannerVisibility(isVisible: Boolean, showLoadingBar: Boolean) {
        when {
            isVisible && postSessionOptInBannerContainer?.visibility == GONE -> {
                showPostSessionOptInBanner(showLoadingBar)
            }

            !isVisible && postSessionOptInBannerContainer?.visibility == VISIBLE -> {
                hidePostSessionOptInBanner()
            }
        }
    }

    private fun showPostSessionOptInBanner(showLoadingBar: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        isPostSessionOptInBannerVisible = true
        isPostSessionOptInBannerLoadingState = showLoadingBar
        updateEndChatMenuText(R.string.ujet_chat_exit)
        showPostSessionOptInBannerAnimation()
        postSessionOptInBannerContainer?.visibility = VISIBLE
        when {
            showLoadingBar -> {
                postSessionBannerProgressBar?.visibility = VISIBLE
                postSessionOptInBanner?.visibility = GONE
            }

            else -> {
                postSessionBannerProgressBar?.visibility = GONE
                postSessionOptInBanner?.visibility = VISIBLE
            }
        }
        (swipeRefreshLayout?.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.removeRule(RelativeLayout.ABOVE)
            it.addRule(RelativeLayout.ABOVE, R.id.post_session_opt_in_banner_container)
            swipeRefreshLayout?.layoutParams = it
        }
        chatInputBarLayout?.setChatInputVisible(activity, false)
        if (formBottomSheetBehavior?.state == STATE_COLLAPSED) {
            formBottomSheet?.visibility = GONE
        }
    }

    private fun hidePostSessionOptInBanner() {
        isPostSessionOptInBannerVisible = false
        postSessionOptInBannerContainer?.visibility = GONE
        postSessionBannerProgressBar?.visibility = GONE
        (swipeRefreshLayout?.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.addRule(RelativeLayout.ABOVE, R.id.chat_input_bar_layout)
            swipeRefreshLayout?.layoutParams = it
        }
    }

    private fun handlePostSessionOptInBannerButtonOrientation(postSessionOptInBannerContainer: LinearLayout) {
        postSessionOptInBannerContainer.findViewById<LinearLayout>(R.id.button_container)?.apply {
            orientation = when {
                isLandscape(context) -> {
                    LinearLayout.HORIZONTAL
                }

                else -> {
                    LinearLayout.VERTICAL
                }
            }
        }
    }

    private fun showPostSessionOptInBannerAnimation() {
        postSessionOptInBannerContainer?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                postSessionOptInBannerContainer?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                val postSessionBannerContainerHeight = postSessionOptInBannerContainer?.height?.toFloat() ?: 0f
                val animation = TranslateAnimation(
                    0f, 0f,
                    postSessionBannerContainerHeight, 0f
                )
                animation.duration = POST_SESSION_ANIMATION_DURATION
                postSessionOptInBannerContainer?.startAnimation(animation)
            }
        })
    }

    private fun initializePostSessionOptInBanner(view: View) {
        postSessionBannerProgressBar = view.findViewById(R.id.post_session_progress_bar)
        postSessionOptInBanner = view.findViewById(R.id.post_session_opt_in_banner)
        view.findViewById<TextView>(R.id.post_session_opt_in_banner_title)?.apply {
            setTextColor(ujetStyle().textPrimaryColor)
        }
        view.findViewById<TextView>(R.id.post_session_opt_in_banner_subtitle)?.apply {
            setTextColor(ujetStyle().textSecondaryColor)
        }
        postSessionOptInBannerContainer = view.findViewById<LinearLayout>(R.id.post_session_opt_in_banner_container)?.apply {
            setOptInBannerBackground(this)
            handlePostSessionOptInBannerButtonOrientation(this)
            findViewById<FancyButton>(R.id.yes_button)?.apply {
                UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
                setOnClickListener {
                    chatService?.updatePostSessionTransferStatus(PostSessionTransferStatus.InProgress, true)
                    postSessionOptInBanner?.visibility = GONE
                    postSessionBannerProgressBar?.visibility = VISIBLE
                    isPostSessionOptInBannerLoadingState = true
                }
                if (isLargeTextAccessibilityEnabled(context)) {
                    setPostSessionOptInBannerButtonWidth(this)
                }
            }
            findViewById<FancyButton>(R.id.no_button)?.apply {
                UjetViewStyler.styleInvertedButton(ujetStyle(), this)
                setOnClickListener {
                    presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
                }
                if (isLargeTextAccessibilityEnabled(context)) {
                    setPostSessionOptInBannerButtonWidth(this)
                }
            }
        }
    }

    private fun setPostSessionOptInBannerButtonWidth(button: FancyButton) {
        button.apply {
            updateLayoutParams {
                width = if (isPortrait(context)) {
                    MATCH_PARENT
                } else {
                    dpToPx(context, 200).toInt()
                }
            }
        }
    }

    private fun setOptInBannerBackground(optInBannerLayout: LinearLayout) {
        val context = context ?: return
        val drawable = ContextCompat.getDrawable(context, R.drawable.ujet_post_session_opt_in_banner_background)
        val insetDrawable = drawable as? InsetDrawable
        val wrappedDrawable = insetDrawable?.drawable
        val gradientDrawable = wrappedDrawable as? GradientDrawable
        gradientDrawable?.setColor(ujetStyle().primaryBackgroundColor)
        val newInsetDrawable = InsetDrawable(
            gradientDrawable,
            dpToPx(context, -1).toInt(),
            dpToPx(context, 1).toInt(),
            dpToPx(context, -1).toInt(),
            dpToPx(context, -1).toInt()
        )
        optInBannerLayout.background = newInsetDrawable
    }


    override fun showWebForm() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        WebFormState.setState(WebFormState.LOADING)
        formBottomSheet = activity.findViewById<FrameLayout>(R.id.web_form_bottom_sheet)?.apply {
            visibility = VISIBLE
            setOnClickListener { }
        }
        isWebFormVisible = true
        formBottomSheet?.findViewById<LinearLayout>(R.id.web_form_container)?.apply {
            val backgroundDrawable = ContextCompat.getDrawable(context, R.drawable.ujet_bottom_sheet_rounded_corner)
            (backgroundDrawable as GradientDrawable).setColor(ujetStyle().primaryBackgroundColor)
            background = backgroundDrawable
        }
        formProgressBar = formBottomSheet?.findViewById(R.id.progress_bar)
        formWebView = formBottomSheet?.findViewById(R.id.web_view)
        formLoadingErrorMessage = formBottomSheet?.findViewById(R.id.web_form_error_container)
        val webFormTitle = formBottomSheet?.findViewById<TextView>(R.id.title)
        formBottomSheet?.let {
            val headerContainer = it.findViewById<RelativeLayout>(R.id.header_container)
            val closeBtn = it.findViewById<TextView>(R.id.close_btn)
            formBottomSheetBehavior = BottomSheetBehavior.from(it).apply {
                //using `post{}` here to make sure that the bottom sheet is fully rendered and we get the actual height of the header container
                headerContainer.post {
                    headerContainerHeight = headerContainer.height
                    chatInputBarLayout?.setQuickReplyButtonsBottomMargin(headerContainerHeight)
                    if (chatInputBarLayout?.isQuickReplyButtonsVisible() == false) {
                        swipeRefreshLayout?.updatePadding(bottom = headerContainerHeight)
                    }
                    webFormTitle?.apply {
                        text = webFormChatMessage?.webForm?.title
                        setTextColor(ujetStyle().webFormTitleTextColor)
                        updateLayoutParams<MarginLayoutParams> {
                            marginEnd = closeBtn.width + dpToPx(context, 80).toInt()
                        }
                    }
                    closeBtn.text = getString(R.string.ujet_chat_form_close)
                    if (webFormInitialState == STATE_HIDDEN || webFormInitialState == STATE_COLLAPSED || webFormInitialState == STATE_EXPANDED) {
                        state = webFormInitialState
                    }
                }
                initBottomSheetBehavior(this)
                closeBtn.setOnClickListener {
                    when {
                        WebFormState.isError() -> {
                            isWebFormClosed = true
                            webFormChatMessage?.let { chatService?.setWebFormStatus(it, "", context) }
                            closeWebFormBottomSheet()
                        }

                        else -> {
                            showWebFormCloseAlert()
                        }
                    }
                }
            }
            formBottomSheetBehavior?.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == STATE_COLLAPSED) {
                        closeBtn.visibility = GONE
                        val headerContainerHeight = headerContainer.height
                        formBottomSheet?.updateLayoutParams {
                            height = headerContainerHeight
                        }
                        formBottomSheetBehavior?.peekHeight = headerContainerHeight + (chatInputBarLayout?.getMainInputContainerHeight() ?: 80)
                        initialWebFormSlideOffset = WEB_FORM_BOTTOM_SHEET_COLLAPSED_OFFSET
                        // Make web form invisible if post-session opt-in banner is visible
                        if (isPostSessionOptInBannerVisible) {
                            formBottomSheet?.visibility = GONE
                        }
                    } else {
                        formBottomSheet?.updateLayoutParams {
                            height = MATCH_PARENT
                        }
                        if (newState == STATE_EXPANDED) {
                            initialWebFormSlideOffset = WEB_FORM_BOTTOM_SHEET_EXPANDED_OFFSET
                            closeBtn.visibility = VISIBLE
                        }
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    val slideOffsetDiff = initialWebFormSlideOffset - slideOffset
                    if (!isWebFormCloseBtnClicked) {
                        when {
                            slideOffsetDiff > 0 -> { // Dragging down
                                formBottomSheetBehavior?.state = STATE_COLLAPSED
                            }

                            slideOffsetDiff < 0 -> { // Dragging up
                                formBottomSheetBehavior?.state = STATE_EXPANDED
                            }
                        }
                    }
                }
            })
        }
    }

    private fun initBottomSheetBehavior(bottomSheetBehavior: BottomSheetBehavior<FrameLayout>) {
        bottomSheetBehavior.isFitToContents = false
        bottomSheetBehavior.isHideable = false
        formProgressBar?.visibility = VISIBLE
        formWebView?.visibility = GONE
        formLoadingErrorMessage?.visibility = GONE
        //Giving 2s delay to set `isWebFormCloseBtnClicked` to false so that `onSlide` callback works properly when user open web form again
        //after pressing close button
        webFormCloseDelayJob = lifecycleScope.launch {
            delay(2000)
            isWebFormCloseBtnClicked = false
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun loadUrlInWebForm(url: String) {
        webFormUrl = url
        formWebView?.apply {
            visibility = VISIBLE
            formProgressBar?.visibility = GONE
            settings.javaScriptEnabled = true
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    WebFormState.setState(WebFormState.SUCCESS)
                    injectScriptToFormContent(view)
                }

                override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                    super.onReceivedError(view, request, error)
                    WebFormState.setState(WebFormState.ERROR)
                }

                // Web view gets destroyed in few 16K devices (with Android 15) because of broken web
                // view version, to handle it, need to override below method
                override fun onRenderProcessGone(view: WebView?, detail: RenderProcessGoneDetail?): Boolean {
                    Logger.d("Web form: web content rendering process killed")
                    return true
                }
            }
            addJavascriptInterface(object : Any() {
                @JavascriptInterface
                fun postMessage(message: String) {
                    try {
                        lifecycleScope.launch {
                            val formCompleteEvent = uson.deserialize(message, FormCompleteEvent::class.java)
                            if (formCompleteEvent?.type == "form_complete") {
                                when (formCompleteEvent.data?.status?.lowercase()) {
                                    "error" -> {
                                        showWebFormSubmitFailedAlert()
                                    }

                                    "success" -> {
                                        handleFormCompleteEvent(getString(R.string.ujet_chat_form_status_completed))
                                    }

                                    "cancelled" -> {
                                        handleFormCompleteEvent("")
                                    }
                                }
                                chatService?.sendFormCompleteEventMessage(formCompleteEvent)
                            }
                        }
                    } catch (e: Throwable) {
                        Logger.e(e.message)
                    } finally {
                        Logger.d("Web form: message received from postMessage: $message")
                    }
                }
            }, "formCompleteEvents")
            loadUrl(url)
        }
    }

    private fun handleFormCompleteEvent(status: String) {
        closeWebFormBottomSheet()
        webFormChatMessage?.let { chatService?.setWebFormStatus(it, status, context) }
    }

    override fun showWebFormLoadingError(errorMessage: Int) {
        webFormErrorMsg = errorMessage
        WebFormState.setState(WebFormState.ERROR)
        formProgressBar?.visibility = GONE
        formWebView?.visibility = GONE
        formLoadingErrorMessage?.visibility = VISIBLE
        formLoadingErrorMessage?.findViewById<TextView>(R.id.web_form_error_text)?.apply {
            text = getString(errorMessage)
            setTextColor(ujetStyle().webFormErrorTextColor)
        }
        formLoadingErrorMessage?.findViewById<FancyButton>(R.id.web_form_error_close_btn)?.apply {
            setOnClickListener {
                webFormChatMessage?.let { chatService?.setWebFormStatus(it, "", context) }
                closeWebFormBottomSheet()
            }
        }
    }

    private fun closeWebFormBottomSheet() {
        formBottomSheetBehavior?.isHideable = true
        formBottomSheetBehavior?.state = STATE_HIDDEN
        isWebFormCloseBtnClicked = true
        chatInputBarLayout?.setQuickReplyButtonsBottomMargin(0)
        swipeRefreshLayout?.updatePadding(bottom = 0)
        presenter?.clearWebFormResources()
        isWebFormInProgress = false
        currentFormAdapterPosition = -1
        webFormCloseDelayJob?.cancel()
        WebFormState.setState(WebFormState.NONE)
        webFormInitialState = STATE_EXPANDED
    }

    private fun injectScriptToFormContent(webView: WebView?) {
        webView?.evaluateJavascript(
            "(function() {\n" +
                    "    const originalPostMessage = window.parent.postMessage;\n" +
                    "    window.postMessage = function(message) {\n" +
                    "        // Convert to string only if not already a string\n" +
                    "        const messageString = typeof message === 'string' ? message : JSON.stringify(message);\n" +
                    "        window.formCompleteEvents.postMessage(messageString);\n" +
                    "        originalPostMessage.apply(window, arguments);\n" +
                    "    };\n" +
                    "})();",
            null
        )
    }

    private fun loadMoreItems() {
        loadingChatHistory = true
        presenter?.requestChatHistory()
    }

    override fun onStop() {
        super.onStop()
        chatInputBarLayout?.onStop()
    }

    private fun showEscalateConfirmation() {
        AlertDialog
            .Builder(activity)
            .setMessage(R.string.ujet_escalation_dialog)
            .setPositiveButton(R.string.ujet_common_yes) { _, _ ->
                presenter?.onEscalateClicked()
                chatInputBarLayout?.invokeFocusEventOnEscalateButton()
            }
            .setNegativeButton(R.string.ujet_common_no) { _, _ ->
                chatInputBarLayout?.invokeFocusEventOnEscalateButton()
            }
            .create()
            .show()

    }

    override fun isActive() = isAdded

    override fun onResume() {
        super.onResume()
        if (isAfterHourMessage) {
            presenter?.onChatAfterHourMessageReceived(errorMessage)
        } else {
            presenter?.onChatErrorMessageReceived(errorMessage)
        }

        //when user back from external deflection link url, chat UI is cleared and need to bind service again
        //before clearing the chat just like we do for other cases
        if (bindChatService()) {
            presenter?.start()
            isChatUIPaused = false // reset
        } else {
            presenter?.onChatServiceUnavailable()
        }

        //Show warning dialog when user back to app within 5 minutes from external deflection link url page
        if (isExternalDeflectionLinkClicked) {
            handler.removeCallbacksAndMessages(null)
            if (isTimerFinished) {
                finish()
            } else {
                showIssueResolvedConfirmationDialog()
            }
        }

        // Refresh header state
        val headerStyles = configuration.ujetStylesOptions?.chatStyles?.header
        chatStatusView?.displayCurrentState(headerStyles?.textContent)
        // Refresh action icon alignment
        chatInputBarLayout?.updateActionIconsAlignment()
    }

    private fun showIssueResolvedConfirmationDialog() {
        if (isActive.not()) {
            return
        }

        ConfirmationDialogFragment.newInstance(
            TAG,
            REQUEST_CODE_WARNING_DIALOG_RESPONSE,
            getString(R.string.ujet_external_deflection_link_message),
            null,
            getString(R.string.ujet_common_yes),
            getString(R.string.ujet_common_no)
        ).show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun onPause() {
        unbindChatService()
        presenter?.stop()
        isChatUIPaused = true
        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        chatMessageRecyclerView = null
        chatStatusView = null
        chatInputBarLayout?.clearResources()
        chatInputBarLayout = null
        chatAfterHoursView = null
        chatDismissView = null
        typingIndicatorView = null
        chatMessageRecyclerView?.viewTreeObserver?.removeOnGlobalLayoutListener(keyboardScreenListener)
        formLoadingErrorMessage = null
        formWebView = null
        formProgressBar = null
        swipeRefreshLayout = null
        formBottomSheet = null
        formBottomSheetBehavior = null
        presenter?.clearWebFormResources()
        presenter?.clearDownloadChatTranscriptResources()
        WebFormState.setState(WebFormState.NONE)
        emptyViewProgressBar = null
        BottomSheetUtil.closeBottomSheet()
        chatOptionsMenuView?.clearResources()
    }

    override fun onDestroy() {
        super.onDestroy()
        chatMessageAdapter?.clear()
        chatMessageAdapter = null
    }

    override fun finish() {
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity ?: return)
    }

    override fun showErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        networkDialog.show(parentFragmentManager, ConnectivityDialogFragment.TAG)
    }

    override fun hideErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        if (networkDialog.showsDialog) {
            networkDialog.closeDialog(parentFragmentManager)
        }
    }

    override fun displayCurrentState(message: String) {
        chatStatusView?.displayCurrentState(message)
    }

    override fun setAgentAvatar(avatarUrl: String) {
        if (!isActive) {
            return
        }
        chatStatusView?.updateAvatarImage(activity ?: return, avatarUrl)
    }

    override fun setChatInputVisible(isVisible: Boolean) {
        val activity = activity ?: return
        chatInputBarLayout?.setChatInputVisible(activity, isVisible)
        formBottomSheetBehavior?.peekHeight = headerContainerHeight + (chatInputBarLayout?.getMainInputContainerHeight() ?: 80)
    }

    override fun setTypingIndicatorVisible(isVisible: Boolean) {
        if (!isActive) {
            return
        }
        if (isVisible) {
            if (typingIndicatorView == null) {
                typingIndicatorView = TypingIndicatorView(activity ?: return, ujetStyle())
                chatMessageAdapter?.addFooterView(typingIndicatorView ?: return)
            }
        } else {
            chatMessageAdapter?.removeFooterView(typingIndicatorView ?: return)
            typingIndicatorView = null
        }
    }

    override fun updateEndChatMenuText(stringId: Int) {
        if (!isActive) {
            return
        }
        chatOptionsMenuView?.updateEndChatButtonText(getString(stringId))
    }

    override fun clearChatMessages() {
        chatMessageAdapter?.clearChatMessages()
    }

    override fun showSurveyErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        val message = getString(R.string.ujet_chat_post_session_survey_failed_to_load)
        val confirmMessage = getString(R.string.ujet_chat_form_close)
        ConfirmationDialogFragment
            .newInstance("", message, confirmMessage, true, true, TAG, POST_SESSION_ERROR_REQUEST)
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun showChatTranscriptMaxRequestErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        val message = getString(R.string.ujet_chat_transcript_download_limit_reached)
        val confirmMessage = getString(R.string.ujet_chat_form_dismiss)
        ConfirmationDialogFragment
            .newInstance("", message, confirmMessage, true, true, TAG, CHAT_TRANSCRIPT_MAX_DOWNLOAD_ERROR_REQUEST)
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun showPostSessionChatUI(value: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (!isPostSessionChatInProgress) {
            setTypingIndicatorVisible(true)
            showPostSessionChatUIAnimation()
            updateEndChatMenuText(R.string.ujet_chat_exit)
            chatStatusView?.displayCurrentState("")
            isPostSessionChatInProgress = true
            swipeRefreshLayout?.apply {
                val containerColorResId = getColorResIdByName(activity, configuration.ujetStylesOptions?.chatStyles?.postSession?.backgroundColor)
                if (containerColorResId != RESOURCE_NOT_FOUND) {
                    setBackgroundColor(ContextCompat.getColor(activity, containerColorResId))
                } else {
                    setBackgroundColor(ContextCompat.getColor(activity, co.ujet.android.ui.R.color.ujet_primary))
                }
                setPadding(dpToPx(activity, 12).toInt())
            }
            val borderWidth = if (configuration.ujetStylesOptions?.chatStyles?.postSession?.border?.width == 0) 0 else dpToPx(activity, 1).toInt()
            val drawable = ContextCompat.getDrawable(activity, R.drawable.ujet_post_session_chat_background)
            val gradientDrawable = drawable as? GradientDrawable
            gradientDrawable?.setColor(ujetStyle().primaryBackgroundColor)
            val borderColorResId = getColorResIdByName(activity, configuration.ujetStylesOptions?.chatStyles?.postSession?.border?.color)
            if (borderColorResId != RESOURCE_NOT_FOUND) {
                gradientDrawable?.setStroke(borderWidth, ContextCompat.getColor(activity, borderColorResId))
            } else {
                gradientDrawable?.setStroke(
                    borderWidth,
                    ContextCompat.getColor(activity, co.ujet.android.ui.R.color.ujet_chat_post_session_border_color)
                )
            }
            chatMessageRecyclerView?.apply {
                updateLayoutParams {
                    updatePadding(top = dpToPx(context, 10).toInt())
                }
                background = gradientDrawable
            }
        }
    }

    private fun showPostSessionChatUIAnimation() {
        val swipeRefreshLayoutHeight = swipeRefreshLayout?.height?.toFloat() ?: 0f
        swipeRefreshLayout?.visibility = VISIBLE
        val animation = TranslateAnimation(
            0f, 0f, // fromXDelta, toXDelta
            -swipeRefreshLayoutHeight, 0f // fromYDelta, toYDelta
        )
        animation.duration = POST_SESSION_ANIMATION_DURATION // Animation duration in milliseconds
        swipeRefreshLayout?.startAnimation(animation)
    }

    override fun setAfterHoursView(isVisible: Boolean, message: String?) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (isVisible) {
            if (chatAfterHoursView == null) {
                chatAfterHoursView = ChatAfterHoursView(activity, ujetStyle()).apply {
                    setMessage(message)
                    setRestartChatButtonClickListener {
                        presenter?.onChatAfterHoursRestartClicked()
                    }
                }
                chatMessageAdapter?.addFooterView(chatAfterHoursView)
            }
        } else {
            chatMessageAdapter?.removeFooterView(chatAfterHoursView ?: return)
            chatAfterHoursView = null
        }
    }

    private fun registerActivityForResult() {
        parentFragmentManager.setFragmentResultListener(TAG, this) { requestKey, result ->
            if (TAG == requestKey) {
                when (result.getInt(REQUEST_CODE)) {
                    CHAT_NOT_AVAILABLE_ERROR_REQUEST -> presenter?.onChatNotAvailableConfirmed()
                    CHANNELS_NOT_AVAILABLE_REQUEST -> presenter?.onChannelsNotAvailableConfirmed()
                    REQUEST_CODE_WARNING_DIALOG_RESPONSE -> {
                        val resultCode = result.getInt(RESULT_CODE)
                        if (resultCode == Activity.RESULT_OK &&
                            result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false)
                        ) {
                            //All other cases like EXTRAS_SECOND_BUTTON_CLICKED and Activity.RESULT_CANCELED
                            // are already handled by onResume() so we can skip
                            presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
                        }
                    }

                    WEB_FORM_CLOSE_REQUEST -> {
                        val resultCode = result.getInt(RESULT_CODE)
                        if (resultCode == Activity.RESULT_OK) {
                            if (result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false)) {
                                isWebFormClosed = true
                                webFormChatMessage?.let { chatService?.setWebFormStatus(it, "", context) }
                                closeWebFormBottomSheet()
                            }
                        }
                    }

                    WEB_FORM_START_NEW_FORM_REQUEST -> {
                        val resultCode = result.getInt(RESULT_CODE)
                        if (resultCode == Activity.RESULT_OK) {
                            if (result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false)) {
                                closeWebFormBottomSheet()
                                this.webFormChatMessage = startNewWebFormChatMessage
                                webFormChatMessage?.let { onWebFormClicked(it, newFormAdapterPosition) }
                            }
                        }
                    }

                    POST_SESSION_ERROR_REQUEST -> {
                        val resultCode = result.getInt(RESULT_CODE)
                        if (resultCode == Activity.RESULT_OK) {
                            if (result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false)) {
                                chatService?.updateChatStatusToFinished()
                                chatService?.removeDuplicateChatEndedMessage(getString(R.string.ujet_chat_notification_ended))
                            }
                        }
                    }
                }
            }
        }
    }

    private fun registerDocumentLauncher() {
        // We use create document activity launcher intent to let user select the destination where file
        // need to be saved and it is used for document file types and also for media types (for
        // Android 9 and below versions only). To handle all the cases, we need separate activity launcher
        // intents based on file type and need to create launcher intent with respective mime type.
        // But launcher intents can only be created on or before onCreate() method is called, we can
        // get mime / file type only after agent sends the attachments so we will be creating launcher
        // intents with all mime type supported in onCreate and will be using respective intent as needed.
        // Another alternative would be to create launcher intent with generic mime type, but this option
        // will show generic icons in the UI when intent was launched irrespective of file type and also
        // saved files shows generic icon as thumbnail which can creates confusion to the user so ignored it.

        // For saving image attachments
        launcherIntentMap[MIME_TYPE_IMAGE_JPEG] = getActivityResultLauncher(MIME_TYPE_IMAGE_JPEG)
        launcherIntentMap[MIME_TYPE_IMAGE_PNG] = getActivityResultLauncher(MIME_TYPE_IMAGE_PNG)
        launcherIntentMap[MIME_TYPE_IMAGE_GIF] = getActivityResultLauncher(MIME_TYPE_IMAGE_GIF)
        launcherIntentMap[MIME_TYPE_IMAGE_WEBP] = getActivityResultLauncher(MIME_TYPE_IMAGE_WEBP)
        // For saving video attachments
        launcherIntentMap[MIME_TYPE_VIDEO_MP4] = getActivityResultLauncher(MIME_TYPE_VIDEO_MP4)
        launcherIntentMap[MIME_TYPE_VIDEO_WEBM] = getActivityResultLauncher(MIME_TYPE_VIDEO_WEBM)
        launcherIntentMap[MIME_TYPE_VIDEO_WMV] = getActivityResultLauncher(MIME_TYPE_VIDEO_WMV)
        launcherIntentMap[MIME_TYPE_VIDEO_MOV] = getActivityResultLauncher(MIME_TYPE_VIDEO_MOV)
        launcherIntentMap[MIME_TYPE_VIDEO_AVI] = getActivityResultLauncher(MIME_TYPE_VIDEO_AVI)
        // For saving audio attachments
        launcherIntentMap[MIME_TYPE_AUDIO_MP3] = getActivityResultLauncher(MIME_TYPE_AUDIO_MP3)
        launcherIntentMap[MIME_TYPE_AUDIO_WEBM] = getActivityResultLauncher(MIME_TYPE_AUDIO_WEBM)
        launcherIntentMap[MIME_TYPE_AUDIO_WAV] = getActivityResultLauncher(MIME_TYPE_AUDIO_WAV)
        launcherIntentMap[MIME_TYPE_AUDIO_X_WAV] = getActivityResultLauncher(MIME_TYPE_AUDIO_X_WAV)
        launcherIntentMap[MIME_TYPE_AUDIO_M4A] = getActivityResultLauncher(MIME_TYPE_AUDIO_M4A)
        // For saving document attachments
        launcherIntentMap[MIME_TYPE_DOCUMENT_PDF] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_PDF)
        launcherIntentMap[MIME_TYPE_DOCUMENT_DOC] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_DOC)
        launcherIntentMap[MIME_TYPE_DOCUMENT_XLS] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_XLS)
        launcherIntentMap[MIME_TYPE_DOCUMENT_PPT] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_PPT)
        launcherIntentMap[MIME_TYPE_DOCUMENT_CSV] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_CSV)
        launcherIntentMap[MIME_TYPE_DOCUMENT_TXT] = getActivityResultLauncher(MIME_TYPE_DOCUMENT_TXT)
    }

    private fun getActivityResultLauncher(mimeType: String): ActivityResultLauncher<String> {
        return registerForActivityResult(
            ActivityResultContracts.CreateDocument(mimeType)) { uri ->
            uri?.let {
                // get media type from mime type
                val mediaType = getMediaTypeFromMimeType(mimeType)
                this.mediaType = mediaType
                val mediaFilesCount = mediaFiles?.size ?: 0
                // We can't relay on fetching file name from uri and using it to retrieve respective
                // media file as end user can rename them to completely different file name. So, we
                // can directly use cached mediaFiles to retrieve media file using index. For document
                // file type, we can use index 0 to get media file as there can only be one media file.
                // But, for media file types under Android 9 and below case, we can use downloadedMediaCount as index.
                val mediaFile = if (downloadedMediaCount < mediaFilesCount) {
                    mediaFiles?.get(downloadedMediaCount)
                } else {
                    null
                }
                if (isCreateDocumentIntentLaunchedForMedia) {
                    val currentDownloadStatus = saveContentInSelectedDestination(uri, mediaFile)
                    updateDownloadSucceededValue(mediaFilesCount, currentDownloadStatus)
                    downloadedMediaCount++
                    if (downloadedMediaCount < mediaFilesCount) {
                        val currentIndex = mediaFiles?.indexOf(mediaFile) ?: -1
                        val nextMediaFile = mediaFiles?.get(currentIndex + 1)
                        if (nextMediaFile != null) {
                            launchIntentForNextMedia(nextMediaFile)
                        }
                    } else {
                        // All media downloaded so reset following values
                        isCreateDocumentIntentLaunchedForMedia = false
                        downloadedMediaCount = 0
                        updateDownloadAttachmentView()
                    }
                } else {
                    val currentDownloadStatus = saveContentInSelectedDestination(uri, mediaFile)
                    updateDownloadSucceededValue(mediaFiles?.size, currentDownloadStatus)
                    updateDownloadAttachmentView()
                }
            } ?: run {
                // User did not select file destination to save
                isDownloadSucceeded = false
                updateDownloadAttachmentView()
            }
        }
    }

    private fun getMediaTypeFromMimeType(mimeType: String): String {
        if (mimeType.contains("/")) {
            // Split the MIME type at '/'
            val parts = mimeType.split("/".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            // The media type is the part before the '/'
            if (parts.isNotEmpty()) {
                return when (val mediaType = parts[0]) {
                    // Convert image and application types to Photo and Document to be used accordingly
                    // in updateDownloadAttachmentView()
                    MEDIA_TYPE_IMAGE -> {
                        MEDIA_TYPE_PHOTO
                    }
                    MEDIA_TYPE_APPLICATION, MEDIA_TYPE_DOCUMENT_TEXT-> {
                        MEDIA_TYPE_DOCUMENT
                    }
                    else -> {
                        mediaType
                    }
                }
            }
        }
        // return default as document type
        return MEDIA_TYPE_DOCUMENT
    }

    private fun launchIntentForNextMedia(mediaFile: MediaFile) {
        val fileName = mediaFile.filename ?: ""
        val extension = fileName.substringAfterLast('.', "")
        val fileMimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
            extension.lowercase(Locale.getDefault()))
        launchCreateDocumentIntent(fileMimeType, fileName)
    }

    override fun setDismissViewVisible(isVisible: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (isVisible) {
            updateEndChatMenuText(R.string.ujet_chat_exit)
            if (chatDismissView == null) {
                chatDismissView = ChatDismissView(activity, ujetStyle()).apply {
                    setResumeChatButtonClickListener {
                        presenter?.onChatResumeClicked()
                    }
                    setNewChatButtonClickListener {
                        presenter?.onChatNewClicked()
                    }
                    chatMessageAdapter?.addFooterView(this)
                }
            }
        } else {
            chatMessageAdapter?.removeFooterView(chatDismissView)
            chatDismissView = null
        }
    }

    override fun showChatNotAvailable(otherChannelsAvailable: Boolean) {
        if (otherChannelsAvailable) {
            showChatNotAvailableError(getString(R.string.ujet_message_chat_deflection_overcapacity))
        } else {
            showChannelsNotAvailableError(getString(R.string.ujet_message_chat_deflection_overcapacity))
        }
    }

    override fun showErrorMessage() {
        showErrorMessage(getString(R.string.ujet_error_chat_connect_fail_android))
    }

    override fun showOvercapacityMessage() {
        showChatNotAvailableError(getString(R.string.ujet_message_chat_deflection_overcapacity))
    }

    override fun setEndChatEnabled(isEnabled: Boolean) {
        if (!isActive) {
            return
        }
        chatOptionsMenuView?.updateEndChatButtonStatus(isEnabled)
    }

    override fun setSendPhotoEnabled(isEnabled: Boolean) {
        if (!isActive) {
            return
        }
        chatActionsMenuView?.setSendPhotoEnabled(isEnabled)
    }

    override fun scrollToBottom() {
        chatMessageRecyclerView?.post {
            // Skip scrolling to bottom position while loading chat history messages, chat UI paused
            // and when returning from media preview or download documents screen.
            if (!isChatUIPaused && !loadingChatHistory && chatService?.isMediaPreviewOrDownloadScreenVisible == false) {
                chatMessageRecyclerView?.smoothScrollToPosition(chatMessageRecyclerView?.adapter?.itemCount ?: return@post)
            }
            // Scroll to the last visible message position when returning from preview or download screen
            // for human agent message attachments messages.
            if (chatService?.isMediaPreviewOrDownloadScreenVisible == true &&
                lastVisibleItemPosition != RecyclerView.NO_POSITION) {
                chatMessageRecyclerView?.smoothScrollToPosition(lastVisibleItemPosition)
                lastVisibleItemPosition = RecyclerView.NO_POSITION
            }
            chatService?.isMediaPreviewOrDownloadScreenVisible = false // reset
        }
    }

    private fun showChatNotAvailableError(message: String) {
        if (!isActive || isStateSaved) {
            return
        }
        AlertDialogFragment
            .newErrorDialog(message, TAG, CHAT_NOT_AVAILABLE_ERROR_REQUEST)
            .show(parentFragmentManager, AlertDialogFragment.TAG)
    }

    private fun showWebFormCloseAlert() {
        if (!isActive || isStateSaved) {
            return
        }
        val message = getString(R.string.ujet_chat_form_close_form_dialog_message)
        val title = getString(R.string.ujet_chat_form_close_form_dialog_title).uppercase()
        val confirmMessage = getString(R.string.ujet_chat_form_close_dialog_close_btn)
        ConfirmationDialogFragment
            .newInstance(TAG, WEB_FORM_CLOSE_REQUEST, title, message, confirmMessage, getString(R.string.ujet_common_cancel))
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    private fun showStartNewWebFormAlert() {
        if (!isActive || isStateSaved) {
            return
        }
        val message = getString(R.string.ujet_chat_form_start_new_form_dialog_message)
        val title = getString(R.string.ujet_chat_form_start_new_form_dialog_title).uppercase()
        val confirmMessage = getString(R.string.ujet_chat_form_start_new_form_dialog_title)
        ConfirmationDialogFragment
            .newInstance(TAG, WEB_FORM_START_NEW_FORM_REQUEST, title, message, confirmMessage, getString(R.string.ujet_common_cancel))
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    private fun showWebFormSubmitFailedAlert() {
        if (!isActive || isStateSaved) {
            return
        }
        val message = getString(R.string.ujet_chat_form_submission_failed_dialog_message)
        val title = getString(R.string.ujet_chat_form_submission_failed_dialog_title).uppercase()
        val confirmMessage = getString(R.string.ujet_chat_form_dismiss)
        ConfirmationDialogFragment
            .newInstance(title, message, confirmMessage, true, true)
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    private fun showChannelsNotAvailableError(message: String) {
        if (!isActive || isStateSaved) {
            return
        }
        AlertDialogFragment
            .newErrorDialog(message, TAG, CHANNELS_NOT_AVAILABLE_REQUEST)
            .show(parentFragmentManager, AlertDialogFragment.TAG)
    }

    override fun showErrorMessage(message: String) {
        if (!isActive) {
            return
        }
        Toast.makeText(activity ?: return, message, Toast.LENGTH_LONG).show()
    }

    override fun showVideo(filename: String) {
        if (!isActive) {
            return
        }
        chatService?.isMediaPreviewOrDownloadScreenVisible = true
        val intent = Intent(activity ?: return, UjetMediaPreviewActivity::class.java)
        intent.putExtra("video", filename)
        startActivity(intent)
    }

    override fun showImage(filename: String) {
        if (!isActive) {
            return
        }
        chatService?.isMediaPreviewOrDownloadScreenVisible = true
        val intent = Intent(activity ?: return, UjetMediaPreviewActivity::class.java)
        intent.putExtra("photo", filename)
        startActivity(intent)
    }

    override fun setChatMessageDataSource(dataSource: ChatMessageDataSource) {
        val visible = dataSource.getCount() <= 0
        setEmptyViewProgressBarVisibility(visible)
        chatMessageAdapter?.setDataSource(dataSource)
    }

    override fun clearChatMessageDataSource() {
        chatMessageAdapter?.clear()
    }

    override fun showCsatRating(isNewChatEnabled: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        activity.finish()
        if (isNewChatEnabled) {
            UjetCsatActivity.start(activity, true)
        } else {
            UjetCsatActivity.start(activity)
        }
    }

    override fun showSurveyScreen(isNewChatEnabled: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }

        activity.finish()
        if (isNewChatEnabled) {
            UjetSurveyActivity.start(activity, isNewChatEnabled)
        } else {
            UjetSurveyActivity.start(activity)
        }
    }

    override fun showMenus() {
        if (!isActive) {
            return
        }
        UjetActivity.startNewComm(activity ?: return, true)
    }

    override fun displayChatConnected(agentName: String) {
        if (!isActive) {
            return
        }
        title = getString(R.string.ujet_chat_title_connected, agentName)
        if (accessibilityManager?.isEnabled == true) {
            // Adding delay for title updating so that talkback gets enough time to announce the title
            handler.postDelayed({
                setActionBarTitle(title)
            }, TITLE_ANNOUNCEMENT_DELAY_DURATION)
        } else {
            setActionBarTitle(title)
        }
    }

    override fun onResendClicked(chatMessage: SendableChatMessage) {
        presenter?.onChatMessageResend(chatMessage)
    }

    override fun onQuickReplyClicked(chatMessage: VirtualAgentQuickReplyChatMessage, quickReplyButton: QuickReplyButton) {
        presenter?.onQuickReplyClicked(chatMessage, quickReplyButton)
    }

    override fun onChatItemClicked(chatMessage: ChatMessage?, position: Int) {
        lastVisibleItemPosition = (chatMessageRecyclerView?.layoutManager as? LinearLayoutManager)
            ?.findLastVisibleItemPosition() ?: RecyclerView.NO_POSITION
        presenter?.onChatMessageClicked(chatMessage ?: return, position)
    }

    override fun onEndChatClicked() {
        presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
    }

    override fun onNewChatButtonClicked() {
        presenter?.onNewChatButtonClicked()
    }

    override fun onContentCardClicked(contentCard: ContentCard) {
        presenter?.onContentCardClicked(contentCard.eventParams, contentCard.title)
    }

    override fun onWebFormClicked(webFormChatMessage: WebFormChatMessage, adapterPosition: Int) {
        if (isWebFormInProgress) {
            if (currentFormAdapterPosition == adapterPosition) {
                // if post-session opt-in banner visible then the bottomSheet would be hidden so we have to make it visible
                if (formBottomSheet?.visibility == GONE) {
                    formBottomSheet?.visibility = VISIBLE
                }
                formBottomSheetBehavior?.state = STATE_EXPANDED
            } else {
                startNewWebFormChatMessage = webFormChatMessage
                newFormAdapterPosition = adapterPosition
                showStartNewWebFormAlert()
            }
        } else {
            presenter?.onWebFormClicked(webFormChatMessage.webForm)
            isWebFormInProgress = true
            currentFormAdapterPosition = adapterPosition
            this.webFormChatMessage = webFormChatMessage
            chatService?.setWebFormStatus(webFormChatMessage, getString(R.string.ujet_chat_form_status_in_progress), context)
        }
    }

    override fun onContentCardButtonClicked(button: ContentCardButton, contentCard: ContentCard, adapterPosition: Int) {
        contentCard.title?.let { title ->
            presenter?.onContentCardButtonClicked(button.eventParams, contentCard.title, button.title)
            if (button.autoReply == true && !contentCardButtonClickedMap.contains(adapterPosition)) {
                presenter?.onChatMessageSend(title)
                contentCardButtonClickedMap[adapterPosition] = true
            }
        }
    }

    override fun onDownloadTranscriptInlineButtonClicked(position: Int) {
        chatEndedMessageViewHolderAdapterPosition = position
        chatEndedMessageViewHolder =
            chatMessageRecyclerView?.findViewHolderForAdapterPosition(position) as? ChatEndedMessageViewHolder
        downloadChatTranscript(true)
    }

    override fun onDownloadIconClicked(
        mediaFiles: ArrayList<MediaFile>?, adapterPosition: Int, mediaType: String,
    ) {
        if (mediaFiles?.isEmpty() == true) {
            return
        }
        this.mediaFiles = mediaFiles
        this.mediaType = mediaType
        this.attachmentsMessageAdapterPosition = adapterPosition
        mediaFiles?.forEach { mediaFile ->
            val fileType = mediaFile.type
            val fileName = mediaFile.filename ?: ""
            val extension = fileName.substringAfterLast('.', "")
            val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                extension.lowercase(Locale.getDefault()))
            when (fileType) {
                Type.Photo, Type.Video -> {
                    when {
                        // For Android 10+ versions, we will be using media store API (scoped storage) to
                        // save media (images, videos) which does not require any permissions. For Android 9 and below
                        // versions, we need write permission to save media but to avoid including any new permissions,
                        // using create document launcher to allow user to select the destination where media need to be
                        // stored, just like how document files were saved.
                        Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                            val currentDownloadStatus = saveMediaContent(mediaFile)
                            updateDownloadSucceededValue(mediaFiles.size, currentDownloadStatus)
                        }
                        !isCreateDocumentIntentLaunchedForMedia -> {
                            launchCreateDocumentIntent(mimeType, fileName)
                            isCreateDocumentIntentLaunchedForMedia = true
                        }
                    }
                }
                else -> {
                    chatService?.isMediaPreviewOrDownloadScreenVisible = true
                    lastVisibleItemPosition = (chatMessageRecyclerView?.layoutManager as? LinearLayoutManager)
                        ?.findLastVisibleItemPosition() ?: RecyclerView.NO_POSITION
                    launchCreateDocumentIntent(mimeType, fileName)
                }
            }
        }
        // we need to call updateDownloadAttachmentView only for images, videos.
        if (mediaType == MEDIA_TYPE_PHOTO || mediaType == MEDIA_TYPE_VIDEO) {
            updateDownloadAttachmentView()
        }
    }

    private fun updateDownloadSucceededValue(mediaFileSize: Int?, currentDownloadStatus: Boolean) {
        val isMultiMediaAvailable = (mediaFileSize ?: 0) > 1
        isDownloadSucceeded = if (isMultiMediaAvailable) {
            isDownloadSucceeded && currentDownloadStatus
        } else {
            currentDownloadStatus
        }
    }

    private fun updateDownloadAttachmentView() {
        if (!isServiceBound) {
            // Chat service is not connected yet and recycler view is null so wait until it is done
            // and call again to get view holder, otherwise it will return null
            resumeUpdateAttachmentView = true
            return
        }

        lifecycleScope.launch {
            val viewHolder: RecyclerView.ViewHolder? =
                chatMessageRecyclerView?.findViewHolderForAdapterPosition(attachmentsMessageAdapterPosition) ?: run {
                    // Giving delay so that the item view gets enough time to get attached to the recycler view
                    delay(300)
                    chatMessageRecyclerView?.findViewHolderForAdapterPosition(attachmentsMessageAdapterPosition)
                }

            when (mediaType) {
                MEDIA_TYPE_PHOTO -> {
                    (viewHolder as? HumanAgentPhotosChatMessageViewHolder)?.updateDownloadAttachmentView(isDownloadSucceeded)
                }

                MEDIA_TYPE_VIDEO -> {
                    (viewHolder as? HumanAgentVideosChatMessageViewHolder)?.updateDownloadAttachmentView(isDownloadSucceeded)
                }

                MEDIA_TYPE_DOCUMENT, MEDIA_TYPE_AUDIO -> {
                    (viewHolder as? HumanAgentDocumentChatMessageViewHolder)?.updateDownloadAttachmentView(isDownloadSucceeded)
                }
            }
        }
    }

    private fun saveMediaContent(mediaFile: MediaFile): Boolean {
        val context = context ?: return false
        val fileName = mediaFile.filename ?: ""
        val cacheFile = mediaFile.cacheFilePath?.let { File(it) }
        val fileType = mediaFile.type
        val extension = cacheFile?.extension
        val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
            extension?.lowercase(Locale.getDefault()))
        val appName = getApplicationName(context)

        if (cacheFile?.exists() == false) {
            return false
        }

        val (relativePath, contentUri) = when (fileType) {
            Type.Photo -> {
                Pair("Pictures", MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            }
            Type.Video -> {
                Pair("Movies", MediaStore.Video.Media.EXTERNAL_CONTENT_URI)
            }
            else -> {
                return false
            }
        }
        try {
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
                put(
                    MediaStore.MediaColumns.RELATIVE_PATH,
                    "$relativePath/$appName"
                )
            }

            val contentResolver = context.contentResolver
            val uri: Uri? = contentResolver?.insert(contentUri, contentValues)
            return copyUriToDestination(uri, cacheFile)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    // Launch the file creation intent based on mime type to save the file
    private fun launchCreateDocumentIntent(mimeType: String?, fileName: String) {
        val intent = launcherIntentMap[mimeType]
        if (intent != null) {
            chatService?.isMediaPreviewOrDownloadScreenVisible = true
            intent.launch(fileName)
        } else {
            Toast.makeText(activity, "Failed to launch intent to download the file", Toast.LENGTH_LONG).show()
            isDownloadSucceeded = false
            updateDownloadAttachmentView()
        }
    }

    private fun saveContentInSelectedDestination(uri: Uri, mediaFile: MediaFile?): Boolean {
        val cacheFile = mediaFile?.cacheFilePath?.let { File(it) }
        try {
            return copyUriToDestination(uri, cacheFile)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    private fun copyUriToDestination(uri: Uri?, cacheFile: File?): Boolean {
        val context = context ?: return false
        val contentResolver = context.contentResolver
        try {
            uri?.let {
                val inputStream = cacheFile?.let { FileInputStream(it) }
                val outputStream = contentResolver.openOutputStream(uri)

                inputStream?.use { input ->
                    outputStream?.use { output ->
                        input.copyTo(output)
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        // delete file as file contents were failed to save in selected destination
        uri?.let {
            DocumentsContract.deleteDocument(contentResolver, uri)
        }
        return false
    }

    override fun saveStatusText(statusText: String) {
        sharedPreferences?.edit()?.putString(STATUS_TEXT_KEY, statusText)?.apply()
    }

    override fun clearStatusText() {
        sharedPreferences?.edit()?.remove(STATUS_TEXT_KEY)?.apply()
    }

    override fun getStatusText() = sharedPreferences?.getString(STATUS_TEXT_KEY, null)

    override fun setConversationConnected(isConnected: Boolean) {
        chatMessageAdapter?.setConversationConnected(isConnected)
    }

    private fun displaySavedChatInput() {
        val message = presenter?.getChatInput() ?: ""
        chatInputBarLayout?.displaySavedChatInput(message)
    }

    private fun bindChatService(): Boolean {
        val activity = activity ?: return false
        if (!isActive) {
            return false
        }
        if (!ServiceUtil.isServiceRunning(activity, UjetChatService::class.java)) {
            return false
        }
        val intent = Intent(activity, UjetChatService::class.java)
        activity.bindService(intent, chatServiceConnection, 0)
        isServiceBound = true
        Logger.d("Bind UjetChatService to %s", javaClass.simpleName)
        return true
    }

    private fun unbindChatService() {
        unbindChatServiceSilently()
        Logger.d("Unbind UjetChatService to %s", javaClass.simpleName)
    }

    @Suppress("UNCHECKED_CAST")
    private val chatServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, binder: IBinder) {
            isServiceBound = true
            chatService = (binder as? UjetBinder<UjetChatService?>)?.service
            chatService?.registerChatListener(chatListener ?: return)
            Logger.d("%s is connected with UjetChatService", <EMAIL>)
            if (deflectToVirtualAgentWhenServiceConnected != ESCALATION_ID_NONE) {
                deflectToVirtualAgent(deflectToVirtualAgentWhenServiceConnected)
                deflectToVirtualAgentWhenServiceConnected = ESCALATION_ID_NONE
            }
            displaySavedChatInput()
            if (!isWebFormOrientationChanged) {
                chatService?.updateWebFormStatusForChatResume()
            }
            if (menuIdToResumeChat > 0) {
                chatService?.resumeChatWithMenuId(menuIdToResumeChat)
                menuIdToResumeChat = -1 // reset
            }
        }

        override fun onServiceDisconnected(name: ComponentName) {
            Logger.d("%s is disconnected with UjetChatService", <EMAIL>)
            unbindChatServiceSilently()
            presenter?.onChatServiceUnavailable()
        }
    }

    private fun unbindChatServiceSilently() {
        if (isServiceBound) {
            try {
                activity?.unbindService(chatServiceConnection)
            } catch (e: RuntimeException) {
                Logger.w(e, "Failed to unbind UjetChatService")
            }
            chatService?.unregisterChatListener(chatListener ?: return)
            chatService = null
            isServiceBound = false
        }
    }

    override fun notifyEndUserIsTyping() {
        chatService?.onUserTypingStarted()
    }

    override fun notifyEndUserStoppedTyping() {
        chatService?.onUserTypingStopped()
    }

    override fun sendEndUserMessage(message: String): Boolean {
        chatService?.sendEndUserMessage(message) ?: return false
        return true
    }

    override fun resendMessage(chatMessage: SendableChatMessage): Boolean {
        chatService?.resendMessage(chatMessage) ?: return false
        return true
    }

    override fun resumeChat(): Boolean {
        chatService?.resumeChat() ?: return false
        return true
    }

    override fun endChat(): Boolean {
        chatService?.endChat(false) ?: return false
        return true
    }

    override fun notifyEventData(endedBy: String) {
        chatService?.notifyEventData(endedBy)
    }

    override fun notifyMessagesUpdated() {
        chatMessageAdapter?.onMessagesChanged()
        updateQuickReplyButtons()
    }

    private fun updateQuickReplyButtons() {
        val lastMessage = chatMessageAdapter?.getItem((chatMessageAdapter?.getDataSourceCount() ?: 0) - 1)
        val postSessionQuickReplyListContainer = view?.findViewById<LinearLayout>(R.id.post_session_quick_reply_list_view)
        val postSessionQuickReplyButtons = view?.findViewById<HorizontalScrollView>(R.id.post_session_footer_quick_reply_buttons)
        if (lastMessage is VirtualAgentQuickReplyButtonsChatMessage && lastMessage.quickReplyButtonsVisible && !isPostSessionChatInProgress) {
            swipeRefreshLayout?.updatePadding(bottom = 0)
        }
        chatInputBarLayout?.updateQuickReplyButtons(
            lastMessage,
            postSessionQuickReplyListContainer,
            postSessionQuickReplyButtons,
            chatMessageRecyclerView,
            isPostSessionChatInProgress
        )
    }

    override fun setEscalateEnabled(enabled: Boolean) {
        chatInputBarLayout?.setEscalateEnabled(enabled)
        isSkippingVirtualAgentAllowed = enabled
    }

    override fun showEmailForm(deflectionType: String?, escalationId: Int) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        Injection.provideDeflectedEventManager(activity).sendDeflectedEvent("deflected", deflectionType, "email", null)
        FragmentHelper.show(this, newInstance(null, deflectionType, false), EmailFragment.TAG)
        // When the user returns from EmailFragment then end the chat just like the user would have
        // clicked on the end chat confirmation button
        parentFragmentManager.addOnBackStackChangedListener {
            if (parentFragmentManager.backStackEntryCount == 0) {
                if (presenter?.isEmailSent() == true) {
                    presenter?.onDoneClicked(CHAT_ENDED_BY_END_USER)
                } else {
                    // User decided to not send the email and instead come back to continue the
                    // chat
                    presenter?.deflectToVirtualAgent(escalationId)
                }
            }
        }
    }

    override fun showEmailClient(supportEmail: String?, menuPath: String?, deflectionType: String?) {
        val activity = activity ?: return
        Injection.provideDeflectedEventManager(activity).sendDeflectedEvent("deflected", deflectionType, "email", null)
        val emailClient = EmailClient()
        when {
            supportEmail.isNullOrEmpty() -> finish()
            emailClient.isAvailable(activity) -> {
                emailClient.start(activity, supportEmail, menuPath)
                finish()
            }

            else -> Toast.makeText(activity, R.string.ujet_error_no_email_client, Toast.LENGTH_LONG).show()
        }
    }

    override fun showErrorMessage(stringRes: Int) {
        showErrorMessage(getString(stringRes))
    }

    override fun escalateChat() {
        chatService?.escalateChat()
    }

    override fun deflectToVirtualAgent(escalationId: Int) {
        if (chatService == null) {
            deflectToVirtualAgentWhenServiceConnected = escalationId
            return
        }
        chatService?.deflectToVirtualAgent(escalationId)
    }

    override fun sendChatMessagePreview(message: String) {
        chatService?.sendChatMessagePreview(message)
    }

    override fun setChatInput(input: String) {
        chatService?.chatInput = input
    }

    override fun getChatInput() = chatService?.chatInput

    override fun requestChatHistory(initialRequest: Boolean) {
        chatService?.requestChatHistory(initialRequest)
    }

    override fun showExternalDeflectionLinks(url: String, deflectionType: String?) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        try {
            //Send external deflection link selected event for tracking
            Injection.provideDeflectedEventManager(activity).sendExternalDeflectionLinksEvent(url, deflectionType)

            startActivity(browserIntent)
        } catch (e: ActivityNotFoundException) {
            //When user selected external deflection link which has invalid url,
            //we can not open the url so we show error message.
            Toast.makeText(activity, R.string.ujet_invalid_url_message, Toast.LENGTH_LONG).show()
            Logger.i("Invalid url configured in external deflection link")
            return
        }

        isExternalDeflectionLinkClicked = true
        handler.postDelayed({
            isTimerFinished = true
        }, WARNING_DIALOG_TIMER)
    }

    override fun displayPendingSmartAction(biometricsVerification: BiometricsVerification) {
        SmartActionManager.displayPendingSmartAction(biometricsVerification, parentFragmentManager, true)
    }

    override fun updateCoBrowseButton(isCoBrowseSupportedAndConnected: Boolean) {
        SmartActionManager.updateCoBrowseButton(
            CommunicationType.Chat, chatActionsMenuView?.getCoBrowseButtonView(),
            chatActionsMenuView?.getCoBrowseButtonVisibility(isCoBrowseSupportedAndConnected) ?: false
        )
    }

    override fun getCoBrowseUI(): CoBrowseUI? {
        return Injection.provideCoBrowseUI(activity ?: return null, this)
    }

    override fun getCoBrowseButtonView() = chatActionsMenuView?.getCoBrowseButtonView()

    override fun getMainFragmentManager() = parentFragmentManager

    // Hide/show transfer banner
    override fun updateTransferBanner(visible: Boolean) {
        if (isActive.not()) {
            return
        }

        if (visible) {
            scrollToBottom()
            chatInputBarLayout?.showTransferBanner(getString(R.string.ujet_chat_transfer_banner_message))
        } else {
            chatInputBarLayout?.showTransferFinishedView(getString(R.string.ujet_common_done))
        }
    }

    override fun setEmptyViewProgressBarVisibility(visible: Boolean) {
        emptyViewProgressBar?.visibility = if (visible) {
            VISIBLE
        } else {
            GONE
        }
    }

    override fun showPreSessionSmartActions(menuId: Int) {
        PsaRouter.showPreSessionSmartActions(this, ChannelType.ChannelChat, menuId, null)
    }

    fun resumeCommunication(menuId: Int) {
        if (chatService == null) {
            // when we come back from PSA screen, if chat service is not connected to chat fragment yet
            // add a flag and resume the chat once it is connected.
            menuIdToResumeChat = menuId
            title = getString(R.string.ujet_chat_title).uppercase(Locale.ROOT)
            setActionBarTitle(title) // Reset chat toolbar title
        } else {
            chatService?.resumeChatWithMenuId(menuId)
        }
    }

    override fun openFilePicker(fileName: String?, cacheFile: File?) {
        this.cacheFile = cacheFile
        createFileLauncher.launch(fileName)
    }

    override fun saveChatTranscriptId(chatTranscriptId: Int) {
        this.chatTranscriptId = chatTranscriptId
    }

    override fun updateChatTranscriptDownloadButtonUI(isDownloadProgressBarVisible: Boolean,
                                                      isDownloadErrorIconVisible: Boolean,
                                                      isInlineButton: Boolean) {
        when {
            isInlineButton -> {
                chatEndedMessageViewHolder?.updateDownloadInlineButtonUI(
                    isDownloadProgressBarVisible,
                    isDownloadErrorIconVisible
                )
            }

            else -> {
                chatOptionsMenuView?.updateDownloadButtonUI(
                    isDownloadProgressBarVisible,
                    isDownloadErrorIconVisible
                )
            }
        }
    }

    companion object {
        const val TAG = "ChatFragment"
        private const val TAG_END_CHAT_CONFIRMATION_DIALOG = "EndChatConfirmationDialog"
        private const val EXTRA_ERROR_MESSAGE = "error_message"
        private const val EXTRA_ERROR_TYPE = "error_type"
        const val SHARED_PREFERENCE_KEY = "co.ujet.android.preferences.chat"
        private const val STATUS_TEXT_KEY = "chat_status_text"
        private const val CHAT_NOT_AVAILABLE_ERROR_REQUEST = 1
        private const val CHANNELS_NOT_AVAILABLE_REQUEST = 2
        private const val WEB_FORM_CLOSE_REQUEST = 3
        private const val WEB_FORM_START_NEW_FORM_REQUEST = 4
        private const val POST_SESSION_ERROR_REQUEST = 5
        private const val CHAT_TRANSCRIPT_MAX_DOWNLOAD_ERROR_REQUEST = 6
        private const val ESCALATION_ID_NONE = -1
        private const val REQUEST_CODE_WARNING_DIALOG_RESPONSE = 10009
        private val WARNING_DIALOG_TIMER = TimeUnit.MINUTES.toMillis(5) //5 minutes
        private const val TITLE_ANNOUNCEMENT_DELAY_DURATION = 300L
        private const val WAITING_FOR_AGENT_TIME_DURATION = 3000L
        private const val POST_SESSION_ANIMATION_DURATION = 300L
        private const val WEB_FORM_BOTTOM_SHEET_EXPANDED_OFFSET = 1.0f
        private const val WEB_FORM_BOTTOM_SHEET_COLLAPSED_OFFSET = 0.0f

        // Constants for onSaveInstanceState keys
        private const val STATE_BOTTOM_SHEET_STATE = "ujetBottomSheetState"
        private const val STATE_BOTTOM_SHEET_ERROR_MSG = "ujetBottomSheetErrorMsg"
        private const val STATE_WEB_FORM_IS_IN_PROGRESS = "ujetWebFormIsInProgress"
        private const val STATE_NEW_FORM_ADAPTER_POSITION = "ujetNewFormAdapterPosition"
        private const val STATE_CURRENT_FORM_ADAPTER_POSITION = "ujetCurrentFormAdapterPosition"
        private const val STATE_WEB_FORM_STATE = "ujetWebFormState"
        private const val STATE_WEB_FORM_URL = "ujetWebFormUrl"
        private const val STATE_HEADER_CONTAINER_HEIGHT = "ujetHeaderContainerHeight"
        private const val STATE_INPUT_CONTAINER_HEIGHT = "ujetInputContainerHeight"
        private const val STATE_WEB_FORM_CHAT_MESSAGE_LOCAL_ID = "ujetWebFormChatMessageLocalId"
        private const val STATE_WEB_FORM_CHAT_MESSAGE_SID = "ujetWebFormChatMessageSid"
        private const val STATE_WEB_FORM_CHAT_MESSAGE_WEB_FORM = "ujetWebFormChatMessageWebForm"
        private const val STATE_WEB_FORM_CHAT_MESSAGE_MEMBER_IDENTITY = "ujetWebFormChatMessageMemberIdentity"
        private const val STATE_WEB_FORM_CHAT_MESSAGE_MESSAGE_INDEX = "ujetWebFormChatMessageMessageIndex"
        private const val STATE_POST_SESSION_OPT_IN_BANNER_VISIBILITY = "ujetPostSessionOptInBannerVisibility"
        private const val STATE_POST_SESSION_OPT_IN_BANNER_LOADING_VISIBILITY = "ujetPostSessionOptInBannerLoadingVisibility"
        private const val STATE_POST_SESSION_CHAT_IN_PROGRESS = "ujetPostSessionChatInProgress"
        private const val STATE_CHAT_TRANSCRIPT_DOWNLOAD_IS_IN_PROGRESS = "ujetChatTranscriptDownloadIsInProgress"
        private const val STATE_CHAT_TRANSCRIPT_DOWNLOAD_INITIATED_FROM_INLINE_BUTTON = "ujetChatTranscriptDownloadInitiatedFromInlineButton"
        private const val STATE_CHAT_TRANSCRIPT_ID = "ujetChatTranscriptId"
        private const val STATE_CHAT_TRANSCRIPT_CHAT_ENDED_MESSAGE_VIEW_HOLDER_ADAPTER_POSITION =
            "ujetChatTranscriptChatEndedMessageViewHolderAdapterPosition"
        private const val CHAT_ENDED_BY_END_USER = "end_user"
        private const val STATE_MEDIA_FILES = "ujetChatMediaAttachments"
        private const val STATE_MEDIA_FILES_IS_INTENT_LAUNCHED = "ujetChatMediaAttachmentsIsIntentLaunched"
        private const val STATE_IS_DOWNLOAD_SUCCEEDED = "ujetChatMediaAttachmentsIsDownloadSucceeded"
        private const val STATE_MEDIA_TYPE = "ujetChatMediaAttachmentsMediaType"
        private const val STATE_MEDIA_FILES_DOWNLOAD_COUNT = "ujetChatMediaAttachmentsDownloadCount"
        private const val STATE_ATTACHMENTS_ADAPTER_POSITION = "ujetChatMediaAttachmentsAdapterPosition"
        private const val STATE_LAST_VISIBLE_ITEM_POSITION = "ujetChatLastVisibleItemPosition"

        // intent launcher for file attachments
        private const val MIME_TYPE_IMAGE_JPEG = "image/jpeg"
        private const val MIME_TYPE_IMAGE_PNG = "image/png"
        private const val MIME_TYPE_IMAGE_GIF = "image/gif"
        private const val MIME_TYPE_IMAGE_WEBP = "image/webp"
        private const val MIME_TYPE_VIDEO_MP4 = "video/mp4"
        private const val MIME_TYPE_VIDEO_WEBM = "video/webm"
        private const val MIME_TYPE_VIDEO_WMV = "video/wmv"
        private const val MIME_TYPE_VIDEO_MOV = "video/quicktime"
        private const val MIME_TYPE_VIDEO_AVI = "video/avi"
        private const val MIME_TYPE_AUDIO_MP3 = "audio/mpeg"
        private const val MIME_TYPE_AUDIO_WAV = "audio/wav"
        private const val MIME_TYPE_AUDIO_X_WAV = "audio/x-wav"
        private const val MIME_TYPE_AUDIO_M4A = "audio/m4a"
        private const val MIME_TYPE_AUDIO_WEBM = "audio/webm"
        private const val MIME_TYPE_DOCUMENT_PDF = "application/pdf"
        private const val MIME_TYPE_DOCUMENT_DOC = "application/msword"
        private const val MIME_TYPE_DOCUMENT_XLS = "application/vnd.ms-excel"
        private const val MIME_TYPE_DOCUMENT_PPT = "application/vnd.ms-powerpoint"
        private const val MIME_TYPE_DOCUMENT_CSV = "text/comma-separated-values"
        private const val MIME_TYPE_DOCUMENT_TXT = "text/plain"

        @JvmStatic
        fun newInstance(message: String?, isAfterHourMessage: Boolean): ChatFragment {
            val chatFragment = ChatFragment()
            val bundle = Bundle()
            bundle.putString(EXTRA_ERROR_MESSAGE, message)
            bundle.putBoolean(EXTRA_ERROR_TYPE, isAfterHourMessage)
            chatFragment.arguments = bundle
            return chatFragment
        }
    }
}
