package co.ujet.android.app.call.scheduled.timepicker

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.data.constant.CallCreateType
import java.util.*

/**
 * Listens to user actions from the UI ([ScheduleTimePickerFragment]), retrieves the data and updates the
 * UI as required.
 */
internal interface ScheduleTimePickerContract {
    interface View : BaseView {
        fun showLoading()
        fun showError()
        fun showPicker(items: Array<String?>, defaultIndex: Int)
        fun showPhoneNumberInput(callCreateType: CallCreateType, scheduledTime: Date?)
        fun enableButton()
        fun showMenuUpdated()
        fun restart()
        fun back()
        fun finish()
        fun showSurveyScreen()
        fun showCsatScreen()
    }

    interface Presenter : BasePresenter {
        fun updateItemAtIndex(index: Int)
        fun confirmSelect()
        fun onBackButtonClicked()
        fun onExitButtonClicked()
    }
}
