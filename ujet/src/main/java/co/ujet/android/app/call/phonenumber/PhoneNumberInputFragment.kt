package co.ujet.android.app.call.phonenumber

import android.app.Activity
import android.app.Service
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.*
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.core.text.HtmlCompat
import co.ujet.android.R
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.phonenumber.PhoneNumberInputContract.Presenter
import co.ujet.android.app.call.regionCode.RegionCodeFragment
import co.ujet.android.app.call.scheduled.confirm.ScheduleConfirmDialogFragment
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.confirmation.ConfirmationDialogFragment
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.error.AlertDialogFragment
import co.ujet.android.app.loadingstate.LoadingStateFragment
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.common.util.ApplicationUtil
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.saveRecordingPermissionStatus
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.AccessibilityUtil.isCountryCodeClicked
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.data.constant.CallCreateType.InAppIvrCall
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.internal.UjetInternal
import co.ujet.android.libs.logger.Logger
import co.ujet.android.ui.button.FancyButton
import java.util.*

/**
 * Phone Number Input Screen for Pro SDK
 */
class PhoneNumberInputFragment @Keep constructor() : BaseFragment(), PhoneNumberInputContract.View {
    private var presenter: Presenter? = null
    private var nextButton: FancyButton? = null
    private var skipButton: FancyButton? = null
    private var disclaimerTextView: TextView? = null
    private var countryCodeTextView: TextView? = null
    private var phoneNumberEditText: EditText? = null
    private var callCreateType: CallCreateType? = null
    private var isSkipButtonVisible = false
    private var deflectionType: String? = null
    private var countryCodeLayout: LinearLayout? = null

    private val onCountryCodeClickListener =
        View.OnClickListener { presenter?.onCountryCodeClicked() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val activity: Activity? = activity
        if (activity == null) {
            finish()
            return
        }
        if (arguments == null) {
            Logger.w("Call create type doesn't exists")
            finish()
            return
        }
        val callCreateTypeStr = arguments?.getString(ARGS_CALL_CREATE_TYPE) ?: return
        callCreateType = CallCreateType.valueOf(callCreateTypeStr)
        val ivrPhoneNumber = arguments?.getString(ARGS_IVR_PHONE_NUMBER)
        isSkipButtonVisible = arguments?.getBoolean(ARGS_SKIP_BUTTON_VISIBILITY) ?: false
        deflectionType = arguments?.getString(ARGS_DEFLECTION_TYPE)
        val scheduledTime = (arguments?.getSerializable(ARGS_SCHEDULED_TIME) as? Date)
        presenter = PhoneNumberInputPresenter(
            activity,
            ujetContext(),
            Injection.provideLocalRepository(activity),
            apiManager(),
            callCreateType ?: return,
            ivrPhoneNumber,
            deflectionType,
            scheduledTime,
            this,
            Injection.provideUseCaseHandler(),
            Injection.provideSavePhoneNumber(activity),
            Injection.provideGetPhoneNumber(activity),
            Injection.provideGetCompany(activity),
            Injection.provideGetSelectedMenuId(activity),
            Injection.provideGetSelectedMenu(activity),
            Injection.provideGetEndUser(activity),
            Injection.provideChooseLanguage(activity),
            Injection.provideInformInAppIvrCallMenuId(activity)
        )
        registerActivityForResult()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = if (callCreateType == InAppIvrCall) {
            createInAppIvrCallView(inflater, container)
        } else {
            createScheduledCallView(inflater, container)
        }

        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_exit -> {
                    presenter?.onExitButtonClicked()
                    true
                }
                android.R.id.home -> {
                    if (parentFragmentManager.findFragmentByTag(LoadingStateFragment.TAG) != null) {
                        finish()
                    } else {
                        presenter?.onBackButtonClicked()
                    }
                    true
                }
                else -> false
            }
        })
        return view
    }

    private fun handleKeyboardAccessibility() {
        view?.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            val menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                countryCodeLayout?.requestFocus()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(navigateUpView, onDpadDown = {
                countryCodeLayout?.requestFocus()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(countryCodeLayout, onTabOrDpadDown = {
                if (nextButton?.isEnabled == true) {
                    nextButton?.requestFocus()
                }
                true
            })
        }
    }

    private fun createInAppIvrCallView(inflater: LayoutInflater, container: ViewGroup?): View {
        val view =
            inflater.inflate(R.layout.ujet_fragment_phone_number_input_in_app_ivr, container, false)
        UjetViewStyler.styleFragmentBottomBackground(ujetStyle(), view)
        val titleTextView = view.findViewById<TextView>(R.id.title_view)
        UjetViewStyler.overrideTypeface(ujetStyle(), titleTextView)
        UjetViewStyler.stylePrimaryText(ujetStyle(), titleTextView)
        titleTextView.isAllCaps = true
        titleTextView.setText(R.string.ujet_ask_phone_number_title_lite)
        titleTextView.contentDescription = getString(R.string.ujet_ask_phone_number_title_lite)
        setupView(view)
        phoneNumberEditText?.typeface = ujetStyle().typeFace
        skipButton = view.findViewById<FancyButton>(R.id.skip_and_call_button)?.apply {
            UjetViewStyler.styleFlatButton(ujetStyle(), this)
            visibility = if (isSkipButtonVisible) {
                View.GONE
            } else {
                View.VISIBLE
            }
            setOnClickListener {
                nextButton?.isEnabled = false
                nextButton?.setIndicatorVisible(true)
                isEnabled = false
                presenter?.skip()
            }
        }
        val descriptionTextView = view.findViewById<TextView>(R.id.description)
        UjetViewStyler.overrideTypeface(ujetStyle(), descriptionTextView)
        UjetViewStyler.styleSecondaryText(ujetStyle(), descriptionTextView)
        return view
    }

    private fun createScheduledCallView(inflater: LayoutInflater, container: ViewGroup?): View {
        val view = inflater.inflate(R.layout.ujet_fragment_phone_number_input, container, false)
        UjetViewStyler.styleFragmentBottomBackground(ujetStyle(), view)
        val titleTextView = view.findViewById<TextView>(R.id.title_view)
        UjetViewStyler.overrideTypeface(ujetStyle(), titleTextView)
        UjetViewStyler.stylePrimaryText(ujetStyle(), titleTextView)
        titleTextView.text = getString(R.string.ujet_ask_phone_number_title)
        view.findViewById<View>(R.id.description).visibility = View.GONE
        disclaimerTextView = view.findViewById(R.id.disclaimer)
        enableRecordingMessage(false)
        setupView(view)
        return view
    }

    private fun setupView(view: View) {
        nextButton = view.findViewById<FancyButton>(R.id.next_button)?.apply {
            isEnabled = false
            setOnClickListener {
                isEnabled = false
                setIndicatorVisible(true)
                skipButton?.isEnabled = false
                presenter?.confirm()
            }
            UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
        }
        countryCodeTextView = view.findViewById<TextView>(R.id.country_code)?.apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.stylePrimaryText(ujetStyle(), this)
        }
        val countryCodeIcon = view.findViewById<ImageView>(R.id.country_code_icon)
        countryCodeIcon.setColorFilter(ujetStyle().colorPrimary)

        //Ujet-FX-6318: Taking a single parent layout(country_code_layout) for the region search elements(textview and icon)
        // so that talkback reads the region search as a whole view not separately
        countryCodeLayout = view.findViewById<LinearLayout>(R.id.country_code_layout).apply {
            setOnClickListener(onCountryCodeClickListener)
        }

        phoneNumberEditText = view.findViewById<EditText>(R.id.phone_number)?.apply {
            UjetViewStyler.stylePrimaryEditText(ujetStyle(), this)
        }
        // clicking on phone number input field using talkback, reads default phone number (set as hint)
        // and then says "edit box double tap to edit text", override content description so that it
        // announces "enter your phone number" instead of default phone number
        phoneNumberEditText?.let {
            AccessibilityUtil.overrideContentDescription(
                it,
                getString(R.string.ujet_ask_phone_number_title)
            )
        }

        val phoneNumberUnderBar = view.findViewById<View>(R.id.phone_number_under_bar)
        phoneNumberUnderBar.setBackgroundColor(ujetStyle().colorPrimary)
        /**
         * [UJET-8342] If clicked the boundary of EditText, start to input the phone number
         * to be more user friendly.
         */
        view.findViewById<View>(R.id.phone_number_boundary).setOnClickListener {
            phoneNumberEditText?.apply {
                requestFocus()
                setSelection(length())
                showSoftKeyboard()
            }
        }
        view.findViewById<LinearLayout>(R.id.phone_number_layout)
            .setBackgroundColor(ujetStyle().pickerBackgroundColor)
    }

    override fun onResume() {
        super.onResume()
        setActionBarTitle(getString(R.string.ujet_common_support).uppercase(Locale.getDefault()))
        presenter?.start()
        phoneNumberEditText?.addTextChangedListener(phoneNumberTextWatcher)
        //if `isCountryCodeClicked` is true then countryCode will be focused after returning to this fragment from RegionCodeFragment
        if (isCountryCodeClicked) {
            countryCodeLayout?.requestFocus()
            countryCodeLayout?.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED)
        }
        handleKeyboardAccessibility()
    }

    override fun onPause() {
        phoneNumberEditText?.removeTextChangedListener(phoneNumberTextWatcher)
        hideSoftKeyboard()
        super.onPause()
    }

    override fun onDestroyView() {
        nextButton = null
        skipButton = null
        countryCodeTextView = null
        phoneNumberEditText = null
        countryCodeLayout = null
        isCountryCodeClicked = false
        super.onDestroyView()
    }

    private val phoneNumberTextWatcher: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable) {
            presenter?.onPhoneNumberChanged(s)
            //[FX-6333] if user refocus phoneNumberEditText after giving input then talkback announces hint with the
            //contentDescription, to prevent it hint should be empty if there's any input else "(*************"
            //and also adding space between every digit (replace(".".toRegex(), "$0 ")) so that talkback reads as digit not number
            phoneNumberEditText?.hint = s.replace(".".toRegex(), "$0 ").ifEmpty { "(*************" }
        }
    }

    private fun showSoftKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(phoneNumberEditText ?: return, 0)
    }

    private fun hideSoftKeyboard() {
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(phoneNumberEditText?.windowToken ?: return, 0)
    }

    private fun registerActivityForResult() {
        parentFragmentManager.setFragmentResultListener(
            TAG, this
        ) { requestKey, result ->
            if (TAG == requestKey) {
                val requestCode = result.getInt(REQUEST_CODE)
                val resultCode = result.getInt(RESULT_CODE)

                when (requestCode) {
                    REQUEST_CODE_COUNTRY_CODE -> {
                        if (resultCode == Activity.RESULT_OK) {
                            val countryCode =
                                result.getString(RegionCodeFragment.EXTRAS_REGION_CODE)
                            presenter?.onRegionCodeChanged(countryCode)
                        }
                    }

                    REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE -> {
                        when (resultCode) {
                            Activity.RESULT_OK -> {
                                if (result.getBoolean(
                                        ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED,
                                        false
                                    )
                                ) {
                                    presenter?.onConfirmationDialogClicked(true)
                                } else if (result.getBoolean(
                                        ConfirmationDialogFragment.EXTRAS_SECOND_BUTTON_CLICKED,
                                        false
                                    )
                                ) {
                                    presenter?.onConfirmationDialogClicked(false)
                                }
                            }

                            Activity.RESULT_CANCELED -> {
                                //Stops loading indicator on coming back from confirmation dialog and enable button
                                nextButton?.setIndicatorVisible(false)
                                nextButton?.isEnabled = true
                            }
                        }
                    }
                }
            }
        }
    }

    override fun showCountyCodeSelect() {
        FragmentHelper.show(
            this,
            RegionCodeFragment.newInstance(TAG, REQUEST_CODE_COUNTRY_CODE),
            RegionCodeFragment.TAG
        )
    }

    override fun showScheduleConfirm(callId: Int, deflectionType: String?) {
        if (!isActive) {
            return
        }
        sendCallScheduledEvent(callId, deflectionType)
        ScheduleConfirmDialogFragment
            .newInstance()
            .show(parentFragmentManager, ScheduleConfirmDialogFragment.TAG)
    }

    private fun sendCallScheduledEvent(callId: Int, deflectionType: String?) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (!deflectionType.isNullOrEmpty()) {
            Injection.provideDeflectedEventManager(activity)
                .sendCallScheduledEvent("call_scheduled", callId, deflectionType)
        }
    }

    override fun showErrorDialog(
        errorTitle: String?,
        errorMessage: String,
        confirmMessage: String?,
        dismissCallback: (() -> Unit)?
    ) {
        if (!isActive) {
            return
        }
        nextButton?.isEnabled = true
        nextButton?.setIndicatorVisible(false)
        AlertDialogFragment
            .newErrorDialog(errorTitle, errorMessage, confirmMessage)
            .setDismissCallback(dismissCallback)
            .show(parentFragmentManager, AlertDialogFragment.TAG)
    }

    override fun setCountryCode(countryCode: String) {
        countryCodeTextView?.text = countryCode
        countryCodeTextView?.let { AccessibilityUtil.addButtonRole(it) }
    }

    override fun announceInvalidPhoneNumberMessage() {
        val accessibilityManager = activity?.getSystemService(Service.ACCESSIBILITY_SERVICE) as? AccessibilityManager
        // We show invalid phone number message when user entered phone number length exceeded the requirement
        // and if user keep entering after that, we keep announcing the same message and due to
        // this talkback will not announce currently entered digit into phone number field (Example, 9).
        // So we explicitly announce the entered text (currentEnteredDigit) along with invalid phone number error message.
        accessibilityManager?.let {
            AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                it,
                getString(R.string.ujet_ask_phone_number_invalid_title)
            )
        }
    }

    override fun setPhoneNumber(phoneNumber: String) {
        phoneNumberEditText?.setText(phoneNumber)
    }

    override fun setPhoneNumberHint(phoneNumberHint: String) {
        phoneNumberEditText?.hint = phoneNumberHint
    }

    override fun enableButton(isEnabled: Boolean) {
        nextButton?.isEnabled = isEnabled
    }

    override fun showMenuUpdated() {
        val activity = activity ?: return
        Toast.makeText(activity, R.string.ujet_menu_updated_alert, Toast.LENGTH_LONG).show()
    }

    override fun restart() {
        val activity = activity ?: return
        val intent = activity.intent
        activity.finish()
        if (activity is UjetActivity) {
            startActivity(intent)
        } else {
            UjetInternal.startUjetActivity()
        }
    }

    override fun finish() {
        activity?.finish()
    }

    override fun isActive() = isAdded

    override fun showRecordingConfirmation() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        val dialogMessage = HtmlCompat.fromHtml(
            getString(
                R.string.ujet_ask_to_record_description,
                ApplicationUtil.getApplicationName(activity.applicationContext)
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        ).toString()

        ConfirmationDialogFragment
            .newInstance(
                TAG,
                REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE,
                getString(R.string.ujet_channel_title),
                dialogMessage,
                getString(R.string.ujet_common_yes),
                getString(R.string.ujet_common_no)
            )
            .show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun saveRecordingPermissionStatus(recordingPermission: String) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        saveRecordingPermissionStatus(recordingPermission, activity.applicationContext)
    }

    override fun enableRecordingMessage(isEnabled: Boolean) {
        disclaimerTextView?.apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
            visibility = if (isEnabled) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    override fun back() {
        if (parentFragmentManager.backStackEntryCount > 1) {
            parentFragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    override fun showCsatScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity)
        UjetCsatActivity.start(activity)
    }

    override fun showSurveyScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }

        ActivityHelper.finishAndRemoveTask(activity)
        UjetSurveyActivity.start(activity)
    }

    fun onKeyDown(keyCode: Int) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            presenter?.onBackButtonClicked()
        }
    }

    companion object {
        const val TAG = "PhoneNumberInputFragment"
        const val REQUEST_CODE_COUNTRY_CODE = 9
        private const val REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE = 1005
        private const val ARGS_CALL_CREATE_TYPE = "call_create_type"
        private const val ARGS_SKIP_BUTTON_VISIBILITY = "skip_button_visibility"
        private const val ARGS_IVR_PHONE_NUMBER = "ivr_phone_number"
        private const val ARGS_DEFLECTION_TYPE = "phone_deflection_type"
        private const val ARGS_SCHEDULED_TIME = "scheduled_date"

        @JvmStatic
        fun newInstance(
            callCreateType: CallCreateType,
            deflectionType: String?,
            scheduledTime: Date?
        ): PhoneNumberInputFragment {
            val args = Bundle()
            args.putString(ARGS_CALL_CREATE_TYPE, callCreateType.name)
            args.putString(ARGS_DEFLECTION_TYPE, deflectionType)
            args.putSerializable(ARGS_SCHEDULED_TIME, scheduledTime)
            val newFragment = PhoneNumberInputFragment()
            newFragment.arguments = args
            return newFragment
        }

        @JvmStatic
        fun newInstance(
            callCreateType: CallCreateType,
            ivrPhoneNumber: String?,
            isSkipButtonVisible: Boolean,
            deflectionType: String?
        ): PhoneNumberInputFragment {
            val args = Bundle()
            args.putString(ARGS_CALL_CREATE_TYPE, callCreateType.name)
            args.putString(ARGS_IVR_PHONE_NUMBER, ivrPhoneNumber)
            args.putBoolean(ARGS_SKIP_BUTTON_VISIBILITY, isSkipButtonVisible)
            args.putString(ARGS_DEFLECTION_TYPE, deflectionType)
            val newFragment = PhoneNumberInputFragment()
            newFragment.arguments = args
            return newFragment
        }
    }
}
