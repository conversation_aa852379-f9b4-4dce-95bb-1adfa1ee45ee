package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.content.Context
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityManager
import androidx.core.text.HtmlCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.MarkdownUtil.loadMessageIntoMarkdownTextView
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView

class NotificationChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup,
                                        activity: Activity, ujetStyle: UjetStyle) :
    ChatMessageViewHolder(adapter, activity.applicationContext, ujetStyle, inflate(activity, parent,
        R.layout.ujet_view_chat_message_notification)) {
    fun bind(message: String, isMarkDownSupported: Boolean): View {
        val noticeText: MarkdownTextView = itemView.findViewById(R.id.message)
        UjetViewStyler.styleSecondaryText(ujetStyle, noticeText)
        UjetViewStyler.styleSecondaryLinkText(ujetStyle, noticeText)
        UjetViewStyler.overrideTypeface(ujetStyle, noticeText)
        noticeText.gravity = Gravity.CENTER
        if (isMarkDownSupported) {
            UjetViewStyler.styleRemoteChatText(ujetStyle, noticeText)
            UjetViewStyler.styleRemoteChatLinkText(ujetStyle, noticeText, true)
            loadMessageIntoMarkdownTextView(context, noticeText, message)
        } else {
            noticeText.text = HtmlCompat.fromHtml(message, HtmlCompat.FROM_HTML_MODE_LEGACY)
        }
        val systemMessageStyle = configuration.ujetStylesOptions?.chatStyles?.systemMessages
        StyleUtil.updateFontStyle(context, noticeText, systemMessageStyle?.font)
        StyleUtil.updateBackgroundStyle(
            noticeText,
            systemMessageStyle?.backgroundColor,
            systemMessageStyle?.cornerRadius,
            systemMessageStyle?.border
        )
        val borderPadding = StyleUtil.getTextPaddingWithInBorder(context, systemMessageStyle?.cornerRadius,
            systemMessageStyle?.border) ?: 0
        noticeText.setPaddingRelative(
            noticeText.paddingStart + borderPadding,
            noticeText.paddingTop + borderPadding,
            noticeText.paddingEnd + borderPadding,
            noticeText.paddingBottom + borderPadding
        )
        AccessibilityUtil.updateAccessibilityAction(noticeText, true, null)
        AccessibilityUtil.invokeAnnouncementOnce(
            context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager,
            adapterPosition,
            noticeText.text.toString()
        )
        return itemView
    }
}
