package co.ujet.android.app.chat.view

import android.annotation.SuppressLint
import android.content.Context
import android.widget.LinearLayout
import co.ujet.android.R
import co.ujet.android.app.chat.MarkdownUtil
import co.ujet.android.clean.entity.menu.channel.Channel.Companion.DEFLECTION_REASON_AFTER_HOURS
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView

@SuppressLint("ViewConstructor")
class ChatAfterHoursView(context: Context, private val ujetStyle: UjetStyle) : LinearLayout(context) {
    private var restartChatButtonClickListener: (() -> Unit)? = null
    private var messageTextView: MarkdownTextView? = null

    init {
        inflate(context, R.layout.ujet_view_chat_after_hours, this)
        isFocusable = false
        val systemMessageStyle = Injection.provideConfiguration().ujetStylesOptions?.chatStyles?.systemMessages
        messageTextView = findViewById<MarkdownTextView>(R.id.after_hours_message).apply {
            UjetViewStyler.styleSecondaryText(ujetStyle, this)
            UjetViewStyler.overrideTypeface(ujetStyle, this)
            StyleUtil.updateFontStyle(context, this, systemMessageStyle?.font)
            StyleUtil.updateBackgroundStyle(this, systemMessageStyle?.backgroundColor,
                systemMessageStyle?.cornerRadius, systemMessageStyle?.border)
            val borderPadding = StyleUtil.getTextPaddingWithInBorder(context, systemMessageStyle?.cornerRadius,
                systemMessageStyle?.border) ?: 0
            setPaddingRelative(
                paddingStart + borderPadding,
                paddingTop + borderPadding,
                paddingEnd + borderPadding,
                paddingBottom + borderPadding
            )
        }
        findViewById<FancyButton>(R.id.restart_button).apply {
            setCustomTypeFace(ujetStyle.typeFace)
            val buttonStyle = systemMessageStyle?.buttonStyle
            StyleUtil.updateFontStyle(context, this, buttonStyle?.font)
            StyleUtil.updateBackgroundStyle(this, buttonStyle?.backgroundColor,
                buttonStyle?.cornerRadius, buttonStyle?.border)
            setOnClickListener {
                restartChatButtonClickListener?.invoke()
            }
        }
    }

    internal fun setRestartChatButtonClickListener(listener: (() -> Unit)) {
        restartChatButtonClickListener = listener
    }

    internal fun setMessage(afterHourMessage: String?) {
        messageTextView?.let { messageTextView ->
            UjetViewStyler.styleRemoteChatText(ujetStyle, messageTextView)
            UjetViewStyler.styleRemoteChatLinkText(ujetStyle, messageTextView, true)
            afterHourMessage?.let { text ->
                MarkdownUtil.loadMessageIntoMarkdownTextView(
                    context,
                    messageTextView,
                    text,
                    deflectionType = DEFLECTION_REASON_AFTER_HOURS
                )
            }
        }
    }
}
