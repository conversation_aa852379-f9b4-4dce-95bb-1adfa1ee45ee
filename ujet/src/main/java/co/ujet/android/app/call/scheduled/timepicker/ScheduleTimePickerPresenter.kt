package co.ujet.android.app.call.scheduled.timepicker

import android.content.Context
import co.ujet.android.R.string
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerContract.Presenter
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerContract.View
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.language.usecase.ChooseLanguage
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId.RequestValues
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId.ResponseValue
import co.ujet.android.common.util.LocaleUtil
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.constant.CallCreateType.Scheduled
import java.util.*

/**
 * Presenter for [ScheduleTimePickerFragment]
 */
internal class ScheduleTimePickerPresenter(
    private val context: Context,
    private val ujetContext: UjetContext,
    private val apiManager: ApiManager,
    private val localRepository: LocalRepository,
    private val view: View,
    private val useCaseHandler: UseCaseHandler,
    private val getSelectedMenuId: GetSelectedMenuId,
    private val chooseLanguage: ChooseLanguage,
    private val cachedScheduleCallTimeSlots: Array<String?>,
    private val cachedPickerSelectedPosition: Int
) : Presenter {

    private var serverTimes: Array<String>? = null
    private var selectedIndex = 0
    private var menuId = 0
    private var language: String? = null

    override fun start() {
        if (view.isActive) {
            view.showLoading()
        }
        getCachedMenuId()
        getPreferredLanguage()
    }

    override fun updateItemAtIndex(index: Int) {
        selectedIndex = index
    }

    override fun confirmSelect() {
        val scheduledTime = TimeUtil.parseTime(serverTimes?.get(selectedIndex))
        if (view.isActive) {
            view.showPhoneNumberInput(Scheduled, scheduledTime)
        }
    }

    override fun onBackButtonClicked() {
        if (view.isActive) {
            view.back()
        }
        showRatingScreen()
    }

    override fun onExitButtonClicked() {
        if (view.isActive) {
            view.finish()
        }
        showRatingScreen()
    }

    private fun showRatingScreen() {
        val rateRepository = localRepository.rateRepository
        if (rateRepository.isRatable.not()) {
            return
        }

        rateRepository.apply {
            //Show survey if enabled, otherwise show CSAT dialog
            if (rateTarget?.surveyEnabled == true) {
                //clear old csat rating config
                isRatable = false
                isSurveyUnanswered = true
                view.showSurveyScreen()
            } else if (csatEnabled()) {
                view.showCsatScreen()
            }
        }
    }

    private fun getTimeSlots() {
        apiManager.getTimeSlots(
            menuId,
            language ?: return,
            object : ApiCallback<Array<String>> {
                override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<Array<String>>) {
                    serverTimes = response.body()
                    if (serverTimes == null) {
                        if (view.isActive) {
                            view.showError()
                        }
                        return
                    }
                    val fieldValue = convertReadableFormat(serverTimes ?: return)
                    if (view.isActive) {
                        view.showPicker(fieldValue, selectedIndex)
                        if ((serverTimes?.size ?: 0) > 0) {
                            view.enableButton()
                        }
                    }
                }

                override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                    if (view.isActive) {
                        view.showError()
                    }
                }
            })
    }

    private fun getCachedMenuId() {
        useCaseHandler.executeImmediate(
            getSelectedMenuId,
            RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    updateMenuId(response.menuId)
                }

                override fun onError() {
                    if (view.isActive) {
                        view.showMenuUpdated()
                        view.restart()
                    }
                }
            })
    }

    private fun getPreferredLanguage() {
        useCaseHandler.execute(
            chooseLanguage,
            ChooseLanguage.RequestValues(),
            object : UseCaseCallback<ChooseLanguage.ResponseValue> {
                override fun onSuccess(response: ChooseLanguage.ResponseValue) {
                    updateLanguage(response.languageCode)
                }

                override fun onError() {
                    if (view.isActive) {
                        view.showError()
                    }
                }
            })
    }

    private fun updateMenuId(menuId: Int) {
        this.menuId = menuId
        onInformDataUpdated()
    }

    private fun updateLanguage(language: String) {
        this.language = language
        onInformDataUpdated()
    }

    private fun onInformDataUpdated() {
        if (language == null) return
        if (menuId == 0) {
            if (view.isActive) {
                view.showError()
            }
            return
        }
        // Update picker view with previously selected item when configuration changed
        when {
            cachedPickerSelectedPosition > 0 -> {
                updateItemAtIndex(cachedPickerSelectedPosition)
                //We need to give the adapter time to draw the views
                MainLooper.postDelayed({
                    displayCachedTimeSlots()
                }, 200)
            }
            cachedScheduleCallTimeSlots.isEmpty() -> {
                getTimeSlots()
            }
            else -> {
                displayCachedTimeSlots()
            }
        }
    }

    private fun displayCachedTimeSlots() {
        view.showPicker(cachedScheduleCallTimeSlots, selectedIndex)
        view.enableButton()
    }

    private fun convertReadableFormat(serverTimes: Array<String>): Array<String?> {
        var result = arrayOfNulls<String>(serverTimes.size)
        if (serverTimes.isEmpty()) {
            result = arrayOf(context.getString(string.ujet_schedule_time_no_available_time))
            return result
        }
        for (index in serverTimes.indices) {
            val serverDate = TimeUtil.parseTime(serverTimes[index]) ?: return result
            result[index] = if (index == 0) getDaysDifferenceTime(serverDate) else TimeUtil.getHourInFormat(serverDate, LocaleUtil.getCurrentLocale(context))
        }
        return result
    }

    private fun getDaysDifferenceTime(date: Date): String {
        val difference = TimeUtil.getDayDifference(Date(), date)
        val timeInHourFormat = TimeUtil.getHourInFormat(date, LocaleUtil.getCurrentLocale(context))
        return when (difference) {
            0 -> timeInHourFormat
            1 -> String.format(context.getString(string.ujet_schedule_time_tomorrow), timeInHourFormat)
            else -> {
                val daysString = String.format(Locale.US, "%d %s ", difference, context.getString(string.ujet_time_days))
                String.format("%s%s", daysString, timeInHourFormat)
            }
        }
    }
}
