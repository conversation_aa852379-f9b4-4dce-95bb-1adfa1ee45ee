package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout.LayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.message.EndUserDocumentChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil

class EndUserDocumentChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup,
                                           activity: Activity, ujetStyle: UjetStyle) :
    DocumentChatMessageViewHolder(adapter, activity, ujetStyle,
        inflate(activity, parent, R.layout.ujet_view_chat_message_end_user_document)) {

    fun bind(message: EndUserDocumentChatMessage, shouldShowAgentNames: <PERSON><PERSON><PERSON>,
             isGroupStart: <PERSON>olean, isGroupEnd: Boolean): View {
        setUpEndUserIcon(itemView, isGroupStart)

        val documentContainer: View = itemView.findViewById(R.id.document_container)
        val consumerMessageStyle = configuration.ujetStylesOptions?.chatStyles?.consumerMessageBubbles
        val background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_end_user)
        val params = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            marginStart = ujetStyle.dpToPx(55f).toInt()
        }
        StyleUtil.applyDefaultStyle(
            context, ujetStyle, documentContainer, consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius, consumerMessageStyle?.border, background,
            ujetStyle.chatEndUserMessageTopBubbleColor, params
        )

        StyleUtil.updateBackgroundStyle(
            documentContainer,
            consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius,
            consumerMessageStyle?.border
        )
        val fileName: TextView = itemView.findViewById(R.id.file_name)
        UjetViewStyler.styleLocalChatText(ujetStyle, fileName)
        setupResendButton(message, itemView.findViewById(R.id.btn_send_failed), itemView.findViewById(R.id.resend_message))
        setupDocumentViewCommonAttributes(itemView, message, null, shouldShowAgentNames,
            isGroupStart, isGroupEnd)
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_mobile_user),
            mainContainer = documentContainer,
            message = fileName,
            documentType = context.getString(R.string.ujet_chat_type_document),
            timestamp = itemView.findViewById(R.id.timestamp),
            resend = itemView.findViewById(R.id.resend_message),
            isDocument = true,
            isClickable = true,
            adapterPosition = adapterPosition
        )
        return itemView
    }
}
