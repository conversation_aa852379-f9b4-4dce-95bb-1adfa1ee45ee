package co.ujet.android.app.csat.sucess

import co.ujet.android.internal.Configuration
import java.util.*

internal class CsatSuccessPresenter(private val view: CsatSuccessContract.View, private val configuration: Configuration) :
    CsatSuccessContract.Presenter {
    override fun start() {
        if (!configuration.showCsatSkipButton) {
            Timer().schedule(object : TimerTask() {
                override fun run() {
                    if (view.isActive) {
                        view.close()
                    }
                }
            }, 1500)
        }
    }
}
