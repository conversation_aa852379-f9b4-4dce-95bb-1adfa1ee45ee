package co.ujet.android.app.call.scheduled.confirm

import co.ujet.android.app.call.scheduled.confirm.ScheduleConfirmContract.Presenter
import co.ujet.android.app.call.scheduled.confirm.ScheduleConfirmContract.View
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.data.LocalRepository

internal class ScheduleConfirmPresenter(
    private val localRepository: LocalRepository,
    private val view: View
    ) : Presenter {
    private val call = localRepository.call

    override fun start() {
        if (view.isActive.not()) {
            return
        }
        val scheduledAt = call?.scheduledAt
        if (scheduledAt.isNullOrEmpty()) {
            view.back()
            showRatingScreen()
        } else {
            val scheduledDate = TimeUtil.parseTime(scheduledAt) ?: return
            view.displayScheduledTime(scheduledDate)
        }
    }

    override fun onConfirmClicked() {
        if (view.isActive) {
            view.finish()
            showRatingScreen()
        }
    }

    override fun onBackButtonClicked() {
        view.back()
        showRatingScreen()
    }

    private fun showRatingScreen() {
        val rateRepository = localRepository.rateRepository
        if (rateRepository.isRatable.not()) {
            return
        }

        rateRepository.apply {
            //Show survey if enabled, otherwise show CSAT dialog
            if (rateTarget?.surveyEnabled == true) {
                //clear old csat rating config
                isRatable = false
                isSurveyUnanswered = true
                view.showSurveyScreen()
            } else if (csatEnabled()) {
                view.showCsatScreen()
            }
        }
    }
}
