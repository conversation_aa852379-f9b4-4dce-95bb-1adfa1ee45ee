package co.ujet.android.app.chat.viewholders

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.RelativeLayout.LayoutParams
import android.widget.TextView
import co.ujet.android.ui.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.util.AccessibilityUtil

class HeaderFooterMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup, context: Context) :
    BaseChatMessageViewHolder(adapter, inflate(context, parent, R.layout.header_footer_message_view_holder)) {

    fun bind(view: View) {
        itemView.findViewById<RelativeLayout>(R.id.root_view).apply {
            removeAllViews()
            view.apply {
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                    setPaddingRelative(
                        view.paddingStart,
                        view.paddingTop,
                        view.paddingEnd,
                        view.paddingBottom
                    )
                }
            }

            AccessibilityUtil.updateAccessibilityAction(this, true, null)
            addView(view)
        }
    }
}
