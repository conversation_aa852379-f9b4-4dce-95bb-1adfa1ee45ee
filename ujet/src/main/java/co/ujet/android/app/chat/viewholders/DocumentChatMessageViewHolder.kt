package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewTreeObserver
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.ChatMediaUtil.ellipsizeFileName
import co.ujet.android.app.chat.ChatMediaUtil.getImageTintColor
import co.ujet.android.app.chat.ChatMediaUtil.initializeDownloadAttachmentIconView
import co.ujet.android.commons.domain.MediaFile.Type
import co.ujet.android.commons.domain.chat.message.DocumentChatMessage
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler

abstract class DocumentChatMessageViewHolder(adapter: ChatAdapterInteractor, activity: Activity,
                                             ujetStyle: UjetStyle, itemView: View) :
    ChatMessageViewHolder(adapter, activity, ujetStyle, itemView
    ) {
        protected fun setupDocumentViewCommonAttributes(
            view: View,
            message: DocumentChatMessage,
            agentName: String?,
            shouldShowAgentNames: Boolean,
            isGroupStart: Boolean,
            isGroupEnd: Boolean,
            onDownloadIconClicked: () -> Unit = {}
        ) {
            val documentContainer: View = view.findViewById(R.id.document_container)
            documentContainer.setOnClickListener {
                openDocumentFile(message.mediaFile)
            }
            view.findViewById<TextView?>(R.id.file_name)?.apply {
                text = message.mediaFile.filename
                setTextColor(ujetStyle.remoteMessageTextColor)
                UjetViewStyler.overrideTypeface(ujetStyle, this)

                viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        viewTreeObserver.removeOnGlobalLayoutListener(this)
                        val ellipsizeText = message.mediaFile.filename?.let {
                            ellipsizeFileName(
                                it,
                                width, this@apply)
                        }
                        text = ellipsizeText
                    }
                })
            }
            setUpMessageFooter(view, message, isGroupEnd)

            val imageView: ImageView = view.findViewById(R.id.file_icon)
            val tintColor = getFileIconTintColor(message.mediaFile.type)
            imageView.imageTintList = getImageTintColor(tintColor)
            imageView.setImageResource(message.getThumbnail())
            initializeDownloadAttachmentIconView(view.findViewById(R.id.download_attachment_icon),
                ujetStyle, onDownloadIconClicked = onDownloadIconClicked)
            setAgentName(view, agentName, isGroupStart, shouldShowAgentNames)
        }

        private fun getFileIconTintColor(mediaFileType: Type?): Int {
            val tintColorId = when (mediaFileType) {
                Type.Audio -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_audio_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_audio_icon
                    }
                }
                Type.Doc -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_word_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_word_icon
                    }
                }
                Type.Excel -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_excel_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_excel_icon
                    }
                }
                Type.PDF -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_pdf_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_pdf_icon
                    }
                }
                Type.PPT -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_power_point_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_power_point_icon
                    }
                }
                Type.Video -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_video_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_video_icon
                    }
                }
                Type.TXT, Type.CSV -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_text_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_text_icon
                    }
                }
                else -> {
                    if (ujetStyle.isDarkModeEnabled) {
                        co.ujet.android.ui.R.color.ujet_document_generic_icon_dark
                    } else {
                        co.ujet.android.ui.R.color.ujet_document_generic_icon
                    }
                }
            }
            return ContextCompat.getColor(context, tintColorId)
        }
    }
