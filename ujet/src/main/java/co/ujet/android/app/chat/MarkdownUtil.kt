package co.ujet.android.app.chat

import android.content.Context
import android.os.Build
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.view.Gravity
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_AUTO
import androidx.core.text.HtmlCompat
import co.ujet.android.clean.entity.menu.channel.Channel
import co.ujet.android.common.util.CustomizableLinkUtil
import co.ujet.android.common.util.StringUtil
import co.ujet.android.common.util.TextViewUtil
import cx.ujet.android.markdown.UjetMarkdown
import cx.ujet.android.markdown.widgets.MarkdownTextView
import java.util.regex.Pattern

object MarkdownUtil {
    private const val RAW_HYPER_LINK_TAG = "a"
    private val FORBIDDEN_HTML_TAGS = listOf(RAW_HYPER_LINK_TAG, "script", "img")

    internal fun loadMessageIntoMarkdownTextView(
        context: Context, messageTextView: MarkdownTextView, messageText: String,
        allowHtml: Boolean = true, isTextCentered: Boolean = false, deflectionType: String? = null,
        supportHtmlInText: Boolean = false
    ) {
        val htmlText = UjetMarkdown.parse(messageText)
        val escapedHtmlText = removeForbiddenTagsFromText(htmlText)
        if (escapedHtmlText == messageText || CustomizableLinkUtil.containsCustomizableLink(escapedHtmlText) ||
            allowHtml.not()) {
            // This means the text is in plain-text or its markdown is not properly formatted or it
            // includes non-markdown formatted links in which case it's treated as a plaintext message
            when {
                CustomizableLinkUtil.containsCustomizableLink(escapedHtmlText) && StringUtil.isUrlAvailable(messageText) -> {
                    TextViewUtil.parseLinks(
                        messageTextView,
                        handleCustomizableLinks(messageTextView, escapedHtmlText, context, deflectionType),
                        deflectionType
                    )
                }
                CustomizableLinkUtil.containsCustomizableLink(escapedHtmlText) -> {
                    handleCustomizableLinks(messageTextView, escapedHtmlText, context, deflectionType)
                }
                StringUtil.isUrlAvailable(messageText) -> TextViewUtil.parseLinks(messageTextView,
                   messageText, deflectionType)
                supportHtmlInText -> {
                    messageTextView.text = HtmlCompat.fromHtml(escapedHtmlText, HtmlCompat.FROM_HTML_MODE_LEGACY)
                }
                else -> {
                    messageTextView.text = messageText
                }
            }
        } else {
            messageTextView.gravity = Gravity.NO_GRAVITY
            messageTextView.setHtml(escapedHtmlText, messageText, isTextCentered)
        }
    }

    internal fun removeForbiddenTagsFromText(message: String): String {
        // Escape forbidden HTML tags
        var escapedMessage = message
        FORBIDDEN_HTML_TAGS.forEach { tag ->
            escapedMessage = "<$tag(.*)>(.*)[(</$tag>)]?".toRegex().replace(escapedMessage) {
                val openTagContent = it.groups[1]?.value
                val innerHtml = it.groups[2]?.value
                val urlPattern = Pattern.compile("href=\"((?!<script[^>]*>.*?</script>)(https?://(?:www\\.)?[^\" ]*))\"")
                val matcher = openTagContent?.let { tagContent ->
                    urlPattern.matcher(tagContent)
                }

                // When forbidden <script> is included inside <a> tag, we should not parse it. If
                // <a> tag contains correct URL link inside href then, parse it otherwise ignore it.
                when {
                    tag == RAW_HYPER_LINK_TAG && matcher?.find() == true -> {
                        // Parse <a> tag with link
                        "<$tag$openTagContent>$innerHtml"
                    }
                    it.groups.size == 3 -> {
                        "&lt;$tag$openTagContent&gt;$innerHtml"
                    }
                    else -> {
                        "&lt;$tag$openTagContent&gt;$innerHtml&lt;/$tag&gt;"
                    }
                }
            }
        }
        return escapedMessage
    }

    private fun handleCustomizableLinks(
        messageTextView: MarkdownTextView,
        escapedHtmlText: String,
        context: Context,
        deflectionType: String?
    ): SpannableStringBuilder {
        val spannable = CustomizableLinkUtil.parseCustomizableLinks(
            escapedHtmlText,
            context.applicationContext, deflectionType
        )
        messageTextView.text = spannable
        messageTextView.importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_AUTO
        messageTextView.isClickable = true
        if (deflectionType == Channel.DEFLECTION_REASON_AFTER_HOURS) {
            messageTextView.movementMethod = LinkMovementMethod.getInstance()
        }
        return spannable
    }

    fun getDisplayableTextFromHtml(htmlText: String): Spanned {
        return if (Build.VERSION.SDK_INT >= 24) {
            Html.fromHtml(htmlText, HtmlCompat.FROM_HTML_MODE_LEGACY) // For API level 24 and above
        } else {
            Html.fromHtml(htmlText) // For below API level 24
        }
    }
}
