package co.ujet.android.app.chat

import co.ujet.android.R
import co.ujet.android.UjetWebFormCallback
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.api.request.EventRequest
import co.ujet.android.api.request.EventRequest.EventRequestPayload
import co.ujet.android.api.response.MessageResponse
import co.ujet.android.app.chat.ChatContract.Presenter
import co.ujet.android.app.chat.ChatContract.View
import co.ujet.android.app.chat.TransferState.*
import co.ujet.android.app.chat.data.ChatMessageDataSource
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.menu.filter.MenuFilter
import co.ujet.android.clean.domain.menu.usecase.GetMenu
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.chat.WebForm
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.commons.domain.chat.message.*
import co.ujet.android.commons.domain.chat.message.base.*
import co.ujet.android.commons.libs.uson.Uson
import co.ujet.android.data.constant.ChatStatus
import co.ujet.android.data.model.Chat
import co.ujet.android.data.model.ChatTranscriptResponse
import co.ujet.android.data.model.VirtualAgentSettings
import co.ujet.android.internal.Configuration
import co.ujet.android.internal.Injection
import co.ujet.android.internal.UjetInternal
import co.ujet.android.libs.logger.Logger
import co.ujet.android.smartaction.manager.SmartActionManager
import co.ujet.android.smartaction.ui.verification.BiometricsVerification
import com.google.gson.GsonBuilder
import kotlinx.coroutines.CompletableJob
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import java.io.File
import java.net.HttpURLConnection
import java.util.*

internal class ChatPresenter(
    private val view: View,
    private val ujetContext: UjetContext,
    private val biometricsVerification: BiometricsVerification,
    private val localRepository: LocalRepository,
    private val chatServiceInteractor: ChatServiceInteractor,
    private val useCaseHandler: UseCaseHandler,
    private val getCompany: GetCompany,
    private val getMenu: GetMenu,
    private val getSelectedMenuId: GetSelectedMenuId,
    private val apiManager: ApiManager,
    private val configuration: Configuration
) : Presenter, ChatListener {
    private var webFormTimer: Timer? = null
    private var virtualAgentSettings: VirtualAgentSettings? = null
    private var chatEnded = false
    private var chatResuming = false
    private var isChatPreviewEnabled = false
    private val userStoppedTypingTimer = Timer()
    private var userStoppedTypingTimerTask: TimerTask? = null
    private var webFormTimerIsInProgress = false
    private var chatTranscriptDownloadIsInProgress = false
    private var isWebFormTimerRunning = false
    private var webFormJob: CompletableJob? = null
    private var webFormScope: CoroutineScope? = null
    private var downloadTranscriptScope: CoroutineScope? = null
    private var chatStatus: ChatStatus? = null
    private val unsupportedVideoExtensions = listOf(VIDEO_FORMAT_WMV, VIDEO_FORMAT_AVI)

    override fun start() {
        getCachedCompany()
        Injection.provideUjetEventNotificationManager().communicationUiStarted("chat")
    }

    override fun onChatServiceUnavailable() {
        view.setEndChatEnabled(true)
        view.setSendPhotoEnabled(false)
        view.setChatInputVisible(false)
    }

    override fun onChatAfterHourMessageReceived(afterHourMessage: String?) {
        if (!afterHourMessage.isNullOrEmpty()) {
            chatEnded = true
            view.setAfterHoursView(true, afterHourMessage)
            view.updateEndChatMenuText(R.string.ujet_chat_exit)
        }
    }

    override fun onChatErrorMessageReceived(errorMessage: String?) {
        if (!errorMessage.isNullOrEmpty()) {
            view.showErrorMessage(errorMessage)
        }
    }

    override fun onChatMessageSend(message: String) {
        chatServiceInteractor.sendEndUserMessage(message)
    }

    override fun onQuickReplyClicked(chatMessage: VirtualAgentQuickReplyChatMessage, quickReplyButton: QuickReplyButton) {
        chatMessage.quickReplyButtonsVisible = false
        view.notifyMessagesUpdated()
        when (quickReplyButton) {
            is EscalationButton -> onEscalateClicked()
            is DeflectToEmailButton -> showEmail(quickReplyButton)
            is DeflectToVirtualAgentButton -> onDeflectToVirtualAgentClicked(quickReplyButton)
            is DeflectToEDLButton -> {
                //We need to show quick reply buttons when user moves back to chat UI within 5 minutes window
                //after deflecting to external deflection link
                chatMessage.quickReplyButtonsVisible = true
                showExternalDeflectionLink(quickReplyButton)
            }

            else -> {
                onQuickReplyButtonClicked(quickReplyButton)
                onChatMessageSend(quickReplyButton.title)
            }
        }
    }

    private fun onQuickReplyButtonClicked(quickReplyButton: QuickReplyButton) {
        quickReplyButton.eventParams?.let { eventData ->
            Injection.provideUjetEventNotificationManager().quickReplyClicked(eventData)
        }
    }

    override fun onChatMessageResend(chatMessage: SendableChatMessage) {
        chatServiceInteractor.resendMessage(chatMessage)
    }

    override fun onChatMessageClicked(chatMessage: ChatMessage, position: Int) {
        when (chatMessage) {
            is EndUserPhotoChatMessage -> {
                val mediaFile = chatMessage.mediaFile
                view.showImage(mediaFile.filename ?: return)
            }

            is VideoChatMessage -> {
                val mediaFile = chatMessage.mediaFile
                view.showVideo(mediaFile.filename ?: return)
            }

            is HumanAgentPhotoChatMessage -> {
                getFilename(chatMessage.mediaFiles, position)?.let {
                    view.showImage(it)
                }
            }

            is HumanAgentVideoChatMessage -> {
                getFilename(chatMessage.mediaFiles, position)?.let { fileName ->
                    if (isVideoFormatSupported(fileName)) {
                        view.showVideo(fileName)
                    } else {
                        view.showErrorMessage("Can't open file")
                    }
                }
            }

            is VirtualAgentPhotoChatMessage -> {
                view.showImage(chatMessage.photoUrl ?: return)
            }
        }
    }

    private fun isVideoFormatSupported(fileName: String): Boolean {
        val extension = fileName.substringAfterLast('.', "")
        return extension.lowercase() !in unsupportedVideoExtensions
    }

    private fun getFilename(mediaFiles: ArrayList<MediaFile>, position: Int): String? {
        return if (position < mediaFiles.size) {
            mediaFiles[position].cacheFilePath
        } else {
            null
        }
    }

    override fun onEscalateClicked() {
        chatServiceInteractor.escalateChat()
    }

    override fun onDeflectToVirtualAgentClicked(deflectToVirtualAgentButton: DeflectToVirtualAgentButton) {
        deflectToVirtualAgent(deflectToVirtualAgentButton.escalationId)
    }

    override fun deflectToVirtualAgent(escalationId: Int) {
        chatServiceInteractor.deflectToVirtualAgent(escalationId)
    }

    override fun isChatPreviewAvailable(): Boolean {
        return isChatPreviewEnabled
    }

    override fun onSendChatMessagePreview(message: String) {
        chatServiceInteractor.sendChatMessagePreview(message)
    }

    override fun onDoneClicked(endedBy: String) {
        chatEnded = true
        chatServiceInteractor.notifyEventData(endedBy)
        chatServiceInteractor.endChat()
        clearStatusText()
        view.updateEndChatMenuText(R.string.ujet_chat_exit)
        showNextScreen(false)
    }

    override fun isPostSessionRequired() = localRepository.rateRepository.isPostSessionRequired

    override fun isPostSessionOptInRequired() = localRepository.rateRepository.isPostSessionOptInRequired

    override fun isEmailSent() = localRepository.isEmailSent

    private fun getCachedCompany() {
        useCaseHandler.executeImmediate(getCompany, GetCompany.RequestValues(false, true),
            object : UseCaseCallback<GetCompany.ResponseValue> {
                override fun onSuccess(response: GetCompany.ResponseValue) {
                    isChatPreviewEnabled = response.company.chatSettings?.messagePreview ?: false
                    Logger.d("Chat preview enabled $isChatPreviewEnabled")
                }

                override fun onError() {
                    Logger.w("Couldn't get cached company data")
                }
            })
    }

    private fun showEmail(emailButton: DeflectToEmailButton) {
        useCaseHandler.executeImmediate(
            getCompany,
            GetCompany.RequestValues(false, true),
            object : UseCaseCallback<GetCompany.ResponseValue> {
                override fun onSuccess(response: GetCompany.ResponseValue) {
                    showEmail(
                        response.company.isEmailEnhancementEnabled,
                        response.company.supportEmail,
                        emailButton.menuPath,
                        emailButton.deflectionType,
                        emailButton.escalationId
                    )
                }

                override fun onError() {
                    view.showErrorMessage(R.string.ujet_error_request)
                }
            }
        )
    }

    private fun showEmail(
        emailEnhancementEnabled: Boolean, supportEmail: String,
        menuPath: String?, deflectionType: String, escalationId: Int
    ) {
        if (emailEnhancementEnabled) {
            view.showEmailForm(deflectionType, escalationId)
        } else {
            view.showEmailClient(supportEmail, menuPath, deflectionType)
            chatServiceInteractor.endChat()
        }
    }

    private fun showExternalDeflectionLink(externalDeflectionLinkButton: DeflectToEDLButton) {
        view.showExternalDeflectionLinks(externalDeflectionLinkButton.url, externalDeflectionLinkButton.deflectionType)
    }

    override fun onChatResumeClicked() {
        chatResuming = true
        //Clear the chat dismiss view if it is still visible to the user
        setChatDismissViewVisible(false)
        view.updateEndChatMenuText(R.string.ujet_chat_end)
        view.setChatInputVisible(true)
        useCaseHandler.execute(
            getSelectedMenuId,
            GetSelectedMenuId.RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<GetSelectedMenuId.ResponseValue> {
                override fun onSuccess(response: GetSelectedMenuId.ResponseValue) {
                    resumeChatForMenu(response.menuId)
                }

                override fun onError() {
                    Logger.e("Can't resume chat: can't get selected menu ID")
                    view.showErrorMessage()
                    showNextScreen(false)
                    chatResuming = false
                }
            }
        )
    }

    override fun getChatStatus() = chatStatus

    private fun resumeChatForMenu(menuId: Int) {
        val requestValues = GetMenu.RequestValues(menuId, ujetContext.directAccessKey, MenuFilter.VISIBLE, true)
        useCaseHandler.execute(
            getMenu,
            requestValues,
            object : UseCaseCallback<GetMenu.ResponseValue> {
                override fun onSuccess(response: GetMenu.ResponseValue) {
                    val menu = response.menu
                    menu.channels.chatChannel?.let { chatChannel ->
                        when {
                            chatChannel.isEnabled -> chatServiceInteractor.resumeChat()
                            chatChannel.isAfterHoursDeflected -> {
                                chatEnded = true
                                view.setAfterHoursView(true, null)
                                view.updateEndChatMenuText(R.string.ujet_chat_exit)
                            }

                            chatChannel.isOverCapacityDeflected -> view.showOvercapacityMessage()
                            else -> view.showChatNotAvailable((menu.enabledChannels?.size ?: 0) > 0)
                        }
                    } ?: run {
                        view.showChatNotAvailable((menu.enabledChannels?.size ?: 0) > 0)
                    }
                }

                override fun onError() {
                    chatResuming = false
                    view.showChatNotAvailable(false)
                }
            }
        )
    }

    override fun onChatNewClicked() {
        //Clear the chat dismiss view if it is still visible to the user
        setChatDismissViewVisible(false)
        view.setChatInputVisible(false)

        //End the chat and redirect users to rating screen
        chatServiceInteractor.notifyEventData("dismissed")
        chatServiceInteractor.endChat()
        clearStatusText()
        showNextScreen(true)
    }

    override fun onChatFailure(errorCode: Int, message: String?) {
        if (errorCode == HttpURLConnection.HTTP_CONFLICT && !message.isNullOrEmpty()) {
            chatEnded = true
            view.setAfterHoursView(true, message)
            view.updateEndChatMenuText(R.string.ujet_chat_exit)
        } else {
            view.showErrorMessage()
        }
    }

    override fun onRegistered(dataSource: ChatMessageDataSource, state: TransferState) {
        view.setChatMessageDataSource(dataSource)
        view.scrollToBottom()
        handleChatTransferState(state)
    }

    override fun onUnRegistered() {
        view.clearChatMessageDataSource()
        view.scrollToBottom()
    }

    override fun onQueued(chat: Chat) {
        chatResuming = false
        chatStatus = chat.getStatus()
        view.setEmptyViewProgressBarVisibility(false)
        setChatDismissViewVisible(false)
        updateAssignedAgentViews(null, chat.statusText)
        view.setEndChatEnabled(true)
        updatePhotoButton(chat)
        view.displayCurrentState(chat.statusText.orEmpty())
        updateEscalateButton(chat)
        updateCoBrowseButton(chat)
    }

    override fun onAssigned(chat: Chat, agent: Agent?) {
        chatResuming = false
        chatStatus = chat.getStatus()
        view.setEmptyViewProgressBarVisibility(false)
        view.setEndChatEnabled(true)
        updatePhotoButton(chat)
        updateAssignedAgentViews(agent, chat.statusText)
        saveStatusText(chat.statusText.orEmpty())
        updateEscalateButton(chat)
        updateCoBrowseButton(chat)
    }

    override fun onTransferred(chat: Chat, agent: Agent) {
        updateAssignedAgentViews(agent, chat.statusText)
        saveStatusText(chat.statusText.orEmpty())
    }

    override fun onConnected(chat: Chat) {
        view.setChatInputVisible(chat.getStatus().isResumable() || chatResuming)
        view.displayPendingSmartAction(biometricsVerification)
    }

    override fun onDisconnected(chat: Chat) {
        view.setChatInputVisible(false)
    }

    override fun showPostSessionOptInBanner(isVisible: Boolean, showLoadingBar: Boolean) {
        view.setPostSessionOptInBannerVisibility(isVisible, showLoadingBar)
    }

    override fun onMessagesUpdated(vararg chatMessages: ChatMessage) {
        for (chatMessage in chatMessages) {
            if (chatMessage is ChatEndedMessage) {
                //When blockChatTerminationByEndUser is set to true, we need to end chat when agent is ended it and show next screen (CSAT/Surveys if enabled)
                if (isBlockChatTerminationByEndUserEnabled()) {
                    val endedBy = if (chatMessage.isTimeout) {
                        "timeout"
                    } else {
                        "agent"
                    }
                    onDoneClicked(endedBy)
                } else {
                    handleChatTimeout(chatMessage.isTimeout)
                    onAgentTypingEnded()
                    view.setChatInputVisible(false)
                }
            }
        }
    }

    override fun onAgentTypingStarted() {
        view.setTypingIndicatorVisible(true)
    }

    override fun onAgentTypingEnded() {
        view.setTypingIndicatorVisible(false)
    }

    override fun onFinished(chat: Chat, agent: Agent?) {
        chatResuming = false
        chatEnded = true
        chatStatus = chat.getStatus()
        view.updateEndChatMenuText(R.string.ujet_chat_exit)
        if (chat.isTimedOut()) {
            chatServiceInteractor.notifyEventData("timeout")
        } else if (chat.getStatus() == ChatStatus.Finished) {
            chatServiceInteractor.notifyEventData("agent")
        }
        //When blockChatTerminationByEndUser is set to true, we need to end chat here and show next screen (CSAT/Surveys if enabled)
        if (isBlockChatTerminationByEndUserEnabled()) {
            chatServiceInteractor.endChat()
            clearStatusText()
            showNextScreen(false)
        } else {
            handleChatTimeout(chat.isTimedOut())
            view.setEndChatEnabled(true)
            updatePhotoButton(chat)
            view.setChatInputVisible(false)
            setChatDismissViewVisible(false)
            view.setAfterHoursView(false, null)
            updateAssignedAgentViews(agent, chat.statusText)
        }
    }

    override fun onNewChatButtonClicked() {
        chatServiceInteractor.endChat()
        clearStatusText()
        showNextScreen(true)
    }

    override fun onChatAfterHoursRestartClicked() {
        view.setAfterHoursView(false, null)
        showNextScreen(true)
    }

    override fun onChatNotAvailableConfirmed() {
        chatServiceInteractor.notifyEventData("end_user")
        chatServiceInteractor.endChat()
        clearStatusText()
        showNextScreen(true)
    }

    override fun onChannelsNotAvailableConfirmed() {
        chatServiceInteractor.notifyEventData("end_user")
        chatServiceInteractor.endChat()
        clearStatusText()
        showNextScreen(true)
    }

    private fun handleChatTimeout(isTimedOut: Boolean) {
        view.scrollToBottom()
        //Clear the chat dismiss view if it is still visible to the user
        setChatDismissViewVisible(false)
        view.setChatInputVisible(false)
        chatEnded = isTimedOut
        view.updateEndChatMenuText(R.string.ujet_chat_exit)
        //Hide opt in banner if it is still visible
        view.setPostSessionOptInBannerVisibility(false)
    }

    override fun setConversationConnected(isConnected: Boolean) {
        if (view.isActive) {
            view.setConversationConnected(isConnected)
        }
    }

    override fun onSynchronizationCompleted() {
        sendPsaTextMessage()
    }

    override fun onError(message: String) {
        view.showErrorMessage(message)
    }

    override fun onChatDismissed(chat: Chat, agent: Agent?) {
        if (!chatResuming) {
            setChatDismissViewVisible(true)
            view.setChatInputVisible(false)
            updateAssignedAgentViews(agent, chat.statusText)
            view.setEndChatEnabled(true)
        }
        chatStatus = chat.getStatus()
        //When blockChatTerminationByEndUser is set to true, we need to end chat when chat is dismissed and show next screen (CSAT/Surveys if enabled)
        if (isBlockChatTerminationByEndUserEnabled()) {
            onDoneClicked("agent")
        }
    }

    override fun onChatRestartFailure(message: String) {
        view.showErrorMessage(message)
        setChatDismissViewVisible(true)
        view.setChatInputVisible(false)
    }

    private fun setChatDismissViewVisible(isVisible: Boolean) {
        if (chatEnded.not() || !isVisible) {
            //Hide chat dismiss view when blockChatTerminationByEndUser is true
            view.setDismissViewVisible(isVisible && !isBlockChatTerminationByEndUserEnabled())
        }
    }

    override fun onChatClear() {
        view.setEndChatEnabled(true)
        view.setSendPhotoEnabled(false)
        view.setChatInputVisible(false)
    }

    override fun showPostSessionChatUI(value: Boolean) {
        view.showPostSessionChatUI(value)
    }

    override fun clearChatMessages() {
        view.clearChatMessages()
    }

    override fun onWarning() {
        if (view.isActive) {
            view.showErrorDialog()
        }
    }

    override fun onNetworkReconnected() {
        if (view.isActive) {
            view.hideErrorDialog()
        }
    }

    override fun stop() {
        Injection.provideUjetEventNotificationManager().communicationUiStopped("chat")
    }

    override fun onVirtualAgentSettingsUpdated(virtualAgentSettings: VirtualAgentSettings?, chat: Chat) {
        this.virtualAgentSettings = virtualAgentSettings
        updateEscalateButton(chat)
    }

    override fun onChatNotSupported(message: String) {
        view.showErrorMessage(message)
        chatEnded = true
        clearStatusText()
        showNextScreen(false)
    }

    override fun getCoBrowseUI() = view.getCoBrowseUI()

    override fun getCoBrowseButtonView() = view.getCoBrowseButtonView()

    override fun getMainFragmentManager() = view.getMainFragmentManager()

    override fun displayPendingSmartAction() {
        view.displayPendingSmartAction(biometricsVerification)
    }

    override fun updateTransferBanner(state: TransferState) {
        handleChatTransferState(state)
    }

    override fun showPreSessionSmartActions(menuId: Int) {
        view.showPreSessionSmartActions(menuId)
    }

    // ⚠️ Added this method to address an edge case, be cautious when you're using for any future work ⚠️
    override fun closeChatScreen() {
        view.finish()
    }

    override fun onPostSessionTransferStatusUpdateFailed() {
        view.showSurveyErrorDialog()
    }

    override fun setChatInput(input: String) {
        chatServiceInteractor.setChatInput(input)
        if (input.isEmpty()) {
            userStoppedTypingTimerTask?.cancel()
            chatServiceInteractor.notifyEndUserStoppedTyping()
        } else {
            chatServiceInteractor.notifyEndUserIsTyping()
            scheduleTypingStoppedEvent()
        }
    }

    private fun scheduleTypingStoppedEvent() {
        userStoppedTypingTimerTask?.cancel()
        userStoppedTypingTimerTask = object : TimerTask() {
            override fun run() {
                chatServiceInteractor.notifyEndUserStoppedTyping()
            }
        }

        userStoppedTypingTimer.schedule(userStoppedTypingTimerTask, USER_STOPPED_TYPING_TIMEOUT)
    }

    override fun getChatInput() = chatServiceInteractor.getChatInput() ?: ""

    override fun onContentCardClicked(eventParams: HashMap<String, Any>?, title: String?) {
        Injection.provideUjetEventNotificationManager().contentCardClicked(eventParams ?: hashMapOf())

        // Send click event to CRM
        val communication = localRepository.ongoingCommunication
        val eventRequest = EventRequest.build(
            name = "content_card_clicked",
            payload = EventRequestPayload(
                title = title
            )
        )
        apiManager.sendEvent(communication.urlPath, communication.id, eventRequest)
    }

    override fun onWebFormClicked(webForm: WebForm) {
        view.showWebForm()
        listenWebFormEvent(webForm)
        sendWebFormClickEvent(webForm)
    }

    private fun sendWebFormClickEvent(webForm: WebForm) {
        // Send click event to CRM
        val communication = localRepository.ongoingCommunication
        val eventRequest = EventRequest.build(
            name = "form_clicked",
            payload = EventRequestPayload(
                name = webForm.name
            )
        )
        apiManager.sendEvent(communication.urlPath, communication.id, eventRequest)
    }

    private fun sendWebFormReceivedEvent(webForm: WebForm) {
        // Send web form received event to CRM
        val communication = localRepository.ongoingCommunication
        val eventRequest = EventRequest.build(
            name = "form_received",
            payload = EventRequestPayload(
                name = webForm.name
            )
        )
        apiManager.sendEvent(communication.urlPath, communication.id, eventRequest)
    }

    private fun listenWebFormEvent(webForm: WebForm) {
        if (webForm.smartActionId == null || webForm.externalFormId.isNullOrEmpty() || webForm.signature.isNullOrEmpty()) {
            view.showWebFormLoadingError(R.string.ujet_chat_form_loading_failed_message)
            Logger.e(
                "web form error: one of the required value in FormMessage is empty: " +
                        "smartActionId: ${webForm.smartActionId}, externalFormId: ${webForm.externalFormId}, signature: ${webForm.signature}"
            )
            return
        }
        val formMessageReceivedEvent = mapOf<String, Any>(
            Pair("type", "form_message_received"),
            Pair("smart_action_id", webForm.smartActionId ?: -1),
            Pair("signature", webForm.signature ?: ""),
            Pair("external_form_id", webForm.externalFormId ?: ""),
        )
        val startTime = System.currentTimeMillis()
        var isTimeout = false
        webFormTimer = Timer()
        webFormTimerIsInProgress = true
        isWebFormTimerRunning = true
        //Total timeOut is 60s of getting event from host app and verifying URI, and starts the time as soon as user press on the web form card
        webFormTimer?.schedule(object : TimerTask() {
            override fun run() {
                isTimeout = true
                webFormScope?.cancel()
                MainLooper.post {
                    view.showWebFormLoadingError(R.string.ujet_chat_form_time_out_message)
                    Logger.e("web form error: timed out")
                }
                webFormTimer = null // Make timer null after the task completes
                webFormTimerIsInProgress = false
            }
        }, GET_EVENT_AND_VERIFY_API_TIMEOUT) // 60 seconds

        UjetInternal.ujetWebFormListener?.ujetWebFormDidReceive(formMessageReceivedEvent, object : UjetWebFormCallback {
            override fun onEvent(event: Map<String, Any?>) {
                if (isTimeout) {
                    return
                }
                Logger.d("web form: FormDataEvent received: $event")
                webFormTimer?.cancel()
                webFormTimer = null
                isWebFormTimerRunning = false
                if (event.isEmpty()) {
                    MainLooper.post {
                        view.showWebFormLoadingError(R.string.ujet_chat_form_loading_failed_message)
                        Logger.e("web form error: FormDataEvent is empty")
                    }
                    return
                }
                val elapsedTime = System.currentTimeMillis() - startTime
                val remainingTime = (GET_EVENT_AND_VERIFY_API_TIMEOUT - elapsedTime).coerceAtLeast(0)
                webFormJob = Job()
                webFormJob?.let {
                    webFormScope = CoroutineScope(Dispatchers.IO + it)
                }
                webFormScope?.launch {
                    try {
                        verifyUriWithinTimeLimit(remainingTime, event, webForm)
                    } catch (e: Throwable) {
                        MainLooper.post {
                            if (e is TimeoutCancellationException) {
                                view.showWebFormLoadingError(R.string.ujet_chat_form_time_out_message)
                            } else {
                                view.showWebFormLoadingError(R.string.ujet_chat_form_loading_failed_message)
                            }
                            Logger.e("web form error: ${e.message}")
                        }
                    } finally {
                        webFormJob?.cancelAndJoin()
                        clearResources()
                    }
                }
            }

            override fun onError() {
                if (isTimeout) {
                    return
                }
                clearResources()
                MainLooper.post {
                    view.showWebFormLoadingError(R.string.ujet_chat_form_loading_failed_message)
                    Logger.e("web form error: received error from host app")
                }
            }
        })
    }

    private suspend fun verifyUriWithinTimeLimit(remainingTime: Long, event: Map<String, Any?>, webForm: WebForm) {
        //Giving a timeout of remaining time of 60 seconds to verify URI
        withTimeout(remainingTime) {
            val dataMap = event["data"] as? Map<*, *>
            val uri = dataMap?.get("uri") as? String
            val gson = GsonBuilder().disableHtmlEscaping().create()
            val verifyApiRequestEvent = gson.toJson(event)
            val result = apiManager.verifyWebFormUri(verifyApiRequestEvent)
            if (result.isSuccess) {
                MainLooper.post {
                    uri?.let { view.loadUrlInWebForm(it) }
                    sendWebFormReceivedEvent(webForm)
                }
            } else {
                MainLooper.post {
                    view.showWebFormLoadingError(R.string.ujet_chat_form_loading_failed_message)
                    Logger.e("web form error: URI verification failed for FormDataEvent: $event  \nFormDataEvent after serialization: $verifyApiRequestEvent")
                }
            }
        }
    }

    private fun clearResources() {
        webFormTimer?.cancel()
        webFormTimer = null
        isWebFormTimerRunning = false
        webFormTimerIsInProgress = false
    }

    override fun clearWebFormResources() {
        if (webFormTimerIsInProgress) {
            try {
                webFormTimerIsInProgress = false
                webFormScope?.cancel()
                if (isWebFormTimerRunning) {
                    webFormTimer?.cancel()
                    webFormTimer = null
                }
            } catch (e: Throwable) {
                Logger.e(e.message.toString())
            }
        }
    }

    override fun clearDownloadChatTranscriptResources() {
        downloadTranscriptScope?.cancel()
    }


    override fun initiateChatTranscriptPdfGeneration(cacheDirectory: File, isInlineButton: Boolean) {
        if (chatTranscriptDownloadIsInProgress) {
            updateChatTranscriptDownloadButtonUI(false, false, isInlineButton)
            return
        }
        chatTranscriptDownloadIsInProgress = true
        val chat = localRepository.ongoingCommunication
        apiManager.generateChatPdfTranscript(chat.id, object : ApiCallback<ChatTranscriptResponse> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<ChatTranscriptResponse>) {
                when {
                    response.code() == HttpURLConnection.HTTP_OK && response.body()?.status == CHAT_TRANSCRIPT_PDF_GENERATION_STATUS_INITIATED -> {
                        response.body()?.chatTranscriptId?.let {
                            view.saveChatTranscriptId(it)
                            downloadChatTranscriptPdf(cacheDirectory, it, isInlineButton)
                        }
                    }

                    else -> {
                        Logger.e("Chat Transcript PDF Generation error ${response.code()}: ${response.message()}")
                        chatTranscriptDownloadIsInProgress = false
                        val errorMessageResponse = Uson().deserialize(response.rawBody(), MessageResponse::class.java)
                        when (errorMessageResponse?.errorCode) {
                            CHAT_TRANSCRIPT_MAX_REQUEST_ERROR_CODE -> {
                                view.showChatTranscriptMaxRequestErrorDialog()
                                updateChatTranscriptDownloadButtonUI(false, false, isInlineButton)
                            }

                            else -> {
                                updateChatTranscriptDownloadButtonUI(false, true, isInlineButton)
                            }
                        }
                    }
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                chatTranscriptDownloadIsInProgress = false
                updateChatTranscriptDownloadButtonUI(false, true, isInlineButton)
            }
        })
    }

    override fun downloadChatTranscriptPdf(cacheDirectory: File, chatTranscriptId: Int, isInlineButton: Boolean) {
        downloadTranscriptScope = CoroutineScope(Dispatchers.IO + Job())
        chatTranscriptDownloadIsInProgress = true
        downloadTranscriptScope?.launch {
            for (attempt in 1..CHAT_TRANSCRIPT_MAX_RETRY_COUNT) {
                val response = apiManager.downloadChatPdfTranscript(cacheDirectory, chatTranscriptId)
                if (response.isSuccess && response.getOrNull()?.status == CHAT_TRANSCRIPT_PDF_DOWNLOAD_STATUS_FINISHED) {
                    view.openFilePicker(response.getOrNull()?.pdfFileName, response.getOrNull()?.pdfFile)
                    downloadTranscriptScope?.cancel()
                    downloadTranscriptScope = null
                    chatTranscriptDownloadIsInProgress = false
                    MainLooper.post {
                        updateChatTranscriptDownloadButtonUI(false, false, isInlineButton)
                    }
                    return@launch
                } else if (response.isFailure) {
                    chatTranscriptDownloadIsInProgress = false
                    MainLooper.post {
                        updateChatTranscriptDownloadButtonUI(false, true, isInlineButton)
                    }
                }
                if (attempt < CHAT_TRANSCRIPT_MAX_RETRY_COUNT) {
                    delay(CHAT_TRANSCRIPT_DELAY_INTERVAL) // Wait 3 seconds before the next attempt
                } else {
                    chatTranscriptDownloadIsInProgress = false
                    MainLooper.post {
                        updateChatTranscriptDownloadButtonUI(false, true, isInlineButton)
                    }
                }
            }
        }
    }

    private fun isBlockChatTerminationByEndUserEnabled(): Boolean {
        return configuration.blockChatTerminationByEndUser && !isPostSessionRequired()
    }

    override fun onContentCardButtonClicked(eventParams: HashMap<String, Any>?, cardTitle: String?, buttonTitle: String?) {
        Injection.provideUjetEventNotificationManager().contentCardButtonClicked(eventParams ?: hashMapOf())

        // Send click event to CRM
        val communication = localRepository.ongoingCommunication
        val eventRequest = EventRequest.build(
            name = "content_card_button_clicked",
            payload = EventRequestPayload(
                title = cardTitle,
                buttonTitle = buttonTitle
            )
        )
        apiManager.sendEvent(communication.urlPath, communication.id, eventRequest)
    }

    override fun requestChatHistory() {
        chatServiceInteractor.requestChatHistory(false)
    }

    private fun updateAssignedAgentViews(agent: Agent?, statusText: String?) {
        if (agent != null) {
            view.setAgentAvatar(agent.avatarUrl)
            view.displayChatConnected(agent.displayName)
        }
        if (statusText.isNullOrEmpty()) {
            val savedStatusText = view.getStatusText()
            if (savedStatusText?.isNotEmpty() == true) {
                view.displayCurrentState(savedStatusText)
            }
        } else {
            view.displayCurrentState(statusText)
        }
    }

    private fun updateChatTranscriptDownloadButtonUI(isDownloadProgressBarVisible: Boolean,
                                                     isDownloadErrorIconVisible: Boolean,
                                                     isInlineButton: Boolean) {
        view.updateChatTranscriptDownloadButtonUI(
            isDownloadProgressBarVisible = isDownloadProgressBarVisible,
            isDownloadErrorIconVisible = isDownloadErrorIconVisible,
            isInlineButton = isInlineButton
        )
    }

    private fun showNextScreen(isNewChatEnabled: Boolean) {
        when {
            localRepository.rateRepository.isRatable && !isPostSessionRequired() -> {
                when {
                    //Show survey / CSAT if enabled, otherwise finish flow
                    localRepository.rateRepository.rateTarget?.surveyEnabled == true -> {
                        //clear old csat rating config
                        localRepository.rateRepository.isRatable = false
                        localRepository.rateRepository.isSurveyUnanswered = true
                        view.showSurveyScreen(isNewChatEnabled)
                    }

                    localRepository.rateRepository.csatEnabled() -> view.showCsatRating(isNewChatEnabled)
                    else -> close(isNewChatEnabled)
                }
            }

            else -> close(isNewChatEnabled)
        }
    }

    private fun close(isNewChatEnabled: Boolean) {
        localRepository.rateRepository.clear()
        view.finish()

        //If new chat is enabled then redirect users to menu screen but when blockChatTerminationByEndUser
        // is set to true, skip it so that SDK is closed in that case
        if (isNewChatEnabled && !isBlockChatTerminationByEndUserEnabled()) {
            view.showMenus()
        }
    }

    private fun sendPsaTextMessage() {
        if (view.isActive) {
            val message = SmartActionManager.getInputText()
            if (!message.isNullOrEmpty()) {
                onChatMessageSend(message)
                SmartActionManager.clearInputText()
            }
        }
    }

    private fun saveStatusText(statusText: String) {
        view.saveStatusText(statusText)
    }

    private fun clearStatusText() {
        view.clearStatusText()
    }

    private fun updateEscalateButton(chat: Chat) {
        view.setEscalateEnabled(virtualAgentSettings?.allowSkipVirtualAgent == true && chat.getStatus() == ChatStatus.VaAssigned)
    }

    private fun updateCoBrowseButton(chat: Chat) {
        view.updateCoBrowseButton(chat.supportsCobrowse && chat.getStatus() == ChatStatus.Assigned)
    }

    private fun updatePhotoButton(chat: Chat) {
        view.setSendPhotoEnabled(
            chat.getStatus() in listOf(
                ChatStatus.Queued, ChatStatus.Assigned,
                ChatStatus.VaAssigned
            )
        )
    }

    private fun handleChatTransferState(state: TransferState) {
        val visible = when (state) {
            COLD_TRANSFER_INITIATED -> true
            TRANSFER_FINISHED -> false
            else -> null
        }
        view.updateTransferBanner(visible ?: return)
    }

    companion object {
        private const val CHAT_TRANSCRIPT_MAX_RETRY_COUNT = 5
        private const val CHAT_TRANSCRIPT_DELAY_INTERVAL = 3000L
        private const val CHAT_TRANSCRIPT_MAX_REQUEST_ERROR_CODE = "max_request"
        private const val CHAT_TRANSCRIPT_PDF_GENERATION_STATUS_INITIATED = "initiated"
        private const val CHAT_TRANSCRIPT_PDF_DOWNLOAD_STATUS_FINISHED = "finished"
        private const val VIDEO_FORMAT_WMV = "wmv"
        private const val VIDEO_FORMAT_AVI = "avi"
        private const val USER_STOPPED_TYPING_TIMEOUT = 5000L
        private const val GET_EVENT_AND_VERIFY_API_TIMEOUT = 60 * 1000L
    }
}
