package co.ujet.android.app.call.scheduled.timepicker

import android.os.Bundle
import android.view.*
import co.ujet.android.ui.widgets.UjetProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.R
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerContract.Presenter
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.internal.UjetInternal
import co.ujet.android.libs.materialcamera.util.Degrees
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.picker.Picker
import co.ujet.android.ui.picker.Picker.PickerItemClickListener
import co.ujet.android.ui.picker.PickerSettings.Builder
import java.util.*

/**
 * Dialog for pick time
 */
class ScheduleTimePickerFragment @Keep constructor() : BaseFragment(), ScheduleTimePickerContract.View {
    private var pickerView: Picker? = null
    private var nextButton: FancyButton? = null
    private var stateTextView: TextView? = null
    private var progressBar: UjetProgressBar? = null
    private var presenter: Presenter? = null
    private var deflectionType: String? = null
    private var cachedScheduleCallTimeSlots: Array<String?>? = null
    private var cachedPickerSelectedPosition = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        deflectionType = arguments?.getString(ARGS_DEFLECTION_TYPE)
        val context = context ?: return
        cachedScheduleCallTimeSlots = savedInstanceState?.getStringArray(CACHED_SCHEDULE_CALL_TIME_SLOTS)
        cachedPickerSelectedPosition = savedInstanceState?.getInt(UJET_PICKER_SELECTED_POSITION) ?: 0
        presenter = ScheduleTimePickerPresenter(
            context,
            ujetContext(),
            apiManager(),
            Injection.provideLocalRepository(context),
            this,
            Injection.provideUseCaseHandler(),
            Injection.provideGetSelectedMenuId(context),
            Injection.provideChooseLanguage(context),
            cachedScheduleCallTimeSlots ?: emptyArray(),
            cachedPickerSelectedPosition
        )
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putStringArray(CACHED_SCHEDULE_CALL_TIME_SLOTS, cachedScheduleCallTimeSlots)
        outState.putInt(UJET_PICKER_SELECTED_POSITION, cachedPickerSelectedPosition)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.ujet_fragment_single_choice, container, false)
        UjetViewStyler.styleFragmentBackground(ujetStyle(), view)
        pickerView = view.findViewById(R.id.picker)
        pickerView?.setOnClickItemPickerListener(
            object : PickerItemClickListener {
                override fun onItemClickPicker(which: Int, position: Int, valueResult: String) {
                    presenter?.updateItemAtIndex(position)
                    nextButton?.requestFocus()
                }

                override fun onCenterItemChanged(position: Int) {
                    cachedPickerSelectedPosition = position
                    presenter?.updateItemAtIndex(position)
                }

                override fun getLanguageCode(languageName: String?): String? {
                    return null
                }
            })
        val titleTextView = view.findViewById<TextView>(R.id.title_view)
        UjetViewStyler.overrideTypeface(ujetStyle(), titleTextView)
        UjetViewStyler.stylePrimaryText(ujetStyle(), titleTextView)
        titleTextView.text = getString(R.string.ujet_schedule_time_title)
        val descriptionTextView = view.findViewById<TextView>(R.id.description)
        UjetViewStyler.overrideTypeface(ujetStyle(), descriptionTextView)
        UjetViewStyler.styleSecondaryText(ujetStyle(), descriptionTextView)
        descriptionTextView.setText(R.string.ujet_schedule_time_description)
        nextButton = view.findViewById<FancyButton>(R.id.next_button)?.apply {
            isEnabled = false
            setText(getString(R.string.ujet_common_next))
            UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
            setOnClickListener {
                presenter?.confirmSelect()
            }
        }

        stateTextView = view.findViewById<TextView>(R.id.state)?.apply {
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }

        progressBar = view.findViewById<UjetProgressBar>(R.id.progressBar).apply {
            val spinnerColor = ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_gray_lighter)
            setColorFilter(BlendModeColorFilterCompat.createBlendModeColorFilterCompat(spinnerColor, BlendModeCompat.SRC_IN))
        }
        pickerView?.setNestedScrollable(true)
        pickerView?.registerGlobalLayoutListener(view.findViewById(R.id.main_container), titleTextView,
            descriptionTextView, nextButton, pickerView, Degrees.isLandscape(context)
        )
        setupNavigationBarMenu()
        return view
    }

    private fun setupNavigationBarMenu() {
        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_exit -> {
                    presenter?.onExitButtonClicked()
                    true
                }
                android.R.id.home -> {
                    presenter?.onBackButtonClicked()
                    true
                }
                else -> false
            }
        })
    }

    private fun handleKeyboardAccessibility() {
        view?.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            val menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                // Move focus to another view when Tab or Down is pressed
                pickerView?.requestFocus()
                true
            }, onEnter = {
                presenter?.onExitButtonClicked()
                true
            })
        }
        AccessibilityUtil.setupKeyboardAccessibility(navigateUpView, onDpadDown = {
            pickerView?.requestFocus()
            true
        }, onEnter = {
            presenter?.onBackButtonClicked()
            true
        })
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
        handleKeyboardAccessibility()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        pickerView?.clearResources()
        pickerView = null
        nextButton = null
        stateTextView = null
        progressBar = null
    }

    override fun showPhoneNumberInput(callCreateType: CallCreateType, scheduledTime: Date?) {
        FragmentHelper.show(
            this,
            PhoneNumberInputFragment.newInstance(callCreateType, deflectionType, scheduledTime),
            PhoneNumberInputFragment.TAG
        )
    }

    override fun showLoading() {
        stateTextView?.visibility = View.VISIBLE
        stateTextView?.setText(R.string.ujet_common_loading)
        progressBar?.visibility = View.VISIBLE
        pickerView?.visibility = View.INVISIBLE
    }

    override fun showError() {
        stateTextView?.visibility = View.VISIBLE
        stateTextView?.setText(R.string.ujet_common_error)
        progressBar?.visibility = View.INVISIBLE
        pickerView?.visibility = View.INVISIBLE
    }

    override fun showPicker(items: Array<String?>, defaultIndex: Int) {
        cachedScheduleCallTimeSlots = items
        val pickerUISettings = Builder()
            .withBackgroundColor(ujetStyle().pickerBackgroundColor)
            .withItems(items, defaultIndex)
            .build()
        pickerView?.let {
            it.setSettings(pickerUISettings, nextButton)
            UjetViewStyler.stylePickerView(ujetStyle(), it)
            it.visibility = View.VISIBLE
        }
        stateTextView?.visibility = View.INVISIBLE
        progressBar?.visibility = View.INVISIBLE
    }

    override fun enableButton() {
        nextButton?.isEnabled = true
    }

    override fun isActive() = isAdded

    override fun showMenuUpdated() {
        val activity = activity ?: return
        Toast.makeText(activity, R.string.ujet_menu_updated_alert, Toast.LENGTH_LONG).show()
    }

    override fun restart() {
        val activity = activity ?: return
        val intent = activity.intent
        activity.finish()
        if (activity is UjetActivity) {
            startActivity(intent)
        } else {
            UjetInternal.startUjetActivity()
        }
    }

    override fun back() {
        if (parentFragmentManager.backStackEntryCount > 1) {
            parentFragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    override fun finish() {
        activity?.finish()
    }

    override fun showCsatScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity)
        UjetCsatActivity.start(activity)
    }

    override fun showSurveyScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }

        ActivityHelper.finishAndRemoveTask(activity)
        UjetSurveyActivity.start(activity)
    }

    fun onKeyDown(keyCode: Int) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            presenter?.onBackButtonClicked()
        }
    }

    companion object {
        const val TAG = "ScheduleTimePickerFragment"
        private const val ARGS_DEFLECTION_TYPE = "schedule_call_deflection_type"
        private const val CACHED_SCHEDULE_CALL_TIME_SLOTS = "ujet_cached_schedule_call_time_slots"
        private const val UJET_PICKER_SELECTED_POSITION = "ujet_picker_selected_position"

        @JvmStatic
        fun newInstance(deflectionType: String?): ScheduleTimePickerFragment {
            val args = Bundle()
            args.putString(ARGS_DEFLECTION_TYPE, deflectionType)
            val scheduleTimePickerFragment = ScheduleTimePickerFragment()
            scheduleTimePickerFragment.arguments = args
            return scheduleTimePickerFragment
        }
    }
}
