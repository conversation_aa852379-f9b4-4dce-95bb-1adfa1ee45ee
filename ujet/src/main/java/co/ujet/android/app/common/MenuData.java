package co.ujet.android.app.common;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by <PERSON> on 2020/02/28.
 */
public class MenuData {
    private String menuId, menuName, menuPath, menuKey;

    public MenuData(String menuId, String menuName, String menuPath, String menuKey) {
        this.menuId = menuId;
        this.menuName = menuName;
        this.menuPath = menuPath;
        this.menuKey = menuKey;
    }

    @NonNull
    public String getMenuId() {
        return menuId;
    }

    @NonNull
    public String getMenuName() {
        return menuName;
    }

    @NonNull
    public String getMenuPath() {
        return menuPath;
    }

    @Nullable
    public String getMenuKey() {
        return menuKey;
    }
}
