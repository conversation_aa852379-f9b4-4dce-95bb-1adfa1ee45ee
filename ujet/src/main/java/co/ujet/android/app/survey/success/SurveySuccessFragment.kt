package co.ujet.android.app.survey.success

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import co.ujet.android.R
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.survey.SurveyListener
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.getDimensionPixelSize

class SurveySuccessFragment @Keep constructor() : BaseFragment(), SurveySuccessContract.View {
    private lateinit var presenter: SurveySuccessContract.Presenter
    private var message: String? = null
    private var exitButton: FancyButton? = null

    private var callback: SurveyListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = try {
            context as SurveyListener
        } catch (_: ClassCastException) {
            throw ClassCastException("$context should implement SurveyListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        message = arguments?.getString(EXTRA_MESSAGE)
        presenter = SurveySuccessPresenter(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.ujet_fragment_survey_success, container, false)
        view.setBackgroundColor(ujetStyle().primaryBackgroundColor)

        view.findViewById<TextView>(R.id.message).apply {
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
            text = message ?: getString(R.string.ujet_survey_sign_off_text)
        }

        exitButton = view.findViewById<FancyButton>(R.id.exit_button).apply {
            UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
            if (isLargeTextAccessibilityEnabled(view.context) && isLandscape(context)) {
                updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    bottomMargin = getDimensionPixelSize(context, co.ujet.android.ui.R.dimen.ujet_survey_thank_you_screen_button_bottom_margin)
                }
            }
            setOnClickListener { presenter.onExitClicked() }
        }
        if (isLandscape(context)) {
            view.findViewById<ImageView>(R.id.ujet_survey_thank_you_icon).apply {
                updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    topMargin = 0
                }
            }
            view.findViewById<ConstraintLayout>(R.id.main_container).apply {
                if (isLargeTextAccessibilityEnabled(context)) {
                    setPadding(0, getDimensionPixelSize(context, co.ujet.android.ui.R.dimen.ujet_survey_thank_you_screen_top_padding_large_text), 0, 0)
                } else {
                    setPadding(0, getDimensionPixelSize(context, co.ujet.android.ui.R.dimen.ujet_survey_thank_you_screen_top_padding), 0, 0)
                }

            }
        }
        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            if (menuItemSelected == R.id.ujet_menu_item_exit) {
                presenter.onExitClicked()
                true
            } else {
                false
            }
        })
        handleKeyboardAccessibility(view)
        return view
    }

    private fun handleKeyboardAccessibility(view: View) {
        view.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            val menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                // Move focus to another view when Tab or Down is pressed
                exitButton?.requestFocus()
                true
            }, onEnter = {
                presenter.onExitClicked()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(exitButton, onDpadUp = {
                menu?.requestFocus()
                true
            })
        }
    }

    override fun onDestroy() {
        callback = null
        exitButton = null
        super.onDestroy()
    }

    override fun isActive(): Boolean = isAdded

    override fun close() {
        callback?.onSurveySucceeded()
    }

    companion object {
        const val TAG = "SurveySuccessFragment"
        private const val EXTRA_MESSAGE = "extra_message"

        @JvmStatic
        fun newInstance(message: String?): SurveySuccessFragment {
            val successFragment = SurveySuccessFragment()
            val bundle = Bundle()
            bundle.putString(EXTRA_MESSAGE, message)
            successFragment.arguments = bundle
            return successFragment
        }
    }
}
