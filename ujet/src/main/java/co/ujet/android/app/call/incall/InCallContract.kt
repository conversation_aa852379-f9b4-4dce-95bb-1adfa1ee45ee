package co.ujet.android.app.call.incall

import androidx.fragment.app.FragmentManager
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.smartaction.data.SmartActionType
import co.ujet.android.data.model.CallDeflection
import co.ujet.android.data.model.CallDeflectionType
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI
import co.ujet.android.smartaction.ui.verification.BiometricsVerification

/**
 * Contract between [InCallFragment] [InCallPresenter]
 */
internal class InCallContract {
    internal interface View : BaseView {
        fun refreshInCallViews()
        fun setMute(isMute: Boolean)
        fun setSpeakerOn(isSpeakerOn: Boolean)
        fun setAgentAvatar(avatarUrl: String)
        fun showDisconnectCallButton()
        fun setTitleViewVisible(isVisible: Boolean)
        fun showAgentName(agentName: String)
        fun showMultipleCallers(firstAgentName: String)
        fun showConnectingMessage(showRecordingDesc: Boolean)
        fun showTransferringMessage()
        fun showVoicemailRecording()
        fun showChronometer(startedAt: Long)
        fun hideChronometer()
        fun stopChronometer()
        fun hide()
        fun finish()
        fun showCsatRating()
        fun showSurveyScreen()
        fun showMenus()
        fun closeCallDeflection()
        fun showCallDeflection(menuId: Int, callDeflection: CallDeflection)
        fun showErrorDialog()
        fun hideErrorDialog()
        fun showErrorMessage()
        fun showErrorMessage(message: String)
        fun showAfterHourMessage(message: String)
        fun isAfterHourMessageDialogPending(): Boolean
        fun showEmailForm()
        fun showEmailClient(email: String?, menuPath: String)
        fun showPhoneNumberInput(callCreateType: CallCreateType, phoneNumber: String)
        fun showScheduleTimePicker()
        fun showVoicemail(menuId: Int, voicemailReason: String)
        fun displayPendingSmartAction(biometricsVerification: BiometricsVerification)
        fun getCachedRecordingPermission(): String
        fun setEscalateActionAvailable(enabled: Boolean)
        fun showEscalatingMessage()
        fun setEscalateActionEnabled(enabled: Boolean)
        fun updateCoBrowseButton(isCoBrowseSupportedAndConnected: Boolean)
        fun getCoBrowseUI(): CoBrowseUI?
        fun getCoBrowseButtonView(): android.view.View?
        fun getMainFragmentManager(): FragmentManager?
        fun handleSmartActionsDuringScreenLock(smartActionType: SmartActionType)
        fun setEmptyViewProgressBarVisibility(visible: Boolean)
        fun setCallScreenViewsVisibility(visible: Boolean)
        fun showPreSessionSmartActions(menuId: Int)
        fun savePreferenceData(menuId: Int, voiceMailReason: String?)
        fun showRecordingConfirmation()
        fun saveRecordingPermissionStatus(recordingPermission: String)
        fun resumeCommunication()
    }

    internal interface Presenter : BasePresenter {
        fun onCallServiceUnavailable()
        fun onCallAfterHourMessageReceived(afterHourMessage: String?)
        fun onCallErrorMessageReceived(errorMessage: String?, failureReason: String?)
        fun onMuteClicked()
        fun onSpeakerClicked()
        fun onMinimizeClicked()
        fun onEndCallClicked()
        fun onCallDeflectionSelected(menuId: Int, type: CallDeflectionType, data: String?)
        fun onScreenUnlocked()
        fun onSmartActionCancelled()
        fun onCallAfterHoursDialogConfirmed()
        fun restartDeflectionTimer()
        fun cancelDeflectionTimer()
        fun stop()
        fun verify()
        fun onEscalateClicked()
        fun onConfirmationDialogClicked(isPermissionGranted: Boolean)
    }
}
