package co.ujet.android.app.call.deflection

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.data.model.CallDeflectionType

class CallDeflectionContract {
    interface View : BaseView {
        fun activate(layoutId: Int, name: String?, description: String?, deflectionType: CallDeflectionType)
        fun setResult(menuId: Int, type: CallDeflectionType, data: String?)
        fun close()
    }

    interface Presenter : BasePresenter {
        fun stayOnHold()
        fun selectDeflection(callDeflectionType: CallDeflectionType)
    }
}