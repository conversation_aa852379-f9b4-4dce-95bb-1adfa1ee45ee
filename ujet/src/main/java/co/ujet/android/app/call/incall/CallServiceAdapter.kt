package co.ujet.android.app.call.incall

import co.ujet.android.service.UjetCallService

class CallServiceAdapter internal constructor() : CallServiceInteractor {
    private var ujetCallService: UjetCallService? = null
    
    fun bind(ujetCallService: UjetCallService) {
        this.ujetCallService = ujetCallService
    }

    fun unbind() {
        ujetCallService = null
    }

    override fun signalEndCall(reason: String?) {
        ujetCallService?.endCall(reason)
    }

    override fun notifyEventData() {
        ujetCallService?.notifyEventData()
    }

    override fun stopCallService() {
        ujetCallService?.stopCallService()
    }

    override fun signalMute(): <PERSON><PERSON><PERSON> {
        return ujetCallService?.let {
            it.toggleMute()
            true
        } ?: false
    }

    override fun signalSpeaker(): Bo<PERSON>an {
        return ujetCallService?.let {
            it.toggleSpeaker()
            true
        } ?: false
    }

    override fun escalateCall() {
        ujetCallService?.escalateCall()
    }
}
