package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.RelativeLayout.LayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_DOCUMENT
import co.ujet.android.app.chat.ChatMediaUtil.hideLoadingAndShowDownloadIcon
import co.ujet.android.app.chat.ChatMediaUtil.onDownloadAttachmentIconClicked
import co.ujet.android.app.chat.ChatMediaUtil.updateDownloadAttachmentView
import co.ujet.android.commons.domain.chat.message.HumanAgentDocumentChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.FileUtil.formatFileSize
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.dpToPx
import co.ujet.android.ui.util.DesignUtil.getColor
import co.ujet.android.ui.util.StyleUtil
import java.io.File

class HumanAgentDocumentChatMessageViewHolder(
    adapter: ChatAdapterInteractor, parent: ViewGroup,
    activity: Activity, ujetStyle: UjetStyle,
) :
    DocumentChatMessageViewHolder(adapter, activity, ujetStyle, inflate(activity,
        parent, R.layout.ujet_view_chat_message_human_agent_document)) {

        fun bind(
            message: HumanAgentDocumentChatMessage,
            shouldShowAgentNames: Boolean,
            isGroupStart: Boolean,
            isGroupEnd: Boolean,
        ): View {
            val documentContainer: View = itemView.findViewById(R.id.document_container)
            val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
            val background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_virtual_agent)
            val borderColor = getColor(context, co.ujet.android.ui.R.color.ujet_gray_light)
            val cornerRadius = agentMessageStyle?.cornerRadius ?: dpToPx(context, 10).toString()
            val mediaFile = message.mediaFile
            hideLoadingAndShowDownloadIcon(itemView)
            updateDocumentContainerParams(R.id.download_attachment_icon)
            StyleUtil.applyDefaultStyle(
                context, ujetStyle, documentContainer, agentMessageStyle?.backgroundColor,
                cornerRadius, agentMessageStyle?.border, background,
                borderColor, null
            )
            StyleUtil.updateBackgroundStyle(
                documentContainer,
                agentMessageStyle?.backgroundColor,
                agentMessageStyle?.cornerRadius,
                agentMessageStyle?.border
            )
            val fileName: TextView = itemView.findViewById(R.id.file_name)
            UjetViewStyler.styleRemoteChatText(ujetStyle, fileName)
            itemView.findViewById<TextView>(R.id.file_description)?.let { fileDescription ->
                UjetViewStyler.styleRemoteChatText(ujetStyle, fileDescription)
                val cacheFile = mediaFile.cacheFilePath?.let { File(it) }
                val extension = cacheFile?.extension?.uppercase() ?: ""
                val fileSize = cacheFile?.let { formatFileSize(it) } ?: "0 B"
                val fileDescriptionText = "$extension ($fileSize)"
                fileDescription.text = fileDescriptionText
                UjetViewStyler.styleTertiaryText(ujetStyle, fileDescription)
                UjetViewStyler.overrideTypeface(ujetStyle, fileDescription)
            }

            setupDocumentViewCommonAttributes(itemView, message, message.agentName, shouldShowAgentNames,
                isGroupStart, isGroupEnd
            ) {
                updateDocumentContainerParams(R.id.progressBar)
                onDownloadAttachmentIconClicked(itemView, ujetStyle.colorPrimary,
                    adapter.getChatItemClickListener(), arrayListOf(mediaFile), adapterPosition, MEDIA_TYPE_DOCUMENT
                )
            }
            setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
            AccessibilityUtil.addChatUserRole(
                userRole = context.getString(R.string.ujet_chat_human_agent),
                mainContainer = documentContainer,
                message = fileName,
                documentType = context.getString(R.string.ujet_chat_type_document),
                timestamp = itemView.findViewById(R.id.timestamp),
                isDocument = true,
                isClickable = true,
                adapterPosition = adapterPosition
            )
            return itemView
        }

        fun updateDownloadAttachmentView(isDownloadSucceeded: Boolean) {
            updateDocumentContainerParams(R.id.download_attachment_icon)
            updateDownloadAttachmentView(itemView, ujetStyle, isDownloadSucceeded)
        }

        private fun updateDocumentContainerParams(startOfId: Int) {
            // update the layout params of the document container based on loading / download icon
            val documentContainer = itemView.findViewById<RelativeLayout>(R.id.document_container)
            documentContainer?.layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT).apply {
                addRule(RelativeLayout.START_OF, startOfId)
            }
        }
}
