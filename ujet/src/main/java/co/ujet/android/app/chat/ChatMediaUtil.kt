package co.ujet.android.app.chat

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Bitmap.createScaledBitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.graphics.drawable.StateListDrawable
import android.media.MediaMetadataRetriever
import android.media.ThumbnailUtils
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.DisplayMetrics
import android.util.Size
import android.view.View
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.core.graphics.ColorUtils
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.R
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.libs.graffiti.decoder.BitmapDecoder
import co.ujet.android.commons.libs.graffiti.decoder.DecodeOptions
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.IOUtil
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.data.repository.UploadRepository.Companion.THUMBNAIL_DESIRED_SIZE
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.extensions.setVisibility
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.textview.AutoResizeTextView.Companion.ELLIPSIS
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.dpToPx
import co.ujet.android.ui.util.StyleUtil.RESOURCE_NOT_FOUND
import co.ujet.android.ui.widgets.UjetProgressBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.util.Locale
import java.util.concurrent.TimeUnit
import kotlin.math.roundToInt

object ChatMediaUtil {
    const val GRID_VIEW_MAX_COLUMNS = 3
    const val THUMBNAIL_DESIRED_HEIGHT = 270
    const val GRID_SPACING_MARGIN = 10
    const val MEDIA_TYPE_IMAGE = "image"
    const val MEDIA_TYPE_PHOTO = "photo"
    const val MEDIA_TYPE_VIDEO = "video"
    const val MEDIA_TYPE_AUDIO = "audio"
    const val MEDIA_TYPE_DOCUMENT = "document"
    const val MEDIA_TYPE_DOCUMENT_TEXT = "text"
    const val MEDIA_TYPE_APPLICATION = "application"
    private const val CACHE_DIRECTORY_NAME = "ujetFileAttachmentsCache"
    private const val DOWNLOAD_ATTACHMENT_DELAY = 3000L
    /* Image sizes for portrait (240, 300) and landscape (296, 296) and video sizes for portrait (240, 300)
    * and landscape  (296, 166), it is adjusted to pixel phone with 1080dp width and 240 is converted to
    * 412 and using that calculations, adjusted all the sizes accordingly and used percentage of screen
    * width and height so that it aligns well with all devices of screen sizes. For reference, Image sizes
    * for portrait converted to (412, 515) and landscape (508, 508) and video sizes for portrait (412, 515)
    * and landscape  (508, 285). When you convert them to percentage based on screen sizes, it is as follows,
    * 412 is 38.15% of 1080, 515 is 47.69% of 1080, 508 is 47.04% and 285 is 26.39% of 1080
    */
    private const val MEDIA_ATTACHMENT_PORTRAIT_MAX_WIDTH_PERCENTAGE = 0.3815
    private const val MEDIA_ATTACHMENT_PORTRAIT_MAX_HEIGHT_PERCENTAGE = 0.4769
    private const val MEDIA_ATTACHMENT_LANDSCAPE_MAX_WIDTH_PERCENTAGE = 0.4704
    private const val VIDEO_ATTACHMENT_LANDSCAPE_MAX_HEIGHT_PERCENTAGE = 0.2639
    private const val VIEW_START_MARGIN = 28
    private const val VIEW_MARGIN_END = 18

    fun initializeDownloadAttachmentIconView(
        downloadAttachmentView: ImageView?,
        ujetStyle: UjetStyle,
        onDownloadIconClicked: () -> Unit = {},
    ) {
        downloadAttachmentView?.apply {
            showDownloadIcon(downloadAttachmentView, ujetStyle)
            setOnClickListener {
                onDownloadIconClicked()
            }
        }
    }

    fun onDownloadAttachmentIconClicked(
        itemView: View, filterColor: Int, listener: ChatAdapter.ChatItemClickListener?,
        mediaFiles: ArrayList<MediaFile>, adapterPosition: Int, mediaType: String,
    ) {
        val progressBar = getDownloadProgressBar(itemView, filterColor)
        val downloadAttachmentView = itemView.findViewById<ImageView>(R.id.download_attachment_icon)
        // Hide download icon and replace it with loading icon
        updateDownloadIconVisibility(downloadAttachmentView, progressBar, false)
        listener?.onDownloadIconClicked(mediaFiles, adapterPosition, mediaType)
    }

    fun updateDownloadAttachmentView(
        itemView: View,
        ujetStyle: UjetStyle,
        isDownloadSucceeded: Boolean,
    ) {
        hideLoadingAndShowDownloadIcon(itemView)
        val downloadAttachmentView = itemView.findViewById<ImageView>(R.id.download_attachment_icon)
        // Change download icon to success /failure icon
        updateDownloadIcon(downloadAttachmentView, ujetStyle, isDownloadSucceeded)
    }

    fun hideLoadingAndShowDownloadIcon(itemView: View) {
        val downloadAttachmentView = itemView.findViewById<ImageView>(R.id.download_attachment_icon)
        val downloadProgressBar = itemView.findViewById<UjetProgressBar>(R.id.progressBar)
        // Hide loading icon and replace it with download icon
        updateDownloadIconVisibility(downloadAttachmentView, downloadProgressBar, true)
    }

    fun ellipsizeFileName(fileName: String, width: Int, textView: TextView): String {
        val extension = fileName.substringAfterLast('.', "")
        val dotIndex = fileName.lastIndexOf(extension) - 1
        val lastCharWithDot = fileName.substring(dotIndex - 1, dotIndex + 1)
        var name = fileName.substring(0, dotIndex)

        if (textView.paint.measureText(fileName) <= width) {
            return fileName // No need to ellipsize
        }

        var truncatedText = name + ELLIPSIS + lastCharWithDot + extension
        // Adjust the text until it can be fit into available space in the screen
        while (textView.paint.measureText(truncatedText) > width && name.isNotEmpty()) {
            name = name.substring(0, name.length - 1)
            truncatedText = name + ELLIPSIS + lastCharWithDot + extension
        }

        return truncatedText
    }

    private fun updateDownloadIcon(
        downloadAttachmentView: ImageView?, ujetStyle: UjetStyle, isDownloadSucceeded: Boolean,
    ) {
        downloadAttachmentView?.let { attachmentView ->
            val context = attachmentView.context
            // Clear previous download image view
            attachmentView.imageTintList = null
            attachmentView.background = null
            attachmentView.setImageResource(0)
            val downloadIconWidth = attachmentView.width
            val downloadIconHeight = attachmentView.height
            attachmentView.setPadding(0, 0, 0, 0) // clear padding
            val downloadAttachmentResourceId = when {
                isDownloadSucceeded && ujetStyle.isDarkModeEnabled -> R.drawable.ujet_download_attachment_success_dark
                isDownloadSucceeded -> R.drawable.ujet_download_attachment_success
                ujetStyle.isDarkModeEnabled -> R.drawable.ujet_download_attachment_error_dark
                else -> R.drawable.ujet_download_attachment_error
            }
            attachmentView.setImageResource(downloadAttachmentResourceId)
            attachmentView.updateLayoutParams {
                // We are using match parent for document_container for documents and views auto adjust
                // its width when switching download icon with success / failure case and to avoid it,
                // use same size as download icon. It wont affect media type views.
                width = downloadIconWidth
                height = downloadIconHeight
            }
            MainLooper.postDelayed({
                // Replace success / failure icon with download icon after 3 seconds
                showDownloadIcon(attachmentView, ujetStyle)
            }, DOWNLOAD_ATTACHMENT_DELAY)
        }
    }

    private fun updateDownloadIconVisibility(
        downloadAttachmentView: ImageView?, progressBar: UjetProgressBar?, visible: Boolean,
    ) {
        downloadAttachmentView?.setVisibility(visible, View.VISIBLE, View.GONE)
        progressBar?.setVisibility(!visible, View.VISIBLE, View.GONE)
    }

    private fun getDownloadProgressBar(itemView: View, filterColor: Int): UjetProgressBar? {
        return itemView.findViewById<UjetProgressBar>(R.id.progressBar)?.apply {
            setColorFilter(
                BlendModeColorFilterCompat.createBlendModeColorFilterCompat(filterColor, BlendModeCompat.SRC_IN)
            )
        }
    }

    fun getMediaDuration(context: Context?, mediaFile: File?): String {
        val retriever = MediaMetadataRetriever()
        var duration = ""
        try {
            val mediaUri = Uri.fromFile(mediaFile)
            retriever.setDataSource(context, mediaUri)
            val time = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLong() ?: 0
            duration = getDurationString(time)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            retriever.release()
        }
        return duration
    }

    fun loadBitmapFromCacheFile(context: Context, lifecycleOwner: LifecycleOwner, mediaFiles: ArrayList<MediaFile>,
                                onBitmapReady: (ArrayList<Bitmap?>?) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            val isMultiMediaEnabled = mediaFiles.size > 1
            val bitmapList: ArrayList<Bitmap?> = ArrayList()
            mediaFiles.forEach { mediaFile ->
                val cacheFile = mediaFile.cacheFilePath?.let { File(it) }
                val bitmap: Bitmap? = when (val fileType = mediaFile.type) {
                    MediaFile.Type.Photo -> {
                        if (isMultiMediaEnabled) {
                            if (cacheFile != null) {
                                val itemWidth = getGridItemWidth(context, mediaFiles)
                                decodeBitmapFromCacheFile(cacheFile, itemWidth, itemWidth)
                            } else {
                                null
                            }
                        } else {
                            val (maxWidth, maxHeight) = getThumbnailMaxDimensions(context, fileType)
                            // Adjust the thumbnail according to design requirements
                            decodeSampledBitmapFromFile(mediaFile.cacheFilePath, maxWidth, maxHeight)
                        }
                    }
                    MediaFile.Type.Video -> {
                        try {
                            if (cacheFile != null) {
                                val (maxWidth, maxHeight) = getThumbnailMaxDimensions(context, fileType)
                                createVideoThumbnail(cacheFile, maxWidth, maxHeight)
                            } else {
                                null
                            }
                        } catch (_: Exception) {
                            null
                        }
                    }
                    else -> null
                }
                bitmapList.add(bitmap)
            }
            onBitmapReady(bitmapList)
        }
    }

    private fun createVideoThumbnail(cacheFile: File, maxWidth: Int, maxHeight: Int): Bitmap? {
        val bitmap = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ThumbnailUtils.createVideoThumbnail(
                cacheFile, Size(
                    maxWidth, maxHeight
                ), null
            )
        } else {
            ThumbnailUtils.createVideoThumbnail(
                cacheFile.absolutePath,
                MediaStore.Images.Thumbnails.MICRO_KIND
            )
        }
        return bitmap?.let {
            createScaledBitmap(it, maxWidth, maxHeight, true)
        }
    }

    fun updateMediaImageView(
        viewHolder: AttachmentsAdapter.ImageViewHolder, mediaFile: MediaFile, mediaBitmap: Bitmap?,
        imageRadius: Float, adapterPosition: Int, timestamp: TextView, isMultiMediaEnabled: Boolean = false,
        itemWidth: Int, isVideo: Boolean
    ) {
        viewHolder.mediaImageView?.let { view ->
            val context = view.context
            mediaBitmap?.let { bitmap ->
                val (bitmapWidth, bitmapHeight) = if (isMultiMediaEnabled) {
                    Pair(itemWidth, itemWidth)
                } else {
                    Pair(bitmap.width, bitmap.height)
                }
                view.updateLayoutParams {
                    height = bitmapHeight
                    width = bitmapWidth
                }
                // Apply rounded corners to bitmap image
                val decoder = BitmapDecoder(DecodeOptions())
                val roundedBitmap = if (isMultiMediaEnabled) {
                    decoder.getRoundedCornerBitmap(bitmap, imageRadius.toInt())
                } else {
                    decoder.getBitmapWithLimitedRoundedCorners(bitmap, imageRadius)
                }
                view.setImageBitmap(roundedBitmap)
            } ?: run {
                // When we failed to create thumbnail, use default icon to show in chat UI.
                val fileType = mediaFile.type
                val resourceId = when (fileType) {
                    MediaFile.Type.Photo -> {
                        co.ujet.android.commons.R.drawable.ujet_file_image
                    }
                    MediaFile.Type.Video -> {
                        co.ujet.android.commons.R.drawable.ujet_file_video
                    }
                    else -> RESOURCE_NOT_FOUND
                }
                if (resourceId != RESOURCE_NOT_FOUND) {
                    fileType?.let {
                        val (bitmapWidth, bitmapHeight) = if (isMultiMediaEnabled) {
                            Pair(itemWidth, itemWidth)
                        } else {
                            Pair(THUMBNAIL_DESIRED_SIZE, THUMBNAIL_DESIRED_HEIGHT)
                        }
                        view.updateLayoutParams {
                            height = bitmapHeight
                            width = bitmapWidth
                        }
                    }
                    viewHolder.mediaImageView.scaleType = ImageView.ScaleType.FIT_XY
                    view.setImageResource(resourceId)
                }
            }
            view.contentDescription = mediaFile.filename
            if (isVideo) {
                AccessibilityUtil.addChatUserRole(
                    userRole = context.getString(R.string.ujet_chat_human_agent),
                    mainContainer = viewHolder.itemView,
                    timestamp = timestamp,
                    isVideo = true,
                    isClickable = true,
                    videoType = context.getString(R.string.ujet_chat_type_video),
                    adapterPosition = adapterPosition
                )
            } else {
                AccessibilityUtil.addChatUserRole(
                    userRole = context.getString(R.string.ujet_chat_human_agent),
                    mainContainer = viewHolder.itemView,
                    timestamp = timestamp,
                    isImage = true,
                    isClickable = true,
                    imageType = context.getString(R.string.ujet_chat_type_image),
                    adapterPosition = adapterPosition
                )
            }
        }
    }

    fun getGridItemWidth(context: Context, mediaFiles: ArrayList<MediaFile>): Int {
        // Get screen width and calculate size per item so that we have two rows and three columns
        val displayMetrics: DisplayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val maxColumns = if (mediaFiles.size >= GRID_VIEW_MAX_COLUMNS) {
            GRID_VIEW_MAX_COLUMNS
        } else {
            mediaFiles.size % GRID_VIEW_MAX_COLUMNS
        }
        val gridSpacingMargin = dpToPx(context, GRID_SPACING_MARGIN).toInt()
        val horizontalGridSpacing = gridSpacingMargin * (maxColumns)
        // We have 28dp margin at the start, 18dp at the end of the chat message view, horizontal grid
        // spacing between grid items, download icon sizes to consider
        val viewStartMargin = dpToPx(context, VIEW_START_MARGIN).toInt()
        val viewEndMargin = dpToPx(context, VIEW_MARGIN_END).toInt()
        val additionalMarginWidthPx = viewStartMargin + viewEndMargin + horizontalGridSpacing + getDownloadIconSize(context)
        return if (isLandscape(context)) {
            val screenHeight = context.resources.displayMetrics.heightPixels / 2
            // subtract additional margin height
            screenHeight - viewStartMargin
        } else {
            (screenWidth - additionalMarginWidthPx) / maxColumns
        }
    }

    fun updateRecyclerViewParams(recyclerView: RecyclerView, mediaFiles: ArrayList<MediaFile>, itemWidth: Int) {
        val itemCount = mediaFiles.size
        val maxColumns = if (itemCount >= GRID_VIEW_MAX_COLUMNS) {
            GRID_VIEW_MAX_COLUMNS
        } else {
            itemCount % GRID_VIEW_MAX_COLUMNS
        }
        val gridSpacingMargin = dpToPx(recyclerView.context, GRID_SPACING_MARGIN).toInt()
        val horizontalGridSpacing = gridSpacingMargin * (maxColumns)
        val rows = (itemCount + GRID_VIEW_MAX_COLUMNS - 1) / GRID_VIEW_MAX_COLUMNS
        val verticalGridSpacing = gridSpacingMargin * (rows - 1)
        when {
            itemCount > 1 -> {
                val params = recyclerView.layoutParams
                params?.width = (itemWidth * maxColumns) + horizontalGridSpacing
                params?.height = (itemWidth * rows) + verticalGridSpacing
                recyclerView.layoutParams = params
            }
            itemCount == 1 -> {
                // Set recycler view width and height to match with bitmap so that download icon
                // is moved accordingly in the UI.
                updateSingleImageView(
                    recyclerView,
                    THUMBNAIL_DESIRED_SIZE,
                    THUMBNAIL_DESIRED_SIZE
                )
            }
        }
    }

    fun updateSingleImageView(recyclerView: RecyclerView, maxWidth: Int, maxHeight: Int) {
        val context = recyclerView.context
        val screenWidth = context.resources.displayMetrics.widthPixels
        val gridSpacingMargin = dpToPx(context, GRID_SPACING_MARGIN).toInt()
        val additionalMarginWidthPx = dpToPx(context, VIEW_START_MARGIN).toInt() +
                dpToPx(context, VIEW_MARGIN_END).toInt() + gridSpacingMargin + getDownloadIconSize(context)
        val currentWidthPx = additionalMarginWidthPx + maxWidth
        val imageWidth = if (screenWidth - currentWidthPx < 0) {
            screenWidth - additionalMarginWidthPx
        } else {
            maxWidth
        }
        val params = recyclerView.layoutParams
        params.width = imageWidth + gridSpacingMargin
        params.height = maxHeight
        recyclerView.layoutParams = params
    }

    private fun getDownloadIconSize(context: Context): Int {
        return getAdjustedViewSize(context, 0.087)
    }

    private fun getAdjustedViewSize(context: Context, viewSizeInPercentage: Double): Int {
        return (context.resources.displayMetrics.widthPixels * viewSizeInPercentage).toInt()
    }

    fun getCacheDirectory(context: Context): File {
        return File(context.cacheDir, CACHE_DIRECTORY_NAME)
    }

    private fun showDownloadIcon(downloadAttachmentView: ImageView, ujetStyle: UjetStyle) {
        downloadAttachmentView.apply {
            val context = this.context
            background = getRoundedRectangleDrawable(context, ujetStyle)
            setImageResource(co.ujet.android.ui.R.drawable.ujet_download_attachment_icon)
            // Apply image tint color to view it in light and dark mode accordingly
            val tintColorId = if (ujetStyle.isDarkModeEnabled) {
                co.ujet.android.ui.R.color.ujet_attachment_download_icon_dark
            } else {
                co.ujet.android.ui.R.color.ujet_attachment_download_icon
            }
            val tintColor = ContextCompat.getColor(context, tintColorId)
            downloadAttachmentView.imageTintList = getImageTintColor(tintColor)
            val paddingInPx = dpToPx(context, 10).toInt()
            downloadAttachmentView.setPadding(paddingInPx, paddingInPx, paddingInPx, paddingInPx)
            updateLayoutParams {
                width = WRAP_CONTENT
                height = WRAP_CONTENT
            }
        }
    }

    private fun getThumbnailMaxDimensions(context: Context, type: MediaFile.Type?): Pair<Int, Int> {
        return when (type) {
            MediaFile.Type.Photo -> {
                if (isLandscape(context)) {
                    Pair(getMediaAttachmentLandscapeMaxWidth(context), getMediaAttachmentLandscapeMaxWidth(context))
                } else {
                    Pair(getMediaAttachmentPortraitMaxWidth(context), getMediaAttachmentPortraitMaxHeight(context))
                }
            }
            else -> {
                if (isLandscape(context)) {
                    Pair(getMediaAttachmentLandscapeMaxWidth(context), getVideoAttachmentLandscapeMaxHeight(context))
                } else {
                    Pair(getMediaAttachmentPortraitMaxWidth(context), getMediaAttachmentPortraitMaxHeight(context))
                }
            }
        }
    }

    private fun getMediaAttachmentPortraitMaxWidth(context: Context): Int {
        return getAdjustedViewSize(context, MEDIA_ATTACHMENT_PORTRAIT_MAX_WIDTH_PERCENTAGE)
    }

    private fun getMediaAttachmentPortraitMaxHeight(context: Context): Int {
        return getAdjustedViewSize(context, MEDIA_ATTACHMENT_PORTRAIT_MAX_HEIGHT_PERCENTAGE)
    }

    private fun getMediaAttachmentLandscapeMaxWidth(context: Context): Int {
        return getAdjustedViewSize(context, MEDIA_ATTACHMENT_LANDSCAPE_MAX_WIDTH_PERCENTAGE)
    }

    private fun getVideoAttachmentLandscapeMaxHeight(context: Context): Int {
        return getAdjustedViewSize(context, VIDEO_ATTACHMENT_LANDSCAPE_MAX_HEIGHT_PERCENTAGE)
    }

    private fun getDurationString(durationMs: Long): String {
        val minutes = TimeUnit.MILLISECONDS.toMinutes(durationMs)
        val seconds = TimeUnit.MILLISECONDS.toSeconds(durationMs)
        val format = if (minutes >= 10) {
            "%02d:%02d"
        } else {
            "%01d:%02d"
        }
        return String.format(
            Locale.getDefault(), format,
            minutes,
            seconds - TimeUnit.MINUTES.toSeconds(minutes)
        )
    }

    fun getImageTintColor(tintColor: Int): ColorStateList {
        val states = arrayOf(intArrayOf(-android.R.attr.state_enabled), intArrayOf())
        val imageColors = intArrayOf(
            ColorUtils.setAlphaComponent(tintColor, (255 * 0.3).toInt()), tintColor
        )
        return ColorStateList(states, imageColors)
    }

    private fun getRoundedRectangleDrawable(
        context: Context, ujetStyle: UjetStyle,
    ): StateListDrawable {
        val strokePx = context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
        val cornerRadiusPx = context.resources.getDimensionPixelSize(
            co.ujet.android.ui.R.dimen.ujet_download_attachment_icon_corner_radius).toFloat()
        val normalDrawable = DesignUtil.createRoundedRectangleDrawable(
            ujetStyle.primaryBackgroundColor, ujetStyle.pickerSeparatorColor, strokePx, cornerRadiusPx
        )
        val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(
            ujetStyle.textFocusBackgroundColor, ujetStyle.pickerSeparatorColor, strokePx, cornerRadiusPx
        )
        return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
    }

    private suspend fun decodeSampledBitmapFromFile(filePath: String?, maxWidth: Int, maxHeight: Int): Bitmap? {
        if (filePath.isNullOrEmpty()) {
            return null
        }

        return withContext(Dispatchers.IO) {
            // Runs on background thread to avoid ANR error
            try {
                // First, decode with inJustDecodeBounds=true to check dimensions
                val options = Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeFile(filePath, options)

                // Calculate the inSampleSize
                options.inSampleSize = calculateInSampleSize(options, maxWidth, maxHeight)

                // Decode bitmap with inSampleSize set
                options.inJustDecodeBounds = false
                BitmapFactory.decodeFile(filePath, options)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private suspend fun decodeBitmapFromCacheFile(file: File, resizeWidth: Int, resizeHeight: Int): Bitmap? {
        return withContext(Dispatchers.IO) {
            // Runs on background thread to avoid ANR error
            try {
                val fileInputStream = FileInputStream(file)
                val fileDescriptor = fileInputStream.fd
                val options = Options()
                if (resizeWidth > 0 && resizeHeight > 0) {
                    options.inJustDecodeBounds = true
                    BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)
                    options.inSampleSize = calculateInSampleSize(options, resizeWidth, resizeHeight)
                }
                options.inJustDecodeBounds = false
                val bitmap = BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)
                IOUtil.closeQuietly(fileInputStream)
                BitmapDecoder(DecodeOptions()).rotate(file, bitmap)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private fun calculateInSampleSize(options: Options, reqWidth: Int?, reqHeight: Int?): Int {
        // Raw height and width of image
        val (height: Int, width: Int) = options.run { outHeight to outWidth }
        var inSampleSize = 1

        if ((reqWidth != null && reqHeight != null) && (height > reqHeight || width > reqWidth)) {
            // Calculate ratios of height and width to requested height and width
            val heightRatio = (height.toFloat() / reqHeight.toFloat()).roundToInt()
            val widthRatio = (width.toFloat() / reqWidth.toFloat()).roundToInt()

            // Choose the smallest ratio as inSampleSize value, this will guarantee a final image with
            // dimensions greater than or equal to the requested height and width
            inSampleSize = if (heightRatio < widthRatio) heightRatio else widthRatio
        }
        return inSampleSize
    }
}
