package co.ujet.android.app.call.scheduled.call

import android.content.Context
import co.ujet.android.R.string
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.app.call.scheduled.call.ScheduledCallContract.Presenter
import co.ujet.android.app.call.scheduled.call.ScheduledCallContract.View
import co.ujet.android.common.TaskCallback
import co.ujet.android.common.util.LocaleUtil
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.constant.CallFailReason.EndUserCanceled
import co.ujet.android.data.constant.CallStatus.*
import co.ujet.android.data.constant.KVSType.IncomingCallCanceled
import co.ujet.android.data.model.Call
import co.ujet.android.libs.logger.Logger
import java.net.HttpURLConnection
import java.util.Date

/**
 * Presenter for [ScheduledCallDialogFragment]
 */
internal class ScheduledCallPresenter(
    private val context: Context,
    private val view: View,
    private val localRepository: LocalRepository,
    private val apiManager: ApiManager
) : Presenter {
    private var canceling = false

    override fun start() {
        checkCallAvailable()
    }

    override fun onKeepClicked() {
        if (view.isActive) {
            view.finish()
        }
    }

    override fun onCancelClicked() {
        if (view.isActive) {
            view.enableButtons(false)
            view.clearCallPreferenceData()
        }
        cancelCall()
    }

    override fun onConnectCallReceived(callId: Int) {
        val call = localRepository.call
        if (call != null && call.id == callId) {
            if (view.isActive) {
                view.finish()
            }
        }
    }

    private fun checkCallAvailable() {
        val call = localRepository.call
        if (call == null) {
            if (localRepository.getKVS(IncomingCallCanceled) != null) {
                // Check the case IncomingCallPresenter canceled the call and resumed to RescheduledCallDialogFragment
                // Ignore and wait actions from IncomingCallPresenter
                rescheduleCall()
            } else {
                clearAndRestart()
            }
            return
        }
        apiManager.getCall(call.id, object : ApiCallback<Call> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<Call>) {
                if (response.code() == HttpURLConnection.HTTP_OK) {
                    val responseCall = response.body()
                    localRepository.call = responseCall
                    if (responseCall?.getStatus() == Scheduled || responseCall?.getStatus() == Queued
                        || responseCall?.getStatus() == Assigned) {
                        displayInformation()
                    } else if (responseCall?.getStatus() == Connected || responseCall?.getStatus() == Connecting
                        || responseCall?.getStatus() == VaConnected || responseCall?.getStatus() == VaAssigned) {
                        if (view.isActive) {
                            view.showInCall()
                        }
                    } else {
                        cancelCall()
                    }
                } else {
                    cancelCall()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                cancelCall()
            }
        })
    }

    private fun displayInformation() {
        val call = localRepository.call
        val resourceString = context.getString(string.ujet_scheduled_call_content)
        val menuString = call?.selectedMenuPath
        val scheduledDate = TimeUtil.parseTime(call?.scheduledAt)
        val timeString = TimeUtil.getHourInFormat(scheduledDate, LocaleUtil.getCurrentLocale(context))
        val dateString = TimeUtil.getDayDayDifferenceString(context, TimeUtil.getDayDifference(Date(), scheduledDate))
        val content = String.format(
            resourceString,
            menuString,
            "$timeString $dateString"
        )
        if (view.isActive) {
            view.displayScheduledTime(content)
            view.enableButtons(true)
        }
    }

    private fun cancelCall() {
        if (canceling) {
            Logger.d("Already canceling")
            return
        }
        canceling = true
        Logger.d("cancel call")
        val call = localRepository.call
        if (call == null) {
            clearAndRestart()
            return
        }
        apiManager.updateCall(
            call.id,
            Failed, EndUserCanceled, "The scheduled call is canceled by end user",
            object : TaskCallback<Call> {
                override fun onTaskSuccess(result: Call) {
                    clearAndRestart()
                }

                override fun onTaskFailure() {
                    clearAndRestart()
                }
            })
    }

    private fun clearAndRestart() {
        Logger.d("clear and restart")
        localRepository.call = null
        localRepository.preferredVoipProvider = null
        if (view.isActive) {
            view.startUjet()
        }
    }

    private fun rescheduleCall() {
        Logger.d("reschedule call")
        localRepository.call = null
        localRepository.preferredVoipProvider = null
        if (view.isActive) {
            view.showScheduleTime()
        }
    }
}
