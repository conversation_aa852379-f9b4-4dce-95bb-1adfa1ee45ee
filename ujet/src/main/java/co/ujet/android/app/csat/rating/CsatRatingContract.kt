package co.ujet.android.app.csat.rating

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.commons.domain.agent.Agent

/**
 * Contract between [CsatRatingDialogFragment] [CsatRatingPresenter]
 */
internal interface CsatRatingContract {
    interface View : BaseView {
        fun showDefaultRatingView(agent: Agent?, rating: Int)
        fun showRatingView(agent: Agent?, rating: Int, isSubmitting: Boolean)
        fun showFeedbackRatingView(
            agent: Agent?,
            rating: Int,
            feedback: String?,
            isSubmitting: Boolean
        )

        fun updateAgent(agent: Agent?)
        fun updateFeedbackLength(length: Int)
        fun updateSubmitStatus(isSubmit: Boolean)
        fun showCsatSuccess()
        fun showCsatRetry()
        val rating: Int
        fun close()
    }

    interface Presenter : BasePresenter {
        fun onRatingChanged(rate: Int)
        fun onFeedbackChanged(feedback: String)
        fun onSubmit()
        fun onSkip()
    }
}
