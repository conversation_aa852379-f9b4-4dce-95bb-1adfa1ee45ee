package co.ujet.android.app.chat.data

import co.ujet.android.commons.domain.chat.message.base.ChatMessage

/**
 * This interface is data source for [ChatAdapter]
 * This interface is implemented by [ChatMessageManager]
 */
interface ChatMessageDataSource {
    fun getMessage(index: Int): ChatMessage?
    fun getCount(): Int
    fun setChangeListener(listener: ChatMessageChangeListener?)
    fun shouldShowAgentNames(): Boolean
    fun clearChatMessages()
}
