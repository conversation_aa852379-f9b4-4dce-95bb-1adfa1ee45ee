package co.ujet.android.app.survey.error

import co.ujet.android.clean.domain.UseCase
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.survey.usecase.SendSurvey
import co.ujet.android.data.LocalRepository

class SurveyErrorPresenter (
    private val view: SurveyErrorContract.View,
    private val localRepository: LocalRepository,
    private val useCaseHandler: UseCaseHandler,
    private val sendSurvey: SendSurvey,
    private val errorType: SurveyErrorType
) : SurveyErrorContract.Presenter {
    private var surveyAnswers: Map<Int, String>? = null
    private var signOffText: String? = null

    override fun start() {
        surveyAnswers = localRepository.rateRepository.surveyAnswersRequest?.answers
        signOffText = localRepository.rateRepository.surveySignOffText
    }

    override fun onExitClicked() {
        close()
    }

    override fun onSkipClicked() {
        close()
    }

    override fun onResendClicked() {
        when (errorType) {
            SurveyErrorType.RE_TRY_GET_SURVEY_DATA -> view.showSurveyScreen()
            SurveyErrorType.RE_TRY_SEND_SURVEY_DATA -> sendSurveyResponse()
            else -> close()
        }
    }

    private fun sendSurveyResponse() {
        view.showInProgress(true)

        val communication = localRepository.rateRepository.rateTarget ?: return
        useCaseHandler.execute(sendSurvey, SendSurvey.RequestValues(communication.urlPath,
            communication.id, surveyAnswers as HashMap<Int, String>),
            object : UseCase.UseCaseCallback<SendSurvey.ResponseValue?> {
                override fun onSuccess(response: SendSurvey.ResponseValue?) {
                    //Survey is answered and sent so clearing survey variable cache
                    localRepository.rateRepository.clear()
                    view.showSuccessFragment(signOffText)
                }

                override fun onError() {
                    //Enable resend button back
                    view.showInProgress(false)
                }
            })
    }

    private fun close() {
        localRepository.rateRepository.clear()
        view.close()
    }
}