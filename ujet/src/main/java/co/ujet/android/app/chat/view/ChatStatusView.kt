package co.ujet.android.app.chat.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import co.ujet.android.ui.style.UjetStyle
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.R.drawable
import co.ujet.android.R.layout
import co.ujet.android.app.chat.MarkdownUtil.loadMessageIntoMarkdownTextView
import co.ujet.android.commons.libs.graffiti.Graffiti
import co.ujet.android.modulemanager.common.ui.domain.ChatHeaderStyle
import co.ujet.android.modulemanager.common.ui.domain.FontStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView

class ChatStatusView(
    context: Context,
    private val ujetStyle: UjetStyle,
    private val headerStyle: ChatHeaderStyle?,
    private val customHeaderTitle: String?,
    message: String
) : LinearLayout(context) {

    private val headerView: LinearLayout
    private val statusTextView: MarkdownTextView
    private val agentAvatarImageView: ImageView
    private val dividerLine: LinearLayout

    init {
        inflate(context, layout.ujet_view_chat_status, this)
        isFocusable = false
        headerView = findViewById(R.id.header)
        statusTextView = findViewById(R.id.status_text)
        UjetViewStyler.styleChatCustomTitleText(ujetStyle, statusTextView)
        updateMessage(message)
        agentAvatarImageView = findViewById(R.id.agent_avatar)
        UjetViewStyler.applyAvatarBackgroundColor(ujetStyle, agentAvatarImageView)
        if (ujetStyle.defaultAvatar != null) {
            agentAvatarImageView.setImageDrawable(ujetStyle.defaultAvatar)
        }
        dividerLine = findViewById(R.id.divider_line)
        dividerLine.setBackgroundColor(ujetStyle.dividerBackgroundColor)
        displayCurrentState(headerStyle?.textContent)
    }

    fun updateAvatarImage(context: Context, avatarURL: String) {
        UjetViewStyler.applyAvatarBackgroundColor(ujetStyle, agentAvatarImageView)
        Graffiti
            .with(context)
            .from(avatarURL)
            .fallback(drawable.ujet_agent_sample)
            .into(agentAvatarImageView)
    }

    private fun updateMessage(message: String) {
        UjetViewStyler.styleRemoteChatText(ujetStyle, statusTextView)
        UjetViewStyler.styleRemoteChatLinkText(ujetStyle, statusTextView, true)
        loadMessageIntoMarkdownTextView(context, statusTextView, message, supportHtmlInText = true)
    }

    private fun updateFontStyle(context: Context, font: FontStyle?) {
        StyleUtil.updateFontStyle(context, statusTextView, font)
    }

    private fun updateMessageTrayStyle(headerStyle: ChatHeaderStyle?) {
        //Show/hide agent icon
        agentAvatarImageView.visibility = if (headerStyle?.agentIconVisible == false) {
            View.GONE
        } else {
            View.VISIBLE
        }
        //apply styles to divider
        val colorResId = StyleUtil.getColorResIdByName(context, headerStyle?.divider?.color)
        if (colorResId != StyleUtil.RESOURCE_NOT_FOUND) {
            dividerLine.setBackgroundColor(ContextCompat.getColor(context, colorResId))
        }
        val dividerWidthEntered = headerStyle?.divider?.width?.toFloat() ?: return
        val dividerWidth = ujetStyle.dpToPx(dividerWidthEntered).toInt()
        dividerLine.layoutParams = dividerLine.layoutParams.apply {
            width = ViewGroup.LayoutParams.MATCH_PARENT
            height = dividerWidth
        }
    }

    private fun updateHeaderViewVisibility(visible: Boolean) {
        headerView.visibility = if (visible) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    fun displayCurrentState(message: String?) {
        updateHeaderViewVisibility(headerStyle?.messageTrayVisible ?: true)
        if (customHeaderTitle.isNullOrEmpty()) {
            if (message.isNullOrEmpty()) {
                visibility = View.GONE
            } else {
                visibility = View.VISIBLE
                // if header text is configured in chat styles (headerStyle.textContent) then replace
                // message the message with its text otherwise use message field which contains text
                // from API response (chat.statusText)
                headerStyle?.textContent?.let { updateMessage(it) } ?: run {
                    updateMessage(message)
                }
                updateFontStyle(context, headerStyle?.font)
                updateMessageTrayStyle(headerStyle)
            }
        } else {
            visibility = View.VISIBLE
            updateMessage(customHeaderTitle)
            updateFontStyle(context, headerStyle?.font)
            updateMessageTrayStyle(headerStyle)
        }
    }
}
