package co.ujet.android.app.survey.rating

import android.annotation.SuppressLint
import android.content.Context
import android.content.Context.ACCESSIBILITY_SERVICE
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.Typeface
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.StateListDrawable
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.util.TypedValue
import android.view.*
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.accessibility.AccessibilityManager
import android.widget.*
import android.widget.GridView.AUTO_FIT
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat.IMPORTANT_FOR_ACCESSIBILITY_NO
import androidx.core.view.children
import androidx.core.view.forEachIndexed
import androidx.core.view.isVisible
import co.ujet.android.R
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.survey.SurveyListener
import co.ujet.android.app.survey.error.SurveyErrorFragment
import co.ujet.android.app.survey.error.SurveyErrorType
import co.ujet.android.app.survey.rating.SurveyPresenter.Companion.QUESTION_TYPE_CSAT
import co.ujet.android.app.survey.rating.SurveyPresenter.Companion.QUESTION_TYPE_ENUMERATED_TEXT
import co.ujet.android.app.survey.rating.SurveyPresenter.Companion.QUESTION_TYPE_FREE_FORM
import co.ujet.android.app.survey.rating.SurveyPresenter.Companion.QUESTION_TYPE_NUMERIC_SCALE
import co.ujet.android.app.survey.rating.SurveyPresenter.Companion.QUESTION_TYPE_STAR
import co.ujet.android.app.survey.success.SurveySuccessFragment
import co.ujet.android.clean.entity.survey.ValidAnswer
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.libs.flowLayout.FlowLayout
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.dpToPx
import com.google.android.material.button.MaterialButton
import java.util.Locale

@SuppressLint("StringFormatMatches")
class SurveyFragment @Keep constructor() : BaseFragment(), SurveyContract.View {
    private lateinit var accessibilityManager: AccessibilityManager
    private var beforeTextChangedCount: Int = 0
    private lateinit var presenter: SurveyContract.Presenter
    private var customArrayAdapter: CustomArrayAdapter? = null
    private var mainLayoutContainer: RelativeLayout? = null
    private var submitButton: FancyButton? = null
    private var menu: View? = null
    private var lastAllocatedViewId = Integer.MIN_VALUE
    private var freeFormViews = hashMapOf<Int, EditText>()
    private var viewIds = hashMapOf<Int, String>()
    private var requiredQuestionTitle = ""

    // Required views
    private var csatView: RatingBar? = null
    private var csatRequiredView: TextView? = null
    private var callback: SurveyListener? = null

    private val roundedRectangleDrawable: StateListDrawable
        get() {
            val strokePx = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_outline_stroke)
            val cornerRadiusPx = ujetStyle().buttonRadius
            val normalDrawable = DesignUtil.createRoundedRectangleDrawable(
                ujetStyle().primaryBackgroundColor,
                ujetStyle().surveyOutlineColor,
                strokePx,
                cornerRadiusPx
            )
            val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(
                ujetStyle().surveyFreeFormBackgroundColor,
                ujetStyle().surveyOutlineColor,
                strokePx,
                cornerRadiusPx
            )
            return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
        }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = try {
            context as SurveyListener
        } catch (_: ClassCastException) {
            throw ClassCastException("$context should implement SurveyListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        retainInstance = true
        val context = activity?.applicationContext ?: return
        accessibilityManager = activity?.getSystemService(ACCESSIBILITY_SERVICE) as AccessibilityManager
        presenter = SurveyPresenter(
            this,
            Injection.provideLocalRepository(context),
            Injection.provideUseCaseHandler(),
            Injection.provideGetSurvey(context),
            Injection.provideSendSurvey(context),
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.ujet_fragment_survey_rating, container, false)
        view.setBackgroundColor(ujetStyle().primaryBackgroundColor)

        mainLayoutContainer = view.findViewById(R.id.main_layout)
        submitButton = view.findViewById<FancyButton?>(R.id.submit_button_portrait)?.apply {
            UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
        }
        if (isLandscape(context) && presenter.getQuestionsCount() >= 2) {
            handleLandscapeOrientation(view)
        }
        initializeViews()
        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_exit -> {
                    presenter.onExitClicked()
                    announceIfSurveyRequired()
                    true
                }

                else -> false
            }
        })
        return view
    }

    private fun handleLandscapeOrientation(view: View) {
        submitButton?.visibility = GONE
        submitButton = view.findViewById<FancyButton?>(R.id.submit_button_landscape)?.apply {
            UjetViewStyler.stylePrimaryButton(ujetStyle(), this)
            visibility = VISIBLE
            //keeping initial state disable because sometimes service gets called before view initialization and
            //in landscape mode it always shows enabled even if user doesn't give any answer
            isEnabled = false
        }
    }

    //Main entry point for keyboard accessibility handling
    override fun handleKeyboardAccessibility() {
        view?.post {
            setupMenuKeyboardAccessibility()
            setupFirstQuestionKeyboardAccessibility()
            postSetupQuestionsKeyboardAccessibility()
        }
    }

    //Sets up accessibility on exit/menu button
    private fun setupMenuKeyboardAccessibility() {
        val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return
        menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
        AccessibilityUtil.setupKeyboardAccessibility(
            menu,
            onTabOrDpadDown = {
                // Move focus to the first question in the survey when Tab/Down pressed
                focusFirstQuestionKeyboardAccessibility()
                true
            },
            onEnter = {
                // Handles exit event and possibly an accessibility announcement
                presenter.onExitClicked()
                announceIfSurveyRequired()
                true
            }
        )
    }

    // Moves focus to the first question input view
    private fun focusFirstQuestionKeyboardAccessibility() {
        mainLayoutContainer?.getChildAt(0)?.requestFocus()
    }

    private fun announceIfSurveyRequired() {
        if (csatRequiredView?.isVisible == true) {
            AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                accessibilityManager,
                "${getString(R.string.ujet_survey_required_text)} $requiredQuestionTitle"
            )
        }
    }

    //Sets up accessibility for the first survey question view
    private fun setupFirstQuestionKeyboardAccessibility() {
        val firstQuestionView = mainLayoutContainer?.getChildAt(0)
        AccessibilityUtil.setupKeyboardAccessibility(
            firstQuestionView,
            onDpadUp = {
                // When Up is pressed on the first question, move focus back to the menu
                menu?.requestFocus()
                true
            }
        )
    }

    //Sets up accessibility for all survey questions, after layout is ready
    private fun postSetupQuestionsKeyboardAccessibility() {
        mainLayoutContainer?.post {
            mainLayoutContainer?.children?.forEachIndexed { index, view ->
                setupQuestionKeyboardAccessibility(view, index)
            }
        }
    }

    //Sets up keyboard accessibility for a single survey question view.
    //Handles different question types (free form, enumerated, numeric scale)
    private fun setupQuestionKeyboardAccessibility(view: View, index: Int) {
        val type = viewIds[view.id]
        val target = when (type) {
            QUESTION_TYPE_FREE_FORM -> (view as? ViewGroup)?.getChildAt(0)
            else -> (view as? ViewGroup)?.getChildAt(1)
        }

        when (type) {
            // Enumerated: set accessibility for each MaterialButton in the FlowLayout
            QUESTION_TYPE_ENUMERATED_TEXT -> (target as? FlowLayout)?.forEachIndexed { i, button ->
                setupButtonKeyboardAccessibility(button, index, isFlowLayout = true, flowViewIndex = i, flowLayout = target)
            }
            // Numeric scale: set accessibility for each MaterialButton in the GridView
            QUESTION_TYPE_NUMERIC_SCALE -> (target as? GridView)?.forEachIndexed { i, itemView ->
                val button = (itemView as? RelativeLayout)?.getChildAt(0) as? MaterialButton
                if (button != null)
                    setupButtonKeyboardAccessibility(button, index, isGridView = true, gridViewItemIndex = i, gridView = target)
            }
            // All other questions (star rating, csat, single input, etc.)
            else -> if (target != null) setupButtonKeyboardAccessibility(target, index)
        }
    }

    //Sets up keyboard navigation handlers for an individual answer button or input field
    private fun setupButtonKeyboardAccessibility(
        view: View,
        index: Int,
        isGridView: Boolean = false,
        isFlowLayout: Boolean = false,
        gridViewItemIndex: Int = -1,
        flowViewIndex: Int = -1,
        gridView: GridView? = null,
        flowLayout: FlowLayout? = null
    ) {
        AccessibilityUtil.setupKeyboardAccessibility(
            view,
            onTabOrDpadDown = {
                // Tab or Down key: move to next survey question/input
                focusNextQuestionInputKeyboardAccessibility(index + 1)
                true
            },
            onDpadUp = {
                // Up key: move to previous survey question/input or back to menu if at start
                focusPreviousQuestionInputKeyboardAccessibility(index - 1)
                true
            },
            onDpadRight = handleRightInGroupKeyboardAccessibility(
                isGridView, isFlowLayout, gridView, flowLayout, gridViewItemIndex, flowViewIndex
            ),
            onDpadLeft = handleLeftInGroupKeyboardAccessibility(
                isGridView, isFlowLayout, gridView, flowLayout, gridViewItemIndex, flowViewIndex
            )
        )
    }

    //Handles navigation to the right (next option) within a group, if applicable
    private fun handleRightInGroupKeyboardAccessibility(
        isGridView: Boolean,
        isFlowLayout: Boolean,
        gridView: GridView?,
        flowLayout: FlowLayout?,
        gridViewItemIndex: Int,
        flowViewIndex: Int
    ): ((View) -> Boolean)? {
        return when {
            isGridView -> {
                {
                    // Next button in GridView
                    (gridView?.getChildAt(gridViewItemIndex + 1) as? RelativeLayout)?.getChildAt(0)?.apply {
                        isFocusable = true
                        requestFocus()
                    }
                    true
                }
            }
            isFlowLayout -> {
                {
                    // Next button in FlowLayout
                    (flowLayout?.getChildAt(flowViewIndex + 1) as? MaterialButton)?.apply {
                        isFocusable = true
                        requestFocus()
                    }
                    true
                }
            }
            else -> null
        }
    }

    //Handles navigation to the left (previous option) within a group, if applicable
    private fun handleLeftInGroupKeyboardAccessibility(
        isGridView: Boolean,
        isFlowLayout: Boolean,
        gridView: GridView?,
        flowLayout: FlowLayout?,
        gridViewItemIndex: Int,
        flowViewIndex: Int
    ): ((View) -> Boolean)? {
        return when {
            isGridView -> {
                {
                    // Previous button in GridView
                    (gridView?.getChildAt(gridViewItemIndex - 1) as? RelativeLayout)?.getChildAt(0)?.apply {
                        isFocusable = true
                        requestFocus()
                    }
                    true
                }
            }
            isFlowLayout -> {
                {
                    // Previous button in FlowLayout
                    (flowLayout?.getChildAt(flowViewIndex - 1) as? MaterialButton)?.apply {
                        isFocusable = true
                        requestFocus()
                    }
                    true
                }
            }
            else -> null
        }
    }

    //Moves focus to the next question/input in the survey
    private fun focusNextQuestionInputKeyboardAccessibility(index: Int) {
        val nextView = mainLayoutContainer?.getChildAt(index) ?: run {
            submitButton?.requestFocus()
            return
        }
        val type = viewIds[nextView.id]
        // Determine which child should receive focus, based on question type
        val focusTarget = when (type) {
            QUESTION_TYPE_ENUMERATED_TEXT -> ((nextView as ViewGroup).getChildAt(1) as? FlowLayout)?.getChildAt(0)
            QUESTION_TYPE_STAR, QUESTION_TYPE_CSAT -> (nextView as ViewGroup).getChildAt(1)
            QUESTION_TYPE_NUMERIC_SCALE -> (((nextView as ViewGroup).getChildAt(1) as? GridView)?.getChildAt(0) as? RelativeLayout)?.getChildAt(0)
            else -> nextView
        }
        (focusTarget as? View)?.requestFocus()
    }

    //Moves focus to the previous question/input in the survey, or the menu if at the beginning
    private fun focusPreviousQuestionInputKeyboardAccessibility(index: Int) {
        val prevView = mainLayoutContainer?.getChildAt(index)
        when {
            prevView != null -> {
                val type = viewIds[prevView.id]
                when (type) {
                    QUESTION_TYPE_NUMERIC_SCALE -> {
                        val button = (((prevView as ViewGroup).getChildAt(1) as? GridView)?.getChildAt(0) as? RelativeLayout)?.getChildAt(0)
                        button?.requestFocus()
                    }
                    else -> {
                        prevView.requestFocus()
                    }
                }
            }
            else -> {
                // If at the top, move focus back to menu
                menu?.requestFocus()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        setActionBarTitle(getString(R.string.ujet_rating_title).uppercase(Locale.getDefault()))
        presenter.start()
    }

    override fun onDestroy() {
        clearResources()
        super.onDestroy()
    }

    override fun isActive(): Boolean = isAdded

    @Suppress("DEPRECATION")
    override fun addCsatView(questionId: Int, displayText: String) {
        if (isActive.not()) {
            return
        }
        requiredQuestionTitle = displayText
        val rateLayout = LayoutInflater.from(context)
            .inflate(R.layout.ujet_csat_rating_layout, null)

        rateLayout.findViewById<TextView>(R.id.rating_title).apply {
            UjetViewStyler.styleSurveyTitleText(ujetStyle(), this)
            visibility = View.VISIBLE
            text = getSpannableStringForTitle(displayText, getString(R.string.ujet_survey_required_text))
            AccessibilityUtil.overrideContentDescription(this, "$displayText ${getString(R.string.ujet_survey_required_text)}")
        }
        csatRequiredView = rateLayout.findViewById(R.id.required_view)

        csatView = rateLayout.findViewById<RatingBar>(R.id.rating_bar).apply {
            onRatingBarChangeListener = RatingBar.OnRatingBarChangeListener { _, rating, _ ->
                presenter.onRatingChanged(true, rating.toInt(), questionId)
                if (rating.toInt() != 0) {
                    AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                        accessibilityManager, "$displayText ${
                            String.format(
                                Locale.getDefault(),
                                getString(R.string.ujet_survey_rating_text_for_talkback),
                                rating.toInt(),
                                numStars
                            )
                        }"
                    )
                }
                if (csatRequiredView?.isVisible == true) {
                    AccessibilityUtil.invokeTalkbackAnnouncementEvent(accessibilityManager, getString(R.string.ujet_survey_required_text))
                }
            }
            rating = presenter.getSurveyAnswer(questionId)?.toFloat() ?: 0f
            //stateDescription is set to empty so every time user give star talk back will announce it otherwise talkback will
            //announce it as percentage
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                stateDescription = ""
            }
            AccessibilityUtil.overrideContentDescription(
                this,
                "$displayText ${
                    String.format(
                        Locale.getDefault(),
                        getString(R.string.ujet_survey_rating_text_for_talkback),
                        rating.toInt(),
                        numStars
                    )
                } ${getString(R.string.ujet_survey_required_text)}"
            )
        }

        val ratingStarDrawable = csatView?.progressDrawable as LayerDrawable
        ratingStarDrawable.findDrawableByLayerId(android.R.id.background).setColorFilter(
            ujetStyle().surveyStarColor, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.progress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.secondaryProgress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )

        val layoutParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_top_margin)
        if (lastAllocatedViewId != Integer.MIN_VALUE) {
            layoutParams.addRule(RelativeLayout.BELOW, lastAllocatedViewId)
        }
        lastAllocatedViewId = View.generateViewId()
        rateLayout.id = lastAllocatedViewId
        viewIds[lastAllocatedViewId] = QUESTION_TYPE_CSAT
        mainLayoutContainer?.addView(rateLayout, layoutParams)
    }

    @Suppress("DEPRECATION")
    override fun addStarRatedView(questionId: Int, displayText: String) {
        if (isActive.not()) {
            return
        }

        val rateLayout = LayoutInflater.from(context)
            .inflate(R.layout.ujet_survey_rating_view_layout, null)

        rateLayout.findViewById<TextView>(R.id.rating_title).apply {
            UjetViewStyler.styleSurveyTitleText(ujetStyle(), this)
            visibility = View.VISIBLE
            text = getSpannableStringForTitle(displayText, getString(R.string.ujet_common_optional))
            AccessibilityUtil.overrideContentDescription(this, "$displayText ${getString(R.string.ujet_common_optional)}")
        }
        val ratingBar = rateLayout.findViewById<RatingBar>(R.id.rating_bar).apply {
            onRatingBarChangeListener = RatingBar.OnRatingBarChangeListener { _, rating, _ ->
                presenter.onRatingChanged(false, rating.toInt(), questionId)
                contentDescription = "$displayText ${
                    String.format(
                        Locale.getDefault(),
                        getString(R.string.ujet_survey_rating_text_for_talkback),
                        rating.toInt(),
                        numStars
                    )
                }"
            }
            rating = presenter.getSurveyAnswer(questionId)?.toFloat() ?: 0f
            //stateDescription is set to empty so every time user give star talk back will announce it otherwise talkback will
            //announce it as percentage
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                stateDescription = ""
            }
            AccessibilityUtil.overrideContentDescription(
                this,
                "$displayText ${
                    String.format(
                        Locale.getDefault(),
                        getString(R.string.ujet_survey_rating_text_for_talkback),
                        rating.toInt(),
                        numStars
                    )
                } ${getString(R.string.ujet_common_optional)}"
            )
        }
        val ratingStarDrawable = ratingBar.progressDrawable as LayerDrawable
        ratingStarDrawable.findDrawableByLayerId(android.R.id.background).setColorFilter(
            ujetStyle().surveyStarColor, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.progress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.secondaryProgress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )

        val layoutParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_top_margin)
        if (lastAllocatedViewId != Integer.MIN_VALUE) {
            layoutParams.addRule(RelativeLayout.BELOW, lastAllocatedViewId)
        }
        lastAllocatedViewId = View.generateViewId()
        rateLayout.id = lastAllocatedViewId
        viewIds[lastAllocatedViewId] = QUESTION_TYPE_STAR
        mainLayoutContainer?.addView(rateLayout, layoutParams)
    }

    override fun addNumericScaleView(
        questionId: Int,
        displayText: String,
        validAnswers: List<ValidAnswer>
    ) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }
        val numericScaleLayout = LayoutInflater.from(context)
            .inflate(R.layout.ujet_survey_numeric_scale_layout, null)

        numericScaleLayout.findViewById<TextView>(R.id.numeric_scale_title)?.apply {
            UjetViewStyler.styleSurveyTitleText(ujetStyle(), this)
            this.visibility = View.VISIBLE
            text = getSpannableStringForTitle(displayText, getString(R.string.ujet_common_optional))
            AccessibilityUtil.overrideContentDescription(this, "$displayText ${getString(R.string.ujet_common_optional)}")
        }
        customArrayAdapter = CustomArrayAdapter(
            activity,
            validAnswers.mapNotNull { it.value }) { selectedItemIndex: Int ->
            validAnswers[selectedItemIndex].key?.let {
                presenter.onSurveyAnswered(questionId, it)
            }
        }

        numericScaleLayout.findViewById<GridView>(R.id.numeric_scale_grid_view)?.apply {
            adapter = customArrayAdapter
            layoutParams.height = getDynamicHeight(this)
            if (isLandscape(context)) {
                numColumns = AUTO_FIT
                columnWidth = dpToPx(context, 80).toInt()
            }

            validAnswers.forEachIndexed { index, validAnswer ->
                if (validAnswer.key == presenter.getSurveyAnswer(questionId)) {
                    //viewTreeObserver is used to wait for the view to draw its layout then we can access its child
                    viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            getChildAt(index).findViewById<MaterialButton>(R.id.numeric_scale_answer)?.apply {
                                //Change selected view
                                setBackgroundColor(ujetStyle().colorPrimary)
                                setTextColor(ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_white))
                                contentDescription = null
                                AccessibilityUtil.updateAccessibilityAction(this, false)
                                customArrayAdapter?.materialButton = this
                            }
                            viewTreeObserver.removeOnGlobalLayoutListener(this)
                        }
                    })
                }
            }
            importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_NO
        }

        val layoutParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_top_margin)
        if (lastAllocatedViewId != Integer.MIN_VALUE) {
            layoutParams.addRule(RelativeLayout.BELOW, lastAllocatedViewId)
        }
        lastAllocatedViewId = View.generateViewId()
        numericScaleLayout.id = lastAllocatedViewId
        viewIds[lastAllocatedViewId] = QUESTION_TYPE_NUMERIC_SCALE
        mainLayoutContainer?.addView(numericScaleLayout, layoutParams)
    }

    override fun addEnumeratedTextView(
        questionId: Int,
        displayText: String,
        validAnswers: List<ValidAnswer>
    ) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        val enumeratedTextLayout = LayoutInflater.from(context)
            .inflate(R.layout.ujet_survey_enumerated_text_layout, null)

        enumeratedTextLayout.findViewById<TextView>(R.id.enumerated_text_title)?.apply {
            UjetViewStyler.styleSurveyTitleText(ujetStyle(), this)
            this.visibility = View.VISIBLE
            text = getSpannableStringForTitle(displayText, getString(R.string.ujet_common_optional))
            AccessibilityUtil.overrideContentDescription(this, "$displayText ${getString(R.string.ujet_common_optional)}")
        }
        var enumeratedTextMaterialButton: MaterialButton? = null
        val flowLayout =
            enumeratedTextLayout.findViewById<FlowLayout>(R.id.enumerated_text_flowLayout)
        flowLayout?.removeAllViews()

        for (validAnswer in validAnswers) {
            val materialButton = MaterialButton(
                activity, null,
                R.style.UjetButton_QuickReplyButton
            )
            //R.style.Widget_MaterialComponents_Button_OutlinedButton_Icon
            materialButton.text = validAnswer.value
            materialButton.visibility = View.VISIBLE
            materialButton.setTextColor(ujetStyle().colorPrimary)
            materialButton.backgroundTintList = ColorStateList.valueOf(Color.TRANSPARENT)
            materialButton.strokeColor = ColorStateList.valueOf(ujetStyle().surveyOutlineColor)
            materialButton.strokeWidth =
                resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_outline_stroke)
            materialButton.cornerRadius = 18
            materialButton.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16F)
            materialButton.minHeight = 48
            materialButton.gravity = Gravity.CENTER
            materialButton.setPadding(50, 50, 50, 50)
            materialButton.setOnClickListener {
                //Reset previously selected view to default color and background color
                enumeratedTextMaterialButton?.setBackgroundColor(Color.TRANSPARENT)
                enumeratedTextMaterialButton?.setTextColor(ujetStyle().colorPrimary)
                enumeratedTextMaterialButton?.apply {
                    contentDescription = null
                    AccessibilityUtil.updateAccessibilityAction(this, false)

                }
                //Change selected view
                materialButton.setBackgroundColor(ujetStyle().colorPrimary)
                materialButton.setTextColor(ContextCompat.getColor(activity, co.ujet.android.ui.R.color.ujet_white))
                //Save selected view to reset it if another view is selected
                enumeratedTextMaterialButton = materialButton
                materialButton.apply {
                    contentDescription = String.format(getString(R.string.ujet_numerical_enumerated_select_text_talkback), text)
                    AccessibilityUtil.updateAccessibilityAction(this, true)
                }
                validAnswer.key?.let { answerKey ->
                    presenter.onSurveyAnswered(
                        questionId,
                        answerKey
                    )
                }
            }

            val materialButtonParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
            )
            //materialButtonParams.setMargins(40, 50, 40, 20)
            materialButton.layoutParams = materialButtonParams

            val flowLayoutParams: FlowLayout.LayoutParams = FlowLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
            )
            flowLayoutParams.rightMargin = 30
            flowLayout?.addView(materialButton, flowLayoutParams)
        }
        validAnswers.forEachIndexed { index, validAnswer ->
            if (validAnswer.key == presenter.getSurveyAnswer(questionId)) {
                (flowLayout.getChildAt(index) as MaterialButton).apply {
                    setBackgroundColor(ujetStyle().colorPrimary)
                    setTextColor(ContextCompat.getColor(activity, co.ujet.android.ui.R.color.ujet_white))
                    //Save selected view to reset it if another view is selected
                    enumeratedTextMaterialButton = this
                    contentDescription = String.format(getString(R.string.ujet_numerical_enumerated_select_text_talkback), text)
                    AccessibilityUtil.updateAccessibilityAction(this, true)
                }
            }
        }
        val layoutParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_top_margin)
        if (lastAllocatedViewId != Integer.MIN_VALUE) {
            layoutParams.addRule(RelativeLayout.BELOW, lastAllocatedViewId)
        }
        lastAllocatedViewId = View.generateViewId()
        enumeratedTextLayout.id = lastAllocatedViewId
        viewIds[lastAllocatedViewId] = QUESTION_TYPE_ENUMERATED_TEXT
        mainLayoutContainer?.addView(enumeratedTextLayout, layoutParams)
    }

    override fun addFreeFormView(questionId: Int, displayText: String) {
        if (isActive.not()) {
            return
        }

        val freeFormLayout = LayoutInflater.from(context)
            .inflate(R.layout.ujet_survey_free_form_layout, null)

        //For sending free form answers with user entered text after submit button is clicked
        freeFormViews[questionId] = freeFormLayout.findViewById(R.id.free_form_text)
        freeFormLayout.findViewById<TextView>(R.id.free_form_text_count)?.apply {
            setTextColor(ujetStyle().textTertiaryColor)
        }
        freeFormLayout.findViewById<EditText>(R.id.free_form_text)?.apply {
            setText(presenter.getSurveyAnswer(questionId) ?: "")
            background = roundedRectangleDrawable
            setTextColor(ujetStyle().textSecondaryColor)
            hint = "$displayText (${getString(R.string.ujet_common_optional)})"
            setHintTextColor(ujetStyle().textTertiaryColor)

            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    charSequence: CharSequence,
                    i: Int,
                    i1: Int,
                    i2: Int
                ) {
                    beforeTextChangedCount = charSequence.length
                }

                override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                    presenter.onFreeFormTextChanged(
                        charSequence.toString(),
                        freeFormLayout.findViewById(R.id.free_form_text_count)
                    )
                    if (beforeTextChangedCount == FREE_FORM_TEXT_MAX_COUNT_INT && charSequence.length == FREE_FORM_TEXT_MAX_COUNT_INT) {
                        AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                            accessibilityManager,
                            getString(R.string.ujet_survey_free_form_maximum_length_reached)
                        )
                    }
                }

                override fun afterTextChanged(editable: Editable) {
                    presenter.onSurveyAnswered(questionId, editable.toString())
                    if (editable.isEmpty()) {
                        AccessibilityUtil.overrideContentDescription(
                            freeFormLayout.findViewById(R.id.free_form_text),
                            "$displayText ${getString(R.string.ujet_common_optional)}"
                        )
                    } else {
                        AccessibilityUtil.overrideContentDescription(freeFormLayout.findViewById(R.id.free_form_text), "$editable")
                    }
                }
            })
        }

        val layoutParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.topMargin = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_top_margin)
        if (lastAllocatedViewId != Integer.MIN_VALUE) {
            layoutParams.addRule(RelativeLayout.BELOW, lastAllocatedViewId)
        }
        lastAllocatedViewId = View.generateViewId()
        freeFormLayout.id = lastAllocatedViewId
        viewIds[lastAllocatedViewId] = QUESTION_TYPE_FREE_FORM
        mainLayoutContainer?.addView(freeFormLayout, layoutParams)
    }

    @SuppressLint("SetTextI18n")
    override fun updateFreeFormTextCount(textLength: Int, textView: TextView) {
        textView.text = "$textLength/$FREE_FORM_TEXT_MAX_COUNT"
    }

    override fun showSuccessFragment(signOffText: String?) {
        if (isActive.not()) {
            return
        }
        clearResources()
        FragmentHelper.showAsTop(
            this,
            SurveySuccessFragment.newInstance(signOffText),
            SurveySuccessFragment.TAG
        )
    }

    override fun showErrorScreen(errorType: SurveyErrorType) {
        if (isActive.not()) {
            return
        }

        FragmentHelper.showAsTop(
            this, SurveyErrorFragment.newInstance(errorType),
            SurveyErrorFragment.TAG
        )
    }

    override fun setLoadingIndicator(enabled: Boolean) {
        if (isActive.not()) {
            return
        }
        submitButton?.setIndicatorVisible(enabled)
    }

    override fun setSubmitButton(enable: Boolean) {
        if (isActive.not()) {
            return
        }
        submitButton?.isEnabled = enable
    }

    @Suppress("DEPRECATION")
    override fun showRequiredViews(requestFocus: Boolean, csatAnswered: Boolean) {
        csatView?.let { csatView ->
            if (requestFocus) {
                csatView.parent.requestChildFocus(csatView, csatView)
            }

            val ratingStarDrawable = csatView.progressDrawable as LayerDrawable
            ratingStarDrawable.findDrawableByLayerId(android.R.id.background).setColorFilter(
                if (csatAnswered) ujetStyle().surveyStarColor else ujetStyle().colorDanger,
                PorterDuff.Mode.SRC_IN
            )
            csatRequiredView?.visibility = if (csatAnswered) View.GONE else View.VISIBLE
        }
    }

    override fun close() {
        callback?.onSurveyFailed()
    }


    private fun initializeViews() {
        clearCustomAdapter()
        mainLayoutContainer?.removeAllViews()
        freeFormViews.clear()

        submitButton?.setOnClickListener {
            //Check if user enters text in any free form views and send it survey answers method
            freeFormViews.mapNotNull { (key, value) ->
                if (value.text.toString().isNotEmpty()) {
                    presenter.onSurveyAnswered(key, value.text.toString())
                }
            }
            presenter.onSubmitClicked()
            if (csatRequiredView?.isVisible == true) {
                AccessibilityUtil.invokeTalkbackAnnouncementEvent(
                    accessibilityManager,
                    "${getString(R.string.ujet_survey_required_text)} $requiredQuestionTitle"
                )
            }
        }
    }

    private fun clearResources() {
        clearCustomAdapter()
        freeFormViews.clear()
        submitButton = null
        mainLayoutContainer?.removeAllViews()
        mainLayoutContainer = null
        csatView = null
        csatRequiredView = null
        callback = null
    }

    private fun getDynamicHeight(gridView: GridView): Int {
        val gridViewAdapter = gridView.adapter ?: return 0
        val items = gridViewAdapter.count
        val listItem = gridViewAdapter.getView(0, null, gridView)
        listItem.measure(0, 0)
        var totalHeight = listItem.measuredHeight
        if (items > 5) {
            val x = (items / 5).toFloat()
            val rows = (x + 1).toInt()
            totalHeight *= rows
        }

        return totalHeight
    }

    private fun getSpannableStringForTitle(displayText: String, suffixString: String): SpannableString {
        val titleText = "$displayText ($suffixString)"
        val spannableString = SpannableString(titleText)
        spannableString.setSpan(StyleSpan(Typeface.BOLD), 0, displayText.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannableString.setSpan(
            ForegroundColorSpan(ujetStyle().textSecondaryColor),
            displayText.length + 1,
            titleText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }

    private fun clearCustomAdapter() {
        customArrayAdapter?.materialButton = null
        customArrayAdapter?.clear()
        customArrayAdapter = null
    }

    internal class CustomArrayAdapter(
        context: Context, surveyDataList: List<String>,
        private val onItemClicked: ((selectedItemIndex: Int) -> Unit)
    ) : ArrayAdapter<String>(context, 0, surveyDataList) {
        var materialButton: MaterialButton? = null
        val ujetStyle: UjetStyle = Injection.provideUjetStyle(context)

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = convertView ?: LayoutInflater.from(context)
                .inflate(R.layout.ujet_survey_numeric_scale_list_item, parent, false)
            view.findViewById<MaterialButton>(R.id.numeric_scale_answer)?.apply {
                text = getItem(position).toString()
                setTextColor(ujetStyle.colorPrimary)
                strokeColor = ColorStateList.valueOf(ujetStyle.surveyOutlineColor)
                strokeWidth = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_survey_outline_stroke)
                visibility = View.VISIBLE
                setOnClickListener {
                    //Reset previously selected view to default color and background color
                    materialButton?.setBackgroundColor(Color.TRANSPARENT)
                    materialButton?.setTextColor(ujetStyle.colorPrimary)
                    materialButton?.apply {
                        contentDescription = null
                        AccessibilityUtil.updateAccessibilityAction(this, false)
                    }
                    //Change selected view
                    setBackgroundColor(ujetStyle.colorPrimary)
                    setTextColor(ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_white))
                    //Save selected view to reset it if another view is selected
                    materialButton = this
                    contentDescription = String.format(context.getString(R.string.ujet_numerical_enumerated_select_text_talkback), text)
                    AccessibilityUtil.updateAccessibilityAction(this, true)
                    onItemClicked(position)
                }
            }
            return view
        }
    }

    companion object {
        const val TAG = "SurveyFragment"
        const val FREE_FORM_TEXT_MAX_COUNT = "10,000"
        const val FREE_FORM_TEXT_MAX_COUNT_INT = 10000

        @JvmStatic
        fun newInstance(): SurveyFragment = SurveyFragment()
    }
}
