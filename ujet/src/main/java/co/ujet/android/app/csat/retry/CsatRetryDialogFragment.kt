package co.ujet.android.app.csat.retry

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.accessibility.AccessibilityManager
import android.widget.TextView
import co.ujet.android.R
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.app.csat.OnCsatRatingListener
import co.ujet.android.app.csat.sucess.CsatSuccessDialogFragment
import co.ujet.android.common.util.DialogUtil.announceCustomDialogTitle
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.internal.Injection
import co.ujet.android.ui.button.FancyButton

class CsatRetryDialogFragment : BaseDialogFragment(), CsatRetryContract.View {

    private var presenter: CsatRetryContract.Presenter? = null
    private var resendButton: FancyButton? = null
    private var callback: OnCsatRatingListener? = null
    private var titleTextView: TextView? = null
    private var descriptionTextView: TextView? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = try {
            context as OnCsatRatingListener
        } catch (ex: ClassCastException) {
            throw ClassCastException("$context should implement OnCsatRatingListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        presenter = CsatRetryPresenter(
            Injection.provideLocalRepository(requireContext()),
            apiManager(),
            this
        )
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val skipButton: FancyButton
        val dialog: Dialog = builder
            .customView(R.layout.ujet_dialog_rating_resend)
            .height(ViewGroup.LayoutParams.WRAP_CONTENT)
            .gravity(Gravity.CENTER)
            .withExit(false)
            .build()
        titleTextView = dialog.findViewById<TextView?>(R.id.title).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.stylePrimaryText(ujetStyle(), this)
            announceCustomDialogTitle(this.text.toString(), this)
        }
        skipButton = dialog.findViewById(R.id.ujet_rating_skip)
        UjetViewStyler.styleInvertedButton(ujetStyle(), skipButton)
        resendButton = dialog.findViewById<FancyButton>(R.id.ujet_rating_resend)?.also {
            it.setOnClickListener { presenter?.resend() }
            UjetViewStyler.stylePrimaryButton(ujetStyle(), it)
        }

        descriptionTextView = dialog.findViewById<TextView?>(R.id.description).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }
        skipButton.setOnClickListener { presenter?.skip() }
        return dialog
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        resendButton = null
        callback = null
        titleTextView = null
        descriptionTextView = null
    }

    override fun onBack() {
        // Prevent closing this by back button
    }

    override fun showCsatSuccess() {
        val fragmentManager = fragmentManager
        if (fragmentManager == null || !isActive) return
        dismiss()
        CsatSuccessDialogFragment
            .newInstance()
            .show(fragmentManager, CsatSuccessDialogFragment.TAG)
    }

    override fun showInProgress(enable: Boolean) {
        if (!enable) {
            titleTextView?.let {
                AccessibilityUtil.setupInitialFocus(
                    activity?.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager,
                    it
                )
            }
        }
        resendButton?.isEnabled = !enable
        resendButton?.setIndicatorVisible(enable)
    }

    override fun isActive() = isAdded

    override fun close() {
        callback?.onCsatRatingFailed()
    }

    companion object {
        const val TAG = "CsatRetryDialogFragment"

        fun newInstance(): CsatRetryDialogFragment {
            return CsatRetryDialogFragment()
        }
    }
}
