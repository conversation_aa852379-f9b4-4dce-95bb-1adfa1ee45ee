package co.ujet.android.app.chat

enum class WebFormState {
    LOADING,
    SUCCESS,
    ERROR,
    NONE;

    companion object {
        internal var activeState: WebFormState = NONE

        fun setState(newState: WebFormState) {
            activeState = newState
        }

        fun isLoading(): Boolean = activeState == LOADING

        fun isSuccess(): Boolean = activeState == SUCCESS

        fun isError(): Boolean = activeState == ERROR

    }
}
