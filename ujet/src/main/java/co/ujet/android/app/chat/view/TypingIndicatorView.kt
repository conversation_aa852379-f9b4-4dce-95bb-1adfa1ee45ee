package co.ujet.android.app.chat.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PorterDuff
import android.widget.ImageView
import android.widget.LinearLayout
import co.ujet.android.R
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.common.ui.TriangleView

@SuppressLint("ViewConstructor")
class TypingIndicatorView(context: Context, ujetStyle: UjetStyle) : LinearLayout(context) {
    init {
        inflate(context, R.layout.ujet_view_chat_typing_indicator, this)
        isFocusable = false
        findViewById<LinearLayout>(R.id.typing_indicator_background).apply {
            this.setBackgroundColor(ujetStyle.chatFooterBackgroundColor)
        }
        findViewById<TriangleView>(R.id.triangle_view).apply {
            this.setColor(ujetStyle.chatFooterBackgroundColor)
        }
        findViewById<ImageView>(R.id.circle_image_view_1).apply {
            this.setColorFilter(ujetStyle.chatTypingIndicatorColor, PorterDuff.Mode.SRC_IN)
        }
        findViewById<ImageView>(R.id.circle_image_view_2).apply {
            this.setColorFilter(ujetStyle.chatTypingIndicatorColor, PorterDuff.Mode.SRC_IN)
        }
        findViewById<ImageView>(R.id.circle_image_view_3).apply {
            this.setColorFilter(ujetStyle.chatTypingIndicatorColor, PorterDuff.Mode.SRC_IN)
        }
    }
}