package co.ujet.android.app.call.phonenumber

import android.content.Context
import android.text.Editable
import co.ujet.android.R.string
import co.ujet.android.api.ApiManager
import co.ujet.android.app.call.phonenumber.PhoneNumberInputContract.Presenter
import co.ujet.android.app.call.phonenumber.PhoneNumberInputContract.View
import co.ujet.android.app.call.phonenumber.callCreate.CallCreateDelegate
import co.ujet.android.app.call.phonenumber.callCreate.InAppIvrCallCreateDelegate
import co.ujet.android.app.call.phonenumber.callCreate.ScheduledCallCreateDelegate
import co.ujet.android.clean.domain.EmptyUseCaseCallback
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.device.usecase.GetPhoneNumber
import co.ujet.android.clean.domain.device.usecase.SavePhoneNumber
import co.ujet.android.clean.domain.enduser.usecase.GetEndUser
import co.ujet.android.clean.domain.enduser.usecase.GetEndUser.RequestValues
import co.ujet.android.clean.domain.enduser.usecase.GetEndUser.ResponseValue
import co.ujet.android.clean.domain.inappivrcall.usecase.InformInAppIvrCall
import co.ujet.android.clean.domain.language.usecase.ChooseLanguage
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId
import co.ujet.android.clean.entity.device.PhoneNumber
import co.ujet.android.common.util.RecordingPermissionUtils
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.data.constant.CallCreateType.InAppIvrCall
import co.ujet.android.data.constant.CallCreateType.Scheduled
import co.ujet.android.libs.logger.Logger
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberType.FIXED_LINE
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberType.MOBILE
import java.util.Date
import java.util.Locale

/**
 * Presenter for [PhoneNumberInputFragment]
 *
 * It is used to get a phone number for scheduled call or action only call.
 * It may be better if separate into two presenters for each.
 */
internal class PhoneNumberInputPresenter(
    private val context: Context,
    private val ujetContext: UjetContext,
    private val localRepository: LocalRepository,
    apiManager: ApiManager,
    private val callCreateType: CallCreateType,
    private val ivrPhoneNumber: String?,
    deflectionType: String?,
    private val scheduledTime: Date?,
    private val view: View,
    private val useCaseHandler: UseCaseHandler,
    private val savePhoneNumber: SavePhoneNumber,
    private val getPhoneNumber: GetPhoneNumber,
    getCompany: GetCompany,
    getSelectedMenuId: GetSelectedMenuId,
    private val getSelectedMenu: GetSelectedMenu,
    private val getEndUser: GetEndUser,
    chooseLanguage: ChooseLanguage,
    informInAppIvrCall: InformInAppIvrCall
) : Presenter {
    private var callCreateDelegate: CallCreateDelegate? = null
    private var regionCode: String? = null
    private var countryCode: String? = null
    private var phoneNumber: String? = null
    private var previousEnteredPhoneNumber: String? = null
    private var maxPhoneNumberLength: Int? = null
    private var endUserPhoneNumber: String? = null
    private var isEndUserRequestFinished = false
    private var isPhoneNumberRequestFinished = false
    private var isAskToRecordEnabled = false
    private var savedPhoneNumber: PhoneNumber? = null
    private val phoneNumberUtil = PhoneNumberUtil.getInstance()

    init {
        callCreateDelegate = when (callCreateType) {
            Scheduled -> {
                ScheduledCallCreateDelegate(
                    context, ujetContext, apiManager,
                    localRepository, view, useCaseHandler, chooseLanguage, getSelectedMenuId, deflectionType
                )
            }
            InAppIvrCall -> {
                InAppIvrCallCreateDelegate(
                    context, ujetContext, useCaseHandler, getCompany,
                    getSelectedMenuId, informInAppIvrCall, chooseLanguage, view, deflectionType
                )
            }
            else -> null
        }
    }

    override fun start() {
        if (callCreateDelegate == null) {
            Logger.w("Invalid call create type: %s", callCreateType)
            if (view.isActive) {
                view.finish()
                showRatingScreen()
            }
            return
        }
        callCreateDelegate?.init()
        getEndUserDetails()
        getSavedPhoneNumber()
        getRecordingPermitted()
    }

    private fun getEndUserDetails() {
        useCaseHandler.execute(
            getEndUser,
            RequestValues(),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    endUserPhoneNumber = response.endUser.phoneNumber
                    isEndUserRequestFinished = true
                    onInformRequestFinished()
                }

                override fun onError() {
                    isEndUserRequestFinished = true
                    onInformRequestFinished()
                }
            })
    }

    private fun getSavedPhoneNumber() {
        useCaseHandler.executeImmediate(
            getPhoneNumber,
            GetPhoneNumber.RequestValues(),
            object : UseCaseCallback<GetPhoneNumber.ResponseValue> {
                override fun onSuccess(response: GetPhoneNumber.ResponseValue) {
                    savedPhoneNumber = response.phoneNumber
                    isPhoneNumberRequestFinished = true
                    onInformRequestFinished()
                }

                override fun onError() {
                    isPhoneNumberRequestFinished = true
                    onInformRequestFinished()
                }
            })
    }

    // In this screen, we show call recording message only when recording option is record always and hides in other cases.
    private fun getRecordingPermitted() {
        useCaseHandler.executeImmediate(
            getSelectedMenu,
            GetSelectedMenu.RequestValues(ujetContext.directAccessKey),
            object : EmptyUseCaseCallback<GetSelectedMenu.ResponseValue>() {
                override fun onSuccess(response: GetSelectedMenu.ResponseValue) {
                    val recordingOption = response.menu.recordingOption
                    val isRecordingPermitted: Boolean
                    if (recordingOption.isNullOrEmpty()) {
                        isAskToRecordEnabled = false
                        isRecordingPermitted = false
                    } else {
                        isAskToRecordEnabled = RecordingPermissionUtils.RECORD_ASK_USER == recordingOption

                        // In this screen, we show call recording message only when recording option is record always and hides in other cases.
                        isRecordingPermitted = RecordingPermissionUtils.RECORD_ALWAYS == recordingOption
                    }
                    if (view.isActive) {
                        view.enableRecordingMessage(isRecordingPermitted)
                    }
                }

                override fun onError() {
                    isAskToRecordEnabled = false
                    if (view.isActive) {
                        view.enableRecordingMessage(false)
                    }
                }
            }
        )
    }

    private fun onInformRequestFinished() {
        if (isEndUserRequestFinished && isPhoneNumberRequestFinished) {
            initPhoneNumber()
        }
    }

    override fun onPhoneNumberChanged(phoneNumber: Editable) {
        val originNumber = phoneNumber.toString()
        val onlyNumbers = originNumber.replace("[-+()\\s]".toRegex(), "")
        if (onlyNumbers.isEmpty()) {
            this.phoneNumber = ""
        } else {
            var fullPhoneNumber = ""
            val asYouTypeFormatter = phoneNumberUtil.getAsYouTypeFormatter(countryCode)
            asYouTypeFormatter.inputDigit('+')
            for (index in 0 until (countryCode?.length ?: 0)) {
                asYouTypeFormatter.inputDigit(countryCode?.get(index) ?: return)
            }
            for (element in onlyNumbers) {
                fullPhoneNumber = asYouTypeFormatter.inputDigit(element)
            }
            this.phoneNumber = fullPhoneNumber.replace("+$countryCode", "").replaceFirst("[- ]".toRegex(), "")
        }
        if (this.phoneNumber != originNumber) {
            phoneNumber.replace(0, phoneNumber.length, this.phoneNumber)
        }
        try {
            val isValid = phoneNumberUtil.isValidNumber(phoneNumberUtil.parse(this.phoneNumber, regionCode))
            if (view.isActive) {
                view.enableButton(isValid)
                val currentEnteredPhoneNumberLength = onlyNumbers.length
                val previousEnteredPhoneNumberLength = previousEnteredPhoneNumber?.length ?: return
                this.maxPhoneNumberLength?.let { maxLength ->
                    // announce error message if user entered more digits than maximum allowed in phone
                    // number edit text and check current and previous entered length to ignore announcement
                    // when user deleting digits (otherwise talkback does not announce number deleted message
                    // as it was overridden by this announcement).
                    if (!isValid && currentEnteredPhoneNumberLength >= maxLength &&
                        currentEnteredPhoneNumberLength >= previousEnteredPhoneNumberLength) {
                        view.announceInvalidPhoneNumberMessage()
                    }
                }
            }
        } catch (ignore: NumberParseException) {
            //if phone number is too long then `NumberException` will be thrown in that case invalid announcement should be invoked
            view.announceInvalidPhoneNumberMessage()
        }
        this.previousEnteredPhoneNumber = onlyNumbers
    }

    override fun onRegionCodeChanged(countryCode: String?) {
        this.regionCode = countryCode
        this.countryCode = phoneNumberUtil.getCountryCodeForRegion(countryCode).toString()
        phoneNumber = null
        initPhoneNumber()
    }

    override fun onCountryCodeClicked() {
        if (view.isActive) {
            view.showCountyCodeSelect()
        }
    }

    private fun initPhoneNumber() {
        // If the saved phone number is the entered phone number
        if (savedPhoneNumber != null && regionCode != null && regionCode == savedPhoneNumber?.regionCode) {
            phoneNumber = savedPhoneNumber?.phoneNumber
            countryCode = PhoneNumberUtil.getInstance().getCountryCodeForRegion(regionCode).toString()
        }

        // use saved phone number by default
        if (regionCode == null && savedPhoneNumber != null) {
            regionCode = savedPhoneNumber?.regionCode
            phoneNumber = savedPhoneNumber?.phoneNumber
            countryCode = PhoneNumberUtil.getInstance().getCountryCodeForRegion(regionCode).toString()
        }

        //If there is no saved phone number then use 'phone' field in JWT signing if available 
        if (phoneNumber.isNullOrEmpty() && !endUserPhoneNumber.isNullOrEmpty()) {
            phoneNumber = endUserPhoneNumber
        }

        // No saved phone number
        if (regionCode.isNullOrEmpty()) {
            regionCode = Locale.getDefault().country

            //Locale does not have country code
            if (regionCode.isNullOrEmpty()) {
                regionCode = Locale.US.country //Set US as default region code
            }
            countryCode = PhoneNumberUtil.getInstance().getCountryCodeForRegion(regionCode).toString()
        }
        if (!countryCode.isNullOrEmpty() && view.isActive) {
            view.setCountryCode("+$countryCode")
        }
        setPhoneNumberHint()
        try {
            val _phoneNumber = phoneNumberUtil.parse(phoneNumber, regionCode)
            phoneNumber = phoneNumberUtil.format(_phoneNumber, INTERNATIONAL)
                .replace("+$countryCode", "")
                .replaceFirst("[- ]".toRegex(), "")
        } catch (ignore: NumberParseException) {
        }
        val phoneNumber = phoneNumber ?: ""
        if (view.isActive) {
            view.setPhoneNumber(phoneNumber)
        }
        try {
            val isValid = phoneNumberUtil.isValidNumber(
                phoneNumberUtil.parse(phoneNumber, regionCode)
            )
            if (view.isActive) {
                view.enableButton(isValid)
            }
        } catch (ignore: NumberParseException) {
        }
    }

    private fun setPhoneNumberHint() {
        var phoneNumberType = MOBILE
        /* For Tristan da Cunha (TA) region code, there is no meta data for MOBILE type so phoneNumberUtil.getExampleNumberForType() returns null.
         * In that case get the example number for FIXED_LINE type instead.
         * Please refer https://github.com/google/libphonenumber/blob/master/resources/PhoneNumberMetadata.xml#L26063 for more details.
         */
        if (regionCode == "TA") {
            phoneNumberType = FIXED_LINE
        }
        val _phoneNumber = phoneNumberUtil.getExampleNumberForType(regionCode, phoneNumberType)
        val fullPhoneNumber = phoneNumberUtil.format(_phoneNumber, INTERNATIONAL)
        val exampleNumber = fullPhoneNumber.replace("+$countryCode", "").replaceFirst("[- ]".toRegex(), "")
        this.maxPhoneNumberLength = exampleNumber.filter { it.isDigit() }.length
        if (view.isActive) {
            view.setPhoneNumberHint(exampleNumber)
        }
    }

    override fun confirm() {
        if (scheduledTime?.before(Date()) == true) {
            val title = context.getString(string.ujet_schedule_time_error_title)
            val message = context.getString(string.ujet_schedule_time_error_message)
            val confirmMessage = context.getString(string.ujet_common_reschedule)
            view.showErrorDialog(errorTitle = title, errorMessage = message, confirmMessage = confirmMessage) {
                view.back()
                showRatingScreen()
            }
            return
        }
        if (phoneNumber.isNullOrEmpty()) {
            view.showErrorDialog(errorMessage = context.getString(string.ujet_ask_phone_number_invalid_message))
            return
        }
        useCaseHandler.execute(
            savePhoneNumber,
            SavePhoneNumber.RequestValues(PhoneNumber(countryCode ?: return, phoneNumber ?: return, regionCode ?: return), true)
        )
        if (view.isActive) {
            if (callCreateType == Scheduled && isAskToRecordEnabled) {
                view.showRecordingConfirmation()
            } else {
                view.saveRecordingPermissionStatus(RecordingPermissionUtils.RECORDING_PERMISSION_NOT_ASKED)
                startScheduleCall(countryCode ?: return, phoneNumber ?: return, ivrPhoneNumber)
                //Show CSAT / Survey if ratable after we move user to PSTN call for InAppIvrCall
                if (callCreateType == InAppIvrCall) {
                    showRatingScreen()
                }
            }
        }
    }

    override fun skip() {
        //Show CSAT / Survey if ratable after we move user to PSTN call for InAppIvrCall
        if (callCreateType == InAppIvrCall) {
            showRatingScreen()
        }
        callCreateDelegate?.create(null, ivrPhoneNumber, scheduledTime)
    }

    override fun onConfirmationDialogClicked(isPermissionGranted: Boolean) {
        if (view.isActive) {
            val recordingPermission = if (isAskToRecordEnabled) {
                if (isPermissionGranted) {
                    RecordingPermissionUtils.RECORDING_PERMISSION_GRANTED
                } else {
                    RecordingPermissionUtils.RECORDING_PERMISSION_DENIED
                }
            } else {
                RecordingPermissionUtils.RECORDING_PERMISSION_NOT_ASKED
            }
            view.saveRecordingPermissionStatus(recordingPermission)
            startScheduleCall(countryCode ?: return, phoneNumber ?: return, ivrPhoneNumber)
        }
    }

    override fun onBackButtonClicked() {
        if (view.isActive) {
            view.back()
            AccessibilityUtil.isBackButtonClicked = true
        }
        //Show CSAT / Survey if ratable when user moves back from phone number input screen for InAppIvrCall.
        //Ignore for schedule call as there is schedule time picker screen available when user clicked
        // back from phone number input screen.
        if (callCreateType == InAppIvrCall) {
            showRatingScreen()
        }
    }

    override fun onExitButtonClicked() {
        if (view.isActive) {
            view.finish()
        }
        showRatingScreen()
    }

    private fun showRatingScreen() {
        val rateRepository = localRepository.rateRepository
        if (rateRepository.isRatable.not()) {
            return
        }

        rateRepository.apply {
            //Show survey if enabled, otherwise show CSAT dialog
            if (rateTarget?.surveyEnabled == true) {
                //clear old csat rating config
                isRatable = false
                isSurveyUnanswered = true
                view.showSurveyScreen()
            } else if (csatEnabled()) {
                view.showCsatScreen()
            }
        }
    }

    private fun startScheduleCall(countryCode: String, phoneNumber: String, ivrPhoneNumber: String?) {
        val internationalPhoneNumber = String.format("+%s %s", countryCode, phoneNumber)
        callCreateDelegate?.create(internationalPhoneNumber, ivrPhoneNumber, scheduledTime)
    }
}
