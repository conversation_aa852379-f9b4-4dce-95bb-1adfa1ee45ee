package co.ujet.android.app.call.regionCode

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import co.ujet.android.R
import co.ujet.android.R.id
import co.ujet.android.R.layout
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled
import co.ujet.android.libs.materialcamera.util.Degrees.isPortrait
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.dpToPx

internal class CountryListAdapter(
    context: Context,
    private val ujetStyle: UjetStyle,
    countries: List<Country>,
    private val dividerHeightCalculator: (Int) -> Unit
) : ArrayAdapter<Country>(context, 0, ArrayList(countries)) {

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        dividerHeightCalculator(position)
        val country = getItem(position)
        return if (country != null) {
            getCountryView(country, convertView, parent)
        } else {
            getDividerView(convertView, parent)
        }
    }

    private fun getCountryView(country: Country, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: LayoutInflater.from(context).inflate(layout.ujet_view_country_list_item, parent, false)
        val countryNameTextView = view.findViewById<TextView>(id.country_name)
        UjetViewStyler.stylePrimaryText(ujetStyle, countryNameTextView)
        countryNameTextView.text = country.getNameWithFlag()
        // FX-6316:Talkback announces country name flag with name as "Albania flag Albania +355" in region search page
        // Now it says "Albania +355"
        AccessibilityUtil.overrideContentDescription(countryNameTextView, country.getAccessibilityText())
        val countryCodeTextView = view.findViewById<TextView>(id.country_code)
        UjetViewStyler.stylePrimaryText(ujetStyle, countryCodeTextView)
        countryCodeTextView.text = country.countryCode
        view.findViewById<ConstraintLayout>(R.id.main_container).apply {
            if (isLargeTextAccessibilityEnabled(context) && isPortrait(context)) {
                setPadding(dpToPx(context, 10).toInt(), dpToPx(context, 13).toInt(), dpToPx(context, 10).toInt(), dpToPx(context, 13).toInt())
            }
        }
        return view
    }

    private fun getDividerView(convertView: View?, parent: ViewGroup): View {
        return convertView ?: LayoutInflater.from(context).inflate(layout.ujet_view_country_list_divider, parent, false)
    }

    override fun getItemViewType(position: Int): Int {
        return if (getItem(position) == null) {
            0
        } else {
            1
        }
    }

    override fun getViewTypeCount() = 2
}
