package co.ujet.android.app.chat.viewholders

import android.content.Context
import android.view.View
import android.view.View.LAYOUT_DIRECTION_LTR
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.ContentCardButton
import co.ujet.android.commons.domain.chat.message.ContentCardChatMessage
import co.ujet.android.ui.R
import co.ujet.android.ui.adapters.ContentCardsAdapter
import co.ujet.android.ui.adapters.viewholders.ContentCardViewHolder.CardClickedCallback
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.viewpager.DynamicHeightPageTransformer

class ContentCardsChatMessageViewHolder(
    adapter: ChatAdapterInteractor,
    parent: ViewGroup,
    context: Context,
    private val ujetStyle: UjetStyle
) : BaseChatMessageViewHolder(adapter, inflate(context, parent, R.layout.chat_content_cards_container_layout)) {

    fun bind(message: ContentCardChatMessage) {
        val viewPager = itemView.findViewById<ViewPager2>(R.id.view_pager)
        val context = viewPager.context
        val contentCards = message.contentCards
        val cardPadding = DesignUtil.dpToPx(context, 48).toInt()
        val cardMargin = DesignUtil.dpToPx(context, 16).toInt()
        val contentCardStyles = configuration.ujetStylesOptions?.chatStyles?.contentCard
        viewPager.offscreenPageLimit = contentCards.size
        viewPager.adapter = ContentCardsAdapter(contentCards, ujetStyle, contentCardStyles, adapterPosition, object : CardClickedCallback {
            override fun onCardClicked(contentCard: ContentCard) {
                adapter.getChatItemClickListener()?.onContentCardClicked(contentCard)
            }

            override fun onCardButtonClicked(button: ContentCardButton, contentCard: ContentCard, adapterPosition: Int) {
                adapter.getChatItemClickListener()?.onContentCardButtonClicked(button, contentCard, adapterPosition)
            }
        })
        viewPager.setPaddingRelative(cardPadding, 0, cardPadding, 0)
        val child = viewPager.getChildAt(0)
        if (child is RecyclerView) {
            child.setOverScrollMode(View.OVER_SCROLL_NEVER)
        }
        val pageTransformer = CompositePageTransformer()
        pageTransformer.addTransformer(MarginPageTransformer(cardMargin))
        pageTransformer.addTransformer(DynamicHeightPageTransformer(viewPager))
        pageTransformer.addTransformer { page, _ ->
            if (viewPager.currentItem == 0) {
                if (page.parent.layoutDirection == LAYOUT_DIRECTION_LTR) {
                    viewPager.scrollX = DesignUtil.dpToPx(context, 48).toInt()
                } else {
                    viewPager.scrollX = -DesignUtil.dpToPx(context, 48).toInt()
                }
            } else {
                viewPager.scrollX = 0
            }
        }
        viewPager.setPageTransformer(pageTransformer)
    }
}
