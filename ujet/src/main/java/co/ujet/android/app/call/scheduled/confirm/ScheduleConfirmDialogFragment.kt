package co.ujet.android.app.call.scheduled.confirm

import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup.LayoutParams
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.core.text.HtmlCompat
import co.ujet.android.R
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.call.scheduled.confirm.ScheduleConfirmContract.Presenter
import co.ujet.android.app.call.scheduled.confirm.ScheduleConfirmContract.View
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.common.util.LocaleUtil
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.internal.Injection
import co.ujet.android.ui.button.FancyButton
import java.util.Date

/**
 * Display confirm dialog for scheduled time
 */
class ScheduleConfirmDialogFragment @Keep constructor() : BaseDialogFragment(), View {
    private var descriptionTextView: TextView? = null
    private var presenter: Presenter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        presenter = ScheduleConfirmPresenter(
            Injection.provideLocalRepository(activity ?: return),
            this
        )
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = builder
            .customView(R.layout.ujet_dialog_schedule_confirm)
            .title(R.string.ujet_scheduled_call_confirm_title)
            .description(R.string.ujet_scheduled_call_confirm_content)
            .height(LayoutParams.WRAP_CONTENT)
            .gravity(Gravity.CENTER)
            .withExit(false)
            .build()
        descriptionTextView = dialog.findViewById<TextView>(R.id.description)?.apply {
            setTextColor(ujetStyle().colorText)
            setTypeface(ujetStyle().typeFace, Typeface.BOLD)
        }
        val icon = dialog.findViewById<ImageView>(R.id.icon)
        icon.setImageResource(R.drawable.ujet_ic_check)
        icon.setColorFilter(ujetStyle().colorPrimary)
        val okButton: FancyButton = dialog.findViewById(R.id.next_button)
        styleDefaultButton(okButton)
        okButton.setOnClickListener { presenter?.onConfirmClicked() }
        return dialog
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        descriptionTextView = null
    }

    override fun displayScheduledTime(scheduledTime: Date) {
        val locale = LocaleUtil.getCurrentLocale(activity ?: return)
        val timeStr = TimeUtil.getHourInFormat(scheduledTime, locale)
        val description = getString(R.string.ujet_scheduled_call_confirm_content, timeStr)
        descriptionTextView?.text = HtmlCompat.fromHtml(description, HtmlCompat.FROM_HTML_MODE_LEGACY)
        descriptionTextView?.contentDescription = "${getString(R.string.ujet_scheduled_call_confirm_content).removeSuffix(" <b>%s</b>")} $timeStr"
    }

    override fun back() {
        if (isActive) {
            activity?.finish()
        }
    }

    override fun finish() {
        if (isActive) {
            activity?.finish()
        }
    }

    override fun showCsatScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity)
        UjetCsatActivity.start(activity)
    }

    override fun showSurveyScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }

        ActivityHelper.finishAndRemoveTask(activity)
        UjetSurveyActivity.start(activity)
    }

    override fun isActive() = isAdded

    override fun onBack() {
        back()
        presenter?.onBackButtonClicked()
    }

    companion object {
        const val TAG = "ScheduleConfirmDialogFragment"

        fun newInstance(): ScheduleConfirmDialogFragment {
            return ScheduleConfirmDialogFragment()
        }
    }
}
