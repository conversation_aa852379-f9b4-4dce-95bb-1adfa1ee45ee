package co.ujet.android.app.call.deflection

import android.content.Context
import co.ujet.android.R
import co.ujet.android.common.util.ApplicationUtil
import co.ujet.android.data.model.CallDeflection
import co.ujet.android.data.model.CallDeflectionType
import co.ujet.android.internal.Injection
import java.util.*

class CallDeflectionPresenter (private val context: Context,
                               private val menuId: Int,
                               private val callDeflection: CallDeflection?,
                               private val view: CallDeflectionContract.View) : CallDeflectionContract.Presenter {
    override fun start() {
        if (menuId == 0) {
            if (view.isActive) {
                view.close()
            }
        }

        setupDeflections()
        ApplicationUtil.ringNotification(context)
    }

    private fun setupDeflections() {
        if (view.isActive.not()) {
            return
        }

        if (callDeflection?.isEmailEnabled == true) {
            view.activate(
                R.id.channel_email,
                context.getString(R.string.ujet_channel_menu_email).toUpperCase(Locale.ROOT),
                null,
                CallDeflectionType.EMAIL)
        }
        if (callDeflection?.isPhoneEnabled == true) {
            view.activate(
                R.id.channel_phone,
                context.getString(R.string.ujet_channel_menu_instant_call).toUpperCase(Locale.ROOT),
                null,
                CallDeflectionType.PHONE)
        }
        if (callDeflection?.isVoicemailEnabled == true) {
            view.activate(
                R.id.channel_voicemail,
                context.getString(R.string.ujet_channel_menu_voicemail).toUpperCase(Locale.ROOT),
                null,
                CallDeflectionType.VOICEMAIL)
        }
        if (callDeflection?.isScheduledCallEnabled == true) {
            view.activate(
                R.id.channel_scheduled_call,
                context.getString(R.string.ujet_channel_menu_scheduled_call).toUpperCase(Locale.ROOT),
                context.getString(R.string.ujet_channel_menu_scheduled_call_subtitle),
                CallDeflectionType.SCHEDULED_CALL)
        }
    }

    override fun stayOnHold() {
        Injection.provideDeflectedEventManager(context).sendDeflectedEvent("deflected",
                "over_capacity", "keep_waiting")

        if (view.isActive) {
            view.close()
            view.setResult(menuId, CallDeflectionType.NONE, null)
        }
    }

    override fun selectDeflection(callDeflectionType: CallDeflectionType) {
        var data: String? = null
        if (callDeflectionType == CallDeflectionType.PHONE) {
            data = callDeflection?.phoneNumber
        } else if (callDeflectionType == CallDeflectionType.VOICEMAIL) {
            data = "over_capacity_deflection"
        }

        if (view.isActive) {
            view.close()
            view.setResult(menuId, callDeflectionType, data)
        }
    }
}