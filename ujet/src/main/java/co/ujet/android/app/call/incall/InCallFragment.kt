package co.ujet.android.app.call.incall

import android.R.color
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.app.KeyguardManager
import android.app.KeyguardManager.KeyguardDismissCallback
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.os.IBinder
import android.os.PowerManager
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.accessibility.AccessibilityEvent
import android.widget.*
import androidx.annotation.Keep
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.Guideline
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.getSystemService
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.core.text.HtmlCompat
import androidx.core.view.AccessibilityDelegateCompat
import androidx.core.view.ViewCompat
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import co.ujet.android.R
import co.ujet.android.R.*
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.CallListener
import co.ujet.android.app.call.deflection.CallDeflectionDialogFragment
import co.ujet.android.app.call.incall.InCallContract.Presenter
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerFragment
import co.ujet.android.app.common.*
import co.ujet.android.app.confirmation.ConfirmationDialogFragment
import co.ujet.android.app.csat.UjetCsatActivity
import co.ujet.android.app.error.AlertDialogFragment
import co.ujet.android.app.error.ConnectivityDialogFragment
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.common.ui.CircleImageView
import co.ujet.android.common.util.*
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.getCachedRecordingPermission
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.hideRecordingMessage
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.isInstantCallEnabled
import co.ujet.android.commons.libs.graffiti.Graffiti
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.MAX_FONT_SIZE
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.getMaxFontSize
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.isFontSizeExceededLimit
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.data.constant.CommunicationType
import co.ujet.android.data.model.CallDeflection
import co.ujet.android.data.model.CallDeflectionType
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.libs.materialcamera.util.Degrees.isPortrait
import co.ujet.android.service.UjetBinder
import co.ujet.android.service.UjetCallService
import co.ujet.android.service.UjetCallService.Companion.startInstantCall
import co.ujet.android.smartaction.data.SmartActionType
import co.ujet.android.smartaction.data.SmartActionType.*
import co.ujet.android.smartaction.manager.SmartActionManager
import co.ujet.android.smartaction.routers.PsaRouter
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI
import co.ujet.android.smartaction.ui.verification.BiometricsVerification
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.dpToPx
import co.ujet.android.ui.widgets.UjetProgressBar
import com.google.android.material.button.MaterialButton
import java.util.*

open class InCallFragment @Keep constructor() : BaseFragment(), InCallContract.View {
    private var callListener: CallListener? = null
    private var presenter: Presenter? = null
    private var mainContainer: RelativeLayout? = null
    private var mainContainerLandscape: ConstraintLayout? = null
    private var guideLine: Guideline? = null
    private var emptyViewProgressBar: UjetProgressBar? = null
    private var chronometer: Chronometer? = null
    private var titleTextView: TextView? = null
    private var agentNameTextView: TextView? = null
    private var descriptionTextView: TextView? = null
    private var agentAvatarView: CircleImageView? = null
    private var buttonsContainer: RelativeLayout? = null
    private var buttonsLayout: LinearLayout? = null
    private var muteButton: Button? = null
    private var muteContainer: View? = null
    private var speakerButton: Button? = null
    private var speakerContainer: View? = null
    private var showAppContainer: View? = null
    private var showAppButton: Button? = null
    private var callEndButton: ImageButton? = null
    private var timerUntilDeviceUnlock: Timer? = null
    private var isWaitingScreenUnlockConfirm = false
    private var isAfterHourMessageDialogPending = false
    private var isServiceBound = false
    private var isAfterHourMessage = false
    private var failureReason: String? = null
    private var errorMessage: String? = null
    private var callService: UjetCallService? = null
    private var proximityWakeLock: PowerManager.WakeLock? = null
    private val callServiceAdapter = CallServiceAdapter()
    private var escalateButton: MaterialButton? = null
    private var cobrowseButton: MaterialButton? = null
    private var announcedContent: Boolean = false
    private var guideLineValue = CALL_DEFAULT_GUIDE_LINE
    private var viewsVisibleCount = 0 // Number of views visible
    private var menuIdToResumeCall = -1

    private val networkDialog by lazy {
        ConnectivityDialogFragment
            .newDialog(getString(string.ujet_call_internet_connection_lost), getString(string.ujet_call_end)).apply {
                setDismissCallback { presenter?.onEndCallClicked() }
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        retainInstance = true
        arguments?.let {
            failureReason = it.getString(EXTRA_ERROR_REASON, "")
            errorMessage = it.getString(EXTRA_ERROR_MESSAGE, "")
            isAfterHourMessage = it.getBoolean(EXTRA_ERROR_TYPE, false)
        }
        val activity = activity ?: return
        val inCallPresenter = InCallPresenter(
            Injection.provideUjetContext(activity),
            Injection.provideBiometrics(activity, this),
            Injection.provideLocalRepository(activity),
            this,
            Injection.provideApiManager(activity),
            callServiceAdapter,
            Injection.provideUseCaseHandler(),
            Injection.provideGetCompany(activity),
            Injection.provideGetMenuPath(activity),
            Injection.provideGetMenus(activity),
            Injection.provideGetSelectedMenu(activity)
        )
        presenter = inCallPresenter
        callListener = inCallPresenter
        isWaitingScreenUnlockConfirm = savedInstanceState?.getBoolean(EXTRA_SCREEN_UNLOCK_CONFIRM) ?: false
        isAfterHourMessageDialogPending = savedInstanceState?.getBoolean(EXTRA_AFTER_HOUR_MESSAGE_PENDING) ?: false

        proximityWakeLock = getSystemService(requireContext(), PowerManager::class.java)
            ?.newWakeLock(PowerManager.PROXIMITY_SCREEN_OFF_WAKE_LOCK, "ujet:proximityWakeLock")

        registerActivityForResult()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(layout.ujet_fragment_in_call, container, false)
        UjetViewStyler.styleFragmentBackground(ujetStyle(), view)
        val context = context ?: return view
        if (isPortrait(context)) {
            mainContainer = view.findViewById(R.id.main_container)
        } else {
            mainContainerLandscape = view.findViewById(R.id.main_container)
            guideLine = view.findViewById(R.id.guideline) as? Guideline
            val params = guideLine?.layoutParams as? ConstraintLayout.LayoutParams
            guideLineValue = if (ResizeTextAccessibilityUtil.isFontAndDisplaySizeMaxedOut(context)) {
                // co-browse or escalate buttons pushed out of the white background at the end in landscape mode
                // as it can't accommodate when font size is maxed out so reduce guide line to adjust it.
                CALL_RESIZE_GUIDE_LINE
            } else {
                CALL_DEFAULT_GUIDE_LINE
            }
            params?.guidePercent = guideLineValue
            guideLine?.layoutParams = params
        }
        // Empty view with loading indicator
        emptyViewProgressBar = view.findViewById<UjetProgressBar?>(R.id.empty_view_progress_bar)?.apply {
            val spinnerColor = ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_gray_lighter)
            setColorFilter(BlendModeColorFilterCompat.createBlendModeColorFilterCompat(spinnerColor, BlendModeCompat.SRC_IN))
            visibility = VISIBLE
        }
        chronometer = view.findViewById<Chronometer>(R.id.ujet_chronometer).apply {
            typeface = ujetStyle().typeFace
            setTextColor(ujetStyle().textSecondaryColor)
            visibility = GONE
        }
        titleTextView = view.findViewById<TextView>(R.id.title).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.stylePrimaryText(ujetStyle(), this)
        }
        agentNameTextView = view.findViewById<TextView>(R.id.agent_name).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }
        descriptionTextView = view.findViewById<TextView>(R.id.description).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }
        agentAvatarView = view.findViewById<CircleImageView?>(R.id.agent_avatar).apply {
            visibility = GONE
        }

        callEndButton = view.findViewById<ImageButton>(R.id.ujet_cancel_call_button).apply {
            setOnClickListener {
                presenter?.onEndCallClicked()
            }
            visibility = GONE
        }
        configureCallControlAccessibility(CallControlAction.END_CALL)
        isWaitingScreenUnlockConfirm = savedInstanceState?.getBoolean(EXTRA_SCREEN_UNLOCK_CONFIRM) ?: false
        isAfterHourMessageDialogPending = savedInstanceState?.getBoolean(EXTRA_AFTER_HOUR_MESSAGE_PENDING) ?: false

        escalateButton = view.findViewById<MaterialButton>(R.id.ujet_escalate_button).apply {
            UjetViewStyler.styleSecondaryButton(ujetStyle(), this)
            setOnClickListener {
                showEscalateConfirmation()
            }
        }

        cobrowseButton = view.findViewById<MaterialButton>(R.id.ujet_cobrowse_button).apply {
            UjetViewStyler.styleSecondaryButton(ujetStyle(), this)
            setOnClickListener {
                SmartActionManager.startUserInitiatedAction(context, COBROWSE, CommunicationType.IncomingCall)
            }
        }
        setupCallContainerButtons(view)
        return view
    }

    private fun setupCallContainerButtons(view: View) {
        buttonsContainer = view.findViewById(R.id.ujet_call_buttons_container) ?: return
        if (isPortrait(context)) {
            buttonsLayout = view.findViewById(R.id.ujet_call_buttons_layout) ?: return
            buttonsLayout?.removeAllViews()
        } else {
            buttonsContainer?.removeAllViews()
        }
        setupCallButtonView(CallControlAction.MUTE)
        setupCallButtonView(CallControlAction.SPEAKER)
        setupCallButtonView(CallControlAction.MINIMIZE)
        refreshInCallViews()
    }

    private fun setupCallButtonView(callControlAction: CallControlAction) {
        val buttonLayout = LayoutInflater.from(context).inflate(layout.ujet_call_buttons_list_item, null)
        val buttonLayoutContainer = buttonLayout.findViewById<RelativeLayout>(R.id.ujet_call_button_layout_container)
        val buttonIconView = buttonLayoutContainer.findViewById<Button>(R.id.ujet_call_button)
        val buttonTextView = buttonLayoutContainer.findViewById<TextView>(R.id.ujet_call_button_text)

        val (buttonIconResId, buttonText) = when (callControlAction) {
            CallControlAction.MUTE -> Pair(drawable.ujet_button_mute, getString(string.ujet_incall_mute))
            CallControlAction.SPEAKER -> Pair(drawable.ujet_button_speaker, getString(string.ujet_incall_speaker))
            CallControlAction.MINIMIZE -> Pair(drawable.ujet_button_minimize, getString(string.ujet_incall_minimize))
            else -> return
        }
        buttonIconView.setBackgroundResource(buttonIconResId)
        buttonIconView.contentDescription = buttonText
        buttonIconView.setOnClickListener { selectCallButton(callControlAction) }
        buttonTextView.text = buttonText
        UjetViewStyler.styleTertiaryText(ujetStyle(), buttonTextView)
        UjetViewStyler.overrideTypeface(ujetStyle(), buttonTextView)

        buttonLayoutContainer.visibility = GONE
        // append call button enum index to button layout container id to make it unique id.
        val index = CallControlAction.getIndexOf(callControlAction)
        buttonLayoutContainer.id = buttonLayoutContainer.id + index
        // Save respective button container and its views to set the visibility  and manage them later
        when (callControlAction) {
            CallControlAction.MUTE -> {
                muteContainer = buttonLayoutContainer
                muteButton = buttonIconView
            }
            CallControlAction.SPEAKER -> {
                speakerContainer = buttonLayoutContainer
                speakerButton = buttonIconView
            }
            CallControlAction.MINIMIZE -> {
                showAppContainer = buttonLayoutContainer
                showAppButton = buttonIconView
            }
            else -> return
        }
        if (isPortrait(context)) {
            val layoutParams = LinearLayout.LayoutParams(
                0, // Width set to 0 to allow weight to take effect
                LinearLayout.LayoutParams.WRAP_CONTENT, // Height wraps content
                1f // Weight set to 1
            )
            buttonLayout.layoutParams = layoutParams
            buttonsLayout?.addView(buttonLayout)
        } else {
            buttonsContainer?.addView(buttonLayout)
        }
    }

    private fun selectCallButton(callControlAction: CallControlAction) {
        when (callControlAction) {
            CallControlAction.MUTE -> presenter?.onMuteClicked()
            CallControlAction.SPEAKER -> presenter?.onSpeakerClicked()
            CallControlAction.MINIMIZE -> presenter?.onMinimizeClicked()
            else -> return
        }
    }

    private fun configureCallControlAccessibility(callControlAction: CallControlAction) {
        when (callControlAction) {
            CallControlAction.MUTE -> {
                muteButton?.let {
                    val accessibilityText = if (it.isSelected) {
                        getString(R.string.ujet_call_unmute)
                    } else {
                        getString(R.string.ujet_call_mute)
                    }
                    AccessibilityUtil.changeAccessibilityClickDescription(it, accessibilityText)
                }
            }

            CallControlAction.SPEAKER -> {
                speakerButton?.let {
                    val accessibilityText = if (it.isSelected) {
                        getString(R.string.ujet_call_disable_speaker)
                    } else {
                        getString(R.string.ujet_call_enable_speaker)
                    }
                    AccessibilityUtil.changeAccessibilityClickDescription(it, accessibilityText)
                }
            }

            CallControlAction.END_CALL -> {
                callEndButton?.let {
                    val accessibilityText = getString(R.string.ujet_call_end)
                    AccessibilityUtil.changeAccessibilityClickDescription(it, accessibilityText)
                }
            }

            CallControlAction.MINIMIZE -> {
                showAppButton?.let {
                    val accessibilityText = getString(R.string.ujet_incall_minimize)
                    AccessibilityUtil.changeAccessibilityClickDescription(it, accessibilityText)
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(EXTRA_SCREEN_UNLOCK_CONFIRM, isWaitingScreenUnlockConfirm)
        outState.putBoolean(EXTRA_AFTER_HOUR_MESSAGE_PENDING, isAfterHourMessageDialogPending)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        isWaitingScreenUnlockConfirm = savedInstanceState?.getBoolean(EXTRA_SCREEN_UNLOCK_CONFIRM) ?: false
        isAfterHourMessageDialogPending = savedInstanceState?.getBoolean(EXTRA_AFTER_HOUR_MESSAGE_PENDING) ?: false
        super.onViewStateRestored(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        if (isAfterHourMessage) {
            presenter?.onCallAfterHourMessageReceived(errorMessage)
        } else {
            presenter?.onCallErrorMessageReceived(errorMessage, failureReason)
        }
        (activity as? UjetBaseActivity)?.supportActionBar?.hide()
        if (bindCallService()) {
            presenter?.start()
        } else {
            presenter?.onCallServiceUnavailable()
        }
        acquireProximityWakeLockIfNeeded()
    }

    override fun onPause() {
        (activity as? UjetBaseActivity)?.supportActionBar?.show()
        stopTimerWaitingForScreenUnlock()
        unbindCallService()
        presenter?.stop()
        releaseProximityWakeLock()
        super.onPause()
    }

    override fun onDestroyView() {
        mainContainer = null
        mainContainerLandscape = null
        chronometer = null
        titleTextView = null
        agentNameTextView = null
        descriptionTextView = null
        agentAvatarView = null
        buttonsContainer = null
        buttonsLayout = null
        muteButton = null
        muteContainer = null
        speakerButton = null
        speakerContainer = null
        showAppContainer = null
        showAppButton = null
        callEndButton = null
        escalateButton = null
        cobrowseButton = null
        emptyViewProgressBar = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        unbindCallService()
        parentFragmentManager.clearFragmentResultListener(TAG)
        presenter?.cancelDeflectionTimer()
        super.onDestroy()
    }

    private fun showEscalateConfirmation() {
        AlertDialog
            .Builder(activity)
            .setMessage(R.string.ujet_escalation_dialog)
            .setPositiveButton(R.string.ujet_common_yes) { _, _ ->
                presenter?.onEscalateClicked()
            }
            .setNegativeButton(R.string.ujet_common_no, null)
            .create()
            .show()
    }

    override fun isActive() = isAdded

    override fun refreshInCallViews() {
        if (!isActive) {
            return
        }
        val buttonsContainerBelowResId = getButtonsContainerBelowResId()
        applyLayoutParamsToView(buttonsContainer, buttonsContainerBelowResId)
        (speakerContainer?.layoutParams as? RelativeLayout.LayoutParams)?.let { speakerContainerParams ->
            speakerContainerParams.addRule(RelativeLayout.CENTER_HORIZONTAL)
            speakerContainer?.layoutParams = speakerContainerParams
        }
        updateSpeakerButtonView()
        updateMinimizeButtonView()
        applyLayoutParamsToView(escalateButton, R.id.ujet_call_buttons_container)
        applyLayoutParamsToView(cobrowseButton, R.id.ujet_call_buttons_container)
        val callEndButtonBelowResId = getCallEndButtonBelowResId()
        applyLayoutParamsToView(callEndButton, callEndButtonBelowResId)
        registerGlobalLayoutListener()
    }

    private fun applyLayoutParamsToView(view: View?, belowResId: Int) {
        if (view?.visibility == VISIBLE) {
            (view.layoutParams as? RelativeLayout.LayoutParams)?.let { buttonParams ->
                applyRuleToView(buttonParams, RelativeLayout.BELOW, belowResId)
                view.layoutParams = buttonParams
            }
        }
    }
    
    private fun applyRuleToView(layoutParams: RelativeLayout.LayoutParams, rule: Int, belowResId: Int) {
        layoutParams.addRule(rule, belowResId)
    }
    
    private fun updateSpeakerButtonView() {
        val context = context ?: return
        (muteContainer?.layoutParams as? RelativeLayout.LayoutParams)?.let { muteContainerParams ->
            // Adjust mute and speaker buttons alignment when font size is at 200% or more
            if (isFontSizeExceeded(context)) {
                (speakerContainer?.layoutParams as? RelativeLayout.LayoutParams)?.let { speakerContainerParams ->
                    speakerContainerParams.removeRule(RelativeLayout.CENTER_HORIZONTAL)
                    muteContainer?.id?.let { applyRuleToView(speakerContainerParams, RelativeLayout.END_OF, it) }
                    speakerContainer?.layoutParams = speakerContainerParams
                }
            } else {
                speakerContainer?.id?.let { applyRuleToView(muteContainerParams, RelativeLayout.START_OF, it) }
            }
            muteContainerParams.marginEnd = dpToPx(context, CALL_BUTTON_MARGIN).toInt()
            muteContainer?.layoutParams = muteContainerParams
        }    
    }

    private fun updateMinimizeButtonView() {
        val context = context ?: return
        (showAppContainer?.layoutParams as? RelativeLayout.LayoutParams)?.let { showAppContainerParams ->
            // Move minimize call button to next line when font size is at 200% or more
            if (isFontSizeExceeded(context)) {
                showAppContainerParams.addRule(RelativeLayout.CENTER_IN_PARENT)
                speakerContainer?.id?.let { applyRuleToView(showAppContainerParams, RelativeLayout.BELOW, it) }
                showAppContainerParams.topMargin = getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_views_top_margin)
            } else {
                speakerContainer?.id?.let { applyRuleToView(showAppContainerParams, RelativeLayout.END_OF, it) }
                showAppContainerParams.marginStart = dpToPx(context, CALL_BUTTON_MARGIN).toInt()
            }
            showAppContainer?.layoutParams = showAppContainerParams
        }
    }

    private fun getButtonsContainerBelowResId(): Int {
        return if (chronometer?.visibility == VISIBLE) {
            R.id.ujet_chronometer
        } else {
            R.id.description
        }
    }

    private fun getCallEndButtonBelowResId(): Int {
        return when {
            escalateButton?.visibility == VISIBLE -> {
                R.id.ujet_escalate_button
            }
            cobrowseButton?.visibility == VISIBLE -> {
                R.id.ujet_cobrowse_button
            }
            else -> {
                R.id.ujet_call_buttons_container
            }
        }
    }

    private fun registerGlobalLayoutListener() {
        buttonsContainer?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                buttonsContainer?.viewTreeObserver?.removeOnGlobalLayoutListener(this)

                val context = context ?: return
                viewsVisibleCount = 1 // Reset and count agent avatar as its always visible
                val agentAvatarViewHeight = agentAvatarView?.height ?: 0
                val titleViewHeight = getViewHeight(titleTextView)
                val descriptionViewHeight = getViewHeight(descriptionTextView)
                val agentNameTextViewHeight = getViewHeight(agentNameTextView)
                val chronometerViewHeight = getViewHeight(chronometer)
                val callButtonsContainerHeight = getViewHeight(buttonsContainer, isPortrait(context))
                val escalateButtonViewHeight = getViewHeight(escalateButton)
                val coBrowseButtonViewHeight = getViewHeight(cobrowseButton)
                val callEndButtonViewHeight = getViewHeight(callEndButton, isPortrait(context))

                // We add 15dp additional top margin for all the views in this screen so we calculate
                // views visible count and multiply by ((viewsVisibleCount-1) * 15dp) to get additional margin
                var additionalMarginHeightPx = (viewsVisibleCount - 1) * getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_views_top_margin)
                if (isLandscape(context)) {
                    // In landscape we add additional 15dp margin at the top for both escalate and co-browse buttons
                    additionalMarginHeightPx += getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_views_top_margin) * VIEW_TOP_BOTTOM_COUNT
                }
                val currentViewHeight = if (isPortrait(context)) {
                    agentAvatarViewHeight + titleViewHeight + descriptionViewHeight +
                            agentNameTextViewHeight + chronometerViewHeight + callButtonsContainerHeight +
                            escalateButtonViewHeight + coBrowseButtonViewHeight + callEndButtonViewHeight + additionalMarginHeightPx
                } else {
                    val leftSideLandscapeModeViewsHeight = agentAvatarViewHeight + titleViewHeight +
                            descriptionViewHeight + agentNameTextViewHeight + chronometerViewHeight
                    val rightSideLandscapeModeViewsHeight = callButtonsContainerHeight +
                            escalateButtonViewHeight + coBrowseButtonViewHeight + callEndButtonViewHeight
                    // In landscape mode, we have two different views (left and right side) and use maximum value among them
                    leftSideLandscapeModeViewsHeight.coerceAtLeast(rightSideLandscapeModeViewsHeight) + additionalMarginHeightPx
                }
                // We add 40dp padding between tool bar and the white background at the top and bottom of the screen
                val topViewMargin = if (isPortrait(context)) {
                    getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_white_background_top_margin)
                } else {
                    getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_white_background_top_margin_landscape)
                }
                val toolBarHeightPx = getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_toolbar_top_margin)
                val actualScreenHeight = context.resources.displayMetrics.heightPixels - toolBarHeightPx
                // When there is not enough space left to accommodate views, screen will be scrollable
                // and we need to add topViewMargin (40dp) padding + extra 20dp padding at the top and
                // bottom of the screen.
                val defaultTopBottomPadding = topViewMargin + getPixelSize(co.ujet.android.ui.R.dimen.ujet_call_extra_scrollable_margin)
                // If current view height exceeds screen height then we do not have enough space left
                // to accommodate all the views
                val currentViewExceedsScreenHeight = actualScreenHeight < currentViewHeight
                val mainContainerHeight = if (currentViewExceedsScreenHeight) {
                    // We need to append defaultTopBottomPadding at the top and bottom of the screen to
                    // current view height to accommodate all the views and make it scrollable.
                    currentViewHeight + (defaultTopBottomPadding * VIEW_TOP_BOTTOM_COUNT)
                } else {
                    // Default height
                    ViewGroup.LayoutParams.MATCH_PARENT
                }
                val mainContainerParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    mainContainerHeight
                )
                mainContainer?.apply { layoutParams = mainContainerParams }
                // In Landscape mode, expand the views when there is not enough space left
                mainContainerLandscape?.apply { layoutParams = mainContainerParams }
                // Calculate vertical center padding to be applied to agentAvatarView to make it vertically centered
                var verticalCenterViewPadding = (actualScreenHeight - currentViewHeight) / 2
                val topMargin = if (verticalCenterViewPadding < 0) {
                    // verticalCenterViewPadding is < 0, means we do not have enough space left to draw all the
                    // views and view changed to scrollable and using default top padding in that case
                    defaultTopBottomPadding
                } else {
                    // Add topViewMargin (40dp) padding to top padding and use default bottom padding
                    verticalCenterViewPadding += topViewMargin
                    verticalCenterViewPadding
                }
                // Apply top margin in portrait mode or if there is not enough space left in case of landscape mode
                if (isPortrait(context) || currentViewExceedsScreenHeight) {
                    agentAvatarView?.let { agentAvatar ->
                        val avatarBackgroundParams = RelativeLayout.LayoutParams(
                            dpToPx(context, CALL_AGENT_AVATAR_VIEW_SIZE).toInt(),
                            dpToPx(context, CALL_AGENT_AVATAR_VIEW_SIZE).toInt()
                        )
                        avatarBackgroundParams.topMargin = topMargin
                        avatarBackgroundParams.addRule(RelativeLayout.CENTER_HORIZONTAL)
                        agentAvatar.layoutParams = avatarBackgroundParams
                    }
                }

                // Align mute and speaker buttons layout centered horizontally when font size is at 200% or
                // more and phone is in portrait mode. Calculate start margin to make it centered and
                // assign it to mute container.
                if (isFontSizeExceeded(context)) {
                    // Calculate width of white background after subtracting topViewMargin padding (40dp
                    // at the start and end of the screen) twice
                    val actualScreenWidth = view?.width?.minus((topViewMargin * VIEW_TOP_BOTTOM_COUNT)) ?: DEFAULT_VIEW_WIDTH
                    val muteWidth = muteContainer?.width ?: DEFAULT_VIEW_WIDTH
                    val speakerWidth = speakerContainer?.width ?: DEFAULT_VIEW_WIDTH
                    val additionalMarginWidthPx = if (isPortrait(context)) {
                        // When switched from landscape mode to portrait mode, we need to update speaker,
                        // minimize buttons before applying start margin to mute buttons.
                        updateSpeakerButtonView()
                        updateMinimizeButtonView()
                        (dpToPx(context, CALL_BUTTON_MARGIN).toInt())
                    } else {
                        // In Landscape mode, we have Guideline of (45% or 38%) of the screen based on
                        // resize font value so add it to additional margin
                        (actualScreenWidth * guideLineValue).toInt() + topViewMargin
                    }
                    val currentWidth = muteWidth + speakerWidth + additionalMarginWidthPx
                    val startMargin = (actualScreenWidth - currentWidth) / 2
                    (muteContainer?.layoutParams as? RelativeLayout.LayoutParams)?.let { muteContainerParams ->
                        muteContainerParams.marginStart = startMargin
                        muteContainer?.layoutParams = muteContainerParams
                    }
                }
            }
        })
    }
    
    private fun getViewHeight(view: View?, conditionToIncrement: Boolean = true): Int {
        return if (view?.visibility == VISIBLE) {
            if (conditionToIncrement) {
                viewsVisibleCount++
            }
            view.height
        } else {
            DEFAULT_VIEW_HEIGHT
        }
    }

    private fun getPixelSize(dimenResId: Int): Int {
        return resources.getDimensionPixelSize(dimenResId)
    }

    private fun isFontSizeExceeded(context: Context): Boolean {
        return getMaxFontSize(context) >= MAX_FONT_SIZE || isFontSizeExceededLimit(context)
    }

    override fun setMute(isMute: Boolean) {
        val wasMuted = muteButton?.isSelected ?: false
        muteButton?.isSelected = isMute
        configureCallControlAccessibility(CallControlAction.MUTE)
        announceCallControlButtonStateChange(CallControlAction.MUTE, wasMuted, isMute)
    }

    override fun setSpeakerOn(isSpeakerOn: Boolean) {
        val speakerPreviousState = speakerButton?.isSelected ?: false
        speakerButton?.isSelected = isSpeakerOn
        configureCallControlAccessibility(CallControlAction.SPEAKER)
        announceCallControlButtonStateChange(CallControlAction.SPEAKER, speakerPreviousState, isSpeakerOn)
    }

    private fun announceCallControlButtonStateChange(callControlAction: CallControlAction, wasActive: Boolean, isActive: Boolean) {
        when (callControlAction) {
            CallControlAction.MUTE -> {
                if (!isActive && wasActive) {
                    muteButton?.announceForAccessibility(getString(R.string.ujet_call_mute_disabled))
                }
            }
            CallControlAction.SPEAKER -> {
                if (!isActive && wasActive) {
                    speakerButton?.announceForAccessibility(getString(R.string.ujet_call_speaker_disabled))
                }
            }
            else -> {
                // Do nothing, state change announcement is not needed for minimize & end call.
            }
        }
    }

    override fun setAgentAvatar(avatarUrl: String) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        Graffiti.with(activity)
            .from(avatarUrl)
            .fallback(drawable.ujet_agent_sample)
            .into(agentAvatarView ?: return)
    }

    override fun showDisconnectCallButton() {
        callEndButton?.visibility = VISIBLE
    }

    override fun setTitleViewVisible(isVisible: Boolean) {
        titleTextView?.visibility = if (isVisible) {
            VISIBLE
        } else {
            GONE
        }
    }

    override fun showAgentName(agentName: String) {
        val previousAgentName = agentNameTextView?.text
        agentNameTextView?.text = agentName
        agentNameTextView?.visibility = VISIBLE
        descriptionTextView?.visibility = GONE
        announceAgentNameChange(agentNameTextView, previousAgentName, agentName)
    }

    private fun announceAgentNameChange(
        view: TextView?,
        previousAgentName: CharSequence?,
        newAgentName: String,
    ) {
        val isAgentDifferent =
            !previousAgentName?.toString().equals(newAgentName)
        if (isAgentDifferent) {
            agentNameTextView?.contentDescription = getString(R.string.ujet_call_agent_name_annoucement, newAgentName)
            view?.announceForAccessibility(getString(R.string.ujet_call_agent_name_annoucement, newAgentName))
        }
    }

    override fun showMultipleCallers(firstAgentName: String) {
        if (!isActive) {
            return
        }
        var multipleCallers = getString(string.ujet_incall_multiple_callers, firstAgentName)
        if (multipleCallers.endsWith("...")) {
            multipleCallers = multipleCallers.substring(0, multipleCallers.length - 3)
        }
        agentNameTextView?.text = multipleCallers
    }

    override fun showConnectingMessage(showRecordingDesc: Boolean) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (isInstantCallEnabled() && !hideRecordingMessage()) {
            val waitTimeStr = TimeUtil.getWaitTimeFormat(
                activity,
                Injection.provideLocalRepository(activity).callRepository.waitSeconds
            )
            val callWaitingContent = HtmlCompat.fromHtml(
                getString(string.ujet_call_waiting_content, waitTimeStr),
                HtmlCompat.FROM_HTML_MODE_LEGACY
            ).toString()

            titleTextView?.text = callWaitingContent
            titleTextView?.visibility = VISIBLE
            setEmptyViewProgressBarVisibility(false) // Hide empty view loading indicator
            if (showRecordingDesc) {
                descriptionTextView?.setText(string.ujet_call_waiting_description)
                descriptionTextView?.visibility = VISIBLE
            } else {
                descriptionTextView?.visibility = GONE
            }
        } else {
            //Hide below text in case of voice mail
            titleTextView?.visibility = GONE
            descriptionTextView?.visibility = GONE
        }
        agentNameTextView?.visibility = GONE
        ujetStyle().defaultAvatar?.let {
            agentAvatarView?.background = null
            agentAvatarView?.setImageResource(color.transparent)
            agentAvatarView?.setImageDrawable(it)
        }
    }

    override fun showTransferringMessage() {
        agentNameTextView?.setText(string.ujet_call_in_transfer)
    }

    override fun showVoicemailRecording() {
        setTitleViewVisible(true)
        titleTextView?.setText(string.ujet_incall_voicemail)
        agentNameTextView?.visibility = GONE
    }

    override fun showChronometer(startedAt: Long) {
        chronometer?.base = startedAt
        chronometer?.visibility = VISIBLE
        chronometer?.start()
        handleAccessibility(chronometer ?: return)
    }

    private fun handleAccessibility(view: View) {
        ViewCompat.setAccessibilityDelegate(view, object : AccessibilityDelegateCompat() {
            override fun onInitializeAccessibilityNodeInfo(
                host: View,
                info: AccessibilityNodeInfoCompat,
            ) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                val isViewFocused = host.isAccessibilityFocused
                // Need to announce when talkback focus moved to call duration timer
                if (isViewFocused) {
                    // To avoid duplicate announcements
                    if (!announcedContent) {
                        val callDurationTimerContent = getString(string.ujet_call_duration_timer_annoucement)
                        info.stateDescription = callDurationTimerContent
                        announcedContent = true
                    }
                } else {
                    announcedContent = false // Reset when focus moved out of timer view
                }
            }
        })
    }

    override fun hideChronometer() {
        chronometer?.visibility = GONE
    }

    override fun stopChronometer() {
        chronometer?.stop()
    }

    override fun hide() {
        activity?.finish()
    }

    override fun finish() {
        if (isActive) {
            ActivityHelper.finishAndRemoveTask(activity ?: return)
        }
        unbindCallServiceSilently()
    }

    override fun showCsatRating() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity)
        unbindCallServiceSilently()
        UjetCsatActivity.start(activity)
    }

    override fun showSurveyScreen() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }

        ActivityHelper.finishAndRemoveTask(activity)
        unbindCallServiceSilently()
        UjetSurveyActivity.start(activity)
    }

    override fun showMenus() {
        if (!isActive) {
            return
        }
        UjetActivity.startNewComm(activity ?: return, true)
    }

    override fun closeCallDeflection() {
        (parentFragmentManager.findFragmentByTag(CallDeflectionDialogFragment.TAG) as? CallDeflectionDialogFragment)?.dismiss()
    }

    override fun showCallDeflection(menuId: Int, callDeflection: CallDeflection) {
        if (isStateSaved) {
            return
        }
        CallDeflectionDialogFragment
            .newInstance(TAG, REQUEST_CODE_CALL_DEFLECTION, menuId, callDeflection)
            .show(parentFragmentManager, CallDeflectionDialogFragment.TAG)
    }

    private fun registerActivityForResult() {
        parentFragmentManager.setFragmentResultListener(TAG, this) { requestKey, result ->
            if (TAG != requestKey) {
                return@setFragmentResultListener
            }

            val requestCode = result.getInt(REQUEST_CODE)
            val resultCode = result.getInt(RESULT_CODE)
            when {
                requestCode == REQUEST_CODE_CALL_DEFLECTION -> {
                    val menuId = result.getInt(CallDeflection.KEY_MENU_ID, 0)
                    val type = result.getString(CallDeflection.KEY_TYPE)
                    val callDeflectionType = CallDeflectionType.get(type)
                    val deflectionData = result.getString(CallDeflection.KEY_DATA)
                    callDeflectionType?.let { deflectionType ->
                        if (deflectionType == CallDeflectionType.NONE) {
                            presenter?.restartDeflectionTimer()
                        } else {
                            presenter?.onCallDeflectionSelected(menuId,
                                deflectionType,
                                deflectionData)
                        }
                    }
                }
                requestCode == REQUEST_CODE_UNLOCK_SCREEN -> {
                    isWaitingScreenUnlockConfirm = false
                    if (resultCode == Activity.RESULT_OK) {
                        if (isScreenLocked()) {
                            if (VERSION.SDK_INT >= VERSION_CODES.O) {
                                requestUnlockScreen()
                            } else {
                                startTimerWaitingForScreenUnlock()
                            }
                        } else {
                            presenter?.onScreenUnlocked()
                        }
                    } else {
                        presenter?.onSmartActionCancelled()
                    }
                }
                requestCode == REQUEST_CODE_CALL_AFTER_HOUR && resultCode == Activity.RESULT_OK -> {
                    isAfterHourMessageDialogPending = false
                    presenter?.onCallAfterHoursDialogConfirmed()
                }
                requestCode == REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE -> {
                    when (resultCode) {
                        Activity.RESULT_OK -> {
                            if (result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false)) {
                                presenter?.onConfirmationDialogClicked(true)
                            } else if (result.getBoolean(ConfirmationDialogFragment.EXTRAS_SECOND_BUTTON_CLICKED, false)) {
                                presenter?.onConfirmationDialogClicked(false)
                            }
                        }
                        else -> {
                            finish()
                        }
                    }
                }
            }
        }
    }

    override fun showErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        networkDialog.show(parentFragmentManager, ConnectivityDialogFragment.TAG)
    }

    override fun hideErrorDialog() {
        if (!isActive || isStateSaved) {
            return
        }
        if (networkDialog.showsDialog) {
            networkDialog.closeDialog(parentFragmentManager)
        }
    }

    override fun showErrorMessage() {
        showErrorMessage(getString(string.ujet_error_call_connect_fail_android))
    }

    override fun showErrorMessage(message: String) {
        if (!isActive) {
            return
        }
        Toast.makeText(activity ?: return, message, Toast.LENGTH_LONG).show()
    }

    override fun showAfterHourMessage(message: String) {
        if (!isActive) {
            return
        }
        isAfterHourMessageDialogPending = true
        AlertDialogFragment.newInstance(
            TAG,
            REQUEST_CODE_CALL_AFTER_HOUR,
            null,
            message,
            false
        ).show(parentFragmentManager, AlertDialogFragment.TAG)
    }

    override fun isAfterHourMessageDialogPending() = isAfterHourMessageDialogPending

    override fun showEmailForm() {
        sendEvent("email", null)
        FragmentHelper.show(
            this,
            EmailFragment.newInstance(null, "over_capacity", true),
            EmailFragment.TAG
        )
    }

    override fun showEmailClient(email: String?, menuPath: String) {
        val activity = activity ?: return
        val emailClient = EmailClient()
        when {
            email.isNullOrEmpty() -> finish()
            emailClient.isAvailable(activity) -> {
                emailClient.start(activity, email, menuPath)
                finish()
            }
            else -> Toast.makeText(getActivity(), string.ujet_error_no_email_client, Toast.LENGTH_LONG).show()
        }
    }

    override fun showPhoneNumberInput(callCreateType: CallCreateType, phoneNumber: String) {
        unbindCallServiceSilently()
        sendEvent("phone", phoneNumber)
        FragmentHelper.popAndShow(
            this,
            PhoneNumberInputFragment.newInstance(callCreateType, phoneNumber, false, "over_capacity"),
            PhoneNumberInputFragment.TAG
        )
    }

    override fun showScheduleTimePicker() {
        unbindCallServiceSilently()
        sendEvent("scheduled_call", null)
        FragmentHelper.popAndShow(
            this,
            ScheduleTimePickerFragment.newInstance("over_capacity"),
            ScheduleTimePickerFragment.TAG
        )
    }

    private fun sendEvent(deflectionMode: String, phoneNumber: String?) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        if (TextUtils.isEmpty(phoneNumber)) {
            Injection.provideDeflectedEventManager(activity).sendDeflectedEvent("deflected", "over_capacity", deflectionMode)
        } else {
            Injection.provideDeflectedEventManager(activity).sendDeflectedEvent("deflected", "over_capacity", deflectionMode, phoneNumber)
        }
    }

    override fun showVoicemail(menuId: Int, voicemailReason: String) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        ActivityHelper.finishAndRemoveTask(activity)
        callServiceAdapter.stopCallService()
        unbindCallServiceSilently()
        if (!TextUtils.isEmpty(voicemailReason)) {
            Injection.provideDeflectedEventManager(activity).sendDeflectedEvent("deflected", voicemailReason, "voicemail")
        }
        startInstantCall(activity, menuId, voicemailReason, true)
    }

    override fun displayPendingSmartAction(biometricsVerification: BiometricsVerification) {
        if (isActive) {
            SmartActionManager.displayPendingSmartAction(biometricsVerification, parentFragmentManager, false)
        }
    }

    private fun isScreenLocked(): Boolean {
        val keyguardManager = activity?.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager
        return keyguardManager?.isKeyguardLocked == true && keyguardManager.isKeyguardSecure
    }

    @RequiresApi(VERSION_CODES.O)
    private fun requestUnlockScreen() {
        val activity = activity ?: return
        val keyguardManager = activity.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager ?: return
        if (!keyguardManager.isKeyguardLocked) {
            presenter?.onScreenUnlocked()
            return
        }
        keyguardManager.requestDismissKeyguard(
            activity,
            object : KeyguardDismissCallback() {
                override fun onDismissError() {
                    Logger.d("Error on dismissing the keyguard")
                    presenter?.onSmartActionCancelled()
                }

                override fun onDismissSucceeded() {
                    Logger.d("Dismissing the keyguard was succeeded")
                    presenter?.onScreenUnlocked()
                }

                override fun onDismissCancelled() {
                    Logger.d("Dismissing the keyguard was cancelled")
                    presenter?.onSmartActionCancelled()
                }
            }
        )
    }

    override fun getCachedRecordingPermission(): String {
        val activity: Activity? = activity
        return if (activity == null || !isActive) {
            RecordingPermissionUtils.RECORDING_PERMISSION_NOT_ASKED
        } else {
            getCachedRecordingPermission(activity.applicationContext)
        }
    }

    override fun setEscalateActionAvailable(enabled: Boolean) {
        escalateButton?.visibility = if (enabled) {
            VISIBLE
        } else {
            GONE
        }
    }

    override fun showEscalatingMessage() {
        agentNameTextView?.visibility = GONE
        titleTextView?.visibility = VISIBLE
        titleTextView?.setText(R.string.ujet_chat_notification_va_escalation)
        ujetStyle().defaultAvatar?.let {
            agentAvatarView?.setImageDrawable(it)
        } ?: run {
            agentAvatarView?.setImageResource(R.drawable.ujet_agent_sample)
        }
    }

    override fun setEscalateActionEnabled(enabled: Boolean) {
        escalateButton?.isEnabled = enabled
        if (enabled) {
            escalateButton?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
        } else {
            escalateButton?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            // Move the talkback focus back to titleTextView to announce - "We're connecting you to a human agent"
            titleTextView?.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED)
        }
    }

    override fun updateCoBrowseButton(isCoBrowseSupportedAndConnected: Boolean) {
        SmartActionManager.updateCoBrowseButton(CommunicationType.IncomingCall, cobrowseButton, isCoBrowseSupportedAndConnected)
    }

    override fun getCoBrowseUI(): CoBrowseUI? {
        return Injection.provideCoBrowseUI(activity ?: return null, this)
    }

    override fun getCoBrowseButtonView() = cobrowseButton

    override fun getMainFragmentManager() = parentFragmentManager

    override fun handleSmartActionsDuringScreenLock(smartActionType: SmartActionType) {
        val context = activity?.applicationContext ?: return
        if (!isActive) {
            return
        }
        SmartActionManager.handleSmartActionsDuringScreenLock(context, parentFragmentManager, smartActionType)
    }

    override fun setEmptyViewProgressBarVisibility(visible: Boolean) {
        emptyViewProgressBar?.visibility = if (visible) {
            VISIBLE
        } else {
            GONE
        }
    }

    override fun setCallScreenViewsVisibility(visible: Boolean) {
        setTitleViewVisible(visible)
        if (visible) {
            agentAvatarView?.visibility = VISIBLE
            descriptionTextView?.visibility = VISIBLE
            setCallControlsVisibility(VISIBLE)
        } else {
            agentAvatarView?.visibility = GONE
            descriptionTextView?.visibility = GONE
            setCallControlsVisibility(GONE)
        }
    }

    private fun setCallControlsVisibility(visibility: Int) {
        muteContainer?.visibility = visibility
        speakerContainer?.visibility = visibility
        showAppContainer?.visibility = visibility
    }


    override fun showPreSessionSmartActions(menuId: Int) {
        PsaRouter.showPreSessionSmartActions(this, ChannelType.ChannelCall, menuId, null)
    }

    override fun savePreferenceData(menuId: Int, voiceMailReason: String?) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        RecordingPermissionUtils.savePreferenceData(
            menuId,
            voiceMailReason,
            activity.applicationContext
        )
    }

    override fun showRecordingConfirmation() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        val dialogMessage = HtmlCompat.fromHtml(activity.getString(string.ujet_ask_to_record_description,
            ApplicationUtil.getApplicationName(activity)), HtmlCompat.FROM_HTML_MODE_LEGACY).toString()

        ConfirmationDialogFragment.newInstance(
            TAG,
            REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE,
            activity.getString(string.ujet_channel_title),
            dialogMessage,
            activity.getString(string.ujet_common_yes),
            activity.getString(string.ujet_common_no)
        ).show(parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    override fun saveRecordingPermissionStatus(recordingPermission: String) {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        RecordingPermissionUtils.saveRecordingPermissionStatus(
            recordingPermission,
            activity.applicationContext
        )
    }

    override fun resumeCommunication() {
        val activity = activity ?: return
        if (!isActive) {
            return
        }
        val sharedPreferences = RecordingPermissionUtils.getCachedPreferences(activity.applicationContext)
        val menuId = sharedPreferences.getInt(RecordingPermissionUtils.MENU_ID_KEY, 0)
        if (menuId > 0) {
            resumeCommunication(menuId)
        } else {
            finish()
        }
    }

    fun resumeCommunication(menuId: Int) {
        if (callService == null) {
            // when we come back from PSA screen, if call service is not connected to call fragment yet
            // add a flag and resume the call once it is connected.
            menuIdToResumeCall = menuId
        } else {
            callService?.resumeCallWithMenuId(menuId)
        }
    }

    private fun isInstantCallEnabled(): Boolean {
        return if (!isActive) {
            false
        } else {
            isInstantCallEnabled(activity ?: return false)
        }
    }

    private fun hideRecordingMessage(): Boolean {
        return if (!isActive) {
            false
        } else {
            hideRecordingMessage(activity ?: return false)
        }
    }

    private fun startTimerWaitingForScreenUnlock() {
        if (timerUntilDeviceUnlock != null || !isScreenLocked()) {
            return
        }
        timerUntilDeviceUnlock = Timer()
        timerUntilDeviceUnlock?.schedule(object : TimerTask() {
            override fun run() {
                MainLooper.post(Runnable {
                    if (timerUntilDeviceUnlock == null) {
                        return@Runnable
                    }
                    if (!isScreenLocked()) {
                        stopTimerWaitingForScreenUnlock()
                        presenter?.onScreenUnlocked()
                    }
                })
            }
        }, 1000, 500)
    }

    private fun stopTimerWaitingForScreenUnlock() {
        timerUntilDeviceUnlock?.cancel()
        timerUntilDeviceUnlock = null
    }

    private fun bindCallService(): Boolean {
        val activity = activity ?: return false
        if (!isActive) {
            return false
        }
        if (!ServiceUtil.isServiceRunning(activity, UjetCallService::class.java)) {
            return false
        }
        val intent = Intent(activity, UjetCallService::class.java)
        activity.bindService(intent, callServiceConnection, 0)
        isServiceBound = true
        Logger.d("Bind UjetCallService to %s", javaClass.simpleName)
        return true
    }

    private fun unbindCallService() {
        unbindCallServiceSilently()
        Logger.d("Unbind UjetCallService to %s", javaClass.simpleName)
    }

    @Suppress("UNCHECKED_CAST")
    private val callServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, binder: IBinder) {
            isServiceBound = true
            callService = (binder as? UjetBinder<UjetCallService>)?.service?.apply {
                registerCallListener(callListener ?: return)
                callServiceAdapter.bind(this)
            }
            Logger.d("%s is connected with UjetCallService", <EMAIL>)
            if (menuIdToResumeCall > 0) {
                callService?.resumeCallWithMenuId(menuIdToResumeCall)
                menuIdToResumeCall = -1 // reset
            }
        }

        override fun onServiceDisconnected(name: ComponentName) {
            Logger.d("%s is disconnected with UjetCallService", <EMAIL>)
            unbindCallServiceSilently()
            presenter?.onCallServiceUnavailable()
        }
    }

    private fun unbindCallServiceSilently() {
        if (isServiceBound) {
            try {
                activity?.unbindService(callServiceConnection)
            } catch (e: RuntimeException) {
                Logger.w(e, "Failed to unbind UjetCallService")
            }
            callServiceAdapter.unbind()
            callService?.unregisterCallListener(callListener ?: return)
            callService = null
            isServiceBound = false
        }
    }

    @SuppressLint("WakelockTimeout")
    protected fun acquireProximityWakeLockIfNeeded() {
        releaseProximityWakeLock()
        proximityWakeLock?.acquire()
    }

    private fun releaseProximityWakeLock() {
        if (proximityWakeLock?.isHeld == true) {
            proximityWakeLock?.release(PowerManager.RELEASE_FLAG_WAIT_FOR_NO_PROXIMITY)
        }
    }

    companion object {
        const val TAG = "InCallFragment"
        private const val EXTRA_ERROR_REASON = "error_reason"
        private const val EXTRA_ERROR_MESSAGE = "error_message"
        private const val EXTRA_ERROR_TYPE = "error_type"
        private const val REQUEST_CODE_CALL_DEFLECTION = 1
        private const val REQUEST_CODE_UNLOCK_SCREEN = 2
        private const val REQUEST_CODE_CALL_AFTER_HOUR = 3
        private const val REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE = 10011
        private const val EXTRA_SCREEN_UNLOCK_CONFIRM = "screen_unlock_confirm"
        private const val EXTRA_AFTER_HOUR_MESSAGE_PENDING = "after_hour_message_pending"
        private const val DEFAULT_VIEW_HEIGHT = 0
        private const val DEFAULT_VIEW_WIDTH = 0
        private const val VIEW_TOP_BOTTOM_COUNT = 2
        private const val CALL_BUTTON_MARGIN = 30
        private const val CALL_AGENT_AVATAR_VIEW_SIZE = 100
        private const val CALL_DEFAULT_GUIDE_LINE = 0.45f
        private const val CALL_RESIZE_GUIDE_LINE = 0.37f

        @JvmStatic
        fun newInstance(reason: String?, message: String?, isAfterHourMessage: Boolean): InCallFragment {
            val inCallFragment = InCallFragment()
            val bundle = Bundle()
            bundle.putString(EXTRA_ERROR_REASON, reason)
            bundle.putString(EXTRA_ERROR_MESSAGE, message)
            bundle.putBoolean(EXTRA_ERROR_TYPE, isAfterHourMessage)
            inCallFragment.arguments = bundle
            return inCallFragment
        }
    }
}

enum class CallControlAction {
    MUTE, SPEAKER, MINIMIZE, END_CALL;
    companion object {
        fun getIndexOf(callControlAction: CallControlAction): Int =
            CallControlAction.values().find { it == callControlAction }?.ordinal ?: 0
    }
}
