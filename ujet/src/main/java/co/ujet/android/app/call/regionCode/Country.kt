package co.ujet.android.app.call.regionCode

import java.util.*

class Country(countryCode: String, val regionCode: String, val name: String) {
    val countryCode = "+$countryCode"
    private var searchName: String? = null

    fun getSearchName(): String {
        if (searchName == null) {
            searchName = name.lowercase(Locale.getDefault())
            searchName = searchName?.replace("\\s".toRegex(), "")
        }
        return searchName ?: name
    }

    fun getNameWithFlag() = "$flagEmoji $name"

    fun getAccessibilityText() = name

    private val flagEmoji: String
        get() {
            val firstLetter = Character.codePointAt(regionCode, 0) - 0x41 + 0x1F1E6
            val secondLetter = Character.codePointAt(regionCode, 1) - 0x41 + 0x1F1E6
            return String(Character.toChars(firstLetter)) + String(Character.toChars(secondLetter))
        }

}