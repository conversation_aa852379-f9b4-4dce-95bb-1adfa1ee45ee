package co.ujet.android.app.call.incall

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.Keep
import androidx.annotation.LayoutRes
import androidx.core.text.HtmlCompat
import co.ujet.android.R
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.service.UjetCallService.Companion.startInstantCall

class CallInterruptedDialogFragment @Keep constructor() : BaseDialogFragment() {

    private var inflater: LayoutInflater? = null

    private var viewContainer: ViewGroup? = null
    private var interruptedView: View? = null

    private var message: String? = null
    private var menuId: Int? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        inflater = LayoutInflater.from(activity)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        viewContainer = inflater?.inflate(R.layout.ujet_dialog_base_rating, null) as ViewGroup?
        interruptedView = createView(R.layout.ujet_call_interrupted_dialog)

        return builder
            .customView(interruptedView)
            .cancelable(false)
            .height(ViewGroup.LayoutParams.WRAP_CONTENT)
            .gravity(Gravity.CENTER)
            .withExit(false)
            .build()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewContainer = null
        interruptedView = null
    }

    override fun onBack() {
        activity?.finish()
    }

    private fun createView(@LayoutRes resId: Int): ViewGroup {
        val interruptedView = inflater?.inflate(resId, null) as ViewGroup

        interruptedView.findViewById<TextView?>(R.id.message).apply {
            text = HtmlCompat.fromHtml(
                String.format(getString(R.string.ujet_call_reconnect_content), message),
                HtmlCompat.FROM_HTML_MODE_LEGACY
            )
            typeface = ujetStyle().typeFace
            setTextColor(ujetStyle().textSecondaryColor)
        }

        interruptedView.findViewById<FancyButton>(R.id.first_button).apply {
            setOnClickListener {
                activity?.let { activity ->
                    menuId?.let { id ->
                        startInstantCall(activity, id, null, false)
                        activity.finish()
                    }
                }
            }
        }
        interruptedView.findViewById<FancyButton>(R.id.second_button).apply {
            setOnClickListener {
                activity?.finish()
            }
        }
        return interruptedView
    }

    fun setMessage(message: String) {
        this.message = message
    }

    fun setMenuId(menuId: Int) {
        this.menuId = menuId
    }

    companion object {
        const val TAG = "CallInterruptedDialogFragment"

        fun newInstance(): CallInterruptedDialogFragment {
            return CallInterruptedDialogFragment()
        }
    }
}
