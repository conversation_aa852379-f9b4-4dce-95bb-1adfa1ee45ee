package co.ujet.android.app.channel

import android.os.Bundle
import co.ujet.android.UjetOption.Companion.DEFAULT_FALLBACK_SENSITIVITY
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.audiblemessage.usecase.GetAudibleMessages
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.executeImmediate
import co.ujet.android.clean.domain.menu.filter.MenuFilter
import co.ujet.android.clean.domain.menu.usecase.GetMenuPath
import co.ujet.android.clean.domain.menu.usecase.GetMenus
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu
import co.ujet.android.clean.domain.menu.usecase.SaveSelectedMenu
import co.ujet.android.clean.entity.company.Company
import co.ujet.android.clean.entity.menu.Menu
import co.ujet.android.clean.entity.menu.MenuContactOption
import co.ujet.android.clean.entity.menu.channel.*
import co.ujet.android.common.TaskCallback
import co.ujet.android.common.util.PreferredChannelUtil
import co.ujet.android.common.util.RecordingPermissionUtils
import co.ujet.android.commons.domain.ExternalDeflectionLink
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.model.WaitTimes
import co.ujet.android.internal.Configuration
import co.ujet.android.libs.logger.Logger
import java.net.HttpURLConnection

class ChannelPresenter(
        private val configuration: Configuration,
        private val ujetContext: UjetContext,
        private val localRepository: LocalRepository,
        private val apiManager: ApiManager,
        private val view: ChannelContract.View,
        private val useCaseHandler: UseCaseHandler,
        private val getCompany: GetCompany,
        private val getMenus: GetMenus,
        private val getMenuPath: GetMenuPath,
        private val getSelectedMenu: GetSelectedMenu,
        private val getAudibleMessages: GetAudibleMessages,
        private val saveSelectedMenu: SaveSelectedMenu,
        private val loadingStateInteractor: LoadingStateInteractor,
        private var fetchRootMenus: Boolean,
        private var channelRouter: ChannelRouter
) : ChannelContract.Presenter, ChannelRouter.Delegate {
    private var areRootMenusUpdated = false
    private var currentMenu: Menu? = null
    private var cachedCompany: Company? = null
    private var isEmailEnhancementEnabled = false
    private var isLoadingStateEnabled = false
    private var isPstnFallbackEnabled = false
    private var isAskToRecordEnabled = false
    private var isSingleMenu = false
    private var isSingleChannelEnabled = false
    private var sensitivity = DEFAULT_FALLBACK_SENSITIVITY
    private var channelType = ChannelType.ChannelNone

    override fun start() {
        Logger.d("presenter start")
        getCachedCompany()
        getFallbackSensitivity()

        currentMenu?.let { currentMenu ->
            activateEnabledChannels(currentMenu.channels.getEnabledChannels(cachedCompany), true)
        } ?: run {
            getSelectedMenuAndUpdateView()
        }

        if (fetchRootMenus) {
            isSingleMenu = true
            getForceUpdatedRootMenus()
            getCompanyForceUpdated()
        } else {
            isSingleMenu = false
            currentMenu?.channels?.getEnabledChannels(cachedCompany)?.let { enabledChannels ->
                if (view.isActive && !view.isVoIPAvailable(sensitivity) && isPstnFallbackEnabled) {
                    substituteInstantCallToPstnCall(enabledChannels)
                }
                val activePreferredChannel: Channel? = PreferredChannelUtil.getActivePreferredChannel(
                    enabledChannels, ujetContext.preferredChannel)
                if (activePreferredChannel != null) {
                    selectChannel(activePreferredChannel)
                }
            }
        }
    }

    private fun getCachedCompany() {
        useCaseHandler.executeImmediate(getCompany, GetCompany.RequestValues(false, true),
                object : UseCaseCallback<GetCompany.ResponseValue> {
                    override fun onSuccess(response: GetCompany.ResponseValue) {
                        cachedCompany = response.company
                        isEmailEnhancementEnabled = response.company.isEmailEnhancementEnabled
                        isPstnFallbackEnabled = response.company.isPstnFallbackEnabled
                    }

                    override fun onError() {
                        cachedCompany = null
                    }
                })
    }

    private fun getFallbackSensitivity() {
        sensitivity = if (isPstnFallbackEnabled) {
            cachedCompany?.fallbackNumberThreshold ?: configuration.pstnFallbackSensitivity
        } else {
            DEFAULT_FALLBACK_SENSITIVITY
        }

        Logger.i("Fallback number sensitivity is [%.2f]", sensitivity)
    }

    private fun setFallbackSensitivity(company: Company) {
        sensitivity = if (isPstnFallbackEnabled) {
            company.fallbackNumberThreshold ?: configuration.pstnFallbackSensitivity
        } else {
            DEFAULT_FALLBACK_SENSITIVITY
        }

        Logger.i("Fallback number sensitivity after updating cache is [%.2f]", sensitivity)
    }

    private fun updateCompany(company: Company) {
        this.cachedCompany = company
    }

    private fun getSelectedMenuAndUpdateView() {
        useCaseHandler.executeImmediate(getSelectedMenu,
                GetSelectedMenu.RequestValues(ujetContext.directAccessKey),
                object : UseCaseCallback<GetSelectedMenu.ResponseValue> {
                    override fun onSuccess(response: GetSelectedMenu.ResponseValue) {
                        currentMenu = response.menu
                        setSingleChannelEnabled(response.menu)
                        setAskToRecordEnabled(response.menu.recordingOption)
                        activateEnabledChannels(response.menu.channels.getEnabledChannels(cachedCompany))
                    }

                    override fun onError() {
                        isAskToRecordEnabled = false
                        if (view.isActive) {
                            view.showMenuUpdated()
                            view.restart()
                        }
                    }
                })
    }

    private fun setAskToRecordEnabled(recordingOption: String?) {
        isAskToRecordEnabled = if (recordingOption.isNullOrEmpty()) {
            false
        } else {
            RecordingPermissionUtils.RECORD_ASK_USER == recordingOption
        }
    }

    private fun setSingleChannelEnabled(selectedMenu: Menu?) {
        isSingleChannelEnabled = selectedMenu != null
                && selectedMenu.channels.getEnabledChannels(cachedCompany).size == 1
                && !configuration.isShowSingleChannelEnabled
    }

    private fun activateEnabledChannels(enabledChannels: MutableList<Channel>, isBackButtonClicked: Boolean = false) {
        if (view.isActive && !view.isVoIPAvailable(sensitivity) && isPstnFallbackEnabled) {
            substituteInstantCallToPstnCall(enabledChannels)
        }

        view.updateEnabledChannels(enabledChannels, fetchRootMenus, areRootMenusUpdated, isBackButtonClicked)
        setChannelType(enabledChannels)
        if (enabledChannels.size == 1) {
            val channel = enabledChannels[0]
            if (ujetContext.directAccessKey != null && !areRootMenusUpdated) {
                if (configuration.isShowSingleChannelEnabled) {
                    updateWaitTimesAndMenuPath()
                } else {
                    when (channel) {
                        is ExternalDeflectionLinks -> {
                            val channelLinksSize = channel.links?.size
                            if (channelLinksSize == 1) {
                                showLoadingState(ChannelType.ChannelExternalDeflectionLinks)
                            } else {
                                updateWaitTimesAndMenuPath()
                            }
                        }
                        is InstantCallChannel -> showLoadingState(ChannelType.ChannelCall)
                        is ChatChannel -> showLoadingState(ChannelType.ChannelChat)
                        is EmailChannel -> showLoadingState(ChannelType.ChannelEmail)
                        is PstnCallChannel -> showLoadingState(ChannelType.ChannelPstnCall)
                        is VoicemailChannel -> showLoadingState(ChannelType.ChannelVoiceMail)
                        is ScheduledCallChannel -> showLoadingState(ChannelType.ChannelScheduleCall)
                        else -> selectChannel(channel)
                    }
                }
            } else {
                // When there is one enabled channel, a menu will be selected automatically only if isShowSingleChannelEnabled option is configured OFF
                if (configuration.isShowSingleChannelEnabled) {
                    updateWaitTimesAndMenuPath()
                } else {
                    when (channel) {
                        is ExternalDeflectionLinks -> {
                            if (channel.links?.size == 1) {
                                selectChannel(channel)
                            } else {
                                updateWaitTimesAndMenuPath()
                            }
                        }
                        is InstantCallChannel -> {
                            if (isVoIPAndPstnConfigDisabled()) {
                                view.showNoDataConnectivityError()
                            } else {
                                selectChannel(channel)
                            }
                        }
                        else -> selectChannel(channel)
                    }
                }
            }
        } else {
            updateWaitTimesAndMenuPath()
        }
    }

    private fun setChannelType(enabledChannels: List<Channel>) {
        for (channel in enabledChannels) {
            if (!channel.isEnabled) continue
            channelType = when (channel) {
                is EmailChannel -> ChannelType.ChannelEmail
                is ChatChannel -> ChannelType.ChannelChat
                is InstantCallChannel -> ChannelType.ChannelCall
                is PstnCallChannel -> ChannelType.ChannelPstnCall
                is ScheduledCallChannel -> ChannelType.ChannelScheduleCall
                is VoicemailChannel -> ChannelType.ChannelVoiceMail
                is ExternalDeflectionLinks -> ChannelType.ChannelExternalDeflectionLinks
                else -> ChannelType.ChannelNone
            }
        }
        if (enabledChannels.size > 1) {
            channelType = ChannelType.ChannelNone
        }
    }

    private fun showLoadingState(channelType: ChannelType) {
        if (view.isActive) {
            view.showLoadingState(channelType)
            isLoadingStateEnabled = true
        }
    }

    private fun updateWaitTimesAndMenuPath() {
        if (view.isActive) {
            view.updateCallWaitTime(-1)
            view.updateChatWaitTime(-1)
        }
        updateWaitTimes()

        /**
         * [UJET-15103] Do not display after hour message when “Display After Hour Message Deflection” is off in queue level.
         */
        val hasDeflectedChannelByAfterHours = currentMenu?.channels?.hasDeflectedChannelByAfterHours() ?: false
        val isAfterHourSettingEnabled = currentMenu?.afterHoursMenuSetting?.message?.isEnabled ?: false
        if (hasDeflectedChannelByAfterHours && isAfterHourSettingEnabled) {
            getCompany(object : TaskCallback<Company?> {
                override fun onTaskSuccess(result: Company?) {
                    val isEnabled = result?.afterHoursSetting?.isEnabled ?: false
                    if (isEnabled) {
                        displayAfterHoursMessage()
                    } else {
                        displayMenuPath()
                    }
                }

                override fun onTaskFailure() {
                    displayMenuPath()
                }
            })
        } else {
            displayMenuPath()
        }
    }

    private fun getForceUpdatedRootMenus() {
        getRootMenus(
                GetMenus.RequestValues.createForceUpdated(
                        ujetContext.directAccessKey, MenuFilter.VISIBLE))
    }

    private fun getRootMenus(requestValues: GetMenus.RequestValues) {
        useCaseHandler.execute(getMenus, requestValues,
                object : UseCaseCallback<GetMenus.ResponseValue> {
                    override fun onSuccess(response: GetMenus.ResponseValue) {
                        areRootMenusUpdated = true
                        isSingleMenu = false //Reset as we fetched latest menus
                        val menus = response.menus
                        if (menus.isNotEmpty()) {
                            val dapMenu = menus[0]
                            if (isLoadingStateEnabled) {
                                loadingStateInteractor.onFinished(response)
                                isLoadingStateEnabled = false
                            } else if (response.isDirectAccess) {
                                if (dapMenu.redirect != null) {
                                    redirect(dapMenu, dapMenu.redirect)
                                } else if (dapMenu.isLeafMenu) {
                                    isSingleMenu = true
                                    val enabledChannels = dapMenu.channels.getEnabledChannels(cachedCompany)
                                    if (enabledChannels.isNotEmpty()) {
                                        currentMenu = dapMenu
                                        if (view.isActive && !view.isVoIPAvailable(sensitivity) && isPstnFallbackEnabled) {
                                            substituteInstantCallToPstnCall(enabledChannels)
                                        }
                                        val activePreferredChannel = PreferredChannelUtil.getActivePreferredChannel(
                                            enabledChannels, ujetContext.preferredChannel)
                                        if (activePreferredChannel != null) {
                                            selectChannel(activePreferredChannel)
                                        } else {
                                            activateEnabledChannels(enabledChannels)
                                            saveSelectedMenu(dapMenu.id)
                                        }
                                    } else {
                                        if (view.isActive) {
                                            view.showErrorDialog()
                                        }
                                    }
                                } else if (view.isActive) {
                                    view.restart()
                                }
                            } else if (view.isActive) {
                                view.restart()
                            }
                        } else if (view.isActive) {
                            view.restart()
                        }
                        fetchRootMenus = false
                    }

                    override fun onError() {
                        if (view.isActive) {
                            view.showErrorDialog()
                        }
                    }
                })
    }

    /**
     * Get company information from server
     */
    private fun getCompanyForceUpdated() {
        useCaseHandler.execute(getCompany, GetCompany.RequestValues(true),
                object : UseCaseCallback<GetCompany.ResponseValue> {
                    override fun onSuccess(response: GetCompany.ResponseValue) {
                        val company = response.company
                        isEmailEnhancementEnabled = company.isEmailEnhancementEnabled
                        isPstnFallbackEnabled = company.isPstnFallbackEnabled
                        setFallbackSensitivity(company)
                        updateCompany(company)
                    }

                    override fun onError() {
                        if (view.isActive) {
                            view.showErrorDialog()
                            view.finish()
                        }
                    }
                })
    }

    private fun saveSelectedMenu(menuId: Int) {
        useCaseHandler.executeImmediate(saveSelectedMenu,
                SaveSelectedMenu.RequestValues(menuId, ujetContext.directAccessKey),
                object : UseCaseCallback<SaveSelectedMenu.ResponseValue> {
                    override fun onSuccess(response: SaveSelectedMenu.ResponseValue) {
                        //We are just updating menu id so we do not need to do anything here.
                    }

                    override fun onError() {
                        if (view.isActive) {
                            view.showMenuUpdated()
                            view.restart()
                        }
                    }
                })
    }

    private fun redirect(dapMenu: Menu, redirection: MenuContactOption) {
        channelRouter.routeManualRedirection(dapMenu, redirection)
    }

    private fun getCompany(callback: TaskCallback<Company?>) {
        if (cachedCompany != null) {
            callback.onTaskSuccess(cachedCompany)
        } else {
            useCaseHandler.executeImmediate(getCompany,
                    GetCompany.RequestValues(true),
                    object : UseCaseCallback<GetCompany.ResponseValue> {
                        override fun onSuccess(response: GetCompany.ResponseValue) {
                            cachedCompany = response.company
                            callback.onTaskSuccess(cachedCompany)
                        }

                        override fun onError() {
                            callback.onTaskFailure()
                        }
                    })
        }
    }

    /**
     * [UJET-14839] Disable VoIP call and enable PSTN call when the network is bad
     *
     * @param channels filtered channels
     */
    private fun substituteInstantCallToPstnCall(channels: MutableList<Channel>) {
        var instantCallChannel: InstantCallChannel? = null
        var pstnCallChannel: PstnCallChannel? = null
        for (channel in channels) {
            if (channel is InstantCallChannel) {
                instantCallChannel = channel
            }
            if (channel is PstnCallChannel) {
                pstnCallChannel = channel
            }
        }
        if (instantCallChannel == null) return
        instantCallChannel.isEnabled = false
        channels.remove(instantCallChannel)
        if (pstnCallChannel == null) {
            pstnCallChannel = PstnCallChannel(instantCallChannel)
            channels.add(pstnCallChannel)
        } else {
            pstnCallChannel.isEnabled = true
        }
        Logger.i("VoIP call is substituted because of bad network")
    }

    private fun displayAfterHoursMessage() {
        val afterHoursMessage = currentMenu?.afterHoursMenuSetting?.message
        if (afterHoursMessage != null && afterHoursMessage.isCustomized) {
            if (view.isActive) {
                view.showTitleAndDescription(
                        afterHoursMessage.header,
                        afterHoursMessage.text)
            }
        } else {
            useCaseHandler.executeImmediate(
                    getAudibleMessages,
                    GetAudibleMessages.RequestValues(false),
                    object : UseCaseCallback<GetAudibleMessages.ResponseValue> {
                        override fun onSuccess(response: GetAudibleMessages.ResponseValue) {
                            var header: String? = null
                            var message: String? = null
                            val audibleMessages = response.audibleMessages
                            if (audibleMessages.afterHoursHeader != null) {
                                header = audibleMessages.afterHoursHeader?.text
                            }
                            if (audibleMessages.afterHoursMessage != null) {
                                message = audibleMessages.afterHoursMessage?.text
                            }
                            if (view.isActive) {
                                view.showTitleAndDescription(header, message)
                            }
                        }

                        override fun onError() {
                            displayMenuPath()
                        }
                    })
        }
    }

    private fun displayMenuPath() {
        useCaseHandler.executeImmediate(
            useCase = getMenuPath,
            requestValue = GetMenuPath.RequestValues(currentMenu?.id ?: Int.MIN_VALUE, ujetContext.directAccessKey),
            onSuccess = {
                view.updateMenuPath(view.adjustMenuPathForRtl(it.path), channelType)
            },
            onError = {
                view.updateMenuPath(view.adjustMenuPathForRtl(currentMenu?.name), channelType)
            }
        )
    }

    override fun initialize() {
        channelRouter.initialize(this)
    }

    override fun handleOnResume() {
        channelRouter.handleOnResume()
    }

    override fun handleOnPause() {
        channelRouter.handleOnPause()
    }

    override fun clearResources() {
        channelRouter.clearResources()
    }

    override fun isVoIPAndPstnConfigDisabled(): Boolean {
        return !isPstnFallbackEnabled && view.isActive && !view.isVoIPAvailable(sensitivity)
    }

    override fun selectChannel(channel: Channel) {
        setSingleChannelEnabled(currentMenu)
        setAskToRecordEnabled(currentMenu?.recordingOption)
        currentMenu?.let { channelRouter.select(it, channel, cachedCompany) }
    }

    override fun selectChannel(externalDeflectionLink: ExternalDeflectionLink) {
        setSingleChannelEnabled(currentMenu)
        setAskToRecordEnabled(currentMenu?.recordingOption)
        channelRouter.selectExternalDeflectionLink(externalDeflectionLink, isSingleChannelAndLinkEnabled())
    }

    override fun updateWaitTimes() {
        val menuId = currentMenu?.id
        val language = localRepository.userPreferredLanguage
        Logger.d("displayWaitingTime menu id [%d] for language [%s]", menuId, language)

        apiManager.getWaitTimes(menuId ?: Int.MIN_VALUE, language, object : ApiCallback<WaitTimes> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<WaitTimes>) {
                val waitTimes = response.body()
                if (response.code() == HttpURLConnection.HTTP_OK && waitTimes != null) {
                    val chatWaitTimeSecs = waitTimes.chatWaitTime
                    localRepository.chatRepository.waitSeconds = chatWaitTimeSecs
                    val callWaitTimeSecs = waitTimes.voiceCallWaitTime
                    localRepository.callRepository.waitSeconds = callWaitTimeSecs
                    if (view.isActive) {
                        view.updateChatWaitTime(chatWaitTimeSecs)
                        view.updateCallWaitTime(callWaitTimeSecs)
                    }
                } else {
                    Logger.w("displayWaitingTime error with code [%d]", response.code())
                    if (view.isActive) {
                        view.updateChatWaitTime(60 * 60 * 24)
                        view.updateCallWaitTime(60 * 60 * 24)
                    }
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                Logger.w(throwable, "displayWaitingTime error")
                if (view.isActive) {
                    view.updateChatWaitTime(60 * 60 * 24)
                    view.updateCallWaitTime(60 * 60 * 24)
                }
            }
        })
    }

    override fun onErrorDialogConfirmed() {
        if (view.isActive) {
            view.finish()
        }
    }

    override fun startPresenter() {
        start()
    }

    override fun handleActivityForResult(requestKey: String, result: Bundle) {
        view.handleActivityForResult(requestKey, result)
    }

    private fun isSingleChannelAndLinkEnabled(): Boolean {
        val enabledChannels: MutableList<Channel>? = currentMenu?.channels?.getEnabledChannels(cachedCompany)
        var isSingleLinkEnabled = false

        if (enabledChannels?.isNotEmpty() == true && enabledChannels.size == 1) {
            val channel = enabledChannels[0]
            if (channel is ExternalDeflectionLinks && channel.links?.size == 1) {
                isSingleLinkEnabled = true
            }
        }

        return isSingleChannelEnabled && isSingleLinkEnabled
    }
}
