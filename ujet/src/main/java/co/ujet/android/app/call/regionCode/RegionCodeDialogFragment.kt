package co.ujet.android.app.call.regionCode

import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.ViewGroup.LayoutParams
import android.widget.EditText
import android.widget.ListView
import androidx.annotation.Keep
import androidx.core.os.bundleOf
import co.ujet.android.R
import co.ujet.android.app.call.regionCode.RegionCodeContract.Presenter
import co.ujet.android.app.call.regionCode.RegionCodeContract.View
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.internal.Injection
import java.util.ArrayList

class RegionCodeDialogFragment @Keep constructor() : BaseDialogFragment(), View {
    private var presenter: Presenter? = null
    private var countryListView: ListView? = null

    private var requestKey : String? = null
    private var requestCode: Int = Integer.MIN_VALUE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            requestKey = it.getString(ARGS_REQUEST_KEY, null)
            requestCode = it.getInt(ARGS_REQUEST_CODE, Integer.MIN_VALUE)
        }
        presenter = RegionCodePresenter(Injection.provideLocalRepository(context ?: return), this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = builder
            .gravity(Gravity.CENTER)
            .customView(R.layout.ujet_dialog_region_code)
            .title(R.string.ujet_country_code_selection_title)
            .height(LayoutParams.MATCH_PARENT)
            .width(LayoutParams.MATCH_PARENT)
            .build()
        val activity = activity ?: return dialog
        countryListView = dialog.findViewById<ListView>(R.id.country_list_view)?.apply {
            adapter = CountryListAdapter(activity, ujetStyle(), ArrayList()) { position ->
                this.dividerHeight = if (position < 2) {
                    1
                } else {
                    2
                }
            }
            setOnItemClickListener { parent, _, position, _ ->
                val country = parent.adapter.getItem(position) as Country
                presenter?.onCountryClicked(country)
            }
        }
        val searchKeywordEditText = dialog.findViewById<EditText>(R.id.country_search)
        UjetViewStyler.stylePrimaryEditText(ujetStyle(), searchKeywordEditText)
        searchKeywordEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                presenter?.onSearchKeywordChanged(s.toString())
            }
        })
        return dialog
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        countryListView = null
    }

    override fun isActive(): Boolean {
        return isAdded
    }

    override fun updateCountryList(countries: List<Country?>) {
        val countryListAdapter = countryListView?.adapter as CountryListAdapter
        countryListView?.adapter = null
        countryListAdapter.clear()
        countryListAdapter.addAll(countries)
        countryListView?.adapter = countryListAdapter
    }

    override fun setResult(regionCode: String?) {
        if (!isActive) return
        val bundle = bundleOf(
            EXTRAS_REGION_CODE to regionCode,
            REQUEST_CODE to requestCode,
            RESULT_CODE to Activity.RESULT_OK
        )
        requestKey?.let { key ->
            parentFragmentManager.setFragmentResult(key, bundle)
        }
    }

    override fun close() {
        dismiss()
    }

    companion object {
        const val TAG = "RegionCodeDialogFragment"
        const val EXTRAS_REGION_CODE = "region_code"
        fun newInstance(requestKey: String, requestCode: Int): RegionCodeDialogFragment {
            val fragment = RegionCodeDialogFragment()
            fragment.arguments = bundleOf(
                ARGS_REQUEST_KEY to requestKey,
                ARGS_REQUEST_CODE to requestCode
            )
            return fragment
        }
    }
}
