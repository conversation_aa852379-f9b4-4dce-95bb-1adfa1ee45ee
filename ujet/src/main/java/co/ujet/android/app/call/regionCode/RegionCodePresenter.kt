package co.ujet.android.app.call.regionCode

import co.ujet.android.app.call.regionCode.RegionCodeContract.Presenter
import co.ujet.android.app.call.regionCode.RegionCodeContract.View
import co.ujet.android.data.LocalRepository
import com.google.i18n.phonenumbers.PhoneNumberUtil
import java.text.Collator
import java.util.ArrayList
import java.util.Collections
import java.util.Comparator
import java.util.Locale

internal class RegionCodePresenter(private val localRepository: LocalRepository, private val view: View) : Presenter {
    private var prioritizedCountries: List<Country?>? = null
    private var supportedCountries: List<Country?>? = null
    private val phoneNumberUtil = PhoneNumberUtil.getInstance()
    private var isStarted = false
    private var lastSearchKeyword: String? = null

    init {
        updateSupportedCountries()
    }

    override fun start() {
        isStarted = true
        if (lastSearchKeyword != null) {
            onSearchKeywordChanged(lastSearchKeyword)
        } else {
            if (view.isActive) {
                view.updateCountryList(supportedCountries ?: return)
            }
        }
    }

    private fun updateSupportedCountries() {
        var highPriorityCountry: Country? = null
        val deviceCountry = Locale.getDefault().country
        val language = localRepository.userPreferredLanguage
        val countryFactory = CountryFactory(language)

        // Supported countries
        val countries: MutableList<Country?> = ArrayList()
        for (region in phoneNumberUtil.supportedRegions) {
            val country = countryFactory.create(region)
            countries.add(country)

            // [UJET-8377] Add a high priority country
            if (region == deviceCountry) {
                highPriorityCountry = countryFactory.create(region)
            }
        }
        Collections.sort(countries, object : Comparator<Country?> {
            private val collator = Collator.getInstance(Locale.US)
            override fun compare(o1: Country?, o2: Country?): Int {
                return collator.compare(o1?.name, o2?.name)
            }
        })
        supportedCountries = ArrayList(countries)
        if (highPriorityCountry != null) {
            countries.add(0, highPriorityCountry)
            countries.add(1, null)
        }
        prioritizedCountries = countries
    }

    override fun onCountryClicked(country: Country) {
        if (view.isActive) {
            view.setResult(country.regionCode)
            view.close()
        }
    }

    override fun onSearchKeywordChanged(searchKeyword: String?) {
        var keyword = searchKeyword
        lastSearchKeyword = keyword
        if (!isStarted) {
            return
        }
        if (keyword.isNullOrEmpty()) {
            if (view.isActive) {
                view.updateCountryList(prioritizedCountries ?: return)
            }
            return
        }
        keyword = keyword.toLowerCase().replace("\\s".toRegex(), "")
        val filteredCountries: MutableList<Country?> = ArrayList()
        supportedCountries?.filterNotNull()?.forEach { country ->
            if (country.getSearchName().contains(keyword)) {
                filteredCountries.add(country)
            }
        }
        if (view.isActive) {
            view.updateCountryList(filteredCountries)
        }
    }

    private inner class CountryFactory(private val language: String) {
        private val languageLocale = Locale(language, "")

        fun create(region: String): Country {
            val code = phoneNumberUtil.getCountryCodeForRegion(region)
            val countryCode = code.toString()
            val countryName = Locale(language, region).getDisplayCountry(languageLocale)
            return Country(countryCode, region, countryName)
        }

    }
}
