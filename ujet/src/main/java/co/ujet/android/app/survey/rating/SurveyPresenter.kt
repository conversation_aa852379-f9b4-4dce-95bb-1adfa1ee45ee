package co.ujet.android.app.survey.rating

import android.widget.TextView
import co.ujet.android.api.request.SurveyRequest
import co.ujet.android.app.survey.error.SurveyErrorType
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.survey.usecase.GetSurvey
import co.ujet.android.clean.domain.survey.usecase.SendSurvey
import co.ujet.android.clean.entity.survey.SurveyQuestion
import co.ujet.android.data.LocalRepository

class SurveyPresenter(
    private val view: SurveyContract.View,
    private val localRepository: LocalRepository,
    private val useCaseHandler: UseCaseHandler,
    private val getSurvey: GetSurvey,
    private val sendSurvey: SendSurvey
) : SurveyContract.Presenter {
    private var questions: List<SurveyQuestion>? = null
    private var surveyAnswers = hashMapOf<Int, String>()
    private var signOffText: String? = null
    private var csatQuestionExit = false
    private var csatAnswered = false
    private var surveyDataRequested = false

    override fun start() {
        if (!surveyDataRequested) {
            //Show loading indicator and grey out submit button
            view.setLoadingIndicator(true)
            view.setSubmitButton(false)
            requestSurveyData()
        } else if (!questions.isNullOrEmpty()) {
            view.setSubmitButton(surveyAnswers.isNotEmpty())
            updateSurveyViews()
        }
    }

    override fun onCsatAnswered(answered: Boolean) {
        csatAnswered = answered
    }

    override fun onRatingChanged(csatViewChanged: Boolean, rating: Int, questionId: Int) {
        val answered = rating > 0
        if (answered) {
            onSurveyAnswered(questionId, rating.toString())
        } else {
            onAnswerCleared(questionId)
        }
        if (csatViewChanged) {
            onCsatAnswered(answered)
            view.showRequiredViews(requestFocus = false, csatAnswered = answered)
        }
    }

    override fun onSurveyAnswered(questionId: Int, answerKey: String) {
        if (answerKey.isNotEmpty()) {
            surveyAnswers[questionId] = answerKey
        } else {
            surveyAnswers.remove(questionId)
        }
        view.setSubmitButton(surveyAnswers.isNotEmpty())
    }

    override fun onAnswerCleared(questionId: Int) {
        surveyAnswers.apply {
            if (contains(questionId)) remove(questionId)
        }
    }

    override fun onFreeFormTextChanged(text: String, textView: TextView) {
        view.updateFreeFormTextCount(text.length, textView)
    }

    private fun requestSurveyData() {
        surveyDataRequested = true
        val communication = localRepository.rateRepository.rateTarget ?: return
        useCaseHandler.executeImmediate(getSurvey, GetSurvey.RequestValues(communication.urlPath, communication.id),
            object : UseCaseCallback<GetSurvey.ResponseValue> {
                override fun onSuccess(response: GetSurvey.ResponseValue) {
                    //Stop loading indicator
                    view.setLoadingIndicator(false)
                    questions = response.data.questions
                    signOffText = response.data.signOffText
                    if (questions.isNullOrEmpty()) {
                        close()
                    } else {
                        updateSurveyViews()
                    }
                }

                override fun onError() {
                    //Stop loading indicator
                    view.setLoadingIndicator(false)
                    view.showErrorScreen(SurveyErrorType.RE_TRY_GET_SURVEY_DATA)
                }
            })
    }

    private fun updateSurveyViews() {
        questions?.let { questions ->
            for (question in questions) {
                if (question.id == null || question.displayText.isNullOrEmpty()) {
                    continue
                }
                when (question.type) {
                    QUESTION_TYPE_CSAT -> {
                        csatQuestionExit = true
                        view.addCsatView(question.id, question.displayText)
                    }

                    QUESTION_TYPE_STAR -> {
                        view.addStarRatedView(question.id, question.displayText)
                    }

                    QUESTION_TYPE_NUMERIC_SCALE -> {
                        if (question.validAnswers?.isNotEmpty() == true) {
                            view.addNumericScaleView(question.id, question.displayText, question.validAnswers)
                        }
                    }

                    QUESTION_TYPE_ENUMERATED_TEXT -> {
                        if (question.validAnswers?.isNotEmpty() == true) {
                            view.addEnumeratedTextView(question.id, question.displayText, question.validAnswers)
                        }
                    }

                    QUESTION_TYPE_FREE_FORM -> {
                        view.addFreeFormView(question.id, question.displayText)
                    }

                    else -> close()
                }
            }
            view.handleKeyboardAccessibility()
        }
    }

    override fun getQuestionsCount() = questions?.size ?: 0

    override fun onSubmitClicked() {
        if (showCsatRequiredUI()) {
            view.showRequiredViews(requestFocus = true, csatAnswered = false)
            return
        }

        if (surveyAnswers.isNullOrEmpty()) {
            return
        }

        sendSurveyResponse(false)
    }

    override fun onExitClicked() {
        //Check if CSAT is answered
        if (showCsatRequiredUI()) {
            view.showRequiredViews(requestFocus = true, csatAnswered = false)
            return
        }
        sendSurveyResponse(true)
    }

    private fun close() {
        //Clear survey cache to avoid loop
        localRepository.rateRepository.clear()
        view.close()
    }

    private fun showCsatRequiredUI() = csatQuestionExit && !csatAnswered

    private fun sendSurveyResponse(isExitClicked: Boolean) {
        val communication = localRepository.rateRepository.rateTarget ?: return
        useCaseHandler.execute(sendSurvey, SendSurvey.RequestValues(
            communication.urlPath,
            communication.id, surveyAnswers
        ),
            object : UseCaseCallback<SendSurvey.ResponseValue?> {
                override fun onSuccess(response: SendSurvey.ResponseValue?) {
                    //Survey is answered and sent so clearing survey variable cache
                    localRepository.rateRepository.clear()
                    if (isExitClicked) {
                        view.close()
                    } else {
                        view.showSuccessFragment(signOffText)
                    }
                }

                override fun onError() {
                    //Save survey response to send it again if user selects resend option from
                    //Survey error screen
                    localRepository.rateRepository.surveyAnswersRequest = SurveyRequest(surveyAnswers)
                    localRepository.rateRepository.surveySignOffText = signOffText
                    view.showErrorScreen(SurveyErrorType.RE_TRY_SEND_SURVEY_DATA)
                }
            })
    }

    override fun getSurveyAnswer(questionId: Int): String? = surveyAnswers[questionId]

    companion object {
        const val QUESTION_TYPE_CSAT = "csat"
        const val QUESTION_TYPE_STAR = "star"
        const val QUESTION_TYPE_NUMERIC_SCALE = "scale"
        const val QUESTION_TYPE_ENUMERATED_TEXT = "enumeration"
        const val QUESTION_TYPE_FREE_FORM = "free-form"
    }
}
