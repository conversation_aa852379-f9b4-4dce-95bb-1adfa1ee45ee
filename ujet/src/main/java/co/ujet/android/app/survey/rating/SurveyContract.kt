package co.ujet.android.app.survey.rating

import android.widget.TextView
import co.ujet.android.app.survey.error.SurveyErrorType
import co.ujet.android.clean.entity.survey.ValidAnswer
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

interface SurveyContract {
    interface View : BaseView {
        fun addCsatView(questionId: Int, displayText: String)
        fun addStarRatedView(questionId: Int, displayText: String)
        fun addNumericScaleView(questionId: Int, displayText: String, validAnswers: List<ValidAnswer>)
        fun addEnumeratedTextView(questionId: Int, displayText: String, validAnswers: List<ValidAnswer>)
        fun addFreeFormView(questionId: Int, displayText: String)
        fun updateFreeFormTextCount(textLength: Int, textView: TextView)
        fun showSuccessFragment(signOffText: String?)
        fun showErrorScreen(errorType: SurveyErrorType)
        fun setLoadingIndicator(enabled: Boolean)
        fun setSubmitButton(enable: Boolean)
        fun showRequiredViews(requestFocus: Boolean, csatAnswered: Boolean)
        fun close()
        fun handleKeyboardAccessibility()
    }

    interface Presenter : BasePresenter {
        fun onCsatAnswered(answered: Boolean)
        fun onRatingChanged(csatViewChanged: Boolean, rating: Int, questionId: Int)
        fun onSurveyAnswered(questionId: Int, answerKey: String)
        fun onAnswerCleared(questionId: Int)
        fun onFreeFormTextChanged(text: String, textView: TextView)
        fun onSubmitClicked()
        fun onExitClicked()
        fun getSurveyAnswer(questionId: Int): String?
        fun getQuestionsCount(): Int
    }
}
