package co.ujet.android.app.csat.retry

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

internal class CsatRetryContract {
    internal interface View : BaseView {
        fun showCsatSuccess()
        fun showInProgress(enable: <PERSON>olean)
        fun close()
    }

    internal interface Presenter : BasePresenter {
        fun resend()
        fun skip()
    }
}