package co.ujet.android.app.channel

import android.os.Bundle
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.clean.entity.menu.channel.Channel
import co.ujet.android.commons.domain.ExternalDeflectionLink
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

interface ChannelContract {
    interface View : BaseView {
        fun isVoIPAvailable(sensitivity: Double): Boolean
        fun showMenuUpdated()
        fun restart()
        fun updateEnabledChannels(enabledChannels: List<Channel>, fetchRootMenus: Boolean,
                                  areRootMenusUpdated: <PERSON>olean, isBackButtonClicked: Boolean)
        fun showTitleAndDescription(header: String?, message: String?)
        fun updateChatWaitTime(waitTime: Int)
        fun updateCallWaitTime(waitTime: Int)
        fun updateMenuPath(menuPath: String?, channelType: ChannelType?)
        fun adjustMenuPathForRtl(path: String?) : String?
        fun onError(message: String?)
        fun back()
        fun finish()
        fun showErrorDialog()
        fun showNoDataConnectivityError()
        fun showLoadingState(channelType: ChannelType)
        fun updateLanguage(language: String)
        fun handleActivityForResult(requestKey: String, result: Bundle)
    }

    interface Presenter : BasePresenter {
        fun initialize()
        fun handleOnResume()
        fun handleOnPause()
        fun clearResources()
        fun isVoIPAndPstnConfigDisabled(): Boolean
        fun selectChannel(channel: Channel)
        fun selectChannel(externalDeflectionLink: ExternalDeflectionLink)
        fun updateWaitTimes()
        fun onErrorDialogConfirmed()
    }
}
