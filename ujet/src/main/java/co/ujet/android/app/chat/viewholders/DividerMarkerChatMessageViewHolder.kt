package co.ujet.android.app.chat.viewholders

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.message.DividerMarkerChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler

class DividerMarkerChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup, context: Context, ujetStyle: UjetStyle) :
    ChatMessageViewHolder(adapter, context, ujetStyle, inflate(context, parent, R.layout.ujet_view_chat_divider_marker)) {

    fun bind(message: DividerMarkerChatMessage): View {
        itemView.findViewById<TextView?>(R.id.divider_text)?.apply {
            text = message.getMessage()
            UjetViewStyler.styleTertiaryText(ujetStyle, this)
            UjetViewStyler.overrideTypeface(ujetStyle, this)
        }
        AccessibilityUtil.updateAccessibilityAction(itemView, true, null)
        return itemView
    }
}
