package co.ujet.android.app.survey

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.widget.Toolbar
import co.ujet.android.R
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.survey.rating.SurveyFragment
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.libs.logger.Logger

class UjetSurveyActivity : UjetBaseActivity(), SurveyListener {
    private var ujetActivityExtras: Bundle? = null
    private var isNewCommInProgress = false

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(co.ujet.android.ui.R.style.Theme_Ujet_Bridge_NoToolBar)
        super.onCreate(savedInstanceState)

        setContentView(R.layout.ujet_activity_survey)
        setToolBar()

        isNewCommInProgress = intent?.getBooleanExtra(EXTRA_NEW_COMM_STATE, false) ?: false
        ujetActivityExtras = intent?.extras?.getBundle(EXTRA_UJET_ACTIVITY_EXTRAS)

        if (FragmentHelper.isEmpty(this)) {
            supportFragmentManager
                .beginTransaction()
                .add(R.id.fragment_container, SurveyFragment.newInstance(), SurveyFragment.TAG)
                .commit()
        }

        Logger.d("UjetSurveyActivity is created")
    }

    override fun checkWebViewAvailability() {
        if (WebViewUtil.isWebViewDisabled(applicationContext)) {
            Logger.w("Web view is disabled")
            WebViewUtil.handleWebViewUnavailability(applicationContext)
        }
    }

    override fun onBackPressed() {
        // Prevent closing this by back button
    }

    override fun onSurveySucceeded() = finishAndStartUjet()

    override fun onSurveyFailed() = finishAndStartUjet()

    private fun finishAndStartUjet() {
        ActivityHelper.finishAndRemoveTask(this)
        if (isNewCommInProgress) {
            UjetActivity.startNewComm(this, true)
        }
        if (ujetActivityExtras != null) {
            UjetActivity.start(this, ujetActivityExtras)
        }
    }

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        toolbar.setBackgroundColor(ujetStyle().colorPrimary)
        setSupportActionBar(toolbar)

        supportActionBar?.title = ""
    }

    companion object {
        private const val EXTRA_UJET_ACTIVITY_EXTRAS = "ujet_activity_extras"
        private const val EXTRA_NEW_COMM_STATE = "new_comm_state"

        fun start(context: Context) {
            context.startActivity(Intent(context, UjetSurveyActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            })
        }

        fun start(context: Context, isNewCommInProgress: Boolean) {
            context.startActivity(Intent(context, UjetSurveyActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                putExtra(EXTRA_NEW_COMM_STATE, isNewCommInProgress)
            })
        }

        /**
         * When unrated survey exists, UJET SDK will start UjetSurveyActivity.
         * And after survey, it will restart UJET SDK with origin Intent Extras.
         *
         * @param activity UjetActivity
         */
        @JvmStatic
        fun start(activity: Activity) {
            activity.startActivity(Intent(activity, UjetSurveyActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                activity.intent.extras?.let {
                    putExtra(EXTRA_UJET_ACTIVITY_EXTRAS, it)
                }
            })
        }
    }
}
