package co.ujet.android.app.call.deflection

import android.app.Activity
import android.app.Dialog
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.os.Bundle
import android.util.SparseArray
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.Keep
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import co.ujet.android.R
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.data.model.CallDeflection
import co.ujet.android.data.model.CallDeflectionType
import co.ujet.android.extensions.dimensionForLandscapeMode
import co.ujet.android.libs.logger.Logger
import co.ujet.android.ui.util.DesignUtil

class CallDeflectionDialogFragment @Keep constructor() : BaseDialogFragment(), CallDeflectionContract.View {
    private var presenter: CallDeflectionContract.Presenter? = null
    private val channelLayouts = SparseArray<ChannelItemViewHolder>()

    private val buttonDrawable: StateListDrawable
        get() {
            val backgroundColor = ujetStyle().primaryBackgroundColor
            val primaryColor = ujetStyle().colorPrimary
            val strokePx = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
            val cornerRadiusPx = activity?.let { DesignUtil.dpToPx(it, ujetStyle().buttonRadius).toInt() } ?: 0
            val normalDrawable = DesignUtil.createRoundedRectangleDrawable(backgroundColor, primaryColor, strokePx, cornerRadiusPx.toFloat())
            val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(primaryColor, primaryColor, strokePx, cornerRadiusPx.toFloat())
            return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val context = activity?.applicationContext ?: return
        var menuId = Integer.MIN_VALUE
        var callDeflection: CallDeflection? = null

        arguments?.run {
            menuId = getInt(ARGS_DEFLECT_MENU_ID, Integer.MIN_VALUE)
            callDeflection = getParcelable(ARGS_ENABLED_CALL_DEFLECTIONS)
        } ?: run {
            Logger.w("CallDeflectionDialogFragment must have an argument")
            dismiss()
            return
        }

        presenter = CallDeflectionPresenter(
                context,
                menuId,
                callDeflection,
                this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = builder
            .customView(R.layout.ujet_dialog_call_deflection)
            .title(R.string.ujet_call_delay_deflection_title)
            .gravity(Gravity.CENTER)
            .withExit(false)
            .cancelable(false)
            .dimensionForLandscapeMode(context)
            .build()

        dialog.findViewById<TextView>(R.id.description).apply {
            UjetViewStyler.overrideTypeface(ujetStyle(), this)
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }

        setupChannelListItemLayout(dialog, R.id.channel_email, R.drawable.ujet_ic_email)
        setupChannelListItemLayout(dialog, R.id.channel_scheduled_call, R.drawable.ujet_ic_scheduled)
        setupChannelListItemLayout(dialog, R.id.channel_phone, R.drawable.ujet_ic_call_now)
        setupChannelListItemLayout(dialog, R.id.channel_voicemail, R.drawable.ujet_ic_voicemail)
        setupStayOnHoldLayout(dialog)

        return dialog
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        channelLayouts.clear()
    }

    private fun setupChannelListItemLayout(dialog: Dialog, layoutId: Int, iconId: Int) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        val channelLayout = dialog.findViewById<RelativeLayout>(layoutId)
        channelLayout.visibility = View.GONE
        channelLayout.background = buttonDrawable

        val channelIconDrawable = ContextCompat.getDrawable(context ?: return, iconId)
        val channelIcon = channelLayout.findViewById<ImageView>(R.id.channel_icon)
        channelIcon.setImageDrawable(channelIconDrawable?.let { getChannelIconDrawable(it) })

        val holder = ChannelItemViewHolder()
        holder.channelLayout = channelLayout
        holder.nameTextView = channelLayout.findViewById(R.id.channel_name)
        holder.nameTextView?.setTextColor(
            DesignUtil.getColorStateList(
                ujetStyle().colorPrimary,
                DesignUtil.getColor(activity.applicationContext, co.ujet.android.ui.R.color.ujet_white)
            )
        )

        holder.descriptionTextView = channelLayout.findViewById(R.id.channel_description)
        holder.descriptionTextView?.setTextColor(
                DesignUtil.getColorStateList(
                    ujetStyle().textTertiaryColor,
                    DesignUtil.getColor(activity.applicationContext, co.ujet.android.ui.R.color.ujet_white)
                )
            )
        channelLayouts.put(layoutId, holder)
    }

    private fun setupStayOnHoldLayout(dialog: Dialog) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        val stayOnHoldLayout = dialog.findViewById<FrameLayout>(R.id.stay_on_hold)
        stayOnHoldLayout.background = buttonDrawable
        stayOnHoldLayout.setOnClickListener { presenter?.stayOnHold() }

        stayOnHoldLayout.findViewById<TextView>(R.id.channel_name).apply {
            setTextColor(
                DesignUtil.getColorStateList(
                    ujetStyle().colorPrimary,
                    DesignUtil.getColor(activity.applicationContext, co.ujet.android.ui.R.color.ujet_white)
                )
            )
        }
    }

    private fun getChannelIconDrawable(origin: Drawable): StateListDrawable? {
        val activity = activity ?: return null
        if (isActive.not()) {
            return null
        }

        val normalDrawable = origin.constantState?.newDrawable()
        val pressedDrawable = origin.constantState?.newDrawable()
        pressedDrawable?.colorFilter = PorterDuffColorFilter(DesignUtil.getColor(activity, co.ujet.android.ui.R.color.ujet_white), PorterDuff.Mode.SRC_ATOP)

        val stateListDrawable = StateListDrawable()
        stateListDrawable.addState(intArrayOf(android.R.attr.state_pressed), pressedDrawable)
        stateListDrawable.addState(intArrayOf(android.R.attr.state_focused), pressedDrawable)
        stateListDrawable.addState(intArrayOf(android.R.attr.state_enabled), normalDrawable)
        return stateListDrawable
    }

    override fun activate(layoutId: Int, name: String?, description: String?, deflectionType: CallDeflectionType) {
        val viewHolder = channelLayouts[layoutId]
        viewHolder?.channelLayout?.setOnClickListener { presenter?.selectDeflection(deflectionType) }
        viewHolder?.channelLayout?.visibility = View.VISIBLE

        if (name.isNullOrEmpty()) {
            viewHolder?.nameTextView?.visibility = View.GONE
        } else {
            viewHolder?.nameTextView?.text = name
            viewHolder?.nameTextView?.visibility = View.VISIBLE
        }

        if (description.isNullOrEmpty()) {
            viewHolder?.descriptionTextView?.visibility = View.GONE
        } else {
            viewHolder?.descriptionTextView?.text = description
            viewHolder?.descriptionTextView?.visibility = View.VISIBLE
        }
    }

    override fun setResult(menuId: Int, type: CallDeflectionType, data: String?) {
        val requestKey = arguments?.getString(ARGS_REQUEST_KEY, null)
        val requestCode = arguments?.getInt(ARGS_REQUEST_CODE, Integer.MIN_VALUE) ?: Integer.MIN_VALUE
        val bundle = bundleOf(
            CallDeflection.KEY_MENU_ID to menuId,
            CallDeflection.KEY_TYPE to type.name,
            CallDeflection.KEY_DATA to data,
            REQUEST_CODE to requestCode,
            RESULT_CODE to Activity.RESULT_OK
        )
        requestKey?.let { key ->
            parentFragmentManager.setFragmentResult(key, bundle)
        }
    }

    override fun close() {
        dismiss()
    }

    override fun onBack() {
        presenter?.stayOnHold()
    }

    override fun isActive(): Boolean {
        return isAdded
    }

    private class ChannelItemViewHolder {
        var channelLayout: RelativeLayout? = null
        var nameTextView: TextView? = null
        var descriptionTextView: TextView? = null
    }

    companion object {
        const val TAG = "CallDeflectionDialogFragment"
        private const val ARGS_DEFLECT_MENU_ID = "args_menu_id"
        private const val ARGS_ENABLED_CALL_DEFLECTIONS = "args_deflections"

        @JvmStatic
        fun newInstance(requestKey: String,
                        requestCode: Int,
                        menuId: Int,
                        callDeflection: CallDeflection): CallDeflectionDialogFragment {
            val fragment = CallDeflectionDialogFragment()
            fragment.arguments = bundleOf(
                ARGS_DEFLECT_MENU_ID to menuId,
                ARGS_ENABLED_CALL_DEFLECTIONS to callDeflection,
                ARGS_REQUEST_KEY to requestKey,
                ARGS_REQUEST_CODE to requestCode
            )
            return fragment
        }
    }
}
