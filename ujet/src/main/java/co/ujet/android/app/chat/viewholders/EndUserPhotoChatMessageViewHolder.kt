package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.message.EndUserPhotoChatMessage
import co.ujet.android.commons.extensions.loadOrSetInvisible
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.util.StyleUtil

class EndUserPhotoChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup, activity: Activity, ujetStyle: UjetStyle) :
    MediaChatMessageViewHolder(adapter, activity, ujetStyle, inflate(activity.applicationContext,
        parent, R.layout.ujet_view_chat_message_photo)) {

    fun bind(message: EndUserPhotoChatMessage, isGroupStart: Boolean, isGroupEnd: Boolean): View {
        setUpEndUserIcon(itemView, isGroupStart)

        val chatContainer: LinearLayout = itemView.findViewById(R.id.chat_contents_container)
        val consumerMessageStyle = configuration.ujetStylesOptions?.chatStyles?.consumerMessageBubbles
        val background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_end_user)
        val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT).apply {
            marginStart = ujetStyle.dpToPx(55f).toInt()
        }
        StyleUtil.applyDefaultStyle(
            context,
            ujetStyle,
            chatContainer,
            consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius,
            consumerMessageStyle?.border,
            background,
            ujetStyle.chatEndUserMessageTopBubbleColor,
            layoutParams
        )

        val imageView: ImageView = itemView.findViewById(R.id.photo_image_view)
        val cornerRadius = consumerMessageStyle?.cornerRadius?.toFloat() ?: 0f
        val imageRadius = if (cornerRadius > 0) {
            ujetStyle.dpToPx(cornerRadius).toInt()
        } else {
            getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_corner_radius)
        }
        imageView.loadOrSetInvisible(message.thumbnailFilename, imageRadius)

        StyleUtil.updateBackgroundStyle(
            imageView,
            consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius,
            consumerMessageStyle?.border
        )
        setUpMessageFooter(itemView, message, isGroupEnd)
        setupResendButton(message, itemView.findViewById(R.id.btn_send_failed), itemView.findViewById(R.id.resend_message))
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_mobile_user),
            mainContainer = imageView,
            timestamp = itemView.findViewById(R.id.timestamp),
            resend = itemView.findViewById(R.id.resend_message),
            isImage = true,
            isClickable = true,
            imageType = context.getString(R.string.ujet_chat_type_image),
            adapterPosition = adapterPosition
        )
        return itemView
    }
}
