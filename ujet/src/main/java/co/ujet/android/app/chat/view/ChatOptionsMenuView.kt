package co.ujet.android.app.chat.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import co.ujet.android.R
import co.ujet.android.modulemanager.common.ui.domain.EndButtonStyle
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.extensions.setVisibility
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.widgets.UjetProgressBar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@SuppressLint("ViewConstructor")
class ChatOptionsMenuView(context: Context,
                          blockChatTerminationByEndUser: Boolean,
                          endChatButtonStyle: EndButtonStyle?,
                          ujetStyle: UjetStyle,
                          listener: ChatMenuOptionsListener) :
    LinearLayout(context) {
    private var listener: ChatMenuOptionsListener? = null
    private var title: TextView
    private var closeButton: TextView
    private var backButton: ImageView
    private var endChatButton: FancyButton
    private var downloadTranscriptContainer: LinearLayout
    private var chatOptionsMenuContainer: LinearLayout
    private var downloadTranscriptProgressBar: UjetProgressBar
    private var downloadTranscriptErrorIcon: ImageView
    private var coroutineScope: CoroutineScope

    init {
        inflate(context, R.layout.ujet_view_chat_menu_options, this)
        isFocusable = false
        this.listener = listener
        title = findViewById<TextView>(R.id.title).apply {
            text = context.getString(R.string.ujet_chat_action_menu_title)
            setTextColor(ujetStyle.textPrimaryColor)
        }
        closeButton = findViewById<TextView>(R.id.close_btn).apply {
            text = context.getString(R.string.ujet_chat_form_close)
            setOnClickListener {
                listener.onCloseButtonClicked()
            }
            setTextColor(ujetStyle.textPrimaryColor)
            handleKeyboardAccessibility(this, CLOSE_BUTTON)
        }
        backButton = findViewById(R.id.ic_back)
        endChatButton = findViewById<FancyButton?>(R.id.chat_ended_button).apply {
            setVisibility(blockChatTerminationByEndUser || endChatButtonStyle?.visible == false, GONE, VISIBLE)
            setOnClickListener {
                listener.onEndChatButtonClicked()
            }
            handleKeyboardAccessibility(this, END_CHAT_BUTTON)
        }
        downloadTranscriptContainer = findViewById<LinearLayout>(R.id.download_transcript_container).apply {
            setOnClickListener {
                updateDownloadButtonUI(isDownloadProgressBarVisible = true, isDownloadErrorIconVisible = false)
                listener.onDownloadChatTranscriptButtonClicked()
            }
            findViewById<TextView>(R.id.download_transcript_text).apply {
                setTextColor(ujetStyle.textPrimaryColor)
            }
            handleKeyboardAccessibility(this, DOWNLOAD_TRANSCRIPT)
        }
        downloadTranscriptProgressBar = findViewById(R.id.download_transcript_progress_bar)
        downloadTranscriptErrorIcon = findViewById(R.id.download_transcript_error_icon)
        coroutineScope = MainScope()
        chatOptionsMenuContainer = findViewById<LinearLayout?>(R.id.chat_options_menu_container).apply {
            val backgroundDrawable = ContextCompat.getDrawable(context, R.drawable.ujet_bottom_sheet_rounded_corner)
            (backgroundDrawable as GradientDrawable).setColor(ujetStyle.primaryBackgroundColor)
            background = backgroundDrawable
        }
    }

    private fun handleKeyboardAccessibility(targetButton: View, type: String) {
        targetButton.setOnKeyListener { v, keyCode, event ->
            when (type) {
                END_CHAT_BUTTON -> {
                    // End Chat Button Key Handling: only ACTION_DOWN and KEYCODE_DPAD_UP
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_UP -> {
                                    when {
                                        downloadTranscriptContainer.isVisible -> {
                                            downloadTranscriptContainer.requestFocus()
                                        }

                                        else -> {
                                            closeButton.requestFocus()
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onEndChatButtonClicked()
                                }

                                else -> {
                                    // pressing down key won't have any effect
                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }

                CLOSE_BUTTON -> {
                    // Close Button Key Handling: only ACTION_DOWN and KEYCODE_DPAD_DOWN or TAB
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_TAB -> {
                                    when {
                                        downloadTranscriptContainer.isVisible -> {
                                            downloadTranscriptContainer.requestFocus()
                                        }

                                        endChatButton.isVisible -> {
                                            endChatButton.requestFocus()
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onCloseButtonClicked()
                                }

                                else -> {

                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }

                DOWNLOAD_TRANSCRIPT -> {
                    // Download Transcript Button Key Handling: ACTION_DOWN and KEYCODE_DPAD_DOWN or TAB and ACTION_UP and KEYCODE_DPAD_UP
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_TAB -> {
                                    when {
                                        endChatButton.isVisible -> {
                                            endChatButton.requestFocus()
                                        }

                                        else -> {
                                            //if endChatButton not visible then pressing down key won't have any effect
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_DPAD_UP -> {
                                    closeButton.requestFocus()
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onDownloadChatTranscriptButtonClicked()
                                }

                                else -> {

                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }
            }
            false
        }
    }

    fun requestFocusOfOptionsMenuCloseBtn() {
        closeButton.requestFocus()
    }

    fun updateEndChatButtonText(text: String) {
        endChatButton.text = text
    }

    fun updateEndChatButtonStatus(isEnabled: Boolean) {
        endChatButton.isEnabled = isEnabled
    }

    fun getEndChatButtonText() = endChatButton.text

    fun updateDownloadButtonUI(isDownloadProgressBarVisible: Boolean, isDownloadErrorIconVisible: Boolean) {
        downloadTranscriptProgressBar.setVisibility(isDownloadProgressBarVisible, VISIBLE, GONE)
        downloadTranscriptContainer.isEnabled = !(isDownloadErrorIconVisible || isDownloadProgressBarVisible)
        if (isDownloadErrorIconVisible) {
            downloadTranscriptErrorIcon.setVisibility(true, VISIBLE, GONE)
            //Hide error icon after 2s
            coroutineScope.launch {
                delay(2000)
                downloadTranscriptErrorIcon.setVisibility(false, VISIBLE, GONE)
                downloadTranscriptContainer.isEnabled = true
            }
        }

        downloadTranscriptContainer.isEnabled = !isDownloadProgressBarVisible
    }

    fun canDownloadChatTranscript(canDownloadChatTranscript: Boolean) {
        downloadTranscriptContainer.setVisibility(canDownloadChatTranscript, VISIBLE, GONE)
    }

    fun clearResources() {
        coroutineScope.cancel()
    }

    interface ChatMenuOptionsListener {
        fun onCloseButtonClicked()
        fun onEndChatButtonClicked()
        fun onDownloadChatTranscriptButtonClicked()
    }

    companion object {
        private const val END_CHAT_BUTTON = "endChatButton"
        private const val DOWNLOAD_TRANSCRIPT = "downloadTranscript"
        private const val CLOSE_BUTTON = "closeButton"
    }
}
