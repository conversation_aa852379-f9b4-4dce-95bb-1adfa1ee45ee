package co.ujet.android.app.chat.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import co.ujet.android.R
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions
import co.ujet.android.modulemanager.common.ui.domain.ChatActionsMenuStyle
import co.ujet.android.ui.extensions.setVisibility
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.util.StyleUtil.setButtonImageOfDefault

@SuppressLint("ViewConstructor")
class ChatActionsMenuView(context: Context,
                          ujetStyle: UjetStyle,
                          ujetStylesOptions: UjetStylesOptions?,
                          hideAttachment: <PERSON><PERSON><PERSON>,
                          listener: ChatActionsMenuListener) :
    LinearLayout(context) {
    private var chatActionsMenuStyle: ChatActionsMenuStyle?
    private var listener: ChatActionsMenuListener? = null
    private var title: TextView
    private var closeButton: TextView
    private var cobrowseContainer: LinearLayout
    private var photoFromLibraryContainer: LinearLayout
    private var takePhotoContainer: LinearLayout
    private var takePhotoIcon: ImageView
    private var photoFromLibraryIcon: ImageView

    init {
        inflate(context, R.layout.ujet_view_chat_menu_actions, this)
        isFocusable = false
        this.listener = listener
        chatActionsMenuStyle = ujetStylesOptions?.chatStyles?.chatActionsMenuStyle
        title = findViewById<TextView>(R.id.title).apply {
            text = context.getString(R.string.ujet_chat_action_menu_title)
            setTextColor(ujetStyle.textPrimaryColor)
        }
        closeButton = findViewById<TextView>(R.id.close_btn).apply {
            text = context.getString(R.string.ujet_chat_form_close)
            setTextColor(ujetStyle.textPrimaryColor)
            setOnClickListener {
                listener.onCloseButtonClicked()
            }
            handleKeyboardAccessibility(this, CLOSE_BUTTON)
        }
        cobrowseContainer = findViewById<LinearLayout?>(R.id.cobrowse_container).apply {
            setVisibility(chatActionsMenuStyle?.cobrowseIcon?.visible == false, GONE, VISIBLE)
            setOnClickListener {
                listener.onCoBrowseButtonClicked()
            }
            findViewById<TextView>(R.id.cobrowse_text).apply {
                setTextColor(ujetStyle.textPrimaryColor)
            }
            handleKeyboardAccessibility(this, COBROWSE)
        }
        photoFromLibraryContainer = findViewById<LinearLayout?>(R.id.select_photo_from_library_container).apply {
            setVisibility(hideAttachment || chatActionsMenuStyle?.selectPhotoFromLibraryIcon?.visible == false, GONE, VISIBLE)
            setOnClickListener {
                listener.onPhotoFromLibraryButtonClicked()
            }
            findViewById<TextView>(R.id.select_photo_from_library_text).apply {
                setTextColor(ujetStyle.textPrimaryColor)
            }
            contentDescription = context.getString(R.string.ujet_chat_select_from_library)
            handleKeyboardAccessibility(this, SELECT_PHOTO_FROM_LIBRARY)
        }
        takePhotoContainer = findViewById<LinearLayout?>(R.id.take_photo_container).apply {
            setVisibility(hideAttachment || chatActionsMenuStyle?.cameraIcon?.visible == false, GONE, VISIBLE)
            setOnClickListener {
                listener.onTakePhotoButtonClicked()
            }
            findViewById<TextView>(R.id.take_photo_text).apply {
                setTextColor(ujetStyle.textPrimaryColor)
            }
            contentDescription = context.getString(R.string.ujet_photo_take)
            handleKeyboardAccessibility(this, TAKE_PHOTO)
        }
        takePhotoIcon = findViewById<ImageView?>(R.id.take_photo_icon).apply {
            setButtonImageOfDefault(context, ujetStyle, this, chatActionsMenuStyle?.cameraIcon?.icon, R.drawable.ujet_ic_photo, false)
        }
        photoFromLibraryIcon = findViewById<ImageView?>(R.id.select_photo_from_library_icon).apply {
            setButtonImageOfDefault(
                context,
                ujetStyle,
                this,
                chatActionsMenuStyle?.selectPhotoFromLibraryIcon?.icon,
                R.drawable.ujet_ic_photo_library,
                false
            )
        }
    }

    private fun handleKeyboardAccessibility(targetButton: View, type: String) {
        targetButton.setOnKeyListener { v, keyCode, event ->
            when (type) {
                SELECT_PHOTO_FROM_LIBRARY -> {
                    // Select Photo From Library Key Handling: only ACTION_DOWN and KEYCODE_DPAD_UP
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_UP -> {
                                    closeButton.requestFocus()
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onPhotoFromLibraryButtonClicked()
                                }

                                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_TAB -> {
                                    when {
                                        takePhotoContainer.isVisible -> {
                                            takePhotoContainer.requestFocus()
                                        }

                                        cobrowseContainer.isVisible -> {
                                            cobrowseContainer.requestFocus()
                                        }

                                        else -> {
                                            // if both views is hidden then nothing will happen
                                        }
                                    }
                                }

                                else -> {
                                    // pressing other keys won't have any effect
                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }

                TAKE_PHOTO -> {
                    // Take Button Key Handling: only ACTION_DOWN and KEYCODE_DPAD_DOWN or TAB and KEYCODE_DPAD_UP
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_TAB -> {
                                    when {
                                        cobrowseContainer.isVisible -> {
                                            cobrowseContainer.requestFocus()
                                        }

                                        else -> {
                                            // if both views is hidden then nothing will happen
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_DPAD_UP -> {
                                    if (photoFromLibraryContainer.isVisible) {
                                        photoFromLibraryContainer.requestFocus()
                                    } else {
                                        closeButton.requestFocus()
                                    }
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onTakePhotoButtonClicked()
                                }

                                else -> {
                                    // pressing other keys won't have any effect
                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }

                COBROWSE -> {
                    // Cobrowse Button Key Handling: ACTION_DOWN and  KEYCODE_DPAD_UP
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_UP -> {
                                    when {
                                        takePhotoContainer.isVisible -> {
                                            takePhotoContainer.requestFocus()
                                        }

                                        photoFromLibraryContainer.isVisible -> {
                                            photoFromLibraryContainer.requestFocus()
                                        }

                                        else -> {
                                            closeButton.requestFocus()
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onCoBrowseButtonClicked()
                                }

                                else -> {
                                    // pressing other keys won't have any effect
                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }

                CLOSE_BUTTON -> {
                    when {
                        event.action == KeyEvent.ACTION_DOWN -> {
                            when (keyCode) {
                                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_TAB -> {
                                    when {
                                        photoFromLibraryContainer.isVisible -> {
                                            photoFromLibraryContainer.requestFocus()
                                        }
                                        takePhotoContainer.isVisible -> {
                                            takePhotoContainer.requestFocus()
                                        }
                                        cobrowseContainer.isVisible -> {
                                            cobrowseContainer.requestFocus()
                                        }
                                        else -> {
                                            // if all views are hidden then nothing will happen
                                        }
                                    }
                                }

                                KeyEvent.KEYCODE_ENTER -> {
                                    listener?.onCloseButtonClicked()
                                }

                                else -> {
                                    // pressing other keys won't have any effect
                                }
                            }
                            return@setOnKeyListener true
                        }
                    }
                }
            }
            false
        }
    }

    fun requestFocusOfActionsMenuCloseBtn() {
        closeButton.requestFocus()
    }

    fun getCoBrowseButtonView() = cobrowseContainer

    fun getCoBrowseButtonVisibility(isCoBrowseSupportedAndConnected: Boolean) =
        isCoBrowseSupportedAndConnected && chatActionsMenuStyle?.cobrowseIcon?.visible != false

    fun setSendPhotoEnabled(enabled: Boolean) {
        takePhotoContainer.isEnabled = enabled
        photoFromLibraryContainer.isEnabled = enabled
    }

    interface ChatActionsMenuListener {
        fun onCloseButtonClicked()
        fun onCoBrowseButtonClicked()
        fun onTakePhotoButtonClicked()
        fun onPhotoFromLibraryButtonClicked()
    }

    companion object {
        private const val SELECT_PHOTO_FROM_LIBRARY = "selectPhotoFromLibrary"
        private const val TAKE_PHOTO = "takePhoto"
        private const val COBROWSE = "cobrowse"
        private const val CLOSE_BUTTON = "closeButton"
    }
}
