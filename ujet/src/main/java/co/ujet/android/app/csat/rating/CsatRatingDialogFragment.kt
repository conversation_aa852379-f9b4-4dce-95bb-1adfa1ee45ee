package co.ujet.android.app.csat.rating

import android.app.Dialog
import android.content.Context
import android.graphics.PorterDuff
import android.graphics.drawable.LayerDrawable
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_NO
import android.view.View.IMPORTANT_FOR_ACCESSIBILITY_YES
import android.view.View.OnFocusChangeListener
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.accessibility.AccessibilityManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.RatingBar
import android.widget.RatingBar.OnRatingBarChangeListener
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.annotation.LayoutRes
import co.ujet.android.R
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.app.csat.OnCsatRatingListener
import co.ujet.android.app.csat.retry.CsatRetryDialogFragment
import co.ujet.android.app.csat.sucess.CsatSuccessDialogFragment
import co.ujet.android.common.ui.CircleImageView
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.libs.graffiti.Graffiti.Companion.with
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.internal.Injection
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetViewStyler

/**
 * Created by mimu on 11/20/16.
 * Dialog for CSAT rating
 */
class CsatRatingDialogFragment @Keep constructor() : BaseDialogFragment(), CsatRatingContract.View {

    private var presenter: CsatRatingContract.Presenter? = null
    private var inflater: LayoutInflater? = null
    private lateinit var accessibilityManager: AccessibilityManager

    /**
     * See [CsatRatingDialogFragment.setCurrentRatingView]
     */
    private var ratingViewContainer: ViewGroup? = null
    private var ratingView: View? = null
    private var feedbackRatingView: View? = null
    private var sharableRatingView: View? = null
    private var currentRatingView: View? = null
    private val ratingViewHolders = HashMap<View, RatingViewHolder>()
    private var callback: OnCsatRatingListener? = null
    private var feedbackText: String? = null
    private var skipButton: FancyButton? = null
    private val configuration = Injection.provideConfiguration()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = try {
            context as OnCsatRatingListener
        } catch (ex: ClassCastException) {
            throw ClassCastException("$context should implement OnCsatRatingListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState != null) {
            feedbackText = savedInstanceState.getString(FEEDBACK_TEXT)
        }
        inflater = LayoutInflater.from(activity)
        accessibilityManager = activity?.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        presenter = CsatRatingPresenter(
            Injection.provideLocalRepository(requireContext()),
            apiManager(),
            this
        )
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(FEEDBACK_TEXT, feedbackText)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        ratingViewContainer =
            inflater?.inflate(R.layout.ujet_dialog_base_rating, null) as ViewGroup?

        ratingView = createRatingView(R.layout.ujet_dialog_rating).apply {
            setCurrentRatingView(this)
        }

        return builder
            .title(R.string.ujet_rating_title)
            .customView(ratingViewContainer)
            .height(ViewGroup.LayoutParams.WRAP_CONTENT)
            .gravity(Gravity.CENTER)
            .withExit(false)
            .build()
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ratingViewContainer = null
        ratingView = null
        feedbackRatingView = null
        sharableRatingView = null
        currentRatingView = null
        callback = null
        feedbackText = null
        skipButton = null
        ratingViewHolders.clear()
    }

    override fun showDefaultRatingView(agent: Agent?, rating: Int) {
        val viewHolder = ratingViewHolders[ratingView] ?: return
        setupRating(viewHolder, rating)
        setupAgentName(viewHolder, agent)
        setupAgentAvatar(viewHolder, agent)
        setCurrentRatingView(ratingView ?: return)
        AccessibilityUtil.setupInitialFocus(accessibilityManager, viewHolder.ratingBar)
    }

    override fun showFeedbackRatingView(
        agent: Agent?,
        rating: Int,
        feedback: String?,
        isSubmitting: Boolean
    ) {
        if (feedbackRatingView == null) {
            feedbackRatingView = createRatingView(R.layout.ujet_dialog_rating_feedback)
        }
        val viewHolder = ratingViewHolders[feedbackRatingView] ?: return
        setupRating(viewHolder, rating)
        setupFeedback(viewHolder, rating, feedback)
        setupAgentAvatar(viewHolder, agent)
        setupSubmitButton(viewHolder, isSubmitting)
        setCurrentRatingView(feedbackRatingView ?: return)
        AccessibilityUtil.setupInitialFocus(accessibilityManager, viewHolder.ratingBar)
    }

    override fun showRatingView(agent: Agent?, rating: Int, isSubmitting: Boolean) {
        if (sharableRatingView == null) {
            sharableRatingView = createRatingView(R.layout.ujet_dialog_rating_sharable)
        }
        val viewHolder = ratingViewHolders[sharableRatingView] ?: return
        setupRating(viewHolder, rating)
        setupAgentAvatar(viewHolder, agent)
        setupSubmitButton(viewHolder, isSubmitting)
        setCurrentRatingView(sharableRatingView ?: return)
        AccessibilityUtil.setupInitialFocus(accessibilityManager, viewHolder.ratingBar)
    }

    override fun updateAgent(agent: Agent?) {
        val viewHolder = ratingViewHolders[currentRatingView] ?: return
        if (viewHolder.agentName != null) {
            setupAgentName(viewHolder, agent)
        }
        setupAgentAvatar(viewHolder, agent)
    }

    override fun updateFeedbackLength(length: Int) {
        val viewHolder = ratingViewHolders[feedbackRatingView] ?: return

        val feedbackLimit = viewHolder.feedbackLimit?.apply {
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
        }
        if (FEEDBACK_VISIBLE_LENGTH_LIMIT <= length) {
            if (length <= FEEDBACK_LENGTH_LIMIT) {
                val limitStr = resources.getString(
                    R.string.ujet_rating_feedback_limit,
                    length.toString(),
                    FEEDBACK_LENGTH_LIMIT.toString()
                )
                feedbackLimit?.text = limitStr
                if (feedbackLimit?.currentTextColor != ujetStyle().colorText) {
                    feedbackLimit?.setTextColor(ujetStyle().colorText)
                }
            } else {
                val limitStr = resources.getString(
                    R.string.ujet_rating_feedback_limit_over,
                    length.toString(),
                    FEEDBACK_LENGTH_LIMIT.toString()
                )
                feedbackLimit?.text = limitStr
                if (feedbackLimit?.currentTextColor != ujetStyle().colorError) {
                    feedbackLimit?.setTextColor(ujetStyle().colorError)
                }
            }
            feedbackLimit?.visibility = View.VISIBLE

            // It is trick to make a button seem like to be disabled
            viewHolder.submitButton?.alpha = if (length <= FEEDBACK_LENGTH_LIMIT) 1f else 0.5f
            viewHolder.submitButton?.isClickable = length <= FEEDBACK_LENGTH_LIMIT
        } else {
            feedbackLimit?.visibility = View.GONE
        }
    }

    override fun updateSubmitStatus(isSubmit: Boolean) {
        val viewHolder = ratingViewHolders[currentRatingView] ?: return
        setupSubmitButton(viewHolder, isSubmit)
    }

    override fun showCsatSuccess() {
        val fragmentManager = fragmentManager
        if (!isActive || fragmentManager == null || isStateSaved) return
        CsatSuccessDialogFragment
            .newInstance()
            .show(fragmentManager, CsatSuccessDialogFragment.TAG)
        dismiss()
    }

    override fun showCsatRetry() {
        val fragmentManager = fragmentManager
        if (!isActive || fragmentManager == null || isStateSaved) return
        CsatRetryDialogFragment
            .newInstance()
            .show(fragmentManager, CsatRetryDialogFragment.TAG)
        dismiss()
    }

    private fun createRatingView(@LayoutRes resId: Int): ViewGroup {
        val ratingView = inflater?.inflate(resId, null) as ViewGroup
        val agentName = ratingView.findViewById<TextView?>(R.id.ujet_agent_name)
        val agentAvatar = ratingView.findViewById<CircleImageView>(R.id.ujet_agent_avatar)
        val ratingBar = ratingView.findViewById<RatingBar>(R.id.ujet_session_rate_bar).apply {
            onRatingBarChangeListener = onRatingChanged
        }
        val ratingStarDrawable = ratingBar.progressDrawable as LayerDrawable
        ratingStarDrawable.findDrawableByLayerId(android.R.id.background).setColorFilter(
            ujetStyle().ratingStarBackgroundColor, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.progress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )
        ratingStarDrawable.findDrawableByLayerId(android.R.id.secondaryProgress).setColorFilter(
            ujetStyle().colorPrimary, PorterDuff.Mode.SRC_IN
        )
        val ratingTitle = ratingView.findViewById<TextView>(R.id.ujet_rating_title)
        val feedbackLimit = ratingView.findViewById<TextView?>(R.id.ujet_session_feedback_limit)
        val feedback = ratingView.findViewById<EditText?>(R.id.ujet_session_feedback)?.apply {
            if (isLandscape(context)) {
                imeOptions = EditorInfo.IME_ACTION_DONE
            }
            setText(feedbackText ?: "")
            addTextChangedListener(onFeedbackChanged)
            onFocusChangeListener = OnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    val handler = Handler()
                    handler.postDelayed({
                        val scrollView =
                            feedbackRatingView?.findViewById<ScrollView>(R.id.ujet_scroll_view)
                        scrollView?.scrollTo(0, scrollView.bottom)
                    }, 300)
                }
            }
        }
        val submitButton = ratingView.findViewById<FancyButton?>(R.id.ujet_rate_button)?.apply {
            styleDefaultButton(this)
            setOnClickListener(onSubmit)
        }
        ratingView.findViewById<FancyButton?>(R.id.ujet_rate_submit_button)?.apply {
            isEnabled = false
        }

        skipButton = ratingView.findViewById<FancyButton?>(R.id.ujet_rate_skip_button)?.apply {
            if (configuration.showCsatSkipButton) {
                visibility = VISIBLE
            }
            setOnClickListener {
                presenter?.onSkip()
            }
        }

        val viewHolder = RatingViewHolder(
            submitButton = submitButton,
            feedback = feedback,
            feedbackLimit = feedbackLimit,
            ratingBar = ratingBar,
            ratingTitle = ratingTitle,
            agentName = agentName,
            agentAvatar = agentAvatar
        )
        ratingViewHolders[ratingView] = viewHolder
        return ratingView
    }

    private fun setupRating(viewHolder: RatingViewHolder, rating: Int) {
        viewHolder.ratingBar.rating = rating.toFloat()
        if (rating > 0) {
            val ratingTitle = resources.getString(RATING_FEEDBACK_SCORE_STRINGS[rating - 1])
            viewHolder.ratingTitle.text = ratingTitle
            UjetViewStyler.styleSecondaryText(ujetStyle(), viewHolder.ratingTitle)
            viewHolder.ratingTitle.importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_YES
        } else {
            viewHolder.ratingTitle.importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_NO
        }
    }

    private fun setupFeedback(viewHolder: RatingViewHolder, rating: Int, feedback: String?) {
        if (viewHolder.feedback == null) {
            return
        }
        UjetViewStyler.styleSecondaryEditText(ujetStyle(), viewHolder.feedback)
        if (TextUtils.isEmpty(feedback)) {
            viewHolder.feedback.setHint(if (rating == 5) R.string.ujet_rating_feedback_placeholder_5 else R.string.ujet_rating_feedback_placeholder)
        } else if (viewHolder.feedback.text.toString() != feedback) {
            viewHolder.feedback.setText(feedback)
        }
    }

    private fun setupAgentName(viewHolder: RatingViewHolder, agent: Agent?) {
        if (viewHolder.agentName == null) {
            return
        }
        UjetViewStyler.styleSecondaryText(ujetStyle(), viewHolder.agentName)
        viewHolder.agentName.text = agent?.displayName ?: ""
    }

    private fun setupAgentAvatar(viewHolder: RatingViewHolder, agent: Agent?) {
        if (agent == null) {
            viewHolder.agentAvatar.setImageResource(R.drawable.ujet_agent_sample)
        } else {
            with(activity)
                .from(agent.avatarUrl)
                .fallback(R.drawable.ujet_agent_sample)
                .into(viewHolder.agentAvatar)
        }
    }

    private fun setupSubmitButton(viewHolder: RatingViewHolder, isSubmitting: Boolean) {
        viewHolder.submitButton?.isEnabled = !isSubmitting
        skipButton?.isEnabled = !isSubmitting
        viewHolder.submitButton?.setIndicatorVisible(isSubmitting)
    }

    /**
     * [UJET-6713] If a view was removed when keeping a child button pressed,
     * an app would be crashed by occurring NullPointerException.
     * So instead of removing a previous view and adding a new view,
     * it changes a visibility of children views.
     *
     * @param ratingView Rating view to be displayed for current rating
     */
    private fun setCurrentRatingView(ratingView: View) {
        if (currentRatingView === ratingView) return
        ratingViewContainer?.let { ratingViewContainer ->
            if (ratingView.parent == null) {
                ratingViewContainer.addView(ratingView)
            }
            for (index in 0 until ratingViewContainer.childCount) {
                val child = ratingViewContainer.getChildAt(index)
                if (child === ratingView) {
                    child.visibility = View.VISIBLE
                    UjetViewStyler.styleDialogBackground(ujetStyle(), child)
                } else {
                    child.visibility = View.GONE
                }
            }
        }
        currentRatingView = ratingView
    }

    override val rating: Int
        get() {
            val ratingViewHolder = ratingViewHolders[currentRatingView]
            if (currentRatingView == null || ratingViewHolder == null) return 0
            return ratingViewHolder.ratingBar.rating.toInt()
        }

    override fun isActive(): Boolean {
        return isAdded
    }

    override fun close() {
        callback?.onCsatRatingFailed()
    }

    override fun onBack() {
        // Prevent closing this by back button
    }

    private val onSubmit = View.OnClickListener {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (imm.isAcceptingText) {
            feedbackRatingView?.findViewById<View>(R.id.ujet_session_feedback)?.let { view ->
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            }
        }
        presenter?.onSubmit()
    }

    private val onRatingChanged = OnRatingBarChangeListener { _, rating, _ ->
        presenter?.onRatingChanged(rating.toInt())
    }

    private val onFeedbackChanged: TextWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable) {
            presenter?.onFeedbackChanged(s.toString())
            feedbackText = s.toString()
        }
    }

    private data class RatingViewHolder(
        val submitButton: FancyButton?,
        val feedback: EditText?,
        val feedbackLimit: TextView?,
        val ratingBar: RatingBar,
        val ratingTitle: TextView,
        val agentName: TextView?,
        val agentAvatar: CircleImageView
    )

    companion object {
        const val TAG = "CsatRatingDialogFragment"
        private const val FEEDBACK_LENGTH_LIMIT = 255
        private const val FEEDBACK_VISIBLE_LENGTH_LIMIT = 225
        private const val FEEDBACK_TEXT = "ujet_feedback_text"
        private val RATING_FEEDBACK_SCORE_STRINGS = intArrayOf(
            R.string.ujet_rating_feedback_score_1,
            R.string.ujet_rating_feedback_score_2,
            R.string.ujet_rating_feedback_score_3,
            R.string.ujet_rating_feedback_score_4,
            R.string.ujet_rating_feedback_score_5
        )

        fun newInstance(): CsatRatingDialogFragment {
            return CsatRatingDialogFragment()
        }
    }
}
