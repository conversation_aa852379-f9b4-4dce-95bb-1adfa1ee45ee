package co.ujet.android.app.call.scheduled.call

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

/**
 * Contract between [ScheduledCallDialogFragment] [ScheduledCallPresenter]
 */
internal class ScheduledCallContract {
    internal interface View : BaseView {
        fun showScheduleTime()
        fun displayScheduledTime(timeString: String)
        fun enableButtons(enabled: Boolean)
        fun startUjet()
        fun showInCall()
        fun finish()
        fun clearCallPreferenceData()
    }

    internal interface Presenter : BasePresenter {
        fun onKeepClicked()
        fun onCancelClicked()
        fun onConnectCallReceived(callId: Int)
    }
}
