package co.ujet.android.app.call.scheduled.call

import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.TextView
import androidx.annotation.Keep
import androidx.core.text.HtmlCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ujet.android.R
import co.ujet.android.activity.incomingcall.UjetScheduleTimePickerActivity
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.call.scheduled.call.ScheduledCallContract.Presenter
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.clearCallPreferenceData
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.data.constant.CallPushType.ConnectCall
import co.ujet.android.data.constant.CallType
import co.ujet.android.data.constant.CallType.Scheduled
import co.ujet.android.internal.Injection
import co.ujet.android.ui.button.FancyButton

class ScheduledCallDialogFragment @Keep constructor() : BaseDialogFragment(), ScheduledCallContract.View {
    internal interface OnScheduledCallListener {
        fun startUjet()
    }

    private var cancelButton: FancyButton? = null
    private var keepButton: FancyButton? = null
    private var textView: TextView? = null
    private var presenter: Presenter? = null
    private var callback: OnScheduledCallListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        callback = try {
            activity as OnScheduledCallListener
        } catch (ex: ClassCastException) {
            throw ClassCastException("$activity should implement OnScheduledCallListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val context = context ?: return
        presenter = ScheduledCallPresenter(
            context,
            this,
            Injection.provideLocalRepository(context),
            apiManager()
        )
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = builder
            .customView(R.layout.ujet_dialog_scheduled_call)
            .title(R.string.ujet_scheduled_call_confirm_title)
            .height(LayoutParams.WRAP_CONTENT)
            .withExit(false)
            .gravity(Gravity.CENTER)
            .build()
        textView = dialog.findViewById<TextView>(R.id.schedule_description)?.apply {
            UjetViewStyler.styleSecondaryText(ujetStyle(), this)
            text = activity?.getString(R.string.ujet_common_loading)
            gravity = Gravity.CENTER
        }
        cancelButton = dialog.findViewById<FancyButton>(R.id.buttonNagative)?.apply {
            visibility = View.INVISIBLE
            styleInvertedButton(this)
            setOnClickListener {
                presenter?.onCancelClicked()
            }
        }
        keepButton = dialog.findViewById<FancyButton>(R.id.buttonPositive)?.apply {
            visibility = View.INVISIBLE
            styleDefaultButton(this)
            setOnClickListener {
                presenter?.onKeepClicked()
            }
        }
        cancelButton?.post {
            val maxHeight = (cancelButton?.height ?: 60).coerceAtLeast(keepButton?.height ?: 60)
            cancelButton?.layoutParams?.height = maxHeight
            keepButton?.layoutParams?.height = maxHeight
        }
        return dialog
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
        LocalBroadcastManager
            .getInstance(activity ?: return)
            .registerReceiver(
                broadcastReceiver,
                IntentFilter(ConnectCall.key)
            )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cancelButton = null
        keepButton = null
        textView = null
    }

    private val broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val callType = intent.getStringExtra("callType")
            if (CallType.find(callType) == Scheduled) {
                val callId = intent.getIntExtra("callId", 0)
                presenter?.onConnectCallReceived(callId)
            }
        }
    }

    override fun onPause() {
        LocalBroadcastManager
            .getInstance(activity ?: return)
            .unregisterReceiver(broadcastReceiver)
        super.onPause()
    }

    override fun onBack() {
        if (activity != null) {
            activity?.finish()
        } else {
            dismiss()
        }
    }

    override fun showScheduleTime() {
        val baseActivity = activity as? UjetBaseActivity ?: return
        ActivityHelper.finishAndRemoveTask(baseActivity)
        UjetScheduleTimePickerActivity.start(baseActivity, null)
    }

    override fun displayScheduledTime(timeString: String) {
        textView?.text = HtmlCompat.fromHtml(timeString, HtmlCompat.FROM_HTML_MODE_LEGACY)
        textView?.gravity = Gravity.NO_GRAVITY
    }

    override fun enableButtons(enabled: Boolean) {
        cancelButton?.visibility = View.VISIBLE
        keepButton?.visibility = View.VISIBLE
    }

    override fun startUjet() {
        callback?.startUjet()
    }

    override fun showInCall() {
        callback?.startUjet()
    }

    override fun finish() {
        if (activity != null) {
            activity?.finish()
        }
    }

    override fun isActive() = isAdded

    override fun clearCallPreferenceData() {
        if (!isActive) {
            return
        }
        clearCallPreferenceData(activity?.applicationContext ?: return)
    }

    companion object {
        const val TAG = "ScheduledCallDialogFragment"

        fun newInstance() = ScheduledCallDialogFragment()
    }
}
