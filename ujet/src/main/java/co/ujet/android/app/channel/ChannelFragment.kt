package co.ujet.android.app.channel

import android.app.Activity
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.StateListDrawable
import android.os.Bundle
import android.util.LayoutDirection
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.INVISIBLE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.core.text.layoutDirection
import androidx.lifecycle.lifecycleScope
import co.ujet.android.R
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.chat.MarkdownUtil
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.error.AlertDialogFragment
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.app.loadingstate.LoadingStateFragment
import co.ujet.android.clean.domain.menu.usecase.GetMenus
import co.ujet.android.clean.entity.common.Quadruple
import co.ujet.android.clean.entity.menu.channel.Channel
import co.ujet.android.clean.entity.menu.channel.Channel.Companion.DEFLECTION_REASON_AFTER_HOURS
import co.ujet.android.clean.entity.menu.channel.ChatChannel
import co.ujet.android.clean.entity.menu.channel.EmailChannel
import co.ujet.android.clean.entity.menu.channel.ExternalDeflectionLinks
import co.ujet.android.clean.entity.menu.channel.InstantCallChannel
import co.ujet.android.clean.entity.menu.channel.PstnCallChannel
import co.ujet.android.clean.entity.menu.channel.ScheduledCallChannel
import co.ujet.android.clean.entity.menu.channel.VoicemailChannel
import co.ujet.android.common.util.LocaleUtil
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.commons.domain.ExternalDeflectionLink
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.FONT_DISPLAY_SIZE_TO_HIDE_ICON
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.getFontAndDisplaySize
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.dpToPx
import cx.ujet.android.markdown.widgets.MarkdownTextView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

class ChannelFragment @Keep constructor() : BaseFragment(), ChannelContract.View, LoadingStateInteractor {
    private lateinit var presenter: ChannelContract.Presenter
    private var titleTextView: TextView? = null
    private var descriptionText: MarkdownTextView? = null
    private var channelContainer: LinearLayout? = null
    private var titleViewTopPadding = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val context = activity?.applicationContext ?: return
        val fetchRootMenus = arguments?.getBoolean(ARGS_IS_LEAF_MENU, false) ?: false

        val channelRouter = ChannelRouter(
            this,
            activity,
            Injection.provideConfiguration(),
            ujetContext(),
            Injection.provideLocalRepository(context),
            Injection.provideUploadRepository(context),
            Injection.provideGetCompany(context),
            apiManager(),
            Injection.provideUseCaseHandler(),
            Injection.provideGetMenus(context),
            Injection.provideGetMenuPath(context),
            Injection.provideGetSelectedMenu(context),
            Injection.provideChooseLanguage(context),
            Injection.provideDeflectedEventManager(context))

        presenter = ChannelPresenter(
            Injection.provideConfiguration(),
            ujetContext(),
            Injection.provideLocalRepository(context),
            apiManager(),
            this,
            Injection.provideUseCaseHandler(),
            Injection.provideGetCompany(context),
            Injection.provideGetMenus(context),
            Injection.provideGetMenuPath(context),
            Injection.provideGetSelectedMenu(context),
            Injection.provideGetAudibleMessages(context),
            Injection.provideSaveSelectedMenu(context),
            this,
            fetchRootMenus,
            channelRouter)

        presenter.initialize()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.ujet_fragment_channels, container, false)

        UjetViewStyler.styleFragmentBackground(ujetStyle(), view)

        titleTextView = view.findViewById(R.id.title_view)
        titleTextView?.let { titleTextView ->
            UjetViewStyler.overrideTypeface(ujetStyle(), titleTextView)
            UjetViewStyler.stylePrimaryText(ujetStyle(), titleTextView)
            titleTextView.text = getString(R.string.ujet_channel_title)
            // Align layout centered vertically by default
            addOrRemoveCenteringLayout(true)
            titleTextView.visibility = INVISIBLE
        }
        channelContainer = view.findViewById<LinearLayout>(R.id.channels_container).apply {
            visibility = INVISIBLE
        }

        descriptionText = view.findViewById(R.id.description)
        descriptionText?.let { descriptionText ->
            descriptionText.gravity = Gravity.CENTER_HORIZONTAL
            UjetViewStyler.overrideTypeface(ujetStyle(), descriptionText)
            UjetViewStyler.styleSecondaryText(ujetStyle(), descriptionText)
            descriptionText.setText(R.string.ujet_channel_description)
            descriptionText.visibility = INVISIBLE
        }
        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_exit -> {
                    finish()
                    true
                }
                else -> false
            }
        })

        if (isLandscape(context)) {
            handleLandscapeOrientation(view)
        }
        return view
    }

    private fun handleLandscapeOrientation(view: View) {
        view.findViewById<ScrollView>(R.id.channel_list_view).apply {
            setPadding(dpToPx(context, 50).toInt(), 0, dpToPx(context, 50).toInt(), 0)
        }
    }

    private fun handleKeyboardAccessibility() {
        view?.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            val menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                // Move focus to another view when Tab or Down is pressed
                channelContainer?.getChildAt(0)?.requestFocus()
                true
            }, onEnter = {
                finish()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(channelContainer?.getChildAt(0), onDpadUp = {
                navigateUpView?.requestFocus()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(navigateUpView, onDpadDown = {
                channelContainer?.getChildAt(0)?.requestFocus()
                true
            })
        }
    }

    override fun onResume() {
        super.onResume()
        setActionBarTitle(getString(R.string.ujet_common_support).uppercase(Locale.getDefault()))
        presenter.handleOnResume()
        handleKeyboardAccessibility()
    }

    override fun onPause() {
        super.onPause()
        presenter.handleOnPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        titleTextView = null
        descriptionText = null
        channelContainer = null
    }

    override fun onDestroy() {
        presenter.clearResources()
        super.onDestroy()
    }

    private fun getButtonDrawable(isDisabled: Boolean): StateListDrawable {
        val strokePx = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
        val cornerRadiusPx = ujetStyle().buttonRadius
        val normalDrawable = if (isDisabled) {
            DesignUtil.createRoundedRectangleDrawable(
                    ujetStyle().disabledChannelViewColor,
                    ujetStyle().disabledChannelViewColor,
                    strokePx, cornerRadiusPx)
        } else {
            DesignUtil.createRoundedRectangleDrawable(
                    ujetStyle().pickerBackgroundColor,
                    ujetStyle().channelBorderColor,
                    strokePx, cornerRadiusPx)
        }
        val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(
                ujetStyle().colorPrimary, ujetStyle().channelBorderColor, strokePx, cornerRadiusPx)
        return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
    }

    override fun isVoIPAvailable(sensitivity: Double): Boolean {
        val activity = activity ?: return true
        if (isActive.not()) {
            return true
        }

        return activity is UjetActivity && activity.voipAvailability?.isAvailable(sensitivity) ?: true
    }

    override fun showMenuUpdated() {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }
        Toast.makeText(activity, R.string.ujet_menu_updated_alert, Toast.LENGTH_LONG).show()
    }

    override fun restart() {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        val intent = activity.intent
        activity.finish()
        startActivity(intent)
    }

    override fun updateEnabledChannels(enabledChannels: List<Channel>, fetchRootMenus: Boolean,
                                       areRootMenusUpdated: Boolean, isBackButtonClicked: Boolean) {
        val context = context ?: return
        if (isActive.not()) {
            return
        }

        val channelsContainer = view?.findViewById<LinearLayout>(R.id.channels_container) ?: return
        channelsContainer.removeAllViews()

        for (channel in enabledChannels) {
            if (!channel.isEnabled) continue

            if (channel is ExternalDeflectionLinks) {
                channel.links?.let { links ->
                    for (link in links) {
                        if (link.enabled.not()) continue

                        var resourceID = RESOURCE_NOT_FOUND
                        link.mobileIconId?.let { icon ->
                            var mobileIconId = icon
                            //Remove the unnecessary extension from icon if present
                            val index = icon.lastIndexOf('.')
                            if (index >= 0) {
                                mobileIconId = icon.substring(0, index)
                            }

                            resourceID = resources.getIdentifier(mobileIconId, "drawable", context.packageName) // returns 0 if resource not found
                        }

                        //If mobileIconId is not set or resource not found then we will use default icon
                        if (resourceID == RESOURCE_NOT_FOUND) {
                            resourceID = R.drawable.ujet_ic_external_deflection_links
                        }

                        val quadruple = Quadruple(R.id.ujet_channel_external_deflection_links, resourceID, link.displayName, link.callToAction)
                        setUpChannel(quadruple, fetchRootMenus, areRootMenusUpdated, link)
                    }
                }
            } else {
                val quadruple = when (channel) {
                    is EmailChannel -> Quadruple(R.id.ujet_channel_email, R.drawable.ujet_ic_email, getString(R.string.ujet_channel_menu_email), null)
                    is ChatChannel -> Quadruple(R.id.ujet_channel_chat, R.drawable.ujet_ic_chat, getString(R.string.ujet_channel_menu_chat), getString(R.string.ujet_channel_menu_waiting_time))
                    is InstantCallChannel -> Quadruple(R.id.ujet_channel_instant_call, R.drawable.ujet_ic_call_now, getString(R.string.ujet_channel_menu_instant_call), getString(R.string.ujet_channel_menu_waiting_time))
                    is PstnCallChannel -> Quadruple(R.id.ujet_channel_phone_call, R.drawable.ujet_ic_call_now, getString(R.string.ujet_channel_menu_instant_call), null)
                    is ScheduledCallChannel -> Quadruple(R.id.ujet_channel_scheduled_call, R.drawable.ujet_ic_scheduled, getString(R.string.ujet_channel_menu_scheduled_call), getString(R.string.ujet_channel_menu_scheduled_call_subtitle))
                    is VoicemailChannel -> Quadruple(R.id.ujet_channel_voicemail, R.drawable.ujet_ic_voicemail, getString(R.string.ujet_channel_menu_voicemail), null)
                    else -> throw IllegalArgumentException("Invalid channel type: $channel")
                }

                setUpChannel(quadruple, fetchRootMenus, areRootMenusUpdated, channel)
            }
        }
        registerGlobalLayoutListener(channelsContainer, isBackButtonClicked)
    }

    private fun registerGlobalLayoutListener(channelsContainer: LinearLayout? = null, isBackButtonClicked: Boolean) {
        channelsContainer?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                channelsContainer.viewTreeObserver.removeOnGlobalLayoutListener(this)
                // Remove title view center alignment to accommodate channel views and padding is
                // calculated below to vertically center the layout instead of this rule.
                addOrRemoveCenteringLayout(false)
                // If we are not coming back from another screen and already applied top padding to title view, ignore from here
                if (!isBackButtonClicked && titleViewTopPadding > 0) {
                    return
                }
                titleTextView?.let { titleView ->
                    titleView.setPadding(titleView.paddingLeft, 0,
                        titleView.paddingRight, titleView.paddingBottom)
                }
                val titleViewHeight = titleTextView?.height ?: 0
                val descriptionViewHeight = descriptionText?.height ?: 0
                val channelsContainerHeight = channelsContainer.height
                // We add 16dp at the description and 30dp for channel list view
                val additionalMarginHeightPx = getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_text_start_margin) +
                        getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_list_view_top_margin)
                val currentViewHeight = titleViewHeight + descriptionViewHeight +
                        channelsContainerHeight + additionalMarginHeightPx
                // We add 40dp padding between tool bar and the white background at the top and bottom of the screen
                val topViewMargin = getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_top_margin)
                // Calculate height of white background after subtracting topViewMargin padding (40dp
                // at the top and bottom of the screen) twice
                val actualScreenHeight = view?.height?.minus((topViewMargin * 2)) ?: 0
                // When there is not enough space left to accommodate views, screen will be scrollable
                // and we need to add topViewMargin (40dp) padding + extra 20dp padding at the top and
                // bottom of the screen.
                val defaultTopBottomPadding = topViewMargin + getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_extra_scrollable_margin)
                var verticalCenterViewPadding = (actualScreenHeight - currentViewHeight) / 2
                val (topPadding, bottomPadding) = when {
                    verticalCenterViewPadding < topViewMargin -> {
                        // verticalCenterViewPadding is < 0, means we do not have enough space left to draw all the
                        // views and view changed to scrollable and using default top padding in that case
                        // and subtracting additional padding that is added in ujet_channel_list_item between
                        // channel views for the bottom padding
                        Pair(defaultTopBottomPadding, defaultTopBottomPadding - getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_view_padding))
                    }
                    verticalCenterViewPadding < defaultTopBottomPadding * 2 -> {
                        // When vertical padding is < default padding or twice the value of it, adding half
                        // of top margin is enough to make it centered vertically
                        verticalCenterViewPadding += topViewMargin / 2
                        Pair(verticalCenterViewPadding, view?.paddingBottom ?: 0)
                    }
                    else -> {
                        // Add topViewMargin (40dp) padding to top padding and use default bottom padding
                        verticalCenterViewPadding += topViewMargin
                        Pair(verticalCenterViewPadding, view?.paddingBottom ?: 0)
                    }
                }
                titleTextView?.let { titleView ->
                    titleView.setPadding(titleView.paddingLeft, topPadding,
                        titleView.paddingRight, titleView.paddingBottom)
                }
                view?.let { channelView ->
                    channelView.setPadding(channelView.paddingLeft, channelView.paddingTop,
                        channelView.paddingRight, bottomPadding)
                }
                // When user minimizes the channel screen and re opened it again, we are applying top
                // padding again and calculations will be wrong and to avoid it, store previous top padding
                // and return it if titleViewTopPadding exists.
                titleViewTopPadding = topPadding
                //Add 5ms delay so that UI gets time to give padding to channel container view
                lifecycleScope.launch {
                    delay(5)
                    channelsContainer.visibility = VISIBLE
                    titleTextView?.visibility = VISIBLE
                    descriptionText?.visibility = VISIBLE
                }
            }
        })
    }

    private fun addOrRemoveCenteringLayout(addRule: Boolean) {
        val titleTextViewParams: RelativeLayout.LayoutParams? = titleTextView?.layoutParams as? RelativeLayout.LayoutParams
        if (addRule) {
            titleTextViewParams?.addRule(RelativeLayout.CENTER_VERTICAL)
        } else {
            titleTextViewParams?.removeRule(RelativeLayout.CENTER_VERTICAL)
        }
        titleTextView?.layoutParams = titleTextViewParams
    }

    private fun getPixelSize(dimenResId: Int): Int {
        return resources.getDimensionPixelSize(dimenResId)
    }

    private fun setUpChannel(
        quadruple: Quadruple<Int?, Int?, String?, String?>,
        fetchRootMenus: Boolean,
        areRootMenusUpdated: Boolean,
        channel: Channel,
    ) {
        setupChannelView(quadruple, fetchRootMenus, areRootMenusUpdated, channel, null)
    }

    private fun setUpChannel(
        quadruple: Quadruple<Int?, Int?, String?, String?>,
        fetchRootMenus: Boolean,
        areRootMenusUpdated: Boolean,
        externalDeflectionLink: ExternalDeflectionLink,
    ) {
        setupChannelView(quadruple, fetchRootMenus, areRootMenusUpdated, null, externalDeflectionLink)
    }

    private fun setupChannelView(
        quadruple: Quadruple<Int?, Int?, String?, String?>,
        fetchRootMenus: Boolean,
        areRootMenusUpdated: Boolean,
        channel: Channel?,
        externalDeflectionLink: ExternalDeflectionLink?,
    ) {
        val context = context ?: return
        if (isActive.not()) {
            return
        }

        val channelsContainer = view?.findViewById<LinearLayout>(R.id.channels_container) ?: return
        val channelLayout = LayoutInflater.from(context).inflate(R.layout.ujet_channel_list_item, null)
        val channelIcon = channelLayout.findViewById<ImageView>(R.id.channel_icon)
        val channelName = channelLayout.findViewById<TextView>(R.id.channel_name)
        val channelDescription = channelLayout.findViewById<TextView>(R.id.channel_description)

        val layoutId = quadruple.first ?: return
        val channelIconDrawableRes = quadruple.second ?: return
        val channelTitle = quadruple.third
        val channelDesc = quadruple.fourth

        channelLayout.id = layoutId
        channelLayout.setOnClickListener {
            if (channel != null) {
                presenter.selectChannel(channel)
            } else if (externalDeflectionLink != null) {
                presenter.selectChannel(externalDeflectionLink)
            }
        }
        channelLayout.visibility = View.VISIBLE

        //Apply drawable changes for existing channels and use raw image in case of external deflection links.
        if (channel != null) {
            channelIcon.setImageDrawable(getIconDrawable(channelIconDrawableRes))
        } else {
            channelIcon.setImageResource(channelIconDrawableRes)
        }

        // Hide channel icon when font size is >= 175%
        channelIcon.visibility = if (getFontAndDisplaySize(context) >= FONT_DISPLAY_SIZE_TO_HIDE_ICON) {
            ViewGroup.GONE
        } else {
            ViewGroup.VISIBLE
        }

        if (channelTitle.isNullOrEmpty()) {
            channelName.visibility = View.GONE
        } else {
            channelName.text = channelTitle
            //To treat custom views as button, add button role to views
            AccessibilityUtil.addButtonRole(channelName)
            channelName.visibility = View.VISIBLE
        }

        if (channelDesc.isNullOrEmpty()) {
            channelDescription.visibility = View.GONE
        } else {
            channelDescription.text = channelDesc
            channelDescription.visibility = View.VISIBLE
        }

        //Set font for channel name and description
        UjetViewStyler.overrideTypeface(ujetStyle(), channelName)
        UjetViewStyler.overrideTypeface(ujetStyle(), channelDescription)

        if (fetchRootMenus && !areRootMenusUpdated) {
            channelLayout.background= getButtonDrawable(true)
            channelLayout.isClickable = false
            channelName.setTextColor(ujetStyle().disabledTextColor)
            channelDescription.setTextColor(ujetStyle().textLoadingViewColor)
            channelDescription.background = roundedRectangleDrawable
        } else if (channel is InstantCallChannel && presenter.isVoIPAndPstnConfigDisabled()) {
            channelLayout.background = getButtonDrawable(true)
            channelLayout.isClickable = false
            channelName.setTextColor(ujetStyle().disabledTextColor)
            channelDescription.text = getString(R.string.ujet_error_insufficient_data_connectivity)
            channelDescription.setTextColor(ujetStyle().disabledTextColor)
            channelDescription.background = null
        } else {
            channelLayout.background = getButtonDrawable(false)
            channelLayout.isClickable = true
            channelName.setTextColor(
                DesignUtil.getColorStateList(
                    ujetStyle().colorPrimary, DesignUtil.getColor(context, co.ujet.android.ui.R.color.ujet_white)))
            UjetViewStyler.styleSecondaryText(ujetStyle(), channelDescription)
            channelDescription.background = null
        }

        val layoutParams: FrameLayout.LayoutParams = channelsContainer.layoutParams as FrameLayout.LayoutParams
        layoutParams.setMargins(0, 0, 0, getPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_list_view_bottom_margin))

        channelsContainer.addView(channelLayout, layoutParams)
    }

    private fun getIconDrawable(@DrawableRes iconResId: Int): StateListDrawable? {
        val channelIconDrawable = ContextCompat.getDrawable(context ?: return null, iconResId)
        val originConstantState = channelIconDrawable?.constantState ?: return null
        val normalDrawable = originConstantState.newDrawable()
        val pressedDrawable = originConstantState.newDrawable()
        pressedDrawable.colorFilter = PorterDuffColorFilter(ujetStyle().channelIconColor, PorterDuff.Mode.SRC_ATOP)
        return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
    }

    override fun showTitleAndDescription(header: String?, message: String?) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        header?.let { titleTextView?.text = it }
        descriptionText?.let { descriptionTextView ->
            UjetViewStyler.styleRemoteChatText(ujetStyle(), descriptionTextView)
            UjetViewStyler.styleRemoteChatLinkText(ujetStyle(), descriptionTextView)
            message?.let { afterHourMessage ->
                MarkdownUtil.loadMessageIntoMarkdownTextView(
                    activity.applicationContext, descriptionTextView, afterHourMessage,
                    deflectionType = DEFLECTION_REASON_AFTER_HOURS
                )
            }
        }
    }

    override fun showLoadingState(channelType: ChannelType) {
        FragmentHelper.show(
                this,
                LoadingStateFragment.newInstance(channelType),
                LoadingStateFragment.TAG)
    }

    override fun updateChatWaitTime(waitTime: Int) {
        val channelDescription = getChannelDescriptionById(R.id.ujet_channel_chat)
        channelDescription?.text = getWaitTimeMessage(waitTime)
    }

    override fun updateCallWaitTime(waitTime: Int) {
        if (presenter.isVoIPAndPstnConfigDisabled()) {
            return
        }

        val channelDescription = getChannelDescriptionById(R.id.ujet_channel_instant_call)
        channelDescription?.text = getWaitTimeMessage(waitTime)
    }

    private fun getChannelDescriptionById(channelLayoutId: Int): TextView? {
        val channelsContainer = view?.findViewById<View>(R.id.channels_container)
        val channelLayout = channelsContainer?.findViewById<View>(channelLayoutId)
        return channelLayout?.findViewById(R.id.channel_description)
    }

    private fun getWaitTimeMessage(waitTime: Int): String {
        if (waitTime >= 0) {
            val waitTimeStr = TimeUtil.getWaitTimeFormat(activity ?: return "", waitTime)
            val waitTimeMessage = getString(R.string.ujet_channel_menu_waiting_time, waitTimeStr)
            return HtmlCompat.fromHtml(waitTimeMessage, HtmlCompat.FROM_HTML_MODE_LEGACY).toString()
        }

        val waitTimeMessage = getString(R.string.ujet_channel_menu_waiting_time, "")
        return HtmlCompat.fromHtml(waitTimeMessage, HtmlCompat.FROM_HTML_MODE_LEGACY).toString()
    }

    override fun updateMenuPath(menuPath: String?, channelType: ChannelType?) {
        if (isActive.not()) {
            return
        }
        val stringRes = when (channelType) {
            ChannelType.ChannelChat -> R.string.ujet_channel_description_chat
            ChannelType.ChannelCall, ChannelType.ChannelPstnCall -> R.string.ujet_channel_description_call
            ChannelType.ChannelVoiceMail -> R.string.ujet_channel_description_voicemail
            ChannelType.ChannelScheduleCall -> R.string.ujet_channel_description_scheduled_call
            ChannelType.ChannelEmail -> R.string.ujet_channel_description_email
            else -> R.string.ujet_channel_description
        }
        descriptionText?.gravity = Gravity.CENTER_HORIZONTAL
        descriptionText?.text = HtmlCompat.fromHtml(getString(stringRes, menuPath), HtmlCompat.FROM_HTML_MODE_LEGACY)
    }

    override fun adjustMenuPathForRtl(path: String?) : String? {
        val context = context ?: return path
        if (LocaleUtil.getCurrentLocale(context).layoutDirection == LayoutDirection.RTL) {
            val pathSeparator = " / "
            return path
                ?.split(pathSeparator)
                ?.reversed()
                ?.joinToString(separator = pathSeparator)
        }
        return path
    }

    override fun updateLanguage(language: String) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }
        LocaleUtil.update(activity, language)
    }

    override fun handleActivityForResult(requestKey: String, result: Bundle) {
        if (TAG == requestKey) {
            val requestCode = result.getInt(REQUEST_CODE)
            val resultCode = result.getInt(RESULT_CODE)
            if (requestCode == REQUEST_CODE_ALERT_RESPONSE && resultCode == Activity.RESULT_OK) {
                presenter.onErrorDialogConfirmed()
            }
        }
    }

    override fun onError(message: String?) {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        Toast.makeText(activity, message, Toast.LENGTH_LONG).show()
    }

    override fun back() {
        parentFragmentManager.popBackStack()
    }

    override fun finish() {
        val activity = activity ?: return
        if (isActive.not()) {
            return
        }

        activity.finish()
    }

    override fun isActive(): Boolean {
        return isAdded
    }

    override fun showErrorDialog() {
        showErrorDialog(getString(R.string.ujet_menu_disabled))
    }

    override fun showNoDataConnectivityError() {
        showErrorDialog(getString(R.string.ujet_error_insufficient_data_connectivity))
    }

    private fun showErrorDialog(message: String) {
        if (isActive.not()) {
            return
        }

        AlertDialogFragment.newInstance(TAG, REQUEST_CODE_ALERT_RESPONSE,
                null, message, true)
            .show(parentFragmentManager, AlertDialogFragment.TAG)
    }

    private val roundedRectangleDrawable: StateListDrawable
        get() {
            val strokePx = resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
            val cornerRadiusPx = ujetStyle().buttonRadius
            val normalDrawable = DesignUtil.createRoundedRectangleDrawable(
                    ujetStyle().textLoadingViewColor,
                    ujetStyle().textLoadingViewColor, strokePx, cornerRadiusPx)
            val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(
                    ujetStyle().textFocusBackgroundColor, ujetStyle().pickerSeparatorColor, strokePx, cornerRadiusPx)
            return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
        }

    override fun onFinished(response: GetMenus.ResponseValue) {
        val loadingStateFragment = parentFragmentManager.findFragmentByTag(LoadingStateFragment.TAG) as LoadingStateFragment?
        loadingStateFragment?.onRootMenusUpdated(response)
    }

    companion object {
        const val TAG = "ChannelFragment"
        private const val ARGS_IS_LEAF_MENU = "fetch_menu_details"
        private const val REQUEST_CODE_ALERT_RESPONSE = 1000
        private const val RESOURCE_NOT_FOUND = 0

        @JvmStatic
        fun newInstance(fetchRootMenus: Boolean):
                ChannelFragment {
            val channelFragment = ChannelFragment()
            val bundle = Bundle()
            bundle.putBoolean(ARGS_IS_LEAF_MENU, fetchRootMenus)
            channelFragment.arguments = bundle
            return channelFragment
        }
    }
}
