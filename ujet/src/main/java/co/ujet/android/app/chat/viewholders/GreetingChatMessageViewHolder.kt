package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.MarkdownUtil.loadMessageIntoMarkdownTextView
import co.ujet.android.commons.domain.chat.message.GreetingChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.extensions.applyColorFilter
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView

class GreetingChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup,
                                    activity: Activity, ujetStyle: UjetStyle) :
    ChatMessageViewHolder(adapter, activity.applicationContext, ujetStyle, inflate(activity, parent,
        R.layout.ujet_view_chat_message_greeting)) {

    fun bind(
        message: GreetingChatMessage,
        shouldShowAgentNames: Boolean,
        isGroupStart: Boolean,
        isMarkDownSupported: Boolean = false
    ): View {
        val messageTextView: MarkdownTextView = itemView.findViewById(R.id.message)
        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        //When there is no background style available use default existing style
        if (!StyleUtil.isBackgroundStyleAvailable(
                context, agentMessageStyle?.backgroundColor,
                agentMessageStyle?.cornerRadius, agentMessageStyle?.border
            )
        ) {
            messageTextView.background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_agent)
            messageTextView.background.applyColorFilter(ujetStyle.chatHumanAgentMessageBubbleColor,
                BlendModeCompat.SRC_IN)
        }
        UjetViewStyler.styleRemoteChatText(ujetStyle, messageTextView)
        UjetViewStyler.styleRemoteChatLinkText(ujetStyle, messageTextView)
        if (isMarkDownSupported) {
            loadMessageIntoMarkdownTextView(context, messageTextView, message.message, supportHtmlInText = true)
        } else {
            messageTextView.text = message.message
        }
        StyleUtil.updateFontStyle(context, messageTextView, agentMessageStyle?.font)
        StyleUtil.updateBackgroundStyle(
            messageTextView,
            agentMessageStyle?.backgroundColor,
            agentMessageStyle?.cornerRadius,
            agentMessageStyle?.border
        )
        val borderPadding = StyleUtil.getTextPaddingWithInBorder(context, agentMessageStyle?.cornerRadius,
            agentMessageStyle?.border) ?: 0
        messageTextView.setPaddingRelative(
            messageTextView.paddingStart + borderPadding,
            messageTextView.paddingTop + borderPadding,
            messageTextView.paddingEnd + borderPadding,
            messageTextView.paddingBottom + borderPadding
        )
        val timestampTextView: TextView = itemView.findViewById(R.id.timestamp)
        setMessageTimestamp(timestampTextView, message.getTimestamp())
        setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
        if (ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled(context)) {
            saveTimestampTextSize(timestampTextView)
        }
        AccessibilityUtil.addChatUserRole(
            userRole = message.userRole,
            mainContainer = itemView.findViewById(R.id.main_container),
            message = messageTextView,
            timestamp = itemView.findViewById(R.id.timestamp),
            adapterPosition = adapterPosition
        )
        return itemView
    }
}
