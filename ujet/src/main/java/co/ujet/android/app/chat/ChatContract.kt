package co.ujet.android.app.chat

import androidx.annotation.StringRes
import androidx.fragment.app.FragmentManager
import co.ujet.android.app.chat.data.ChatMessageDataSource
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import co.ujet.android.commons.domain.chat.WebForm
import co.ujet.android.commons.domain.chat.message.DeflectToVirtualAgentButton
import co.ujet.android.commons.domain.chat.message.QuickReplyButton
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyChatMessage
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import co.ujet.android.data.constant.ChatStatus
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI
import co.ujet.android.smartaction.ui.verification.BiometricsVerification
import java.io.File

internal class ChatContract {
    internal interface View : BaseView {
        fun setChatMessageDataSource(dataSource: ChatMessageDataSource)
        fun clearChatMessageDataSource()
        fun scrollToBottom()
        fun displayChatConnected(agentName: String)
        fun displayCurrentState(message: String)
        fun setAgentAvatar(avatarUrl: String)
        fun setTypingIndicatorVisible(isVisible: Boolean)
        fun setAfterHoursView(isVisible: Boolean, message: String?)
        fun updateEndChatMenuText(@StringRes stringId: Int)
        fun setDismissViewVisible(isVisible: Boolean)
        fun showChatNotAvailable(otherChannelsAvailable: Boolean)
        fun showErrorMessage()
        fun showOvercapacityMessage()
        fun setEndChatEnabled(isEnabled: Boolean)
        fun setSendPhotoEnabled(isEnabled: Boolean)
        fun setChatInputVisible(isVisible: Boolean)
        fun showVideo(filename: String)
        fun showImage(filename: String)
        fun showWebForm()
        fun loadUrlInWebForm(url: String)
        fun showWebFormLoadingError(@StringRes errorMessage: Int)
        fun showCsatRating(isNewChatEnabled: Boolean)
        fun showSurveyScreen(isNewChatEnabled: Boolean)
        fun showErrorMessage(message: String)
        fun showMenus()
        fun finish()
        fun showErrorDialog()
        fun hideErrorDialog()
        fun saveStatusText(statusText: String)
        fun clearStatusText()
        fun getStatusText(): String?
        fun notifyMessagesUpdated()
        fun setEscalateEnabled(enabled: Boolean)
        fun showEmailForm(deflectionType: String?, escalationId: Int)
        fun showEmailClient(supportEmail: String?, menuPath: String?, deflectionType: String?)
        fun showErrorMessage(@StringRes stringRes: Int)
        fun setConversationConnected(isConnected: Boolean)
        fun showExternalDeflectionLinks(url: String, deflectionType: String?)
        fun displayPendingSmartAction(biometricsVerification: BiometricsVerification)
        fun updateCoBrowseButton(isCoBrowseSupportedAndConnected: Boolean)
        fun getCoBrowseUI(): CoBrowseUI?
        fun getCoBrowseButtonView(): android.view.View?
        fun getMainFragmentManager(): FragmentManager?
        fun updateTransferBanner(visible: Boolean)
        fun setEmptyViewProgressBarVisibility(visible: Boolean)
        fun showPreSessionSmartActions(menuId: Int)
        fun setPostSessionOptInBannerVisibility(isVisible: Boolean, showLoadingBar: Boolean = false)
        fun showPostSessionChatUI(value: Boolean)
        fun clearChatMessages()
        fun showSurveyErrorDialog()
        fun openFilePicker(fileName: String?, cacheFile: File?)
        fun saveChatTranscriptId(chatTranscriptId: Int)
        fun showChatTranscriptMaxRequestErrorDialog()
        fun updateChatTranscriptDownloadButtonUI(isDownloadProgressBarVisible: Boolean, isDownloadErrorIconVisible: Boolean, isInlineButton: Boolean)

    }

    internal interface Presenter : BasePresenter {
        fun onChatServiceUnavailable()
        fun onChatAfterHourMessageReceived(afterHourMessage: String?)
        fun onChatErrorMessageReceived(errorMessage: String?)
        fun onChatMessageSend(message: String)
        fun onQuickReplyClicked(chatMessage: VirtualAgentQuickReplyChatMessage, quickReplyButton: QuickReplyButton)
        fun onChatMessageResend(chatMessage: SendableChatMessage)
        fun onChatMessageClicked(chatMessage: ChatMessage, position: Int)
        fun onEscalateClicked()
        fun onDeflectToVirtualAgentClicked(deflectToVirtualAgentButton: DeflectToVirtualAgentButton)
        fun onDoneClicked(endedBy: String)
        fun onChatResumeClicked()
        fun onChatNewClicked()
        fun onNewChatButtonClicked()
        fun onChatAfterHoursRestartClicked()
        fun onChatNotAvailableConfirmed()
        fun onChannelsNotAvailableConfirmed()
        fun stop()
        fun isEmailSent(): Boolean
        fun deflectToVirtualAgent(escalationId: Int)
        fun isChatPreviewAvailable(): Boolean
        fun onSendChatMessagePreview(message: String)
        fun setChatInput(input: String)
        fun getChatInput(): String
        fun onContentCardClicked(eventParams: HashMap<String, Any>?, title: String?)
        fun onWebFormClicked(webForm: WebForm)
        fun onContentCardButtonClicked(eventParams: HashMap<String, Any>?, cardTitle: String?, buttonTitle: String?)
        fun requestChatHistory()
        fun clearWebFormResources()
        fun isPostSessionRequired(): Boolean
        fun isPostSessionOptInRequired(): Boolean
        fun getChatStatus(): ChatStatus?
        fun initiateChatTranscriptPdfGeneration(cacheDirectory: File, isInlineButton: Boolean)
        fun clearDownloadChatTranscriptResources()
        fun downloadChatTranscriptPdf(cacheDirectory: File, chatTranscriptId: Int, isInlineButton: Boolean)
    }
}
