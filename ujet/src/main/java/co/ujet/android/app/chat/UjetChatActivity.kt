package co.ujet.android.app.chat

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.view.KeyEvent
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.Toolbar
import co.ujet.android.R
import co.ujet.android.app.chat.ChatFragment.Companion.newInstance
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.common.util.ServiceUtil
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger
import co.ujet.android.service.UjetChatService
import co.ujet.android.ui.util.StyleUtil
import java.net.HttpURLConnection
import java.util.*

class UjetChatActivity : UjetBaseActivity() {
    private var errorMessage: String? = null
    private var isAfterHourMessage = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.ujet_activity_chat)
        // We need app theme to be Theme.MaterialComponents (or a descendant) to allow material buttons
        // to be added to MaterialButtonToggleGroup programmatically in VirtualAgentChatMessageViewHolder
        setTheme(R.style.Theme_UjetButton_GroupedQuickReplyListButton)
        setToolBar()
        window.statusBarColor = ujetStyle().colorPrimaryDark
        parseIntent(intent)
        if (supportFragmentManager.findFragmentByTag(ChatFragment.TAG) == null) {
            supportFragmentManager
                    .beginTransaction()
                    .setCustomAnimations(R.anim.ujet_anim_slide_out_left, R.anim.ujet_anim_slide_in_right)
                    .add(R.id.fragment_container, newInstance(errorMessage, isAfterHourMessage), ChatFragment.TAG)
                    .commit()
        }
    }

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        toolbar.setBackgroundColor(ujetStyle().colorPrimary)
        setSupportActionBar(toolbar)

        //Apply style options to back button in chat screen
        val context = this.applicationContext ?: return
        val backButtonStyles = Injection.provideUjetStyle(context).customStylesOptions?.chatStyles?.backButton
        val backButtonVisibility = backButtonStyles?.visible ?: true
        val backButtonImageName = backButtonStyles?.image
        val backButtonIconResId = StyleUtil.getDrawableResIdByName(context, backButtonImageName) // returns 0 if resource not found

        supportActionBar?.apply {
            title = getString(R.string.ujet_chat_title).uppercase(Locale.ROOT)
            setDisplayHomeAsUpEnabled(backButtonVisibility)
            if (backButtonIconResId != StyleUtil.RESOURCE_NOT_FOUND) {
                try {
                    //Re size back button icons to fit in action bar
                    val backButtonBitmap = (AppCompatResources.getDrawable(context, backButtonIconResId) as BitmapDrawable).bitmap
                    val backButtonDrawable = BitmapDrawable(resources, Bitmap.createScaledBitmap(backButtonBitmap, 50, 50, true))
                    setHomeAsUpIndicator(backButtonDrawable)
                } catch (e: Exception) {
                    //If customer supplied vector drawable icons then we will fall through here and use it as it is
                    setHomeAsUpIndicator(backButtonIconResId)
                }
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        back()
        return true
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Logger.i("Started the chat activity with new intent")
        parseIntent(intent)

        val transaction = supportFragmentManager
                .beginTransaction()
                .setCustomAnimations(R.anim.ujet_anim_slide_out_left, R.anim.ujet_anim_slide_in_right)
        supportFragmentManager.findFragmentByTag(ChatFragment.TAG)?.let {
                transaction.remove(it)
        }
        transaction
                .add(R.id.fragment_container, newInstance(errorMessage, isAfterHourMessage), ChatFragment.TAG)
                .commit()
    }

    override fun checkWebViewAvailability() {
        if (WebViewUtil.isWebViewDisabled(applicationContext)) {
            Logger.w("Web view is disabled")
            WebViewUtil.handleWebViewUnavailability(applicationContext)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            when (val emailFragment = supportFragmentManager.findFragmentByTag(EmailFragment.TAG)) {
                is EmailFragment -> emailFragment.onKeyDown(keyCode)
                else -> back()
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun back() {
        if (supportFragmentManager.backStackEntryCount > 1) {
            supportFragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    private fun parseIntent(intent: Intent?) {
        var errorMessage: String? = null
        val errorCode: Int

        if (intent?.hasExtra(EXTRA_ERROR_CODE) == true) {
            errorCode = intent.getIntExtra(EXTRA_ERROR_CODE, 0)
            if (intent.hasExtra(EXTRA_ERROR_MESSAGE)) {
                errorMessage = intent.getStringExtra(EXTRA_ERROR_MESSAGE)
            }
            if (errorCode == HttpURLConnection.HTTP_CONFLICT && errorMessage?.isNotEmpty() == true) {
                isAfterHourMessage = true
                this.errorMessage = errorMessage
            } else {
                isAfterHourMessage = false
                this.errorMessage = getString(R.string.ujet_error_chat_connect_fail_android)
            }
        }
    }

    companion object {
        private const val EXTRA_ERROR_CODE = "error_code"
        private const val EXTRA_ERROR_MESSAGE = "error_message"

        @JvmStatic
        fun start(context: Context) {
            if (!ServiceUtil.isServiceRunning(context, UjetChatService::class.java)) {
                Logger.w("Try to start chat activity without chat service")
                return
            }

            val intent = Intent(context, UjetChatActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            context.startActivity(intent)
        }

        @JvmStatic
        fun start(context: Context, errorCode: Int, errorMessage: String?) {
            if (!ServiceUtil.isServiceRunning(context, UjetChatService::class.java)) {
                Logger.w("Try to start chat activity without chat service")
                return
            }

            val intent = Intent(context, UjetChatActivity::class.java)
            intent.putExtra(EXTRA_ERROR_CODE, errorCode)
            intent.putExtra(EXTRA_ERROR_MESSAGE, errorMessage)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(intent)
        }
    }
}
