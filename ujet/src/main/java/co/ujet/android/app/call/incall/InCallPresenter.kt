package co.ujet.android.app.call.incall

import android.os.SystemClock
import android.text.TextUtils
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.app.call.CallListener
import co.ujet.android.app.call.incall.InCallContract.Presenter
import co.ujet.android.app.call.incall.InCallContract.View
import co.ujet.android.smartaction.ui.verification.BiometricsVerification
import co.ujet.android.clean.domain.EmptyUseCaseCallback
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.UseCaseHandler2
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.menu.filter.MenuFilter
import co.ujet.android.clean.domain.menu.usecase.GetMenuPath
import co.ujet.android.clean.domain.menu.usecase.GetMenus
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu.RequestValues
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu.ResponseValue
import co.ujet.android.clean.entity.menu.Menu
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.common.util.RecordingPermissionUtils
import co.ujet.android.smartaction.helper.SmartActionHelper
import co.ujet.android.smartaction.helper.SmartActionHelper.sendSmartActionStatus
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.call.InCallState
import co.ujet.android.data.constant.CallCreateType.InAppIvrCall
import co.ujet.android.data.constant.CallStatus
import co.ujet.android.data.model.*
import co.ujet.android.data.model.CallDeflectionType.*
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger
import java.net.HttpURLConnection
import java.util.*
import kotlin.math.min

/**
 * Presenter for [InCallFragment]
 */
class InCallPresenter internal constructor(
    private val ujetContext: UjetContext,
    private val biometricsVerification: BiometricsVerification,
    private val localRepository: LocalRepository,
    private val view: View,
    private val apiManager: ApiManager,
    private val callServiceInteractor: CallServiceInteractor,
    private val useCaseHandler: UseCaseHandler,
    private val getCompany: GetCompany,
    private val getMenuPath: GetMenuPath,
    private val getMenus: GetMenus,
    private val getSelectedMenu: GetSelectedMenu
) : Presenter, CallListener {
    private var inCallState: InCallState? = null
    private var deflectionTimer: Timer? = null
    private var pendingDeflectionMenuId = 0
    private var pendingDeflectionType: CallDeflectionType? = null
    private var pendingDeflectionData: String? = null
    private var isSingleMenu = false
    private var isSelectedMenuFetched = false
    private var isSingleChannelEnabled = false
    private var isEmailEnhancementEnabled = false
    private var virtualAgentSettings: VirtualAgentSettings? = null

    // Begin to implement InCallContract.Presenter
    override fun start() {
        Logger.d("Start call presenter")
        Injection.provideUjetEventNotificationManager().communicationUiStarted("call")

        getEmailEnhancementFeatureAvailability()
        getCachedRootMenus()
        if (!isTransferring() && inCallState?.isEscalating != true) {
            showConnectingMessage()
        } else {
            updateAgentNamesInCall()
            getCachedSelectedMenu()
        }
    }

    private fun showConnectingMessage() {
        useCaseHandler.executeImmediate(
            getSelectedMenu,
            RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    isSelectedMenuFetched = true
                    val recordingOption = response.menu.recordingOption
                    val isRecordingPermitted = if (TextUtils.isEmpty(recordingOption)) {
                        false
                    } else {
                        // In this screen, we show call recording message only when recording option is record always or
                        // permission granted through confirmation screen in case of ask user option.
                        val recordingPermission = view.getCachedRecordingPermission()
                        RecordingPermissionUtils.RECORD_ALWAYS == recordingOption ||
                                RecordingPermissionUtils.RECORD_ASK_USER == recordingOption && recordingPermission == RecordingPermissionUtils.RECORDING_PERMISSION_GRANTED
                    }
                    if (view.isActive) {
                        view.showConnectingMessage(isRecordingPermitted)
                        view.setEmptyViewProgressBarVisibility(false)
                        view.setCallScreenViewsVisibility(true)
                    }
                    isSingleChannelEnabled = getIfSingleChannelEnabled(response.menu)
                }

                override fun onError() {
                    if (view.isActive) {
                        view.setEmptyViewProgressBarVisibility(true)
                        view.setCallScreenViewsVisibility(false)
                    }
                }
            }
        )
    }

    private fun getEmailEnhancementFeatureAvailability() {
        useCaseHandler.executeImmediate(
            getCompany,
            GetCompany.RequestValues(false, true),
            object : EmptyUseCaseCallback<GetCompany.ResponseValue>() {
                override fun onSuccess(response: GetCompany.ResponseValue) {
                    isEmailEnhancementEnabled = response.company.isEmailEnhancementEnabled
                }

                override fun onError() {
                    Logger.w("Couldn't get email enchantment feature availability")
                }
            })
    }

    private fun getCachedRootMenus() {
        useCaseHandler.execute(
            getMenus,
            GetMenus.RequestValues.createCached(ujetContext.directAccessKey, MenuFilter.VISIBLE),
            object : EmptyUseCaseCallback<GetMenus.ResponseValue>() {
                override fun onSuccess(response: GetMenus.ResponseValue) {
                    isSingleMenu = response.isDirectAccess && response.menus.isNotEmpty() && response.menus[0].isLeafMenu
                }

                override fun onError() {
                    Logger.w("Couldn't get cached root menu data")
                }
            })
    }

    private fun getCachedSelectedMenu() {
        if (isSelectedMenuFetched) {
            return
        }
        useCaseHandler.execute(
            getSelectedMenu,
            RequestValues(ujetContext.directAccessKey),
            object : EmptyUseCaseCallback<ResponseValue>() {
                override fun onSuccess(response: ResponseValue) {
                    isSelectedMenuFetched = true
                    isSingleChannelEnabled = getIfSingleChannelEnabled(response.menu)
                    if (view.isActive) {
                        view.setEmptyViewProgressBarVisibility(false)
                        view.setCallScreenViewsVisibility(true)
                    }
                }

                override fun onError() {
                    Logger.w("Couldn't get cached selected menu data")
                    if (view.isActive) {
                        view.setEmptyViewProgressBarVisibility(true)
                        view.setCallScreenViewsVisibility(false)
                    }
                }
            })
    }

    private fun getIfSingleChannelEnabled(menu: Menu?): Boolean {
        return menu != null &&
                menu.channels.getEnabledChannels().size == 1 &&
                !Injection.provideConfiguration().isShowSingleChannelEnabled
    }

    override fun onCallServiceUnavailable() {
        Logger.w("No call service")
        cancelDeflectionTimer()
        showNextScreen()
    }

    override fun onCallAfterHourMessageReceived(afterHourMessage: String?) {
        if (!afterHourMessage.isNullOrEmpty() && view.isActive) {
            view.showAfterHourMessage(afterHourMessage)
        }
    }

    override fun onCallErrorMessageReceived(errorMessage: String?, failureReason: String?) {
        if (!errorMessage.isNullOrEmpty() && view.isActive) {
            Logger.w("Call error message received. Message: %s", errorMessage)
            view.showErrorMessage(errorMessage)
            if (!TextUtils.isEmpty(failureReason)) {
                callServiceInteractor.signalEndCall(failureReason)
            }
        }
    }

    override fun onMuteClicked() {
        if (!callServiceInteractor.signalMute()) {
            showNextScreen()
        }
    }

    override fun onSpeakerClicked() {
        if (!callServiceInteractor.signalSpeaker()) {
            showNextScreen()
        }
    }

    override fun onMinimizeClicked() {
        if (view.isActive) {
            view.hide()
        }
    }

    override fun onEndCallClicked() {
        callServiceInteractor.notifyEventData()
        callServiceInteractor.signalEndCall("call end button clicked")
    }

    override fun onCallDeflectionSelected(menuId: Int, type: CallDeflectionType, data: String?) {
        Logger.i("Long wait deflection is selected: %s", type.name)
        var needToEndCall = false
        pendingDeflectionMenuId = menuId
        when (type) {
            EMAIL -> {
                needToEndCall = true
                pendingDeflectionType = type
            }
            PHONE -> {
                pendingDeflectionType = type
                pendingDeflectionData = data
                needToEndCall = true
            }
            SCHEDULED_CALL -> {
                pendingDeflectionType = type
                needToEndCall = true
            }
            VOICEMAIL -> {
                pendingDeflectionType = type
                pendingDeflectionData = data
                needToEndCall = true
            }
            else -> {
                // Nothing to do here
            }
        }
        if (needToEndCall) {
            callServiceInteractor.signalEndCall(String.format("deflection to %s", type.name))
        }
    }

    private fun showEmail() {
        if (isEmailEnhancementEnabled) {
            view.showEmailForm()
        } else {
            val getCompanyRV = GetCompany.RequestValues(false)
            val getMenuPathRV = GetMenuPath.RequestValues(
                pendingDeflectionMenuId, ujetContext.directAccessKey
            )
            UseCaseHandler2<GetCompany.ResponseValue, GetMenuPath.ResponseValue>(useCaseHandler)
                .execute(getCompany, getCompanyRV, getMenuPath, getMenuPathRV) { response, _, response2, _ ->
                    callServiceInteractor.signalEndCall("deflection to email")
                    val company = response.company
                    val menuPath = response2.path
                    if (view.isActive) {
                        view.showEmailClient(company.supportEmail, menuPath)
                    }
                }
        }
    }

    override fun onScreenUnlocked() {
        view.handleSmartActionsDuringScreenLock(localRepository.ongoingSmartAction ?: return)
    }

    override fun onSmartActionCancelled() {
        sendSmartActionStatus(apiManager, localRepository, null, SmartActionHelper.SMART_ACTION_STATUS_CANCELLED)
        localRepository.clearOngoingSmartAction()
    }

    override fun onCallAfterHoursDialogConfirmed() {
        if (view.isActive) {
            view.finish()
            /** [UJET-21815] There is a bug on server side that deflection response is not included for leaf menu single channel enabled.
             * It causes user to stuck in loop for leaf menu, instant call enabled case, so do not restart the SDK in that case.
             * TODO Please remove the below check when server side fixes the issue.
             */
            if (!(isSingleMenu && isSingleChannelEnabled)) {
                view.showMenus()
            }
        }
    }

    override fun restartDeflectionTimer() {
        inCallState?.isDeflectionShown = false
        localRepository.call?.let { call ->
            startOrCancelDeflectionTimer(call)
        }
    }

    // Begin to implement CallListener
    override fun onCallFailure(failureReason: String, errorCode: Int, message: String?) {
        if (!view.isActive) {
            return
        }
        Logger.w("Call failure. Reason: %s", failureReason)
        if (errorCode == HttpURLConnection.HTTP_CONFLICT && !message.isNullOrEmpty()) {
            view.showAfterHourMessage(message)
        } else {
            view.showErrorMessage()
            if (!TextUtils.isEmpty(failureReason)) {
                callServiceInteractor.signalEndCall(failureReason)
            }
        }
    }

    override fun onRegistered(inCallState: InCallState) {
        Logger.i("CallListener registered")
        this.inCallState = inCallState
    }

    override fun onQueued(call: Call) {
        startOrCancelDeflectionTimer(call)
        if (!isSelectedMenuFetched) {
            showConnectingMessage()
        }
        if (view.isActive) {
            view.setEmptyViewProgressBarVisibility(false)
            view.setCallScreenViewsVisibility(true)
            view.hideChronometer()
            view.setMute(inCallState?.isMute ?: false)
            view.setSpeakerOn(inCallState?.isSpeakerOn ?: false)
            view.refreshInCallViews()
            view.showDisconnectCallButton()
        }
        updateEscalationCoBrowseButtons(call)
    }

    private fun startOrCancelDeflectionTimer(call: Call) {
        checkCallEscalationStatus(call.id)
        if (call.isDeflectable) {
            var maxWaitTimeSeconds = call.announcementInterval
            if (maxWaitTimeSeconds <= 0) {
                maxWaitTimeSeconds = 120 // default time: 2 minutes
            }
            startDeflectionTimer(call.id, call.menuId, maxWaitTimeSeconds)
        } else {
            cancelDeflectionTimer()
        }
    }

    override fun onAssigned(call: Call) {
        cancelDeflectionTimer()
        if (view.isActive) {
            view.hideChronometer()
            view.closeCallDeflection()
            view.showDisconnectCallButton()
        }
        updateEscalationCoBrowseButtons(call)
    }

    override fun onConnecting(call: Call) {
        cancelDeflectionTimer()
        if (view.isActive) {
            view.hideChronometer()
            view.closeCallDeflection()
            view.showDisconnectCallButton()
        }
    }

    override fun onSwitching(call: Call) {
        if (view.isActive) {
            view.hideChronometer()
            view.closeCallDeflection()
            view.showDisconnectCallButton()
        }
    }

    override fun onConnected(call: Call, agent: Agent) {
        if (call.participants?.none { it.isAgent && it.status != "finished" } == true) {
            startOrCancelDeflectionTimer(call)
        } else {
            cancelDeflectionTimer()
        }
        showChronometer()
        updateAgentNamesInCall(call)
        if (view.isActive) {
            view.setTitleViewVisible(false)
            view.closeCallDeflection()
            view.showDisconnectCallButton()
        }
        updateEscalationCoBrowseButtons(call)
    }

    override fun onFinished(call: Call) {
        if (view.isActive) {
            view.stopChronometer()
            callServiceInteractor.notifyEventData()
        }
    }

    override fun onFailed(call: Call) {
        if (view.isActive) {
            view.stopChronometer()
        }
    }

    override fun onRecovered(call: Call) {
        if (view.isActive) {
            view.stopChronometer()
        }
    }

    override fun onVoicemailConnecting(voicemail: Call) {
        view.showDisconnectCallButton()
    }

    override fun onVoicemailRecording(voicemail: Call) {
        view.showDisconnectCallButton()
        view.showVoicemailRecording()
        showChronometer()
        view.refreshInCallViews()
    }

    override fun onVoicemailRecorded(voicemail: Call) {
        view.finish()
    }

    override fun onParticipantLeft(call: Call, userId: Int) {
        updateAgentNamesInCall(call)
        if (call.participants?.none { it.isAgent && it.status != "finished" } == true) {
            startOrCancelDeflectionTimer(call)
        }
    }

    override fun onTransferred(call: Call, agent: Agent) {
        updateAgentNamesInCall(call)
        cancelDeflectionTimer()
    }

    override fun onMute(isMute: Boolean) {
        view.setMute(isMute)
    }

    override fun onSpeaker(isSpeakerOn: Boolean) {
        view.setSpeakerOn(isSpeakerOn)
    }

    override fun onWarning() {
        if (view.isActive) {
            view.showErrorDialog()
        }
    }

    override fun onNetworkReconnected() {
        if (view.isActive) {
            view.hideErrorDialog()
        }
    }

    override fun onError(message: String?) {
        if (!message.isNullOrEmpty() && view.isActive) {
            view.showErrorMessage(message)
        }
    }

    override fun onCallClear() {
        cancelDeflectionTimer()
        showNextScreen()
    }

    override fun onVirtualAgentSettingsUpdated(virtualAgentSettings: VirtualAgentSettings?, call: Call) {
        this.virtualAgentSettings = virtualAgentSettings
        updateEscalateButton(call)
        view.refreshInCallViews()
    }

    override fun onEscalating(call: Call) {
        startOrCancelDeflectionTimer(call)
        view.showEscalatingMessage()
        view.setEscalateActionEnabled(false)
    }

    override fun onCallNotSupported(message: String) {
        if (view.isActive) {
            view.showErrorMessage(message)
            view.stopChronometer()
            cancelDeflectionTimer()
            view.finish()
        }
    }

    override fun getCoBrowseUI() = view.getCoBrowseUI()

    override fun getCoBrowseButtonView() = view.getCoBrowseButtonView()

    override fun getMainFragmentManager() = view.getMainFragmentManager()

    override fun displayPendingSmartAction() {
        view.displayPendingSmartAction(biometricsVerification)
    }

    override fun showPreSessionSmartActions(menuId: Int) {
        view.showPreSessionSmartActions(menuId)
    }

    // ⚠️ Added this method to address an edge case, be cautious when you're using for any future work ⚠️
    override fun closeCallScreen() {
        view.finish()
    }

    override fun showRecordingConfirmation(menuId: Int) {
        view.savePreferenceData(menuId, null)
        view.showRecordingConfirmation()
    }

    private fun updateEscalationCoBrowseButtons(call: Call) {
        updateEscalateButton(call)
        updateCoBrowseButton(call)
        view.refreshInCallViews()
    }

    private fun updateEscalateButton(call: Call) {
        view.setEscalateActionAvailable(virtualAgentSettings?.allowSkipVirtualAgent == true
                && (CallStatus.VaConnected == call.getStatus()
                || inCallState?.isEscalating == true)
        )
        view.setEscalateActionEnabled(inCallState?.isEscalating != true)
    }

    private fun updateCoBrowseButton(call: Call) {
        view.updateCoBrowseButton(call.supportsCobrowse && call.getStatus() == CallStatus.Connected)
    }

    override fun stop() {
        Injection.provideUjetEventNotificationManager().communicationUiStopped("call")
    }

    override fun verify() {
        biometricsVerification.start()
    }

    override fun onEscalateClicked() {
        callServiceInteractor.escalateCall()
    }

    override fun onConfirmationDialogClicked(isPermissionGranted: Boolean) {
        if (view.isActive) {
            val recordingPermission: String = if (isPermissionGranted) {
                RecordingPermissionUtils.RECORDING_PERMISSION_GRANTED
            } else {
                RecordingPermissionUtils.RECORDING_PERMISSION_DENIED
            }
            view.saveRecordingPermissionStatus(recordingPermission)
            view.resumeCommunication()
        }
    }

    private fun startDeflectionTimer(callId: Int, menuId: Int, maxWaitTimeSeconds: Int) {
        if (deflectionTimer != null || inCallState?.isDeflectionShown == true) {
            return
        }
        deflectionTimer = Timer()
        deflectionTimer?.schedule(object : TimerTask() {
            override fun run() {
                MainLooper.post { showEnabledDeflections(callId, menuId) }
                inCallState?.isDeflectionShown = true
                cancelDeflectionTimer()
            }
        }, (maxWaitTimeSeconds * 1000).toLong(), 1000)
    }

    @Synchronized
    override fun cancelDeflectionTimer() {
        deflectionTimer?.cancel()
        deflectionTimer = null
    }

    private fun showEnabledDeflections(callId: Int, menuId: Int) {
        apiManager.getCallDeflection(callId, object : ApiCallback<CallDeflection> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<CallDeflection>) {
                if (response.code() != HttpURLConnection.HTTP_OK || !view.isActive) return
                val callDeflection = response.body()
                if (callDeflection == null || !callDeflection.hasEnabledChannel()) {
                    Logger.d("No deflections for long waiting call")
                    return
                }
                if (view.isActive) {
                    view.showCallDeflection(menuId, callDeflection)
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                Logger.w(throwable, "Failed to get the call deflection")
            }
        })
    }

    private fun showChronometer() {
        if (view.isActive) {
            var startedAt = inCallState?.connectedTime ?: 0
            startedAt = min(startedAt, SystemClock.elapsedRealtime())
            view.showChronometer(startedAt)
        }
    }

    private fun isTransferring(): Boolean = inCallState?.isTransferring ?: false

    private fun updateAgentNamesInCall(call: Call? = null) {
        val joinedAgents = inCallState?.joinedAgents ?: ArrayList()
        if (joinedAgents.size == 0 && call?.virtualAgent == null) {
            view.showTransferringMessage()
        } else {
            val agents: MutableList<Pair<String, String>> = ArrayList()
            for (agent in joinedAgents) {
                if (agent != null) {
                    agents.add(Pair(agent.displayName, agent.avatarUrl))
                }
            }
            call?.virtualAgent?.let {
                agents.add(Pair(it.displayName, it.avatarUrl))
            }

            val mainAgentName = call?.agent?.displayName ?: call?.virtualAgent?.displayName ?: agents.getOrNull(0)?.first
            val mainAgentAvatar = call?.agent?.avatarUrl ?: call?.virtualAgent?.avatarUrl ?: agents.getOrNull(0)?.second

            when (agents.size) {
                0 -> view.showTransferringMessage()
                1 -> {
                    mainAgentName?.let { view.showAgentName(it) }
                    mainAgentAvatar?.let { view.setAgentAvatar(it) }
                }
                2 ->  {
                    view.showAgentName(agents[0].first + " & " + agents[1].first)
                    mainAgentAvatar?.let { view.setAgentAvatar(it) }
                }
                else -> mainAgentName?.let { view.showAgentName(it) }
            }
            view.refreshInCallViews()
        }
    }

    private fun showNextScreen() {
        if (view.isActive.not()) {
            return
        }

        when {
            localRepository.rateRepository.isRatable && pendingDeflectionType == null -> {
                when {
                    //Show survey / CSAT if enabled, otherwise finish flow
                    localRepository.rateRepository.rateTarget?.surveyEnabled == true -> {
                        //clear old csat rating config
                        localRepository.rateRepository.isRatable = false
                        localRepository.rateRepository.isSurveyUnanswered = true
                        view.showSurveyScreen()
                    }
                    localRepository.rateRepository.csatEnabled() -> view.showCsatRating()
                    !view.isAfterHourMessageDialogPending() -> view.finish()

                }
            }
            pendingDeflectionType == SCHEDULED_CALL -> {
                view.showScheduleTimePicker()
                pendingDeflectionType = null
            }
            pendingDeflectionType == VOICEMAIL -> {
                view.showVoicemail(pendingDeflectionMenuId, pendingDeflectionData ?: return)
                pendingDeflectionType = null
            }
            pendingDeflectionType == PHONE -> view.showPhoneNumberInput(InAppIvrCall, pendingDeflectionData ?: return)
            pendingDeflectionType == EMAIL -> {
                showEmail()
                pendingDeflectionType = null
            }
            !view.isAfterHourMessageDialogPending() -> view.finish()
        }
    }

    private fun checkCallEscalationStatus(callId: Int) {
        apiManager.getCallEscalationStatus(callId, object : ApiCallback<Array<Escalation>> {
            override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<Array<Escalation>>) {
                if (response.body()?.lastOrNull()?.status == "deflected") {
                    cancelDeflectionTimer()
                }
            }

            override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                Logger.w(throwable, "Failed to get call escalation status for call %d", callId)
            }
        })
    }
}
