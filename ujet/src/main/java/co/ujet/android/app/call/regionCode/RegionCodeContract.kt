package co.ujet.android.app.call.regionCode

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

interface RegionCodeContract {
    interface View : BaseView {
        fun updateCountryList(countries: List<Country?>)
        fun setResult(regionCode: String?)
        fun close()
    }

    interface Presenter : BasePresenter {
        fun onCountryClicked(country: Country)
        fun onSearchKeywordChanged(searchKeyword: String?)
    }
}
