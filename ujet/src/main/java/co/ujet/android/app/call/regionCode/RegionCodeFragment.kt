package co.ujet.android.app.call.regionCode

import android.app.Activity
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.AbsListView
import android.widget.EditText
import android.widget.ListView
import androidx.annotation.Keep
import androidx.appcompat.widget.Toolbar
import androidx.core.os.bundleOf
import androidx.core.view.updateLayoutParams
import co.ujet.android.R
import co.ujet.android.app.call.regionCode.RegionCodeContract.Presenter
import co.ujet.android.app.call.regionCode.RegionCodeContract.View
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.android.internal.Injection
import co.ujet.android.libs.materialcamera.util.Degrees.isLandscape
import co.ujet.android.ui.style.UjetViewStyler

class RegionCodeFragment @Keep constructor() : BaseFragment(), View {
    private var presenter: Presenter? = null
    private var countryListView: ListView? = null
    private var searchKeywordEditText: EditText? = null
    private var requestKey: String? = null
    private var requestCode: Int = Integer.MIN_VALUE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.run {
            requestKey = getString(BaseDialogFragment.ARGS_REQUEST_KEY, null)
            requestCode = getInt(BaseDialogFragment.ARGS_REQUEST_CODE, Integer.MIN_VALUE)
        }
        presenter = RegionCodePresenter(Injection.provideLocalRepository(context ?: return), this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): android.view.View? {
        val activity = activity ?: return null
        val view = inflater.inflate(R.layout.ujet_fragment_region_code, container, false)
        UjetViewStyler.styleFragmentBackground(ujetStyle(), view)
        countryListView = view.findViewById<ListView>(R.id.country_list_view)?.apply {
            adapter = CountryListAdapter(activity, ujetStyle(), ArrayList()) { position ->
                this.dividerHeight = if (position < 2) {
                    1
                } else {
                    2
                }
            }
            setOnItemClickListener { parent, _, position, _ ->
                val country = parent.adapter.getItem(position) as Country
                presenter?.onCountryClicked(country)
            }
        }
        searchKeywordEditText = view.findViewById<EditText>(R.id.country_search)?.apply {
            UjetViewStyler.stylePrimaryEditText(ujetStyle(), this)
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable) {
                    presenter?.onSearchKeywordChanged(s.toString())
                }
            })
        }
        if (isLandscape(context)) {
            countryListView?.updateLayoutParams {
                height = resources.displayMetrics.heightPixels
            }
        }

        registerNavigationBarMenuProvider(R.menu.ujet_menu_exit, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.ujet_menu_item_exit -> {
                    activity.finish()
                    true
                }

                android.R.id.home -> {
                    parentFragmentManager.popBackStack()
                    true
                }

                else -> false
            }
        })
        return view
    }

    override fun onResume() {
        super.onResume()
        presenter?.start()
        countryListView?.postDelayed({
            countryListView?.getChildAt(0)?.isFocusable = true
            countryListView?.getChildAt(0)?.requestFocus()
        }, 200)
        handleKeyboardAccessibility()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        countryListView = null
        searchKeywordEditText = null
    }

    private fun handleKeyboardAccessibility() {
        view?.post {
            val toolbar = activity?.findViewById<Toolbar>(R.id.toolbar) ?: return@post
            val menu = getMenuItemView(toolbar, R.id.ujet_menu_item_exit)
            AccessibilityUtil.setupKeyboardAccessibility(menu, onTabOrDpadDown = {
                searchKeywordEditText?.requestFocus()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(navigateUpView, onTabOrDpadDown = {
                searchKeywordEditText?.requestFocus()
                true
            })
            AccessibilityUtil.setupKeyboardAccessibility(searchKeywordEditText, onTabOrDpadDown = {
                countryListView?.getChildAt(0)?.requestFocus()
                true
            })
            countryListView?.let { setupListViewKeyNavigation(it) }
        }
    }

    private fun setupListViewKeyNavigation(countryListView: ListView) {
        // Run initially, and whenever the ListView's visible children change (e.g., after scrolling)
        setKeyListenerOnVisibleItems(countryListView)
        countryListView.setOnScrollListener(object : AbsListView.OnScrollListener {
            override fun onScroll(
                view: AbsListView?, firstVisibleItem: Int, visibleItemCount: Int, totalItemCount: Int
            ) {
                setKeyListenerOnVisibleItems(countryListView)
            }

            override fun onScrollStateChanged(view: AbsListView?, scrollState: Int) {}
        })
    }

    // Set up a key listener for every visible child
    fun setKeyListenerOnVisibleItems(countryListView: ListView) {
        val adapterCount = countryListView.adapter?.count ?: 0
        val first = countryListView.firstVisiblePosition
        val last = countryListView.lastVisiblePosition
        for (i in first..last) {
            countryListView.getChildAt(i - first)?.let { child ->
                val country = countryListView.adapter.getItem(i) as Country
                AccessibilityUtil.setupKeyboardAccessibility(child, onTabOrDpadDown = {
                    handleNavigation(countryListView, i, adapterCount, next = true)
                    true
                }, onDpadUp = {
                    handleNavigation(countryListView, i, adapterCount, next = false)
                    true
                }, onEnter = {
                    presenter?.onCountryClicked(country)
                    true
                })
                child.setOnClickListener {
                    presenter?.onCountryClicked(country)
                }
            }
        }
    }

    private fun handleNavigation(
        listView: ListView, pos: Int, count: Int, next: Boolean
    ) {
        val newPos = if (next) pos + 1 else pos - 1
        when (newPos) {
            -1 -> {
                searchKeywordEditText?.requestFocus()
            }

            in 0 until count -> {
                if (shouldScroll(listView, newPos, next)) {
                    scrollAndFocus(listView, newPos)
                } else {
                    focusVisibleItem(listView, newPos)
                }
            }
        }
    }

    private fun shouldScroll(listView: ListView, newPos: Int, next: Boolean): Boolean {
        val first = listView.firstVisiblePosition
        val last = listView.lastVisiblePosition
        return if (next) newPos >= last - 1 else newPos <= first + 1
    }

    private fun scrollAndFocus(listView: ListView, newPos: Int) {
        listView.smoothScrollToPosition(newPos)
        listView.postDelayed({
            focusVisibleItem(listView, newPos)
        }, 200)
    }

    private fun focusVisibleItem(listView: ListView, newPos: Int) {
        val first = listView.firstVisiblePosition
        val last = listView.lastVisiblePosition
        val index = newPos - first
        if (index in 0..(last - first)) {
            listView.getChildAt(index)?.apply {
                isFocusable = true
                requestFocus()
            }
        }
    }

    override fun isActive() = isAdded

    override fun updateCountryList(countries: List<Country?>) {
        val countryListAdapter = countryListView?.adapter as CountryListAdapter
        countryListView?.adapter = null
        countryListAdapter.clear()
        countryListAdapter.addAll(countries)
        countryListView?.adapter = countryListAdapter
    }

    override fun setResult(regionCode: String?) {
        if (!isActive) {
            return
        }
        val bundle = bundleOf(
            EXTRAS_REGION_CODE to regionCode,
            REQUEST_CODE to requestCode,
            RESULT_CODE to Activity.RESULT_OK
        )
        requestKey?.let { key ->
            parentFragmentManager.setFragmentResult(key, bundle)
        }
    }

    override fun close() {
        AccessibilityUtil.isCountryCodeClicked = true
        parentFragmentManager.popBackStack()
    }

    companion object {
        const val TAG = "RegionCodeFragment"
        const val EXTRAS_REGION_CODE = "region_code"

        fun newInstance(requestKey: String, requestCode: Int): RegionCodeFragment {
            val fragment = RegionCodeFragment()
            fragment.arguments = bundleOf(
                ARGS_REQUEST_KEY to requestKey,
                ARGS_REQUEST_CODE to requestCode
            )
            return fragment
        }
    }
}
