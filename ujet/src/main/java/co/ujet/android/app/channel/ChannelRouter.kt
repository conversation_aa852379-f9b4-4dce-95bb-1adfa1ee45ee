package co.ujet.android.app.channel

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.core.text.HtmlCompat
import androidx.fragment.app.Fragment
import co.ujet.android.R
import co.ujet.android.UjetErrorCode
import co.ujet.android.activity.incomingcall.UjetScheduleTimePickerActivity
import co.ujet.android.api.ApiManager
import co.ujet.android.api.lib.ApiCallback
import co.ujet.android.api.lib.ApiResponse
import co.ujet.android.api.lib.HttpRequest
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.incall.InCallFragment
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerFragment
import co.ujet.android.app.chat.ChatFragment
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.app.confirmation.ConfirmationDialogFragment
import co.ujet.android.app.loadingstate.ChannelType
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.executeImmediate
import co.ujet.android.clean.domain.language.usecase.ChooseLanguage
import co.ujet.android.clean.domain.menu.filter.MenuFilter
import co.ujet.android.clean.domain.menu.usecase.GetMenuPath
import co.ujet.android.clean.domain.menu.usecase.GetMenus
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenu
import co.ujet.android.clean.entity.company.Company
import co.ujet.android.clean.entity.menu.Menu
import co.ujet.android.clean.entity.menu.MenuContactOption
import co.ujet.android.clean.entity.menu.channel.*
import co.ujet.android.clean.presentation.deflectedevent.DeflectedEventManager
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.clean.presentation.message.MessageFragment
import co.ujet.android.clean.presentation.psa.PsaFragment
import co.ujet.android.common.util.*
import co.ujet.android.commons.domain.ExternalDeflectionLink
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.constant.CallCreateType
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.data.constant.CommunicationType
import co.ujet.android.data.model.WaitTimes
import co.ujet.android.data.repository.UploadRepository
import co.ujet.android.internal.Configuration
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger
import co.ujet.android.receiver.NetworkConnectionReceiver
import co.ujet.android.service.UjetCallService
import co.ujet.android.service.UjetChatService
import co.ujet.android.smartaction.manager.SmartActionManager
import co.ujet.android.smartaction.routers.PsaRouter
import java.io.File
import java.net.HttpURLConnection
import java.util.concurrent.TimeUnit

//Responsible for handling channel selections and routing manual redirection
class ChannelRouter(
    private val fragment: Fragment?,
    private val activity: Activity?,
    private val configuration: Configuration,
    private val ujetContext: UjetContext,
    private val localRepository: LocalRepository,
    private val uploadRepository: UploadRepository,
    private val getCompany: GetCompany,
    private val apiManager: ApiManager,
    private val useCaseHandler: UseCaseHandler,
    private val getMenus: GetMenus,
    private val getMenuPath: GetMenuPath,
    private val getSelectedMenu: GetSelectedMenu,
    private val chooseLanguage: ChooseLanguage,
    private val deflectedEventManager: DeflectedEventManager
) {
    private var delegate: Delegate? = null
    private var selectedMenu: Menu? = null
    private var selectedChannel: Channel? = null
    private var cachedCompany: Company? = null
    private val networkConnectionReceiver = NetworkConnectionReceiver()
    private var isSingleMenu = false
    private var isDAPEnabled = false
    private var isSingleChannelAndLinkEnabled = false
    private var isPsaEnabled = false
    private var isEmailEnhancementEnabled = false
    private var isAskToRecordEnabled = false
    private var isExternalDeflectionLinkClicked = false
    private var isTimerFinished = false
    private var isVoicemailByTemporaryRedirection = false
    private var emailIntentSent : Boolean = false

    private val handler = Handler(Looper.getMainLooper())

    private lateinit var permissionResultLauncher : ActivityResultLauncher<Array<String>>

    interface Delegate {
        fun startPresenter()
        fun handleActivityForResult(requestKey: String, result: Bundle)
    }

    fun initialize(delegate: Delegate) {
        this.delegate = delegate
        registerActivityForResult()
        refreshIsEmailEnhancementEnabled() //To fetch latest isEmailEnhancementEnabled for showing channels after PSA screen
        refreshIsAskToRecordEnabled() //To fetch latest isAskToRecordEnabled for showing channels after PSA screen
    }

    fun handleOnResume() {
        when {
            fragment is PsaFragment -> {
                if (emailIntentSent) {
                    clearAndFinish()
                } else {
                    delegate?.startPresenter()
                }
            }
            //Show warning dialog when user back to app within 5 minutes from external deflection link url page
            isExternalDeflectionLinkClicked -> {
                handler.removeCallbacksAndMessages(null)
                if (isTimerFinished) {
                    finish()
                } else {
                    showIssueResolvedConfirmationDialog()
                }
            }
            else -> {
                delegate?.startPresenter()
            }
        }
        registerNetworkCallback()
    }

    fun handleOnPause() {
        unregisterNetworkCallback()
    }

    fun clearResources() {
        handler.removeCallbacksAndMessages(null)
    }

    //handle channel selections
    fun select(menu: Menu, channel: Channel, cachedCompany: Company?) {
        selectedMenu = menu
        selectedChannel = channel
        this.cachedCompany = cachedCompany
        this.isEmailEnhancementEnabled = cachedCompany?.isEmailEnhancementEnabled ?: false
        setAskToRecordEnabled(menu.recordingOption)
        //We need to wait presenter fetches root menu details from server and then access cache
        getCachedRootMenus()

        menu.sdkMenuSetting.preSessionSmartAction?.let { psaSetting ->
            SmartActionManager.updateSmartActionsInfo(psaSetting)
            SmartActionManager.setEnabledSmartActions(psaSetting)
            SmartActionManager.setSkipEnabled(psaSetting.isSkipEnabled())
            isPsaEnabled = psaSetting.isEnabled(menu, channel)
        }

        when (channel) {
            is EmailChannel -> onEmailSelected(menu, channel)
            is InstantCallChannel -> onInstantCallSelected(menu)
            is PstnCallChannel -> onPstnCallSelected(channel)
            is ScheduledCallChannel -> onScheduledCallSelected(channel)
            is ChatChannel -> onChatSelected(menu)
            is VoicemailChannel -> onVoicemailSelected(menu, channel)
            is ExternalDeflectionLinks -> {
                val (list) = channel
                list?.filter { it.enabled }?.forEach {
                    onExternalDeflectionLinksSelected(
                        it, isSingleChannelEnabled && list.size == 1
                    )
                }
            }
        }
    }

    fun selectExternalDeflectionLink(externalDeflectionLink: ExternalDeflectionLink,
                                     isSingleChannelAndLinkEnabled: Boolean) {
        //We need to wait presenter fetches root menu details from server and then access cache
        getCachedRootMenus()
        onExternalDeflectionLinksSelected(externalDeflectionLink, isSingleChannelAndLinkEnabled)
    }

    fun showInChat(menuId: Int) {
        val activity = getActiveActivity() ?: return
        val context = activity.applicationContext
        // Start chat with menu id for DAP with preferred channel case
        if (ServiceUtil.isServiceRunning(context, UjetChatService::class.java)) {
            fragment?.parentFragmentManager?.popBackStack() // close PSA screen
            val chatFragment = fragment?.parentFragmentManager?.findFragmentByTag(ChatFragment.TAG)
            if (chatFragment != null && chatFragment is ChatFragment) {
                chatFragment.setEmptyViewProgressBarVisibility(true)
                chatFragment.resumeCommunication(menuId)
            } else {
                Toast.makeText(activity, R.string.ujet_error_chat_connect_fail_android, Toast.LENGTH_LONG).show()
                activity.finish()
            }
        } else {
            activity.finish()
            UjetChatService.startChat(activity, menuId)
        }
    }

    fun showInCall(menuId: Int, voicemailReason: String?) {
        if (voicemailReason == null) {
            setExpectedCallWaitTime(localRepository, apiManager, menuId)
            if (isAskToRecordEnabled) {
                savePreferenceData(menuId, null)
                showRecordingConfirmation()
            } else {
                saveRecordingPermissionStatus(RecordingPermissionUtils.RECORDING_PERMISSION_NOT_ASKED)
                startInstantCall(menuId, null)
            }
        } else {
            savePreferenceData(menuId, voicemailReason)
            startInstantCall(menuId, voicemailReason)
        }
    }

    fun showEmailForm(email: String, deflectionType: String?) {
        sendEmailEvent(email, deflectionType)
        FragmentHelper.show(
            fragment as BaseFragment,
            EmailFragment.newInstance(email, deflectionType, false),
            EmailFragment.TAG)
    }

    fun showEmailClient(email: String, subject: String?, deflectionType: String?) {
        val activity = getActiveActivity() ?: return

        sendEmailEvent(email, deflectionType)
        startEmailActivity(activity, email, activity.getString(R.string.ujet_email_subject, String.format("[%s]", subject)))
    }

    fun showEmailClient(authority: String) {
        //When user moves away from psa screen to email client, language is reset to English so updating it here
        // so that email client message body text translated accordingly
        getPreferredLanguage()
        val activity = getActiveActivity() ?: return
        val media = SmartActionManager.getAttachedPsaFiles()
        val uriList = arrayListOf<Uri>()
        media.forEach {
            it.file?.let { file ->
                uriList.add(getUriForFile(activity.applicationContext, authority, file))
                Logger.d("File ${file.name} exists: ${file.exists()}  can read: ${file.canRead()}")
            }
        }
        createEmailIntent(uriList)
    }

    //Route manual redirection
    fun routeManualRedirection(selectedMenu: Menu, redirection: MenuContactOption) {
        if (!isActive()) {
            return
        }
        this.selectedMenu = selectedMenu

        if (redirection.isURL) {
            sendEventData("url", redirection.data)
            startBrowser(redirection.data)
        } else if (redirection.isPhone) {
            /**
             * Disable outgoing call receiver prevent dial number is hooked.
             * [OutgoingCallReceiver]
             */
            if (redirection.data.isNullOrEmpty()) {
                showPhoneNumberInput(CallCreateType.InAppIvrCall, configuration.fallbackPhoneNumber, "manual_redirection")
            } else {
                showPhoneNumberInput(CallCreateType.InAppIvrCall, redirection.data, "manual_redirection")
            }
        } else if (redirection.isMessage) {
            sendEventData("message", null)
            showMessage(null, redirection.data)
        } else if (redirection.isVoicemail) {
            isVoicemailByTemporaryRedirection = true
            if (!checkCallPermissionsGranted()) {
                requestCallPermissions()
            } else {
                showVoicemailByTemporaryRedirection(selectedMenu.id)
            }
        }
    }

    //Following are used in PSA
    fun routeToChannelAfterPsa() {
        val activity = getActiveActivity() ?: return
        val authority = "${activity.packageName}.ujet.fileprovider"
        PsaRouter.routeToChannelAfterPsa(this, activity.applicationContext, isEmailEnhancementEnabled, authority)
    }

    private fun createEmailIntent(content: ArrayList<Uri>) {
        val activity = getActiveActivity() ?: return
        var intent = buildAttachmentIntent(content, true)

        if (canHandleIntent(intent ?: return)) {
            activity.startActivity(Intent.createChooser(intent, activity.getString(R.string.ujet_email_send_to)))
            emailIntentSent = true
        } else {
            //create intent without selector and type set
            intent = buildAttachmentIntent(content, false)
            if (intent?.resolveActivity(activity.packageManager) != null) {
                activity.startActivity(intent)
            } else {
                //no email client supporting attachments found so fallback to legacy way
                val email = PsaUtil.getEmail(activity.applicationContext) ?: return
                val subject = PsaUtil.getEmailTitle(activity.applicationContext)
                startEmailActivity(activity, email, activity.getString(R.string.ujet_email_subject, String.format("[%s]", subject)))
            }
        }
    }
    
    private fun buildAttachmentIntent(content: ArrayList<Uri>, withSelector: Boolean): Intent? {
        val activity = getActiveActivity() ?: return null
        val context = activity.applicationContext
        val email = PsaUtil.getEmail(context)
        val emailTitle = PsaUtil.getEmailTitle(context)
        val deflectionType = PsaUtil.getDeflectionType(context)
        sendEmailEvent(email ?: return null, deflectionType)

        val selectorIntent = Intent(Intent.ACTION_SENDTO)
        selectorIntent.data = Uri.parse("mailto:")

        return Intent(Intent.ACTION_SEND_MULTIPLE).apply {
            putExtra(Intent.EXTRA_EMAIL, arrayOf(email))

            //Use activity reference instead of application context to use current locale for message body text
            val messageBody = getMessageBody(activity)
            if (messageBody.isNotEmpty()) {
                putExtra(
                    Intent.EXTRA_TEXT, getHtmlMessageBody(
                        messageBody.plus(
                            getDefaultHtmlMessage(activity)
                        )
                    )
                )
                putExtra(Intent.EXTRA_HTML_TEXT, messageBody.plus(getDefaultHtmlMessage(activity)))
            }
            if (emailTitle?.isNotEmpty() == true) {
                putExtra(
                    Intent.EXTRA_SUBJECT,
                    activity.getString(R.string.ujet_email_subject, "[$emailTitle]")
                )
            }

            if (withSelector) {
                selector = selectorIntent
            } else {
                // gmail will only match with type set and without selector from Android 13
                type = "*/*"
            }

            if (content.isNotEmpty()) {
                putExtra(Intent.EXTRA_STREAM, content)
            }
        }
    }

    private fun getUriForFile(context: Context, authority: String, file: File): Uri {
        return try {
            FileProvider.getUriForFile(context, authority, file)
        } catch (e: Exception) {
            Logger.w("Couldn't get Uri for file ${file.name}: ${e.printStackTrace()}")
            Uri.EMPTY
        }
    }

    private fun refreshIsAskToRecordEnabled() {
        useCaseHandler.executeImmediate(
            getSelectedMenu,
            GetSelectedMenu.RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<GetSelectedMenu.ResponseValue> {
                override fun onSuccess(response: GetSelectedMenu.ResponseValue) {
                    val recordingOption: String? = response.menu?.recordingOption

                    isAskToRecordEnabled = if (recordingOption.isNullOrEmpty()) {
                        false
                    } else {
                        RecordingPermissionUtils.RECORD_ASK_USER == recordingOption
                    }
                }

                override fun onError() {
                    isAskToRecordEnabled = false
                }
            }
        )
    }

    private fun refreshIsEmailEnhancementEnabled() {
        useCaseHandler.executeImmediate(
            getCompany,
            GetCompany.RequestValues(false, true),
            object : UseCaseCallback<GetCompany.ResponseValue?> {
                override fun onSuccess(response: GetCompany.ResponseValue?) {
                    response?.company?.let {
                        isEmailEnhancementEnabled = it.isEmailEnhancementEnabled
                    }
                }

                override fun onError() {
                    Logger.w("Couldn't get cached company data")
                }
            }
        )
    }

    private fun showVoicemailByTemporaryRedirection(menuId: Int) {
        savePreferenceData(menuId, "temporary_redirection")
        showInCall(menuId, "temporary_redirection")
    }

    private fun sendEventData(deflectionMode: String, toUrl: String?) {
        deflectedEventManager.sendDeflectedEvent(deflectionMode, toUrl)
    }

    private fun sendEmailEvent(toEmail: String, deflectionType: String?) {
        val activity = getActiveActivity() ?: return

        if (deflectionType?.isNotEmpty() == true) {
            Injection.provideDeflectedEventManager(activity).sendEmailDeflectedEvent("deflected",
                toEmail, deflectionType, "email")
        }
        Injection.provideDeflectedEventManager(activity).sendEmailSelectedEvent(toEmail, deflectionType)
    }

    private fun startBrowser(url: String) {
        val activity = getActiveActivity() ?: return
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        activity.startActivity(browserIntent)
        finish()
    }

    private fun showMessage(title: String?, message: String?) {
        FragmentHelper.showAsTop(
            fragment as BaseFragment,
            MessageFragment.newInstance(title, message),
            MessageFragment.TAG)
    }

    private fun showPhoneNumberInput(callCreateType: CallCreateType, phoneNumber: String?, deflectionType: String?) {
        sendEvent(deflectionType, "phone", phoneNumber)
        FragmentHelper.showAsTop(
            fragment as BaseFragment,
            PhoneNumberInputFragment.newInstance(callCreateType, phoneNumber, false, deflectionType),
            PhoneNumberInputFragment.TAG)
    }

    private fun registerActivityForResult() {
        val fragmentTag = fragment?.tag ?: return
        fragment.parentFragmentManager.setFragmentResultListener(fragmentTag, fragment) { requestKey, result ->
            if (fragmentTag == requestKey) {
                val requestCode = result.getInt(BaseFragment.REQUEST_CODE)
                val resultCode = result.getInt(BaseFragment.RESULT_CODE)
                when (requestCode) {
                    REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE -> {
                        when (resultCode) {
                            Activity.RESULT_OK -> {
                                when {
                                    result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false) -> onConfirmationDialogClicked(true)
                                    result.getBoolean(ConfirmationDialogFragment.EXTRAS_SECOND_BUTTON_CLICKED, false) -> onConfirmationDialogClicked(false)
                                }
                            }
                            Activity.RESULT_CANCELED -> onConfirmationDialogCanceled()
                        }
                    }
                    REQUEST_CODE_WARNING_DIALOG_RESPONSE -> {
                        when (resultCode) {
                            Activity.RESULT_OK -> {
                                when {
                                    result.getBoolean(ConfirmationDialogFragment.EXTRAS_FIRST_BUTTON_CLICKED, false) -> finish()
                                    result.getBoolean(ConfirmationDialogFragment.EXTRAS_SECOND_BUTTON_CLICKED, false) -> {
                                        //We should exit the SDK after end user selected No from the alert dialog if DAP has single channel enabled and only one link is enabled for external deflection link.
                                        //Otherwise, user might stuck in infinite loop as we auto select single channel and it does not affect if user navigated through non-DAP menus
                                        when {
                                            isDAPWithSingleChannelAndLinkEnabled() -> finish()
                                            else -> delegate?.startPresenter()
                                        }
                                    }
                                }
                            }
                            Activity.RESULT_CANCELED -> delegate?.startPresenter()
                        }
                    }
                    /* Here we need to pass fragment results (requestKey, result) to the caller of this class so that
                     * it can handle activity results of its own. For example, PsaFragment shows exit confirmation dialog response
                     * to alert user that psa results will be discarded when user exits the screen so we pass results to PsaFragment to handle
                     * exit confirmation dialog response accordingly. We need to do the same for menu and channel fragment and other callers of this class.
                    */
                    else -> {
                        delegate?.handleActivityForResult(requestKey, result)
                    }
                }
            }
        }

        permissionResultLauncher = fragment.registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) {
                onRequestPermissionsResult()
        }
    }

    private fun onCallPermissionsGranted() {
        if (isVoicemailByTemporaryRedirection) {
            showVoicemailByTemporaryRedirection(selectedMenu?.id ?: return)
            return
        }
        val menuCopy = selectedMenu
        val channelCopy = selectedChannel
        if (menuCopy != null && channelCopy != null) {
            select(menuCopy, channelCopy, cachedCompany)
        } else {
            Logger.w("onCallPermissionsGranted() called while selectedMenu=$menuCopy, selectedChannel=$channelCopy")
        }
    }

    private fun registerNetworkCallback() {
        activity?.let {
            networkConnectionReceiver.register(it)
        }
    }

    private fun unregisterNetworkCallback() {
        activity?.let {
            networkConnectionReceiver.unregister(it)
        }
    }

    private fun showIssueResolvedConfirmationDialog() {
        //When user moves away from web browser to app, we show this confirmation dialog and we need to
        // use activity reference directly so that text translated accordingly
        val activity = getActiveActivity() ?: return
        ConfirmationDialogFragment.newInstance(
            fragment?.tag ?: return,
            REQUEST_CODE_WARNING_DIALOG_RESPONSE,
            activity.getString(R.string.ujet_external_deflection_link_message),
            null,
            activity.getString(R.string.ujet_common_yes),
            activity.getString(R.string.ujet_common_no)
        ).show(fragment.parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    private fun onRequestPermissionsResult() {
        val context = getActiveActivity()?.applicationContext ?: return
        Logger.d("permission result")
        if (PermissionUtil.isPermissionsForCallGranted(context)) {
            onCallPermissionsGranted()
        } else {
            onPermissionNotGranted()
        }
    }

    private fun setAskToRecordEnabled(recordingOption: String?) {
        isAskToRecordEnabled = if (recordingOption.isNullOrEmpty()) {
            false
        } else {
            RecordingPermissionUtils.RECORD_ASK_USER == recordingOption
        }
    }

    private fun onEmailSelected(menu: Menu, emailChannel: EmailChannel) {
        val email = emailChannel.email
        val deflectionType = emailChannel.getDeflectionType()?.toString()

        if (email.isNullOrEmpty()) {
            finish()
            return
        }

        if (isEmailEnhancementEnabled) {
            showEmail(email, null, deflectionType)
        } else {
            if (isEmailClientAvailable()) {
                useCaseHandler.executeImmediate(
                    getMenuPath,
                    GetMenuPath.RequestValues(menu.id, ujetContext.directAccessKey),
                    onSuccess = { showEmail(email, it.path, deflectionType) },
                    onError = { showEmail(email, menu.name, deflectionType) }
                )
            } else {
                showEmailClientUnavailable()
            }
        }
    }

    private fun showEmail(email: String, title: String?, deflectionType: String?) {
        //We need to set channel type for email to save PSA results
        SmartActionManager.setChannelType(ChannelType.ChannelEmail)
        PsaRouter.checkEmail(this, fragment as BaseFragment, isPsaEnabled, isEmailEnhancementEnabled, email, title, deflectionType)
    }

    private fun onInstantCallSelected(menu: Menu) {
        if (checkCallPermissionsGranted()) {
            //We need to set comm type to call to save PSA results
            SmartActionManager.communicationType = CommunicationType.IncomingCall
            PsaRouter.checkCommunication(this, fragment as BaseFragment, isPsaEnabled,
                ChannelType.ChannelCall, menu, null)
        } else {
            requestCallPermissions()
        }
    }

    private fun onChatSelected(menu: Menu) {
        //We need to set comm type to chat to save PSA results
        SmartActionManager.communicationType = CommunicationType.Chat
        PsaRouter.checkCommunication(this, fragment as BaseFragment, isPsaEnabled,
            ChannelType.ChannelChat, menu, null)
    }

    private fun onPstnCallSelected(pstnCallChannel: PstnCallChannel) {
        Logger.i("PSTN call selected")
        if (!pstnCallChannel.phoneNumber.isNullOrEmpty()) {
            showPhoneCall(pstnCallChannel.phoneNumber, pstnCallChannel.deflectionReason)
        } else {
            val phoneNumber = cachedCompany?.phoneNumber ?: configuration.fallbackPhoneNumber
            showPhoneCall(phoneNumber, pstnCallChannel.deflectionReason)
        }
    }

    private fun onScheduledCallSelected(channel: ScheduledCallChannel) {
        if (checkCallPermissionsGranted()) {
            showScheduleTime(isSingleMenu && isSingleChannelEnabled, channel.deflectionType)
        } else {
            requestCallPermissions()
        }
    }

    private val isSingleChannelEnabled: Boolean
        get() = selectedMenu?.channels?.getEnabledChannels()?.size == 1 && !configuration.isShowSingleChannelEnabled

    private fun onVoicemailSelected(menu: Menu, channel: VoicemailChannel) {
        if (checkCallPermissionsGranted()) {
            showInCall(menu.id, channel.voicemailReason)
        } else {
            requestCallPermissions()
        }
    }

    private fun onExternalDeflectionLinksSelected(externalDeflectionLink: ExternalDeflectionLink,
                                                  isSingleChannelAndLinkEnabled: Boolean) {
        val url = externalDeflectionLink.url
        if (!url.isNullOrEmpty()) {
            Logger.d("start external deflection links")
            this.isSingleChannelAndLinkEnabled = isSingleChannelAndLinkEnabled
            showExternalDeflectionLinks(url, externalDeflectionLink.deflectionType)
        }
    }

    private fun showEmailClientUnavailable() {
        val activity = getActiveActivity() ?: return
        Toast.makeText(activity, R.string.ujet_error_no_email_client, Toast.LENGTH_LONG).show()
    }

    private fun showPhoneCall(phoneNumber: String?, deflectionType: String?) {
        sendEvent(deflectionType, "phone", phoneNumber)
        val isPreventDirectPstnCallEnabled = cachedCompany?.isPreventDirectPstnCallEnabled ?: false
        FragmentHelper.show(
            fragment as BaseFragment,
            PhoneNumberInputFragment.newInstance(CallCreateType.InAppIvrCall, phoneNumber, isPreventDirectPstnCallEnabled, deflectionType),
            PhoneNumberInputFragment.TAG)
    }

    private fun showScheduleTime(isSingleChannelEnabled: Boolean, deflectionType: String?) {
        if (deflectionType?.isNotEmpty() == true) {
            sendEvent(deflectionType, "scheduled_call", null)
        }
        if (isSingleChannelEnabled) {
            val activity = getActiveActivity() ?: return
            activity.finish()
            UjetScheduleTimePickerActivity.start(activity, deflectionType)
        } else {
            FragmentHelper.show(
                fragment as BaseFragment,
                ScheduleTimePickerFragment.newInstance(deflectionType),
                ScheduleTimePickerFragment.TAG)
        }
    }

    private fun showInCall() {
        val activity = getActiveActivity() ?: return
        val sharedPreferences = RecordingPermissionUtils.getCachedPreferences(activity.applicationContext)
        startInstantCall(sharedPreferences.getInt(RecordingPermissionUtils.MENU_ID_KEY, Int.MIN_VALUE),
            sharedPreferences.getString(RecordingPermissionUtils.VOICE_MAIL_REASON_KEY, null))
    }

    private fun showRecordingConfirmation() {
        //When user moves away from web browser to app, we show this confirmation dialog and we need to
        // use activity reference directly so that text translated accordingly
        val activity = getActiveActivity() ?: return
        val dialogMessage = HtmlCompat.fromHtml(activity.getString(R.string.ujet_ask_to_record_description,
            ApplicationUtil.getApplicationName(activity)), HtmlCompat.FROM_HTML_MODE_LEGACY).toString()

        ConfirmationDialogFragment.newInstance(
            fragment?.tag ?: return,
            REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE,
            activity.getString(R.string.ujet_channel_title),
            dialogMessage,
            activity.getString(R.string.ujet_common_yes),
            activity.getString(R.string.ujet_common_no)
        ).show(fragment.parentFragmentManager, ConfirmationDialogFragment.TAG)
    }

    private fun showExternalDeflectionLinks(url: String, deflectionType: String?) {
        val activity = getActiveActivity() ?: return
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        try {
            //Send external deflection link selected event for tracking
            Injection.provideDeflectedEventManager(activity).sendExternalDeflectionLinksEvent(url, deflectionType)

            activity.startActivity(browserIntent)
        } catch (e: ActivityNotFoundException) {
            //When user selected external deflection link which has invalid url,
            //we can not open the url so we show error message.
            Toast.makeText(activity, R.string.ujet_invalid_url_message, Toast.LENGTH_LONG).show()
            Logger.i("Invalid url configured in external deflection link")
            return
        }

        isExternalDeflectionLinkClicked = true
        handler.postDelayed({
            isTimerFinished = true
        }, WARNING_DIALOG_TIMER)
    }

    private fun startInstantCall(menuId: Int, voicemailReason: String?) {
        val activity = getActiveActivity() ?: return
        if (networkConnectionReceiver.areAllNetworksDisconnected()) {
            Injection.provideUjetErrorHandler(activity).onError(UjetErrorCode.NETWORK_ERROR.errorCode)
        } else {
            // Start call with menu id for DAP with preferred channel case
            val context = activity.applicationContext
            if (ServiceUtil.isServiceRunning(context, UjetCallService::class.java)) {
                fragment?.parentFragmentManager?.popBackStack() // close PSA screen
                val inCallFragment = fragment?.parentFragmentManager?.findFragmentByTag(InCallFragment.TAG)
                if (inCallFragment != null && inCallFragment is InCallFragment) {
                    inCallFragment.setEmptyViewProgressBarVisibility(true)
                    inCallFragment.resumeCommunication(menuId)
                } else {
                    Toast.makeText(activity, R.string.ujet_error_call_create_fail_android, Toast.LENGTH_LONG).show()
                    activity.finish()
                }
            } else {
                activity.finish()
                if (voicemailReason?.isNotEmpty() == true) {
                    Injection.provideDeflectedEventManager(activity)
                        .sendDeflectedEvent("deflected", voicemailReason, "voicemail")
                }
                UjetCallService.startInstantCall(activity, menuId, voicemailReason, false)
            }
        }
    }

    private fun isEmailClientAvailable(): Boolean {
        val activity = getActiveActivity() ?: return false
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "message/rfc822"
        return activity.packageManager.queryIntentActivities(intent, 0).size > 0
    }

    private fun sendEvent(deflectionType: String?, deflectionMode: String, phoneNumber: String?) {
        val context = getActiveActivity()?.applicationContext ?: return
        phoneNumber?.let {
            Injection.provideDeflectedEventManager(context).sendDeflectedEvent("deflected",
                deflectionType, deflectionMode, it)
        } ?: run {
            Injection.provideDeflectedEventManager(context).sendDeflectedEvent("deflected",
                deflectionType, deflectionMode)
        }
    }

    private fun checkCallPermissionsGranted(): Boolean {
        val context = getActiveActivity()?.applicationContext ?: return false
        return PermissionUtil.isPermissionsForCallGranted(context)
    }

    private fun requestCallPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(arrayOf(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.BLUETOOTH_CONNECT))
        } else {
            requestPermissions(arrayOf(Manifest.permission.RECORD_AUDIO))
        }
    }

    private fun requestPermissions(permissions: Array<String>) {
        permissionResultLauncher.launch(permissions)
    }

    private fun onPermissionNotGranted() {
        val activity = getActiveActivity() ?: return
        /* We reset language to original language (what host app was using) in UjetBaseActivity#onPause()
        when user moves away from any UJET app screen and then update language in UjetBaseActivity#onResume()
        to match with what user selected in entry screen in UJET app. When user taps on call channel,
        we moved away from screen to check and request call permissions and language is reset to original
        language (example: english), if any of the permissions are not accepted, we show error message
        but at this point language is not yet updated (to match with UJET app language) so we are
        explicitly requesting preferred language and updating here */
        getPreferredLanguage()
        Toast.makeText(activity, R.string.ujet_error_ujet_audio_permission_denied_android, Toast.LENGTH_LONG).show()

        // if selected menu has only one channel or redirection, go back to menu
        if (fragment is ChannelFragment &&
            (selectedMenu?.channels?.getEnabledChannels(cachedCompany)?.size == 1 || selectedMenu?.redirect != null)) {
            back(isSingleMenu)
        }
    }

    private fun getPreferredLanguage() {
        useCaseHandler.executeImmediate(
            useCase = chooseLanguage,
            requestValue = ChooseLanguage.RequestValues(),
            onSuccess = {
                updateLanguage(it.languageCode)
            }
        )
    }

    private fun getCachedRootMenus() {
        useCaseHandler.executeImmediate(getMenus,
            GetMenus.RequestValues.createCached(ujetContext.directAccessKey, MenuFilter.VISIBLE),
            object : UseCaseCallback<GetMenus.ResponseValue> {
                override fun onSuccess(response: GetMenus.ResponseValue) {
                    isDAPEnabled = response.isDirectAccess
                    isSingleMenu = response.isDirectAccess && response.menus?.getOrNull(0)?.isLeafMenu == true
                }

                override fun onError() {
                    isDAPEnabled = false
                }
            })
    }

    private fun updateLanguage(language: String) {
        val activity = getActiveActivity() ?: return
        LocaleUtil.update(activity, language)
    }

    private fun setExpectedCallWaitTime(localRepository: LocalRepository, apiManager: ApiManager, menuId: Int) {
        val waitTimeSecs = localRepository.callRepository.waitSeconds
        val language = localRepository.userPreferredLanguage
        if (waitTimeSecs == 0) {
            apiManager.getWaitTimes(menuId, language, object : ApiCallback<WaitTimes> {
                override fun onSuccess(httpRequest: HttpRequest, response: ApiResponse<WaitTimes>) {
                    val waitTimes = response.body()
                    if (response.code() == HttpURLConnection.HTTP_OK && waitTimes != null) {
                        setWaitTime(waitTimes.voiceCallWaitTime)
                    } else {
                        setWaitTime(24 * 60 * 60)
                    }
                }

                override fun onFailure(httpRequest: HttpRequest, throwable: Throwable) {
                    setWaitTime(24 * 60 * 60)
                }

                private fun setWaitTime(waitTime: Int) {
                    val expectedConnectTime =
                        System.currentTimeMillis() + 30.coerceAtLeast(waitTime) * 1000L
                    localRepository.callRepository.waitSeconds = waitTime
                    localRepository.callRepository.expectedConnectTime = expectedConnectTime
                }
            })
        } else {
            val expectedConnectTime = System.currentTimeMillis() + 30.coerceAtLeast(waitTimeSecs) * 1000L
            localRepository.callRepository.expectedConnectTime = expectedConnectTime
        }
    }

    private fun savePreferenceData(menuId: Int, voiceMailReason: String?) {
        val activity = getActiveActivity() ?: return
        RecordingPermissionUtils.savePreferenceData(menuId, voiceMailReason, activity.applicationContext)
    }

    private fun saveRecordingPermissionStatus(recordingPermission: String) {
        val activity = getActiveActivity() ?: return
        RecordingPermissionUtils.saveRecordingPermissionStatus(recordingPermission, activity.applicationContext)
    }

    private fun onConfirmationDialogClicked(isPermissionGranted: Boolean) {
        val recordingPermission: String = if (isAskToRecordEnabled) {
            if (isPermissionGranted) {
                RecordingPermissionUtils.RECORDING_PERMISSION_GRANTED
            } else {
                RecordingPermissionUtils.RECORDING_PERMISSION_DENIED
            }
        } else {
            RecordingPermissionUtils.RECORDING_PERMISSION_NOT_ASKED
        }
        saveRecordingPermissionStatus(recordingPermission)
        showInCall()
    }

    private fun onConfirmationDialogCanceled() {
        if (isSingleMenu && isSingleChannelEnabled && isActive()) {
            finish()
        }
    }

    private fun clearAndFinish(finish: Boolean = true) {
        val activity = getActiveActivity() ?: return

        PsaUtil.clearPreferenceData(activity.applicationContext)
        clearSmartActionResults()

        if (finish) {
            activity.finish()
        } else {
            fragment?.parentFragmentManager?.popBackStack()
        }
    }

    private fun clearSmartActionResults() {
        localRepository.isOngoingPsa = false
        uploadRepository.removeByState(MediaFile.Status.Selected)
        uploadRepository.removeByState(MediaFile.Status.Pending)
        SmartActionManager.deleteSmartActionResults()
    }

    private fun canHandleIntent(intent: Intent) : Boolean {
        val activity = getActiveActivity() ?: return false
        return intent.resolveActivityInfo(activity.packageManager, 0) != null
    }

    private fun isDAPWithSingleChannelAndLinkEnabled(): Boolean {
        return isDAPEnabled && isSingleChannelAndLinkEnabled
    }

    private fun back(finishAble: Boolean) {
        if (finishAble) {
            finish()
        } else {
            fragment?.parentFragmentManager?.popBackStack()
        }
    }

    private fun finish() {
        val activity = getActiveActivity() ?: return
        activity.finish()
    }

    private fun isActive(): Boolean {
        return fragment?.isAdded == true
    }
    
    private fun getActiveActivity(): Activity? {
        if (isActive().not()) {
            return null
        }
        return activity
    }

    companion object {
        private const val REQUEST_CODE_CONFIRMATION_DIALOG_RESPONSE = 1004
        private const val REQUEST_CODE_WARNING_DIALOG_RESPONSE = 1009
        private val WARNING_DIALOG_TIMER = TimeUnit.MINUTES.toMillis(5) //5 minutes
    }
}
