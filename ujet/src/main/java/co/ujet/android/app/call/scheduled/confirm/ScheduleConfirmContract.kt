package co.ujet.android.app.call.scheduled.confirm

import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView
import java.util.Date

internal interface ScheduleConfirmContract {
    interface View : BaseView {
        fun displayScheduledTime(scheduledTime: Date)
        fun back()
        fun finish()
        fun showSurveyScreen()
        fun showCsatScreen()
    }

    interface Presenter : BasePresenter {
        fun onConfirmClicked()
        fun onBackButtonClicked()
    }
}
