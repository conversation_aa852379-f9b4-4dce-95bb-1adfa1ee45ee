package co.ujet.android.app.call.phonenumber

import android.text.Editable
import co.ujet.android.clean.presentation.BasePresenter
import co.ujet.android.clean.presentation.BaseView

/**
 * Listens to user actions from the UI ([PhoneNumberInputFragment]), retrieves the data and updates the
 * UI as required.
 */
interface PhoneNumberInputContract {
    interface View : BaseView {
        fun showCountyCodeSelect()
        fun showScheduleConfirm(callId: Int, deflectionType: String?)
        fun showErrorDialog(errorTitle: String? = null, errorMessage: String, confirmMessage: String? = null, dismissCallback: (() -> Unit)? = null)
        fun setCountryCode(countryCode: String)
        fun setPhoneNumber(phoneNumber: String)
        fun setPhoneNumberHint(phoneNumberHint: String)
        fun announceInvalidPhoneNumberMessage()
        fun enableButton(isEnabled: Boolean)
        fun showMenuUpdated()
        fun restart()
        fun finish()
        fun showRecordingConfirmation()
        fun saveRecordingPermissionStatus(recordingPermission: String)
        fun enableRecordingMessage(isEnabled: Boolean)
        fun back()
        fun showSurveyScreen()
        fun showCsatScreen()
    }

    interface Presenter : BasePresenter {
        fun onPhoneNumberChanged(phoneNumber: Editable)
        fun onRegionCodeChanged(countryCode: String?)
        fun onCountryCodeClicked()
        fun confirm()
        fun skip()
        fun onConfirmationDialogClicked(isPermissionGranted: Boolean)
        fun onBackButtonClicked()
        fun onExitButtonClicked()
    }
}
