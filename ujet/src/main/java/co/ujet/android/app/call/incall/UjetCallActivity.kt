package co.ujet.android.app.call.incall

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import co.ujet.android.R.*
import co.ujet.android.app.ActivityHelper
import co.ujet.android.app.call.phonenumber.PhoneNumberInputFragment
import co.ujet.android.app.call.scheduled.timepicker.ScheduleTimePickerFragment
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.clean.presentation.email.EmailFragment
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.libs.logger.Logger
import co.ujet.android.service.UjetCallService
import java.net.HttpURLConnection

/**
 * Call activity should be run after [UjetCallService] is started.
 */
class UjetCallActivity : UjetBaseActivity() {
    private var errorReason: String? = null
    private var errorMessage: String? = null
    private var isAfterHourMessage = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(layout.ujet_activity_in_call)
        window.statusBarColor = ujetStyle().colorPrimary
        ActivityHelper.setShowWhenLockedAndTurnScreenOn(this)
        parseIntent(intent)
        val fragment = supportFragmentManager.findFragmentByTag(InCallFragment.TAG)
        if (fragment == null) {
            supportFragmentManager
                .beginTransaction()
                .add(id.fragment_container, InCallFragment.newInstance(errorReason, errorMessage, isAfterHourMessage), InCallFragment.TAG)
                .commit()
        }
        Logger.d("UjetCallActivity is created")
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Logger.i("Started the call activity with new intent")

        val fragmentTransaction = supportFragmentManager.beginTransaction()
        supportFragmentManager.findFragmentByTag(InCallFragment.TAG)?.let {
            fragmentTransaction.remove(it)
        }

        if (!intent.hasExtra(EXTRA_MENU_ID)) {
            parseIntent(intent)
            fragmentTransaction
                .add(id.fragment_container, InCallFragment.newInstance(errorReason, errorMessage, isAfterHourMessage), InCallFragment.TAG)
                .commit()
        } else {
            CallInterruptedDialogFragment.newInstance().apply {
                setMessage(intent.extras?.getString(EXTRA_MESSAGE) ?: "")
                setMenuId(intent.extras?.getInt(EXTRA_MENU_ID) ?: 0)
                show(fragmentTransaction, CallInterruptedDialogFragment.TAG)
            }
        }
    }

    override fun checkWebViewAvailability() {
        if (WebViewUtil.isWebViewDisabled(applicationContext)) {
            Logger.w("Web view is disabled")
            WebViewUtil.handleWebViewUnavailability(applicationContext)
        }
    }

    override fun onBackPressed() {
        back()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            val emailFragment = supportFragmentManager.findFragmentByTag(EmailFragment.TAG)
            val scheduleTimePickerFragment = supportFragmentManager.findFragmentByTag(ScheduleTimePickerFragment.TAG)
            val phoneNumberInputFragment = supportFragmentManager.findFragmentByTag(PhoneNumberInputFragment.TAG)
            when {
                emailFragment is EmailFragment -> emailFragment.onKeyDown(keyCode)
                phoneNumberInputFragment is PhoneNumberInputFragment -> phoneNumberInputFragment.onKeyDown(keyCode)
                scheduleTimePickerFragment is ScheduleTimePickerFragment -> scheduleTimePickerFragment.onKeyDown(keyCode)
                else -> back()
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun back() {
        if (supportFragmentManager.backStackEntryCount > 1) {
            supportFragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    private fun parseIntent(intent: Intent?) {
        var errorMessage: String? = null
        val errorCode: Int
        if (intent?.hasExtra(EXTRA_ERROR_REASON) == true) {
            errorReason = intent.getStringExtra(EXTRA_ERROR_REASON)
        }
        if (intent?.hasExtra(EXTRA_ERROR_CODE) == true) {
            errorCode = intent.getIntExtra(EXTRA_ERROR_CODE, 0)
            if (intent.hasExtra(EXTRA_ERROR_MESSAGE)) {
                errorMessage = intent.getStringExtra(EXTRA_ERROR_MESSAGE)
            }
            if (errorCode == HttpURLConnection.HTTP_CONFLICT && !errorMessage.isNullOrEmpty()) {
                isAfterHourMessage = true
                this.errorMessage = errorMessage
            } else {
                isAfterHourMessage = false
                this.errorMessage = getString(string.ujet_error_call_connect_fail_android)
            }
        }
    }

    companion object {
        private const val EXTRA_ERROR_REASON = "error_reason"
        private const val EXTRA_ERROR_CODE = "error_code"
        private const val EXTRA_ERROR_MESSAGE = "error_message"

        private const val EXTRA_MESSAGE = "ujet_message_extra"
        private const val EXTRA_MENU_ID = "ujet_menu_id"

        @JvmStatic
        fun start(context: Context) {
            val startCallActivity = Intent(context, UjetCallActivity::class.java)
            startCallActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startCallActivity.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(startCallActivity)
        }

        @JvmStatic
        fun start(context: Context, reason: String, errorCode: Int, errorMessage: String?) {
            val startCallActivity = Intent(context, UjetCallActivity::class.java)
            startCallActivity.putExtra(EXTRA_ERROR_REASON, reason)
            startCallActivity.putExtra(EXTRA_ERROR_CODE, errorCode)
            startCallActivity.putExtra(EXTRA_ERROR_MESSAGE, errorMessage)
            startCallActivity.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startCallActivity.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(startCallActivity)
        }

        @JvmStatic
        fun start(context: Context, path: String, menuId: Int) {
            Intent(context, UjetCallActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                putExtra(EXTRA_MESSAGE, path)
                putExtra(EXTRA_MENU_ID, menuId)
                context.startActivity(this)
            }
        }
    }
}
