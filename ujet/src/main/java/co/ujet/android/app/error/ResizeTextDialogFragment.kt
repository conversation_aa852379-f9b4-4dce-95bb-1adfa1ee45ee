package co.ujet.android.app.error

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.annotation.Keep
import androidx.fragment.app.FragmentManager
import co.ujet.android.R
import co.ujet.android.app.common.BaseDialogFragment
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.libs.logger.Logger
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.getDisplayMetrics

class ResizeTextDialogFragment @Keep constructor() : BaseDialogFragment() {

    private var message: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            message = it.getString(ARGS_DIALOG_MESSAGE) ?: getString(R.string.ujet_greeting_navigation_title)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialogWidth = context?.let { context ->
            // When font size exceeded, wrap the content to make use of extra width so that text will
            // fit into the screen, otherwise we can add left and right padding to the dialog.
            if (ResizeTextAccessibilityUtil.isFontSizeExceededLimit(context)) {
                ViewGroup.LayoutParams.WRAP_CONTENT
            } else {
                getDisplayMetrics(context).widthPixels - DesignUtil.dpToPx(
                    context,
                    context.resources.getDimension(co.ujet.android.ui.R.dimen.ujet_picker_layout_margin)
                ).toInt()
            }
        } ?: ViewGroup.LayoutParams.WRAP_CONTENT

        val dialog = builder
            .customView(R.layout.ujet_dialog_resize_text)
            .gravity(Gravity.CENTER)
            .height(ViewGroup.LayoutParams.WRAP_CONTENT)
            .width(dialogWidth)
            .withExit(false)
            .build()
        setDescription(message, true)

        (dialog.findViewById(R.id.dialog_layout_view) as? LinearLayout)?.apply {
            // Change dialog background color
            this.setBackgroundColor(ujetStyle().dividerBackgroundColor)
        }
        return dialog
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (dialogShowing()) return
        super.show(manager, tag)
    }

    override fun onBack() {
        invokeDismiss(null)
    }

    override fun dismiss() {
        invokeDismiss(null)
    }

    fun closeDialog(fragmentManager: FragmentManager) {
        invokeDismiss(fragmentManager)
    }

    private fun invokeDismiss(fragmentManager: FragmentManager?) {
        /* Sometimes due to race condition, we get show and hide dialog requests almost at same time
        * when wifi switches to cellular data and dialog is never closed as dialog is null when we
        * are trying to hide it and then system process show dialog requests which resulted in showing
        * reconnection dialog in this case which is wrong so we always close the dialog without even
        * checking if dialog is showing or not to address that race condition.
        * Recently, we noticed app crashes due to fragment not associated with a fragment manager, while
        * dismissing dialog in getParentFragmentManager() so adding (isAdded && !isRemoving) check to ensure
        * fragment is attached and not marked for removing.
         */
        try {
            if (isAdded && !isRemoving) {
                super.dismiss()
            }
        } catch(e:Exception) {
            Logger.e("failed to dismiss reconnection dialog: ${e.message}")
            // In case of exception, try to see if we can find ConnectivityDialogFragment from fragmentManager and
            // dismiss it, otherwise we can skip dismissing the dialog.
            val resizeTextDialogFragment: ResizeTextDialogFragment? = fragmentManager?.findFragmentByTag(TAG) as ResizeTextDialogFragment?
            if (resizeTextDialogFragment != null) {
                super.dismiss()
            }
        }
    }

    private fun dialogShowing() : Boolean = dialog?.isShowing == true && !isRemoving

    companion object {
        const val TAG = "Resize Text Dialog Fragment"

        private const val ARGS_DIALOG_MESSAGE = "dialog_message"

        @JvmStatic
        fun newDialog(message: String): ResizeTextDialogFragment {
            return ResizeTextDialogFragment().apply {
                arguments = Bundle().apply {
                    putString(ARGS_DIALOG_MESSAGE, message)
                }
            }
        }
    }

}
