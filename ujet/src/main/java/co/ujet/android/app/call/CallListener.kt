package co.ujet.android.app.call

import android.view.View
import androidx.fragment.app.FragmentManager
import co.ujet.android.data.call.InCallState
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.data.model.Call
import co.ujet.android.data.model.VirtualAgentSettings
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI

interface CallListener {
    fun onCallFailure(failureReason: String, errorCode: Int, message: String?)
    fun onRegistered(inCallState: InCallState)
    fun onQueued(call: Call)
    fun onAssigned(call: Call)
    fun onConnecting(call: Call)
    fun onSwitching(call: Call)
    fun onConnected(call: Call, agent: Agent)
    fun onFinished(call: Call)
    fun onFailed(call: Call)
    fun onRecovered(call: Call)
    fun onVoicemailConnecting(voicemail: Call)
    fun onVoicemailRecording(voicemail: Call)
    fun onVoicemailRecorded(voicemail: Call)
    fun onParticipantLeft(call: Call, userId: Int)
    fun onTransferred(call: Call, agent: Agent)
    fun onMute(isMute: Boolean)
    fun onSpeaker(isSpeakerOn: Boolean)
    fun onWarning()
    fun onNetworkReconnected()
    fun onError(message: String?)
    fun onCallClear()
    fun onVirtualAgentSettingsUpdated(virtualAgentSettings: VirtualAgentSettings?, call: Call)
    fun onEscalating(call: Call)
    fun onCallNotSupported(message: String)
    fun getCoBrowseUI(): CoBrowseUI?
    fun getCoBrowseButtonView(): View?
    fun getMainFragmentManager(): FragmentManager?
    fun displayPendingSmartAction()
    fun showPreSessionSmartActions(menuId: Int)
    fun closeCallScreen()
    fun showRecordingConfirmation(menuId: Int)
}
