package co.ujet.android.app.common

import android.content.Context
import android.content.DialogInterface
import android.graphics.Typeface
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import co.ujet.android.R
import co.ujet.android.api.ApiManager
import co.ujet.android.app.chat.MarkdownUtil
import co.ujet.android.app.chat.MarkdownUtil.getDisplayableTextFromHtml
import co.ujet.android.clean.entity.menu.channel.Channel.Companion.DEFLECTION_REASON_AFTER_HOURS
import co.ujet.android.common.util.CustomizableLinkUtil.Companion.containsCustomizableLink
import co.ujet.android.common.util.CustomizableLinkUtil.Companion.parseCustomizableLinks
import co.ujet.android.common.util.DialogUtil.announceCustomDialogTitle
import co.ujet.android.common.util.DialogUtil.getAlertTextContent
import co.ujet.android.common.util.StringUtil
import co.ujet.android.common.util.TextViewUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.data.UjetContext
import co.ujet.android.internal.Injection
import co.ujet.android.libs.logger.Logger.consoleOnly
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.style.UjetViewStyler.styleInvertedButton
import co.ujet.android.ui.style.UjetViewStyler.stylePrimaryButton
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.getDisplayMetrics
import cx.ujet.android.markdown.widgets.MarkdownTextView
import java.util.Locale

abstract class BaseDialogFragment : DialogFragment() {
    private lateinit var ujetStyle: UjetStyle
    private lateinit var apiManager: ApiManager
    private lateinit var ujetContext: UjetContext
    private var title: String? = null
    private var description: String? = null
    private var titleTextView: TextView? = null
    private var descriptionTextView: TextView? = null
    private var descriptionMarkdownTextView: MarkdownTextView? = null
    private var headerBar: View? = null
    private var exitType = 0
    private var exitButton: View? = null
    private var customView: View? = null
    private var customViewContainer: ViewGroup? = null
    private var isResizeTextDialogEnabled = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getUjetComponent(activity)
        consoleOnly("onCreate %s", javaClass.simpleName)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        getUjetComponent(activity)
    }

    private fun getUjetComponent(context: Context?) {
        ujetStyle = Injection.provideUjetStyle(context ?: return)
        apiManager = Injection.provideApiManager(context)
        ujetContext = Injection.provideUjetContext(context)
    }

    override fun onStart() {
        super.onStart()
        updateTitle()
        updateDescription()
        setupExitButton()
        consoleOnly("onStart %s", javaClass.simpleName)
    }

    override fun onResume() {
        super.onResume()
        getUjetComponent(activity)
        dialog?.setOnKeyListener(onKeyListener)
        consoleOnly("onResume %s", javaClass.simpleName)
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        activity?.finish()
        consoleOnly("onCancel %s", javaClass.simpleName)
    }

    override fun onPause() {
        super.onPause()
        consoleOnly("onPause %s", javaClass.simpleName)
    }

    override fun onStop() {
        super.onStop()
        consoleOnly("onStop %s", javaClass.simpleName)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        titleTextView = null
        descriptionTextView = null
        descriptionMarkdownTextView = null
        headerBar = null
        exitButton = null
        customView = null
        customViewContainer = null
        consoleOnly("onDestroyView %s", javaClass.simpleName)
    }

    override fun onDestroy() {
        super.onDestroy()
        consoleOnly("onDestroy %s", javaClass.simpleName)
    }

    protected val builder: DialogBuilder
        get() = DialogBuilderImpl(this, ujetStyle())

    protected open fun onBack() {
        dismiss()
    }

    fun initialize(customViewContainer: ViewGroup?, title: String?, description: String?) {
        this.customViewContainer = customViewContainer
        setTitle(title)
        setDescription(description)
    }

    fun setContentView(customView: View?) {
        if (this.customView != null) {
            customViewContainer?.removeView(this.customView)
        }
        this.customView = customView
        customViewContainer?.addView(customView)
    }

    protected fun setTitle(title: String?) {
        this.title = title
        updateTitle()
    }

    private fun updateTitle() {
        if (titleTextView == null && dialog != null) {
            titleTextView = dialog?.findViewById<TextView>(R.id.titleTextView)?.apply {
                isAllCaps = true
                setTypeface(ujetStyle().typeFace, Typeface.BOLD)
                setTextColor(ujetStyle().textPrimaryColor)
                announceCustomDialogTitle(title, this)
            }
        }

        if (title == null && titleTextView != null) {
            titleTextView?.visibility = View.GONE
        } else if (title != null && titleTextView != null) {
            titleTextView?.text = title?.uppercase(Locale.getDefault())?.let {
                getDisplayableTextFromHtml(it)
            }
            titleTextView?.visibility = View.VISIBLE
        }
        updateHeaderBar()
    }

    protected fun setDescription(description: String?, isResizeTextDialogEnabled: Boolean = false) {
        this.description = description
        this.isResizeTextDialogEnabled = isResizeTextDialogEnabled
        updateDescription()
    }

    private fun updateDescription() {
        if ((descriptionTextView == null && descriptionMarkdownTextView == null) && dialog != null) {
            try {
                descriptionMarkdownTextView = dialog?.findViewById(R.id.description)
                descriptionMarkdownTextView?.let { applyTextStyles(it) }
            } catch (_: ClassCastException) {
                // We get class cast exception when description is not MarkdownTextView but simple
                // text view so save it in descriptionTextView
                descriptionTextView = dialog?.findViewById(R.id.description)
                descriptionTextView?.let { applyTextStyles(it) }
            }
        }
        val descriptionView = if (descriptionTextView != null) {
            descriptionTextView
        } else if (descriptionMarkdownTextView != null) {
            descriptionMarkdownTextView
        } else {
            null
        }
        description?.let { description ->
            // Setting initial descriptionText empty space so that talkback doesn't announce app name
            descriptionView?.text = " "
            setDescriptionTextView(description)
        }
    }

    private fun applyTextStyles(descriptionView: TextView) {
        descriptionView.typeface = ujetStyle().typeFace
        val context = context ?: return
        val descriptionTextColor = if (isResizeTextDialogEnabled) {
            ujetStyle().dialogTextColor
        } else {
            ujetStyle().textSecondaryColor
        }
        descriptionView.setTextColor(descriptionTextColor)
        if (isResizeTextDialogEnabled) {
            // Increase text size based on accessibility setting
            descriptionView.textSize = (DesignUtil.pxToSp(context, descriptionView.textSize)
                .toFloat()) * (ResizeTextAccessibilityUtil.getMaxFontSize(context))
        }
    }

    private fun setDescriptionTextView(description: String) {
        val activity = activity ?: return
        descriptionTextView?.let { descriptionView ->
            when {
                containsCustomizableLink(description) -> {
                    val spannable = parseCustomizableLinks(
                        description, activity, DEFLECTION_REASON_AFTER_HOURS
                    )
                    descriptionView.text = spannable
                    descriptionView.isClickable = true
                    descriptionView.movementMethod = LinkMovementMethod.getInstance()
                }
                StringUtil.isUrlAvailable(description) -> {
                    descriptionView.let {
                        TextViewUtil.parseLinks(
                            it,
                            description,
                            DEFLECTION_REASON_AFTER_HOURS
                        )
                    }
                }
                else -> {
                    descriptionView.text = getDisplayableTextFromHtml(description)
                }
            }
            descriptionView.contentDescription = getDescriptionViewAccessibilityContent(activity.applicationContext,
                descriptionView.text.toString())
        }
        descriptionMarkdownTextView?.let { markdownTextView ->
            UjetViewStyler.styleRemoteChatText(ujetStyle, markdownTextView)
            UjetViewStyler.styleRemoteChatLinkText(ujetStyle, markdownTextView)
            MarkdownUtil.loadMessageIntoMarkdownTextView(
                activity.applicationContext,
                markdownTextView,
                description,
                deflectionType = DEFLECTION_REASON_AFTER_HOURS
            )
            markdownTextView.contentDescription = getDescriptionViewAccessibilityContent(activity.applicationContext,
                markdownTextView.text.toString())
        }
    }

    private fun getDescriptionViewAccessibilityContent(context: Context, descriptionViewText: String): String {
        // If title is empty / null then we should announce description as `Alert [dialog description]` format
        return if (title.isNullOrBlank()) {
            "${getAlertTextContent(context)} $descriptionViewText"
        } else {
            descriptionViewText
        }
    }

    fun setExitType(exitType: Int) {
        this.exitType = exitType
    }

    private fun setupExitButton() {
        if (exitButton == null && dialog != null) {
            exitButton = dialog?.findViewById(R.id.exit)
            exitButton?.setOnClickListener(View.OnClickListener {
                val exitToFinish = exitType and EXIT_TYPE_FINISH
                if (exitToFinish > 0) {
                    activity?.finish()
                } else {
                    onBack()
                }
            })
            updateHeaderBar()
        }
    }

    private fun updateHeaderBar() {
        if (headerBar == null && dialog != null) {
            headerBar = dialog?.findViewById(R.id.header_bar)
        }
        if (headerBar != null) {
            if ((titleTextView == null || titleTextView?.visibility == View.GONE)
                && (exitButton == null || exitButton?.visibility == View.GONE)) {
                headerBar?.visibility = View.GONE
            } else {
                headerBar?.visibility = View.VISIBLE
            }
        }
    }

    protected fun ujetContext() = ujetContext

    protected fun ujetStyle() = ujetStyle

    protected fun apiManager() = apiManager

    fun getDialogHeight(): Int {
        val activity = activity ?: return 0
        return getDisplayMetrics(activity).heightPixels - resources.getDimension(co.ujet.android.ui.R.dimen.ujet_dialog_top_margin).toInt()
    }

    protected fun getDialogHeight(divider: Float): Int {
        val activity = activity ?: return 0
        return ((getDisplayMetrics(activity).heightPixels - resources.getDimension(co.ujet.android.ui.R.dimen.ujet_dialog_top_margin).toInt()) / divider).toInt()
    }

    protected fun styleDefaultButton(button: FancyButton?) {
        stylePrimaryButton(ujetStyle(), button ?: return)
    }

    protected fun styleInvertedButton(button: FancyButton?) {
        styleInvertedButton(ujetStyle(), button ?: return)
    }

    private val onKeyListener: DialogInterface.OnKeyListener =
        object : DialogInterface.OnKeyListener {
            private var backDownPressed = false
            override fun onKey(dialog: DialogInterface, keyCode: Int, event: KeyEvent): Boolean {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
                    if (event.action == KeyEvent.ACTION_DOWN) {
                        backDownPressed = true
                        return true
                    } else if (event.action == KeyEvent.ACTION_UP && backDownPressed) {
                        onBack()
                        backDownPressed = false
                        return true
                    }
                }
                return false
            }
        }

    companion object {
        const val EXIT_TYPE_FINISH = 0x0001
        const val EXIT_TYPE_BACK = 0x0002
        const val REQUEST_CODE = "request_code"
        const val RESULT_CODE = "result_code"
        const val ARGS_REQUEST_KEY = "args_request_key"
        const val ARGS_REQUEST_CODE = "args_request_code"
    }
}
