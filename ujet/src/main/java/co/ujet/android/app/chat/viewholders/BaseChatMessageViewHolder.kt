package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.internal.Configuration
import co.ujet.android.internal.Injection

abstract class BaseChatMessageViewHolder(protected val adapter: ChatAdapterInteractor, itemView: View) : RecyclerView.ViewHolder(itemView) {

    protected val configuration: Configuration = Injection.provideConfiguration()
    private val cachedDimensions = mapOf(
        co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half to itemView.context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half),
        co.ujet.android.ui.R.dimen.ujet_chat_message_padding to itemView.context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_chat_message_padding),
        co.ujet.android.ui.R.dimen.ujet_chat_corner_radius to itemView.context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_chat_corner_radius)
    )

    protected fun getCachedDimension(id: Int) = cachedDimensions[id] ?: 0

    fun bind() {
    }

    companion object {
        fun inflate(context: Context, parent: ViewGroup, @LayoutRes layoutResId: Int): View {
            return LayoutInflater.from(context).inflate(layoutResId, parent, false)
        }

        // When clicking html links (within <a>) inside greeting message markdown text view, encountered
        // app crash with message "android.util.AndroidRuntimeException: Calling startActivity() from outside
        // of an Activity context requires the FLAG_ACTIVITY_NEW_TASK flag. Is this really what you want?".
        // It happens when custom text views inflated with non activity context and to fix it, pass activity
        // reference to inflate the layout.
        fun inflate(activity: Activity, parent: ViewGroup, @LayoutRes layoutResId: Int): View {
            return activity.layoutInflater.inflate(layoutResId, parent, false)
        }
    }
}
