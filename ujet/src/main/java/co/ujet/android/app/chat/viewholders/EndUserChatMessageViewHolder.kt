package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.MarkdownUtil.loadMessageIntoMarkdownTextView
import co.ujet.android.commons.domain.chat.message.EndUserChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView

class EndUserChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup,
                                   activity: Activity, ujetStyle: UjetStyle) :
    ChatMessageViewHolder(adapter, activity.applicationContext, ujetStyle, inflate(activity, parent,
        R.layout.ujet_view_chat_message_end_user)) {

    fun bind(message: EndUserChatMessage, isGroupStart: Boolean, isGroupEnd: Boolean): View {
        setUpEndUserIcon(itemView, isGroupStart)

        val consumerMessageStyle = configuration.ujetStylesOptions?.chatStyles?.consumerMessageBubbles
        val mainContainer: LinearLayout = itemView.findViewById(R.id.main_container)
        mainContainer.setPaddingRelative(ujetStyle.dpToPx(18f).toInt(), 0,
            ujetStyle.dpToPx(18f).toInt(), ujetStyle.dpToPx(12f).toInt())
        val chatContainer: LinearLayout = itemView.findViewById(R.id.chat_contents_container)
        val messageTextView: MarkdownTextView = itemView.findViewById(R.id.message)

        val background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_end_user)
        val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT).apply {
            marginStart = ujetStyle.dpToPx(55f).toInt()
        }
        val defaultStylesApplied = StyleUtil.applyDefaultStyle(
            context,
            ujetStyle,
            chatContainer,
            consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius,
            consumerMessageStyle?.border,
            background,
            ujetStyle.chatEndUserMessageTopBubbleColor,
            layoutParams
        )

        UjetViewStyler.styleLocalChatText(ujetStyle, messageTextView)
        UjetViewStyler.styleLocalChatLinkText(ujetStyle, messageTextView)
        loadMessageIntoMarkdownTextView(context, messageTextView, message.message)
        StyleUtil.updateFontStyle(context, messageTextView, consumerMessageStyle?.font)
        StyleUtil.updateBackgroundStyle(
            messageTextView,
            consumerMessageStyle?.backgroundColor,
            consumerMessageStyle?.cornerRadius,
            consumerMessageStyle?.border
        )

        if (!defaultStylesApplied) {
            // Add default padding besides the one generated by the corner radius
            val borderPadding =
                StyleUtil.getTextPaddingWithInBorder(context, consumerMessageStyle?.cornerRadius,
                    consumerMessageStyle?.border) ?: 0
            messageTextView.setPaddingRelative(
                messageTextView.paddingStart + borderPadding,
                messageTextView.paddingTop + borderPadding,
                messageTextView.paddingEnd + borderPadding,
                messageTextView.paddingBottom + borderPadding
            )
        }
        setUpMessageFooter(itemView, message, isGroupEnd)
        setupResendButton(message, itemView.findViewById(R.id.btn_send_failed), itemView.findViewById(R.id.resend_message))
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_mobile_user),
            mainContainer = mainContainer,
            message = messageTextView,
            timestamp = itemView.findViewById(R.id.timestamp),
            resend = itemView.findViewById(R.id.resend_message),
            adapterPosition = adapterPosition
        )

        return itemView
    }
}
