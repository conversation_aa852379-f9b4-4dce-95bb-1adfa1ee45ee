package co.ujet.android.app.chat.viewholders

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.TypedValue
import android.view.View
import android.webkit.MimeTypeMap
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.RelativeLayout.LayoutParams
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.content.FileProvider
import co.ujet.android.R
import co.ujet.android.R.drawable
import co.ujet.android.activity.UjetMediaPreviewActivity
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_AUDIO
import co.ujet.android.common.util.LocaleUtil
import co.ujet.android.common.util.TimeUtil
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.chat.message.ChatMessageStatus.Failed
import co.ujet.android.commons.domain.chat.message.ChatMessageStatus.Resent
import co.ujet.android.commons.domain.chat.message.ChatMessageStatus.Sending
import co.ujet.android.commons.domain.chat.message.GreetingChatMessage
import co.ujet.android.commons.domain.chat.message.HumanAgentChatMessage
import co.ujet.android.commons.domain.chat.message.HumanAgentDocumentChatMessage
import co.ujet.android.commons.domain.chat.message.HumanAgentPhotoChatMessage
import co.ujet.android.commons.domain.chat.message.HumanAgentVideoChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentDocumentChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentPhotoChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentVideoChatMessage
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import co.ujet.android.commons.extensions.getValueOrDefault
import co.ujet.android.commons.extensions.loadOrSetDefault
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.libs.logger.Logger
import co.ujet.android.libs.logger.Logger.w
import co.ujet.android.modulemanager.common.ui.domain.AgentMessageStyle
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil
import cx.ujet.android.markdown.widgets.MarkdownTextView
import java.io.File
import java.util.Date
import java.util.Locale
import kotlin.math.max

abstract class ChatMessageViewHolder(
    adapter: ChatAdapterInteractor,
    protected val context: Context,
    protected val ujetStyle: UjetStyle,
    itemView: View
) : BaseChatMessageViewHolder(adapter, itemView) {

    protected fun setUpEndUserIcon(messageView: View, isGroupStart: Boolean) {
        val avatarBackground = messageView.findViewById<View>(R.id.end_user_avatar_background)
        val endUserAvatar = messageView.findViewById<ImageView>(R.id.end_user_avatar)
        val endUserMessageStyle = configuration.ujetStylesOptions?.chatStyles?.consumerMessageBubbles

        setEndUserAvatarVisibility(messageView, View.VISIBLE)
        UjetViewStyler.applyAvatarBorderColor(ujetStyle, ujetStyle.chatHumanAgentMessageBubbleBorderColor, avatarBackground)
        UjetViewStyler.applyAvatarBackgroundColor(ujetStyle, endUserAvatar)
        //End user avatar has primary color (blue) tint around the avatar and since we do not have any customization
        // options at the moment, we decided to use consumer message border color for avatar border.
        if (!endUserMessageStyle?.border?.color.isNullOrEmpty()) {
            StyleUtil.applyAvatarBorderColor(ujetStyle, endUserMessageStyle?.border?.color,
                avatarBackground)
        }
        val endUserIconResId = StyleUtil.getDrawableResIdByName(context, endUserMessageStyle?.icon?.icon)
        if (endUserIconResId != RESOURCE_NOT_FOUND) {
            endUserAvatar.setImageResource(endUserIconResId)
            //Set margin end to match space between icon and message matches with agent message
            messageView.findViewById<LinearLayout>(R.id.message_container)?.layoutParams =
                LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT).apply {
                        marginEnd = getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half)
                    }
        } else {
            //Hide avatar view when icon resources not found
            setEndUserAvatarVisibility(messageView, View.GONE)
        }
        if (endUserMessageStyle?.icon?.size != null) {
            val iconSizeEntered = endUserMessageStyle.icon?.size?.toFloat() ?: return
            val iconSize = ujetStyle.dpToPx(iconSizeEntered).toInt()
            avatarBackground.layoutParams = avatarBackground.layoutParams.apply {
                width = iconSize
                height = getValueOrDefault(isGroupStart, iconSize, 0)
            }
        }

        if (endUserMessageStyle?.icon?.visible == false) {
            setEndUserAvatarVisibility(messageView, View.GONE)
        } else if (!isGroupStart) {
            if (endUserIconResId != RESOURCE_NOT_FOUND) {
                setEndUserAvatarVisibility(messageView, View.INVISIBLE)
            } else {
                setEndUserAvatarVisibility(messageView, View.GONE)
            }
        }
    }

    private fun setEndUserAvatarVisibility(messageView: View, visibility: Int) {
        val avatarBackground = messageView.findViewById<View>(R.id.end_user_avatar_background)
        val endUserAvatar = messageView.findViewById<ImageView>(R.id.end_user_avatar)
        avatarBackground.visibility = visibility
        endUserAvatar.visibility = visibility
    }

    protected fun setUpMessageFooter(messageView: View, chatMessage: ChatMessage, isGroupEnd: Boolean) {
        val timestamp = messageView.findViewById<TextView>(R.id.timestamp)
        if (isGroupEnd) {
            timestamp.visibility = View.VISIBLE
            setMessageTimestamp(timestamp, chatMessage.getTimestamp())
        } else {
            timestamp.visibility = View.GONE
        }
        AccessibilityUtil.updateAccessibilityAction(timestamp, true, null)
    }

    protected fun setMessageTimestamp(timestampTextView: TextView, timestamp: Date) {
        val fontStyles = configuration.ujetStylesOptions?.chatStyles?.timeStamps?.font
        val locale = LocaleUtil.getCurrentLocale(timestampTextView.context)
        timestampTextView.text = TimeUtil.getHourInFormat(timestamp, locale)
        UjetViewStyler.styleTertiaryText(ujetStyle, timestampTextView)
        UjetViewStyler.overrideTypeface(ujetStyle, timestampTextView)
        StyleUtil.updateFontStyle(context, timestampTextView, fontStyles)
    }

    fun saveTimestampTextSize(timestampTextView: TextView) {
        ResizeTextAccessibilityUtil.chatTimestampFontSize = timestampTextView.textSize
    }

    fun setTimestampTextSize(timestampTextView: TextView) {
        if (ResizeTextAccessibilityUtil.chatTimestampFontSize != 0.0f) {
            timestampTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResizeTextAccessibilityUtil.chatTimestampFontSize)
        }
    }

    protected fun setupResendButton(chatMessage: SendableChatMessage, resendButton: ImageButton,
                                    resendMessageView: TextView) {
        if (chatMessage.messageStatus == Failed || chatMessage.messageStatus == Sending &&
            adapter.isConversationConnected() == false) {
            chatMessage.messageStatus = Failed
            resendButton.visibility = View.VISIBLE
            UjetViewStyler.overrideTypeface(ujetStyle, resendMessageView)
            resendMessageView.visibility = View.VISIBLE
            resendButton.setImageResource(R.drawable.ujet_ic_resend)
            resendButton.setOnClickListener {
                adapter.getChatItemClickListener()?.let {
                    if (adapter.isConversationConnected() == true) {
                        resendButton.visibility = View.GONE
                        resendMessageView.visibility = View.GONE
                        chatMessage.messageStatus = Resent
                        it.onResendClicked(chatMessage)
                    }
                }
            }
        } else {
            resendButton.visibility = View.GONE
            resendMessageView.visibility = View.GONE
        }
    }

    protected fun setAgentName(view: View, agentName: String?, canBeDisplayed: Boolean,
                               shouldShowAgentNames: Boolean) {
        view.findViewById<TextView>(R.id.agent_name)?.let { agentNameTextView ->
            agentNameTextView.visibility =
                if (!canBeDisplayed) {
                    View.GONE
                } else if (shouldShowAgentNames) {
                    UjetViewStyler.styleSecondaryText(ujetStyle, agentNameTextView)
                    UjetViewStyler.overrideTypeface(ujetStyle, agentNameTextView)
                    agentNameTextView.text = agentName
                    View.VISIBLE
                } else {
                    View.INVISIBLE
                }
        }
    }

    protected fun setUpMessageHeader(messageView: View, message: ChatMessage,
                                     shouldShowAgentNames: Boolean, isGroupStart: Boolean) {
        val config = getHeaderConf(message)
        val avatarBackgroundView = messageView.findViewById<View>(R.id.agent_avatar_background)
        val agentAvatarView = messageView.findViewById<ImageView>(R.id.agent_avatar)

        // Message body: message, documents, photos
        val messageTextView: MarkdownTextView? = messageView.findViewById(R.id.message)
        val documentContainer: View? = messageView.findViewById(R.id.document_container)
        val imageContainer: View? = messageView.findViewById(R.id.photo_image_view)

        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        val imageVisibility = if (message is GreetingChatMessage) {
            View.INVISIBLE
        } else {
            View.VISIBLE
        }
        avatarBackgroundView?.let { avatarBackground ->
            avatarBackground.visibility = imageVisibility
            UjetViewStyler.applyAvatarBorderColor(ujetStyle, config.avatarBorderColor, avatarBackground)
        }
        agentAvatarView?.let { agentAvatar ->
            agentAvatar.visibility = imageVisibility
            UjetViewStyler.applyAvatarBackgroundColor(ujetStyle, agentAvatar)
            agentAvatar.loadOrSetDefault(
                config.avatarUrl,
                config.defaultAvatarDrawable,
                ujetStyle.colorPrimary
                )
        }
        //Agent avatar has primary color (blue) tint around the avatar and since we do not have any customization
        // options at the moment, we decided to use agent message border color for avatar border.
        if (!agentMessageStyle?.border?.color.isNullOrEmpty()) {
            agentAvatarView?.let {
                StyleUtil.applyAvatarBorderColor(ujetStyle, agentMessageStyle?.border?.color, it)
            }
        }
        val borderThickness = (agentMessageStyle?.border?.width ?: 0) + ujetStyle.dpToPx(2f).toInt()
        val iconPositionLeft = agentMessageStyle?.icon?.position?.lowercase() ==
                AgentMessageStyle.Position.LEFT.name.lowercase()
        val defaultPadding = getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_message_padding)
        if (agentMessageStyle?.icon?.size != null && agentMessageStyle.icon?.visible == true) {
            val iconSizeEntered = agentMessageStyle.icon?.size?.toFloat() ?: return
            val iconWidth = ujetStyle.dpToPx(iconSizeEntered).toInt()
            val iconHeight = getValueOrDefault(isGroupStart, iconWidth, 0)
            //top and start margin should be half of icon size entered
            val halfIconSize = ujetStyle.dpToPx(iconSizeEntered / 2).toInt()
            val avatarBackgroundParams = LayoutParams(iconWidth, iconHeight).apply {
                val layoutId = when {
                    messageTextView != null -> R.id.message
                    imageContainer != null -> R.id.photo_image_view
                    documentContainer != null -> R.id.document_container
                    else -> 0
                }
                addRule(RelativeLayout.ALIGN_TOP, layoutId)
                topMargin = -halfIconSize
            }
            avatarBackgroundView?.layoutParams = avatarBackgroundParams
            val messageViewParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            messageViewParams.marginStart = halfIconSize
            messageViewParams.addRule(RelativeLayout.BELOW, R.id.agent_name)
            messageTextView?.layoutParams = messageViewParams

            val messageComponentViewParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            messageComponentViewParams.topMargin = halfIconSize + borderThickness
            messageComponentViewParams.marginStart = halfIconSize
            documentContainer?.layoutParams = messageComponentViewParams

            // Special treatment for image container because it should fit the "whole" width
            imageContainer?.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT).apply {
                    topMargin = halfIconSize + borderThickness
                    marginStart = halfIconSize
                    marginEnd = ujetStyle.dpToPx(120f).toInt()
                }
            //Start padding should be half of icon size entered so that icon does not mess up text and other values should be default
            val messageStartPadding = if (iconPositionLeft) {
                defaultPadding
            } else {
                halfIconSize
            }
            messageTextView?.setPaddingRelative(messageStartPadding, defaultPadding, defaultPadding,
                defaultPadding)
        }
        if (iconPositionLeft) {
            val agentNameView = messageView.findViewById<TextView>(R.id.agent_name)
            val iconWidth = ujetStyle.dpToPx(agentMessageStyle?.icon?.size?.toFloat() ?: 20f).toInt() //Default icon size to 20dp
            val iconHeight = getValueOrDefault(isGroupStart, iconWidth, 0)
            val avatarBackgroundParams = LayoutParams(iconWidth, iconHeight)
            avatarBackgroundParams.topMargin = context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size)
            avatarBackgroundView?.layoutParams = avatarBackgroundParams

            val agentNameViewParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            agentNameViewParams.addRule(RelativeLayout.END_OF, R.id.agent_avatar_background)
            agentNameViewParams.marginStart = getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half)
            agentNameView.layoutParams = agentNameViewParams

            val messageViewParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            messageViewParams.addRule(RelativeLayout.END_OF, R.id.agent_avatar_background)
            messageViewParams.addRule(RelativeLayout.BELOW, R.id.agent_name)
            messageViewParams.marginStart = getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half)
            messageTextView?.layoutParams = messageViewParams
            documentContainer?.layoutParams = messageViewParams

            // Special treatment for image container because it should fit the "whole" width
            imageContainer?.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT).apply {
                    addRule(RelativeLayout.END_OF, R.id.agent_avatar_background)
                    addRule(RelativeLayout.BELOW, R.id.agent_name)
                    marginStart = getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_message_avatar_size_half)
                    marginEnd = ujetStyle.dpToPx(120f).toInt()
                }
        }
        if (agentMessageStyle?.icon?.visible == false) {
            avatarBackgroundView?.visibility = View.GONE
            agentAvatarView?.visibility = View.GONE
        } else if (!isGroupStart) {
            avatarBackgroundView?.visibility = View.INVISIBLE
            agentAvatarView?.visibility = View.INVISIBLE
        }
        messageTextView?.apply {
            setPaddingRelative(
                max(defaultPadding, paddingStart),
                max(defaultPadding, paddingTop),
                max(defaultPadding, paddingEnd),
                max(defaultPadding, paddingBottom)
            )
        }
        setAgentName(messageView, config.agentName, isGroupStart, shouldShowAgentNames)
    }

    private fun getHeaderConf(message: ChatMessage): ChatHeaderConfig {
        return when (message) {
            is HumanAgentChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.chatHumanAgentMessageBubbleBorderColor,
                message.agentAvatarUrl,
                drawable.ujet_agent_sample
            )

            is HumanAgentPhotoChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.chatHumanAgentMessageBubbleBorderColor,
                message.agentAvatarUrl,
                drawable.ujet_agent_sample
            )

            is HumanAgentDocumentChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.chatHumanAgentMessageBubbleBorderColor,
                message.agentAvatarUrl,
                drawable.ujet_agent_sample
            )

            is HumanAgentVideoChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.chatHumanAgentMessageBubbleBorderColor,
                message.agentAvatarUrl,
                drawable.ujet_agent_sample
            )

            is VirtualAgentChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.colorPrimary,
                message.getAgentAvatarUrl(),
                drawable.ujet_virtual_agent_default
            )

            is VirtualAgentPhotoChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.colorPrimary,
                message.getAgentAvatarUrl(),
                drawable.ujet_virtual_agent_default
            )

            is VirtualAgentDocumentChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.colorPrimary,
                message.getAgentAvatarUrl(),
                drawable.ujet_virtual_agent_default
            )

            is VirtualAgentVideoChatMessage -> ChatHeaderConfig(
                message.agentName,
                ujetStyle.colorPrimary,
                message.getAgentAvatarUrl(),
                drawable.ujet_virtual_agent_default
            )

            is GreetingChatMessage -> ChatHeaderConfig(
                null,
                ujetStyle.chatHumanAgentMessageBubbleBorderColor,
                null,
                drawable.ujet_agent_sample
            )

            else -> throw IllegalArgumentException("Invalid message passed into getHeaderConf(): $message")
        }
    }

    protected fun openDocumentFile(mediaFile: MediaFile) {
        try {
            val uriString = mediaFile.url
            if (uriString.isNullOrEmpty()) {
                val fileName = mediaFile.cacheFilePath ?: run {
                    Logger.i("Document file is empty so could not open it")
                    return
                }
                if (mediaFile.type == MediaFile.Type.Audio) {
                    playAudio(fileName)
                } else {
                    val documentFile = File(fileName)
                    displayDocumentFileContents(documentFile)
                }
            } else {
                val remoteDocumentIntent = Intent(Intent.ACTION_VIEW, Uri.parse(uriString))
                context.startActivity(remoteDocumentIntent)
            }
        } catch (e: Exception) {
            Logger.e(e, e.message)
        }
    }

    internal data class ChatHeaderConfig(
        val agentName: String?, @ColorInt val avatarBorderColor: Int, val avatarUrl: String?,
        @DrawableRes val defaultAvatarDrawable: Int
    )

    private fun playAudio(filename: String) {
        val intent = Intent(context, UjetMediaPreviewActivity::class.java)
        intent.putExtra(MEDIA_TYPE_AUDIO, filename)
        context.startActivity(intent)
    }

    private fun displayDocumentFileContents(documentFile: File) {
        try {
            val documentUri = FileProvider.getUriForFile(
                context,
                String.format("%s.ujet.fileprovider", context.packageName),
                documentFile
            )
            val extension = documentFile.extension
            val fileMimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                extension.lowercase(Locale.getDefault()))
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(documentUri, fileMimeType)
                flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            }
            // Check if there is any application capable of handling document files
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            } else {
                Toast.makeText(
                    context,
                    "Can't open file",
                    Toast.LENGTH_LONG
                ).show()
                w("No app found to open the file")
            }
        } catch (e: Exception) {
            Logger.e(e, e.message)
        }
    }

    companion object {
        private const val RESOURCE_NOT_FOUND = 0
    }
}
