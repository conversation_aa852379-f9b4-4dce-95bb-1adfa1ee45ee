package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.message.VirtualAgentVideoChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.StyleUtil

class VirtualAgentVideoChatMessageViewHolder(
    adapter: <PERSON>t<PERSON>dapterInteractor,
    parent: ViewGroup,
    activity: Activity,
    ujetStyle: UjetStyle
) : MediaChatMessageViewHolder(
    adapter,
    activity,
    ujetStyle,
    inflate(activity, parent, R.layout.ujet_view_chat_message_virtual_agent_document)
) {

    fun bind(
        message: VirtualAgentVideoChatMessage,
        shouldShowAgentNames: <PERSON><PERSON>an,
        isGroupStart: <PERSON>olean,
        isGroupEnd: Boolean
    ): View {
        val documentContainer: View = itemView.findViewById(R.id.document_container)
        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        val background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_virtual_agent)
        StyleUtil.applyDefaultStyle(
            context, ujetStyle, documentContainer, agentMessageStyle?.backgroundColor,
            agentMessageStyle?.cornerRadius, agentMessageStyle?.border, background,
            ujetStyle.chatVirtualAgentMessageBubbleBorderColor, null
        )
        StyleUtil.updateBackgroundStyle(
            documentContainer,
            agentMessageStyle?.backgroundColor,
            agentMessageStyle?.cornerRadius,
            agentMessageStyle?.border
        )
        val fileName: TextView = itemView.findViewById(R.id.file_name)
        UjetViewStyler.styleRemoteChatText(ujetStyle, fileName)
        setupDocumentViewCommonAttributes(itemView, message, message.agentName, shouldShowAgentNames,
            isGroupStart, isGroupEnd)
        setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_virtual_agent),
            mainContainer = documentContainer,
            timestamp = itemView.findViewById(R.id.timestamp),
            isClickable = true,
            isVideo = true,
            videoType = context.getString(R.string.ujet_chat_type_video),
            adapterPosition = adapterPosition
        )
        return itemView
    }
}
