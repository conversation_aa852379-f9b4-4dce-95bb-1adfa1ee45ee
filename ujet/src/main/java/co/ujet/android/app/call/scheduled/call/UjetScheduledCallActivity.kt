package co.ujet.android.app.call.scheduled.call

import android.content.Intent
import android.os.Bundle
import co.ujet.android.activity.UjetActivity
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.call.scheduled.call.ScheduledCallDialogFragment.OnScheduledCallListener
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.libs.logger.Logger

class UjetScheduledCallActivity : UjetBaseActivity(), OnScheduledCallListener {
    private var ujetActivityExtras: Bundle? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ujetActivityExtras = intent.extras?.getBundle(EXTRA_UJET_ACTIVITY_EXTRAS)
        if (FragmentHelper.isEmpty(this)) {
            ScheduledCallDialogFragment.newInstance()
                .show(supportFragmentManager, ScheduledCallDialogFragment.TAG)
        }
    }

    override fun startUjet() {
        finish()
        UjetActivity.start(this, ujetActivityExtras ?: return)
    }

    override fun checkWebViewAvailability() {
        if (WebViewUtil.isWebViewDisabled(this.applicationContext)) {
            Logger.w("Web view is disabled")
            WebViewUtil.handleWebViewUnavailability(this.applicationContext)
        }
    }

    companion object {
        private const val EXTRA_UJET_ACTIVITY_EXTRAS = "ujet_activity_extras"

        /**
         * When scheduled call exists, UJET SDK will start UjetScheduledCallActivity.
         * If an end user cancel the call, it will start UJET SDK with origin Intent Extras.
         * [co.ujet.android.activity.UjetActivityPresenter.resumeCall]
         *
         * @param activity UjetActivity
         */
        @kotlin.jvm.JvmStatic
        fun start(activity: UjetActivity) {
            val intent = Intent(activity, UjetScheduledCallActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            if (activity.intent.extras != null) {
                intent.putExtra(EXTRA_UJET_ACTIVITY_EXTRAS, activity.intent.extras)
            }
            activity.startActivity(intent)
        }
    }
}
