package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.view.Gravity
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.LinearLayout.VERTICAL
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.MarkdownUtil.loadMessageIntoMarkdownTextView
import co.ujet.android.clean.entity.common.Quadruple
import co.ujet.android.common.util.CustomizableLinkUtil.Companion.containsCustomizableLink
import co.ujet.android.common.util.StringUtil
import co.ujet.android.commons.domain.chat.message.QuickReplyLinkButton
import co.ujet.android.commons.domain.chat.message.VirtualAgentChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyButtonsChatMessage
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyListChatMessage
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.extensions.applyColorFilter
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions.Companion.QuickReplyButtonsStyle
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil.dpToPx
import co.ujet.android.ui.util.StyleUtil
import com.google.android.material.button.MaterialButton
import com.google.android.material.button.MaterialButtonToggleGroup
import cx.ujet.android.markdown.widgets.MarkdownTextView

class VirtualAgentChatMessageViewHolder(
    adapter: ChatAdapterInteractor, parent: ViewGroup,
    val activity: Activity, ujetStyle: UjetStyle,
) :
    ChatMessageViewHolder(
        adapter,
        activity,
        ujetStyle,
        inflate(
            activity,
            parent,
            when (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle) {
                QuickReplyButtonsStyle.INDIVIDUAL -> R.layout.ujet_view_chat_message_virtual_agent_individual_pills
                else -> R.layout.ujet_view_chat_message_virtual_agent_grouped_pills
            }
        )
    ) {

    fun bind(
        message: VirtualAgentChatMessage,
        shouldShowAgentNames: Boolean,
        isGroupStart: Boolean,
        isGroupEnd: Boolean,
        isMarkDownSupported: Boolean = false
    ): View {
        val timestamp: TextView = itemView.findViewById(R.id.timestamp)
        val messageTextView: MarkdownTextView = itemView.findViewById(R.id.message)
        UjetViewStyler.styleRemoteChatText(ujetStyle, messageTextView)
        UjetViewStyler.styleRemoteChatLinkText(ujetStyle, messageTextView)
        if (isMarkDownSupported || containsLinks(message.getMessage())) {
            loadMessageIntoMarkdownTextView(context, messageTextView, message.getMessage())
        } else {
            messageTextView.text = message.getMessage()
        }
        // We are hiding message text view when quickReplyButtonsVisible is true (check if(message is VirtualAgentQuickReplyListChatMessage){})
        // and next messages after that are also hidden so explicitly setting message view to visible here.
        messageTextView.visibility = VISIBLE
        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        //When there is no background style available use default existing style
        if (!StyleUtil.isBackgroundStyleAvailable(
                context,
                agentMessageStyle?.backgroundColor,
                agentMessageStyle?.cornerRadius,
                agentMessageStyle?.border
            )
        ) {
            messageTextView.background = ContextCompat.getDrawable(context, R.drawable.ujet_chat_message_background_virtual_agent)
            messageTextView.background.applyColorFilter(ujetStyle.chatVirtualAgentMessageBubbleBorderColor, BlendModeCompat.SRC_IN)
            StyleUtil.applyCornerRadiusToDefaultStyle(agentMessageStyle?.cornerRadius, messageTextView.background as? GradientDrawable)
            StyleUtil.applyBorderWidthToDefaultStyle(
                context, agentMessageStyle?.border?.color, agentMessageStyle?.border?.width,
                ujetStyle.chatVirtualAgentMessageBubbleBorderColor, messageTextView.background as? GradientDrawable
            )
        }
        StyleUtil.updateFontStyle(context, messageTextView, agentMessageStyle?.font)
        StyleUtil.updateBackgroundStyle(
            messageTextView,
            agentMessageStyle?.backgroundColor,
            agentMessageStyle?.cornerRadius,
            agentMessageStyle?.border
        )

        setUpMessageFooter(itemView, message, isGroupEnd)

        setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
        val mainContainer = itemView.findViewById<LinearLayout>(R.id.quick_reply_list_view)
        hideInlineButtons(mainContainer)
        if (message is VirtualAgentQuickReplyListChatMessage) {
            setupInlineButtonView(message, mainContainer)
            //Remove empty header view when content cards with quick replies are shown
            if(message.getMessage().isEmpty()) {
                itemView.findViewById<View>(R.id.agent_avatar_background).visibility = View.GONE
                timestamp.visibility = View.GONE
                itemView.findViewById<View>(R.id.agent_name).visibility = View.GONE
                messageTextView.visibility = View.GONE
            }
        } else if (message is VirtualAgentQuickReplyButtonsChatMessage) {
            // Sticky buttons are handled in ChatInputBarLayout#updateQuickReplyButtons() as they need to
            // stick along with edit text box at the bottom of the chat screen when user scrolls. Here
            // we show sticky button view header text that is shown before sticky buttons along with timestamp.
            if (message.getMessage().isEmpty()) {
                messageTextView.visibility = GONE
                timestamp.visibility = GONE
            } else {
                messageTextView.visibility = VISIBLE
                timestamp.visibility = VISIBLE
            }
        }

        val borderPadding = StyleUtil.getTextPaddingWithInBorder(context, agentMessageStyle?.cornerRadius, agentMessageStyle?.border) ?: 0
        messageTextView.setPaddingRelative(
            messageTextView.paddingStart + borderPadding,
            messageTextView.paddingTop + borderPadding,
            messageTextView.paddingEnd + borderPadding,
            messageTextView.paddingBottom + borderPadding
        )
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_virtual_agent),
            mainContainer = itemView.findViewById(R.id.main_container),
            message = messageTextView,
            timestamp = timestamp,
            adapterPosition = adapterPosition
        )

        return itemView
    }

    private fun setupInlineButtonView(
        message: VirtualAgentQuickReplyListChatMessage,
        mainContainer: LinearLayout
    ) {
        // Inline buttons view shown in grouped list
        val areQuickReplyButtonsVisible = message.quickReplyButtonsVisible
        if (areQuickReplyButtonsVisible) {
            mainContainer.removeAllViews()
            mainContainer.visibility = if (message.quickReplyButtons.isNotEmpty()) {
                VISIBLE
            } else {
                GONE
            }
            // We need to pass activity reference (not context) here to avoid exception "The style on this
            // component requires your app theme to be Theme.AppCompat (or a descendant)"
            val materialButtonsGroup = if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                null
            } else {
                MaterialButtonToggleGroup(
                    activity,
                    null,
                    R.style.Theme_UjetButton_GroupedQuickReplyListButton
                )
            }
            val materialButtonsGroupParams: RelativeLayout.LayoutParams =
                RelativeLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
                )
            materialButtonsGroup?.layoutParams = materialButtonsGroupParams
            materialButtonsGroup?.orientation = VERTICAL
            materialButtonsGroup?.isSingleSelection = true
            message.quickReplyButtons.forEachIndexed { index, inlineButton ->
                // Same as MaterialButtonToggleGroup, we need to pass activity reference here too
                val inlineButtonStyle = if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                    R.style.UjetButton_IndividualQuickReplyListButton
                } else {
                    R.style.Theme_UjetButton_GroupedQuickReplyListButton
                }
                val inlineButtonView = MaterialButton(
                    activity,
                    null,
                    inlineButtonStyle
                )
                inlineButtonView.visibility = VISIBLE
                val layoutWidth = if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                    ViewGroup.LayoutParams.WRAP_CONTENT
                } else {
                    ViewGroup.LayoutParams.MATCH_PARENT
                }
                val materialButtonParams: RelativeLayout.LayoutParams = RelativeLayout.LayoutParams(
                    layoutWidth, ViewGroup.LayoutParams.WRAP_CONTENT
                )
                if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                    materialButtonParams.marginStart = getPixelSize(10)
                }
                inlineButtonView.layoutParams = materialButtonParams
                inlineButtonView.text = inlineButton.title
                val quadruple = if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                    inlineButtonView.gravity = Gravity.CENTER_VERTICAL
                    Quadruple(4, 16, 4, 16)
                } else {
                    inlineButtonView.gravity = Gravity.CENTER
                    inlineButtonView.strokeWidth = getPixelSize(2)
                    Quadruple(0, 8,0, 20)
                }
                // Explicitly set inset padding otherwise default inset padding is added between material buttons
                inlineButtonView.insetTop = getPixelSize(quadruple.first)
                inlineButtonView.insetBottom = getPixelSize(quadruple.first)
                inlineButtonView.cornerRadius = getPixelSize(quadruple.second)
                inlineButtonView.isAllCaps = false
                inlineButtonView.minHeight = getPixelSize(52)
                inlineButtonView.setPaddingRelative(
                    getPixelSize(quadruple.fourth),
                    getPixelSize(quadruple.third),
                    getPixelSize(quadruple.fourth),
                    getPixelSize(quadruple.third)
                )
                if (inlineButton is QuickReplyLinkButton) {
                    inlineButtonView.paintFlags = Paint.UNDERLINE_TEXT_FLAG
                }
                inlineButtonView.setOnClickListener {
                    if (inlineButton is QuickReplyLinkButton && inlineButton.link != null) {
                        try {
                            context.startActivity(
                                Intent(
                                    Intent.ACTION_VIEW,
                                    Uri.parse(inlineButton.link)
                                )
                            )
                        } catch (e: ActivityNotFoundException) {
                            Logger.w(e, e.message)
                        }
                    }
                    adapter.getChatItemClickListener()?.onQuickReplyClicked(message, inlineButton)
                }
                AccessibilityUtil.addButtonRoleToToggleButton(
                    inlineButtonView,
                    context.getString(R.string.ujet_chat_activate_text_talkback)
                )
                configureQuickReplyButton(index, inlineButtonView)
                if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
                    mainContainer.addView(inlineButtonView)
                } else {
                    materialButtonsGroup?.addView(inlineButtonView)
                    // Set max lines after adding child view to group to allow material buttons to support
                    // multi lines, otherwise it wont work
                    inlineButtonView.maxLines = 2
                }
            }
            materialButtonsGroup?.let {
                mainContainer.addView(it)
            }
        } else {
            hideInlineButtons(mainContainer)
        }
    }

    private fun hideInlineButtons(mainContainer: LinearLayout) {
        // Hide quickRepliesViews when quick replies are answered
        mainContainer.removeAllViews()
        mainContainer.visibility = GONE
    }

    private fun configureQuickReplyButton(index: Int, button: MaterialButton) {
        if (ujetStyle.customStylesOptions?.chatQuickReplyButtonsStyle == QuickReplyButtonsStyle.INDIVIDUAL) {
            UjetViewStyler.styleChatQuickReplyIndividualButton(ujetStyle, index, button)
        } else {
            UjetViewStyler.stylePrimaryButton(ujetStyle, button)
        }
    }

    private fun getPixelSize(dpSize: Int): Int {
        return dpToPx(activity.applicationContext, dpSize).toInt()
    }

    private fun containsLinks(message: String): Boolean {
        return containsCustomizableLink(message) || StringUtil.isUrlAvailable(message)
    }
}
