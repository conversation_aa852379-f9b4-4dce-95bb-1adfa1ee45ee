package co.ujet.android.app.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;

import java.util.List;

import co.ujet.android.R;
import co.ujet.android.common.util.EmailUtil;

/**
 * Created by mimu on 12/9/16.
 * Start the email client
 */
public class EmailClient {
    public boolean isAvailable(Context context) {
        Intent intent = new Intent(Intent.ACTION_SEND);
        intent.setType("message/rfc822");
        PackageManager packageManager = context.getPackageManager();
        List<ResolveInfo> intentActivities = packageManager.queryIntentActivities(intent, 0);
        return intentActivities.size() > 0;
    }

    public void start(Activity activity, String email, String subject) {
        EmailUtil.startEmailActivity(activity, email, activity.getString(R.string.ujet_email_subject, String.format("[%s]", subject)));
    }
}
