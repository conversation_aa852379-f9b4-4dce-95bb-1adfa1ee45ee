package co.ujet.android.app.chat

import android.content.Context
import android.net.Uri
import java.io.File
import java.io.IOException

private const val DEFAULT_BLOCK_SIZE = 4096

/**
 * Copies a file from the cache directory to the user-selected destination URI.
 */
fun copyFileToUri(context: Context, sourceFile: File, destinationUri: Uri) {
    try {
        context.contentResolver.openOutputStream(destinationUri)?.use { outputStream ->
            sourceFile.inputStream().use { inputStream ->
                inputStream.copyTo(outputStream, DEFAULT_BLOCK_SIZE)
            }
            outputStream.flush()
        }
    } catch (e: IOException) {
        e.printStackTrace()
    }
}
