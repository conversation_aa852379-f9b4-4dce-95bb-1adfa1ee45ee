package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.R
import co.ujet.android.app.chat.AttachmentsAdapter
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.ChatMediaUtil
import co.ujet.android.app.chat.ChatMediaUtil.THUMBNAIL_DESIRED_HEIGHT
import co.ujet.android.app.chat.ChatMediaUtil.initializeDownloadAttachmentIconView
import co.ujet.android.app.chat.ChatMediaUtil.loadBitmapFromCacheFile
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.chat.message.DocumentChatMessage
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.data.repository.UploadRepository.Companion.THUMBNAIL_DESIRED_SIZE
import co.ujet.android.ui.style.UjetStyle

abstract class MediaChatMessageViewHolder(adapter: ChatAdapterInteractor, activity: Activity,
                                          ujetStyle: UjetStyle, itemView: View) :
    ChatMessageViewHolder(adapter, activity.applicationContext, ujetStyle, itemView) {
    init {
        if (ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled(context)) {
            setTimestampTextSize(itemView.findViewById(R.id.timestamp))
        }
    }

    protected fun setupDocumentViewCommonAttributes(
        view: View,
        message: DocumentChatMessage,
        agentName: String?,
        shouldShowAgentNames: Boolean,
        isGroupStart: Boolean,
        isGroupEnd: Boolean
    ) {
        val documentContainer: View = view.findViewById(R.id.document_container)
        documentContainer.setOnClickListener {
            openDocumentFile(message.mediaFile)
        }
        val fileName: TextView = view.findViewById(R.id.file_name)
        fileName.text = message.mediaFile.filename
        setUpMessageFooter(view, message, isGroupEnd)

        val imageView: ImageView = view.findViewById(R.id.file_icon)
        imageView.setImageResource(message.getThumbnail())
        initializeDownloadAttachmentIconView(view.findViewById(R.id.download_attachment_icon), ujetStyle)
        setAgentName(view, agentName, isGroupStart, shouldShowAgentNames)
    }

    protected fun setupDownloadAttachmentIconView(
        downloadAttachmentView: ImageView?,
        ujetStyle: UjetStyle,
        onDownloadIconClicked: () -> Unit
    ) {
        initializeDownloadAttachmentIconView(downloadAttachmentView, ujetStyle, onDownloadIconClicked)
    }

    protected fun loadBitmapFromCacheFile(lifecycleOwner: LifecycleOwner, mainContainer: RelativeLayout?,
                             recyclerView: RecyclerView?, mediaFiles: ArrayList<MediaFile>,
                             mediaImageAdapter: AttachmentsAdapter?) {
        loadBitmapFromCacheFile(context, lifecycleOwner, mediaFiles
        ) { bitmapList ->
            mainContainer?.visibility = View.VISIBLE
            if (bitmapList?.size == 1) {
                val bitmap = bitmapList[0]
                val bitmapWidth = bitmap?.width ?: THUMBNAIL_DESIRED_SIZE
                val bitmapHeight = bitmap?.height ?: THUMBNAIL_DESIRED_HEIGHT
                recyclerView?.let {
                    ChatMediaUtil.updateSingleImageView(
                        it,
                        bitmapWidth,
                        bitmapHeight
                    )
                }
                mediaImageAdapter?.notifyDataSetChanged()
            }
            mediaImageAdapter?.updateBitmapList(bitmapList)
            recyclerView?.post {
                mediaImageAdapter?.let { adapter ->
                    if (adapter.itemCount > 0) {
                        recyclerView.scrollToPosition(adapter.itemCount - 1)
                    }
                }
            }
        }
    }
}
