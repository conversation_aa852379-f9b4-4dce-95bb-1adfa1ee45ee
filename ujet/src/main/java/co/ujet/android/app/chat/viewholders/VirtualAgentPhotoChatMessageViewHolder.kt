package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import co.ujet.android.R
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.commons.domain.chat.message.VirtualAgentPhotoChatMessage
import co.ujet.android.commons.extensions.loadOrSetInvisible
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.util.StyleUtil

class VirtualAgentPhotoChatMessageViewHolder(adapter: ChatAdapterInteractor, parent: ViewGroup,
                                             activity: Activity, ujetStyle: UjetStyle) :
    MediaChatMessageViewHolder(adapter, activity, ujetStyle,
        inflate(activity, parent, R.layout.ujet_view_chat_message_virtual_agent_photo)) {

    fun bind(
        message: VirtualAgentPhotoChatMessage,
        shouldShowAgentNames: <PERSON><PERSON><PERSON>,
        isGroupStart: <PERSON><PERSON>an,
        isGroupEnd: <PERSON>olean
    ): View {
        val imageView: ImageView = itemView.findViewById(R.id.photo_image_view)
        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        val cornerRadius = agentMessageStyle?.cornerRadius?.toFloat() ?: 0f
        val imageRadius = if (cornerRadius > 0) {
            ujetStyle.dpToPx(cornerRadius).toInt()
        } else {
            getCachedDimension(co.ujet.android.ui.R.dimen.ujet_chat_corner_radius)
        }
        imageView.loadOrSetInvisible(message.photoUrl, imageRadius)
        StyleUtil.updateBackgroundStyle(
            imageView,
            agentMessageStyle?.backgroundColor,
            agentMessageStyle?.cornerRadius,
            agentMessageStyle?.border
        )
        setUpMessageFooter(itemView, message, isGroupEnd)
        setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
        AccessibilityUtil.addChatUserRole(
            userRole = context.getString(R.string.ujet_chat_virtual_agent),
            mainContainer = imageView,
            timestamp = itemView.findViewById(R.id.timestamp),
            isImage = true,
            isClickable = true,
            imageType = context.getString(R.string.ujet_chat_type_image),
            adapterPosition = adapterPosition
        )
        return itemView
    }
}
