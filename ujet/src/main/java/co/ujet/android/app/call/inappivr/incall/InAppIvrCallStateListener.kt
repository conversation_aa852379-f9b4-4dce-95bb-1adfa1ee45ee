package co.ujet.android.app.call.inappivr.incall

import androidx.fragment.app.FragmentManager
import co.ujet.android.smartaction.ui.cobrowse.CoBrowseUI

interface InAppIvrCallStateListener {
    /**
     * Customer calls redirection number
     */
    fun onReady()

    /**
     * Action only call ends
     */
    fun onEnd()

    fun onWarning()

    fun onNetworkReconnected()

    fun getCoBrowseUI(): CoBrowseUI?

    fun getMainFragmentManager(): FragmentManager?

    fun updateAgentNames()

    fun displayPendingSmartAction()
}
