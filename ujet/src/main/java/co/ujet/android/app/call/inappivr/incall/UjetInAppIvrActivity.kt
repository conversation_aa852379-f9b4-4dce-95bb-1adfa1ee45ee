package co.ujet.android.app.call.inappivr.incall

import android.content.Context
import android.content.Intent
import android.os.Bundle
import co.ujet.android.clean.presentation.UjetBaseActivity
import co.ujet.android.common.util.WebViewUtil
import co.ujet.android.libs.logger.Logger

class UjetInAppIvrActivity : UjetBaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val fragment = supportFragmentManager.findFragmentByTag(InAppIvrCallDialogFragment.TAG)
        if (fragment == null) {
            InAppIvrCallDialogFragment
                .newInstance()
                .show(supportFragmentManager, InAppIvrCallDialogFragment.TAG)
        }
    }

    override fun checkWebViewAvailability() {
        if (WebViewUtil.isWebViewDisabled(applicationContext)) {
            Logger.w("Web view is disabled")
            WebViewUtil.handleWebViewUnavailability(applicationContext)
        }
    }

    companion object {
        @kotlin.jvm.JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, UjetInAppIvrActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            context.startActivity(intent)
        }
    }
}
