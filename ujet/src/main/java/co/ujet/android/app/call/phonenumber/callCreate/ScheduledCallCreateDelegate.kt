package co.ujet.android.app.call.phonenumber.callCreate

import android.content.Context
import co.ujet.android.R.string
import co.ujet.android.api.ApiManager
import co.ujet.android.app.call.phonenumber.PhoneNumberInputContract.View
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.language.usecase.ChooseLanguage
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId.RequestValues
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId.ResponseValue
import co.ujet.android.common.util.RecordingPermissionUtils.Companion.getCachedRecordingPermission
import co.ujet.android.data.LocalRepository
import co.ujet.android.data.UjetContext
import co.ujet.android.data.constant.CallType.Scheduled
import co.ujet.android.data.model.Call
import co.ujet.android.service.call.CallFactory
import co.ujet.android.service.call.CallFactory.Callback
import co.ujet.android.service.common.CustomDataDelegate
import java.util.*

/**
 * Created by allen on 24/08/2017.
 */
class ScheduledCallCreateDelegate(
    private val context: Context,
    private val ujetContext: UjetContext,
    private val apiManager: ApiManager,
    private val localRepository: LocalRepository,
    private val view: View,
    private val useCaseHandler: UseCaseHandler,
    private val chooseLanguage: ChooseLanguage,
    private val getSelectedMenuId: GetSelectedMenuId,
    private val deflectionType: String?
) : CallCreateDelegate {

    override fun init() {
        val existingCall = localRepository.call
        if (existingCall != null && existingCall.getType() == Scheduled) {
            view.finish()
        }
    }

    override fun create(endUserPhoneNumber: String?, ivrPhoneNumber: String?, scheduledTime: Date?) {
        // To create auth token with phone number
        useCaseHandler.executeImmediate(
            getSelectedMenuId,
            RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    val menuId = response.menuId
                    create(endUserPhoneNumber, menuId, scheduledTime)
                }

                override fun onError() {
                    val errorStr = context.getString(string.ujet_error_call_create_fail_android)
                    view.showErrorDialog(errorMessage = errorStr)
                }
            })
    }

    private fun create(endUserPhoneNumber: String?, menuId: Int, scheduledTime: Date?) {
        val recordingPermission = getRecordingPermission()
        CallFactory(context, ujetContext, apiManager, chooseLanguage, useCaseHandler, localRepository, CustomDataDelegate())
            .createScheduledCall(menuId, endUserPhoneNumber, recordingPermission, scheduledTime, object : Callback {
                override fun onCallCreated(call: Call) {
                    if (view.isActive) {
                        view.showScheduleConfirm(call.id, deflectionType)
                    }
                }

                override fun onVoipNotAvailable() {
                    val errorStr = context.getString(string.ujet_error_call_create_fail_android)
                    view.showErrorDialog(errorMessage = errorStr)
                }

                override fun onCallCreateFailed(errorCode: Int, message: String?) {
                    val errorStr = context.getString(string.ujet_error_call_create_fail_android)
                    view.showErrorDialog(errorMessage = errorStr)
                }

                override fun onCallNotSupported() {
                    val errorStr = context.getString(string.ujet_not_supported_error_message)
                    view.showErrorDialog(errorMessage = errorStr)
                }
            })
    }

    private fun getRecordingPermission() = getCachedRecordingPermission(context.applicationContext)
}
