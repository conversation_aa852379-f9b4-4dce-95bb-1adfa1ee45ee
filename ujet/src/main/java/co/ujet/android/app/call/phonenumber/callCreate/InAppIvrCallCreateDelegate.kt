package co.ujet.android.app.call.phonenumber.callCreate

import android.content.Context
import co.ujet.android.R.string
import co.ujet.android.UjetPayloadType.CustomData
import co.ujet.android.UjetTokenCallback
import co.ujet.android.app.call.phonenumber.PhoneNumberInputContract.View
import co.ujet.android.clean.domain.EmptyUseCaseCallback
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.company.usecase.GetCompany
import co.ujet.android.clean.domain.inappivrcall.usecase.InformInAppIvrCall
import co.ujet.android.clean.domain.inappivrcall.usecase.InformInAppIvrCall.RequestValues
import co.ujet.android.clean.domain.language.usecase.ChooseLanguage
import co.ujet.android.clean.domain.menu.usecase.GetSelectedMenuId
import co.ujet.android.common.util.PhoneUtil.dial
import co.ujet.android.data.UjetContext
import co.ujet.android.internal.Injection
import co.ujet.android.internal.UjetInternal
import java.util.*

/**
 * Create a In-app IVR call with end user phone number.
 * Then re-authenticate with adding the end user phone number to auth params.
 */
class InAppIvrCallCreateDelegate(
    private val context: Context,
    private val ujetContext: UjetContext,
    private val useCaseHandler: UseCaseHandler,
    private val getCompany: GetCompany,
    private val getSelectedMenuId: GetSelectedMenuId,
    private val informInAppIvrCall: InformInAppIvrCall,
    private val chooseLanguage: ChooseLanguage,
    private val view: View,
    private val deflectionType: String?
) : CallCreateDelegate {
    private var ivrPhoneNumber: String? = null
    private var isIvrPhoneNumberSet = false
    private var endUserPhoneNumber: String? = null
    private var isEndUserPhoneNumberSet = false
    private var menuId = 0
    private var language: String? = null
    private var isCustomDataSet = false
    private var signedCustomData: String? = null
    private var unsignedCustomData: Map<String, Map<String, Any>>? = null

    override fun init() {
        initIvrPhoneNumber()
    }

    override fun create(endUserPhoneNumber: String?, ivrPhoneNumber: String?, scheduledTime: Date?) {
        this.endUserPhoneNumber = endUserPhoneNumber
        isEndUserPhoneNumberSet = true
        if (!ivrPhoneNumber.isNullOrEmpty()) {
            this.ivrPhoneNumber = ivrPhoneNumber
        }
        if (isIvrPhoneNumberSet) {
            informPhoneNumberAndCall()
        }
    }

    /**
     * 1. IVR phone number from [co.ujet.android.Ujet.startIvr]
     * 2. Company's phone number
     * 3. Fallback phone number from [co.ujet.android.UjetOption.fallbackPhoneNumber]
     */
    private fun initIvrPhoneNumber() {
        setDefaultIvrPhoneNumber()
    }

    private fun setDefaultIvrPhoneNumber() {
        ivrPhoneNumber = ujetContext.ivrPhoneNumber
        isIvrPhoneNumberSet = !ivrPhoneNumber.isNullOrEmpty()
        if (isIvrPhoneNumberSet) {
            informPhoneNumberAndCall()
        } else {
            getIvrPhoneNumberFromCompany()
        }
    }

    private fun getIvrPhoneNumberFromCompany() {
        useCaseHandler.execute(
            getCompany,
            GetCompany.RequestValues(false),
            object : UseCaseCallback<GetCompany.ResponseValue> {
                override fun onSuccess(response: GetCompany.ResponseValue) {
                    ivrPhoneNumber = response.company.phoneNumber
                    isIvrPhoneNumberSet = true
                    informPhoneNumberAndCall()
                }

                override fun onError() {
                    ivrPhoneNumber = Injection.provideConfiguration().fallbackPhoneNumber
                    isIvrPhoneNumberSet = true
                    informPhoneNumberAndCall()
                }
            })
    }

    private fun informPhoneNumberAndCall() {
        if (!isEndUserPhoneNumberSet || !isIvrPhoneNumberSet) {
            return
        }
        if (ivrPhoneNumber.isNullOrEmpty()) {
            view.showErrorDialog(errorMessage = context.getString(string.ujet_error_request))
            return
        }

        // If end user phone number is empty, just dial to customer service.
        when {
            endUserPhoneNumber.isNullOrEmpty() -> call()
            else -> {
                getCachedMenuId()
                getPreferredLanguage()
                requestSignedCustomData()
            }
        }
    }

    private fun getCachedMenuId() {
        useCaseHandler.execute(
            getSelectedMenuId,
            GetSelectedMenuId.RequestValues(ujetContext.directAccessKey),
            object : UseCaseCallback<GetSelectedMenuId.ResponseValue> {
                override fun onSuccess(response: GetSelectedMenuId.ResponseValue) {
                    updateMenuId(response.menuId)
                }

                override fun onError() {
                    view.showErrorDialog(errorMessage = context.getString(string.ujet_error_request))
                }
            })
    }

    private fun getPreferredLanguage() {
        useCaseHandler.execute(
            chooseLanguage,
            ChooseLanguage.RequestValues(),
            object : EmptyUseCaseCallback<ChooseLanguage.ResponseValue>() {
                override fun onSuccess(response: ChooseLanguage.ResponseValue) {
                    updateLanguage(response.languageCode)
                }
            })
    }

    // TODO: Should we extract this out to Use Case using the data source from UjetRequestListener?
    private fun requestSignedCustomData() {
        UjetInternal.getUjetRequestListener()
            .onSignPayloadRequest(
                HashMap(),
                CustomData,
                object : UjetTokenCallback {
                    override fun onToken(jwt: String?) {
                        updateCustomData(jwt)
                    }

                    override fun onError() {
                        updateCustomData(null)
                    }
                }
            )
    }

    private fun updateMenuId(menuId: Int) {
        this.menuId = menuId
        onInformDataUpdated()
    }

    private fun updateLanguage(language: String) {
        this.language = language
        onInformDataUpdated()
    }

    private fun updateCustomData(signedCustomData: String?) {
        isCustomDataSet = true
        this.signedCustomData = signedCustomData
        if (ujetContext.unsignedCustomData != null) {
            unsignedCustomData = ujetContext.unsignedCustomData.getData()
        }
        onInformDataUpdated()
    }

    private fun onInformDataUpdated() {
        if (language == null || !isCustomDataSet) return
        val requestValues = RequestValues.create(
            endUserPhoneNumber, menuId, language, signedCustomData, unsignedCustomData
        )
        informInAppIvrCall(requestValues)
    }

    private fun informInAppIvrCall(requestValues: RequestValues) {
        useCaseHandler.execute(
            informInAppIvrCall,
            requestValues,
            object : UseCaseCallback<InformInAppIvrCall.ResponseValue> {
                override fun onSuccess(response: InformInAppIvrCall.ResponseValue) {
                    call()
                }

                override fun onError() {
                    view.showErrorDialog(errorMessage = context.getString(string.ujet_error_request))
                }
            })
    }

    private fun call() {
        sendPhoneDialedEvent()
        view.finish()
        dial(context, ivrPhoneNumber)
    }

    private fun sendPhoneDialedEvent() {
        Injection.provideDeflectedEventManager(context).sendPhoneDialedEvent("phone_dialed", ivrPhoneNumber ?: return, deflectionType)
    }

}
