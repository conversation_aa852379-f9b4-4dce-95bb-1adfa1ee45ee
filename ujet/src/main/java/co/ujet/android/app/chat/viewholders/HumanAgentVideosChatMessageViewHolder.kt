package co.ujet.android.app.chat.viewholders

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.R
import co.ujet.android.app.chat.AttachmentsAdapter
import co.ujet.android.app.chat.ChatAdapterInteractor
import co.ujet.android.app.chat.ChatMediaUtil.GRID_VIEW_MAX_COLUMNS
import co.ujet.android.app.chat.ChatMediaUtil.MEDIA_TYPE_VIDEO
import co.ujet.android.app.chat.ChatMediaUtil.getGridItemWidth
import co.ujet.android.app.chat.ChatMediaUtil.hideLoadingAndShowDownloadIcon
import co.ujet.android.app.chat.ChatMediaUtil.onDownloadAttachmentIconClicked
import co.ujet.android.app.chat.ChatMediaUtil.updateDownloadAttachmentView
import co.ujet.android.app.chat.ChatMediaUtil.updateRecyclerViewParams
import co.ujet.android.commons.domain.chat.message.HumanAgentVideoChatMessage
import co.ujet.android.ui.style.UjetStyle

class HumanAgentVideosChatMessageViewHolder(
    adapter: ChatAdapterInteractor, parent: ViewGroup, activity: Activity, ujetStyle: UjetStyle,
    private val lifecycleOwner: LifecycleOwner
) :
    MediaChatMessageViewHolder(
        adapter, activity, ujetStyle, inflate(
            activity, parent,
            R.layout.ujet_view_chat_message_human_agent_videos
        )
    ) {
    private var mediaImageAdapter: AttachmentsAdapter? = null

    fun bind(
        message: HumanAgentVideoChatMessage,
        shouldShowAgentNames: Boolean,
        isGroupStart: Boolean,
        isGroupEnd: Boolean,
    ): View {
        val mainContainer = itemView.findViewById<RelativeLayout>(R.id.main_container)
        mainContainer.visibility = View.GONE
        if (!message.isVisible) {
            return itemView
        }
        val listener = adapter.getChatItemClickListener()
        val agentMessageStyle = configuration.ujetStylesOptions?.chatStyles?.agentMessageBubbles
        val cornerRadius = agentMessageStyle?.cornerRadius?.toFloat() ?: 0f
        val imageRadius = if (cornerRadius > 0) {
            ujetStyle.dpToPx(cornerRadius)
        } else {
            16f
        }
        hideLoadingAndShowDownloadIcon(itemView)
        val itemWidth = getGridItemWidth(context, message.mediaFiles)
        val recyclerView = itemView.findViewById<RecyclerView>(R.id.videos_recycler_view).apply {
            adapter = AttachmentsAdapter(
                context, message.mediaFiles, imageRadius,
                itemView.findViewById(R.id.timestamp), object : MediaClickedCallback {
                    override fun onMediaClicked(position: Int) {
                        listener?.onChatItemClicked(message, position)
                    }
                }, true, null, itemWidth
            )
                .also {
                    mediaImageAdapter = it
                }
            val numColumns = getGridViewColumnsCount(message.mediaFiles.size)
            val gridLayoutManager = GridLayoutManager(context, numColumns)
            layoutManager = gridLayoutManager
        }
        recyclerView?.let {
            updateRecyclerViewParams(it, message.mediaFiles, itemWidth)
        }
        itemView.findViewById<ImageView>(R.id.download_attachment_icon)?.apply {
            setupDownloadAttachmentIconView(this, ujetStyle) {
                onDownloadAttachmentIconClicked(
                    itemView, ujetStyle.colorPrimary,
                    adapter.getChatItemClickListener(), message.mediaFiles, adapterPosition, MEDIA_TYPE_VIDEO
                )
            }
            contentDescription = context.getString(R.string.ujet_chat_save_video_accessibility)
        }
        setUpMessageFooter(itemView, message, isGroupEnd)
        setUpMessageHeader(itemView, message, shouldShowAgentNames, isGroupStart)
        loadBitmapFromCacheFile(lifecycleOwner, mainContainer, recyclerView, message.mediaFiles, mediaImageAdapter)
        return itemView
    }

    fun updateDownloadAttachmentView(isDownloadSucceeded: Boolean) {
        updateDownloadAttachmentView(itemView, ujetStyle, isDownloadSucceeded)
        refreshMedia()
    }

    private fun refreshMedia() {
        // When media is downloaded in Android 9 and below version using create document launcher,
        // sometimes, media is hidden / cleared after downloaded so refreshing here makes them visible.
        mediaImageAdapter?.notifyDataSetChanged()
    }

    private fun getGridViewColumnsCount(mediaFilesSize: Int): Int {
        return if (mediaFilesSize >= GRID_VIEW_MAX_COLUMNS) {
            GRID_VIEW_MAX_COLUMNS
        } else {
            mediaFilesSize
        }
    }
}
