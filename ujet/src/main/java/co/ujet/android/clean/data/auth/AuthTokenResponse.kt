package co.ujet.android.clean.data.auth

import co.ujet.android.commons.libs.uson.SerializedName

class AuthTokenResponse {
    @co.ujet.android.commons.libs.uson.SerializedName("auth_token")
    val authToken: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("log_proxy_auth_token")
    val logProxyAuthToken: String? = null

    @co.ujet.android.commons.libs.uson.SerializedName("log_proxy_url")
    val logProxyUrl: String? = null
}
