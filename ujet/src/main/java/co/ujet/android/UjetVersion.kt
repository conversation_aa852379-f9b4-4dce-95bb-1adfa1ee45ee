package co.ujet.android

object UjetVersion {
    // Refers to UJET SDK Version, used in logs, sent to server to determine client SDK version and
    // publishing RC and prod apps into firebase (.github/get_version.sh). Need to be changed in
    // every new SDK release.
    const val BUILD = "2.13.1"

    // Refers to sprint number and used in publishing QA app into firebase (.github/get_version.sh).
    // Need to be changed in every new PI sprint.
    const val SPRINT_NUMBER = "24.5"
}
