package co.ujet.android.service.call

import android.content.Context
import android.webkit.URLUtil
import co.ujet.android.clean.domain.UseCase.UseCaseCallback
import co.ujet.android.clean.domain.UseCaseHandler
import co.ujet.android.clean.domain.audiblemessage.usecase.GetAudibleMessages
import co.ujet.android.clean.domain.audiblemessage.usecase.GetAudibleMessages.RequestValues
import co.ujet.android.clean.domain.audiblemessage.usecase.GetAudibleMessages.ResponseValue
import co.ujet.android.common.TaskCallback
import co.ujet.android.common.audio.AudioPlayer
import co.ujet.android.common.util.NetworkUtil
import java.io.File

/**
 * Download and play call audible messages
 */
class CallAudibleMessagePlayer internal constructor(
    context: Context, 
    useCaseHandler: UseCaseHandler, 
    getAudibleMessages: GetAudibleMessages
) {
    interface OnCompletionListener {
        fun onWaitingMessagePlay()
    }

    private val applicationContext: Context = context.applicationContext
    private val useCaseHandler: UseCaseHandler
    private val getAudibleMessages: GetAudibleMessages
    private val audioPlayer: AudioPlayer?
    private var playStatus: Int
    private var isRecordingPermitted = false
    private var connectingAudioUrl: String? = null
    private var recordingAudioUrl: String? = null
    private var waitingAudioUrl: String? = null
    private var connectingAudioFileName: String? = null
    private var recordingAudioFileName: String? = null
    private var waitingAudioFileName: String? = null
    private var connectingAudioFileStatus = 0
    private var recordingAudioFileStatus = 0
    private var waitingAudioFileStatus = 0
    private var queueLevelAudioUrl: String? = null

    // Call when connecting audio is played
    private var completionListener: OnCompletionListener? = null
    private fun downloadAudibleMessages() {
        connectingAudioFileStatus = FILE_STATUS_READY
        recordingAudioFileStatus = FILE_STATUS_READY
        waitingAudioFileStatus = FILE_STATUS_READY
        useCaseHandler.execute(
            getAudibleMessages,
            RequestValues(false),
            object : UseCaseCallback<ResponseValue> {
                override fun onSuccess(response: ResponseValue) {
                    val audibleMessages = response.audibleMessages
                    connectingAudioUrl = audibleMessages.connecting.audioUrl
                    recordingAudioUrl = audibleMessages.recording.audioUrl
                    waitingAudioUrl = audibleMessages.waiting?.audioUrl ?: audibleMessages.holding.audioUrl
                    downloadConnectingAudioFile()
                    downloadRecordingAudioFile()
                    downloadWaitingAudioFile()
                }

                override fun onError() {
                    connectingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                    recordingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                    waitingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                    tryToPlayConnectingAudioFile()
                }
            })
    }

    private fun downloadConnectingAudioFile() {
        connectingAudioFileStatus = FILE_STATUS_DOWNLOADING
        val fileName = URLUtil.guessFileName(connectingAudioUrl, null, null)
        val filePath = File(applicationContext.applicationContext.cacheDir.absolutePath, fileName).absolutePath
        // [UJET-8109] Timeout for connecting and recording audible message
        NetworkUtil.downloadFile(connectingAudioUrl, filePath, 10 * 1000, object : TaskCallback<String> {
            override fun onTaskSuccess(fileName: String) {
                connectingAudioFileName = fileName
                connectingAudioFileStatus = FILE_STATUS_DOWNLOADED
                tryToPlayConnectingAudioFile()
            }

            override fun onTaskFailure() {
                connectingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                tryToPlayConnectingAudioFile()
            }
        })
    }

    private fun downloadRecordingAudioFile() {
        recordingAudioFileStatus = FILE_STATUS_DOWNLOADING
        val fileName = URLUtil.guessFileName(recordingAudioUrl, null, null)
        val filePath = File(applicationContext.applicationContext.cacheDir.absolutePath, fileName).absolutePath
        // [UJET-8109] Timeout for connecting and recording audible message
        NetworkUtil.downloadFile(recordingAudioUrl, filePath, 10 * 1000, object : TaskCallback<String> {
            override fun onTaskSuccess(fileName: String) {
                recordingAudioFileName = fileName
                recordingAudioFileStatus = FILE_STATUS_DOWNLOADED
                tryToPlayRecordingAudioFile()
            }

            override fun onTaskFailure() {
                recordingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                tryToPlayRecordingAudioFile()
            }
        })
    }

    private fun downloadWaitingAudioFile() {
        waitingAudioFileStatus = FILE_STATUS_DOWNLOADING
        val fileName = URLUtil.guessFileName(waitingAudioUrl, null, null)
        val filePath = File(applicationContext.applicationContext.cacheDir.absolutePath, fileName).absolutePath
        NetworkUtil.downloadFile(waitingAudioUrl, filePath, object : TaskCallback<String> {
            override fun onTaskSuccess(fileName: String) {
                waitingAudioFileName = fileName
                waitingAudioFileStatus = FILE_STATUS_DOWNLOADED
                tryToPlayWaitingAudioFile()
            }

            override fun onTaskFailure() {
                waitingAudioFileStatus = FILE_STATUS_DOWNLOAD_FAILED
                tryToPlayWaitingAudioFile()
            }
        })
    }

    fun play(isRecordingPermitted: Boolean, queueLevelAudioUrl: String?,
             completionListener: OnCompletionListener?) {
        if (playStatus != PLAY_STATUS_READY) {
            return
        }
        this.isRecordingPermitted = isRecordingPermitted
        this.queueLevelAudioUrl = queueLevelAudioUrl
        this.completionListener = completionListener
        playStatus = PLAY_STATUS_PLAYING_CONNECTING
        tryToPlayConnectingAudioFile()
    }

    val isReady: Boolean
        get() = playStatus == PLAY_STATUS_READY
    val isStoppable: Boolean
        get() = PLAY_STATUS_PLAYING_WAITING <= playStatus

    fun reset() {
        if (audioPlayer?.isPlaying == true) {
            audioPlayer.stop()
            audioPlayer.clear()
        }
        playStatus = PLAY_STATUS_READY
    }

    fun stop() {
        if (audioPlayer?.isPlaying == true) {
            audioPlayer.stop()
            audioPlayer.clear()
        }
        playStatus = PLAY_STATUS_STOPPED
    }

    fun clear() {
        completionListener = null
    }

    private fun tryToPlayConnectingAudioFile() {
        if (playStatus != PLAY_STATUS_PLAYING_CONNECTING) return
        if (connectingAudioFileStatus == FILE_STATUS_DOWNLOADED) {
            audioPlayer?.play(connectingAudioFileName, false, object : TaskCallback<Void?> {
                override fun onTaskSuccess(result: Void?) {
                    playRecordingAudio()
                }

                override fun onTaskFailure() {
                    playRecordingAudio()
                }
            })
        } else if (connectingAudioFileStatus == FILE_STATUS_DOWNLOAD_FAILED) {
            playRecordingAudio()
        }
    }

    private fun playRecordingAudio() {
        playStatus = PLAY_STATUS_PLAYING_RECORDING
        if (isRecordingPermitted) {
            tryToPlayRecordingAudioFile()
        } else {
            playWaitingAudio()
        }
    }

    private fun tryToPlayRecordingAudioFile() {
        if (playStatus != PLAY_STATUS_PLAYING_RECORDING) return
        if (recordingAudioFileStatus == FILE_STATUS_DOWNLOADED) {
            audioPlayer?.play(recordingAudioFileName, false, object : TaskCallback<Void?> {
                override fun onTaskSuccess(result: Void?) {
                    playWaitingAudio()
                }

                override fun onTaskFailure() {
                    playWaitingAudio()
                }
            })
        } else if (recordingAudioFileStatus == FILE_STATUS_DOWNLOAD_FAILED) {
            playWaitingAudio()
        }
    }

    private fun playWaitingAudio() {
        playStatus = PLAY_STATUS_PLAYING_WAITING
        tryToPlayWaitingAudioFile()
        // Note: completionListener?.onWaitingMessagePlay() is now called from tryToPlayWaitingAudioFile()
        // after waiting audio actually starts playing, to prevent race condition with call connection
    }

    private fun tryToPlayWaitingAudioFile() {
        if (playStatus != PLAY_STATUS_PLAYING_WAITING) return

        //Play queue level wait music if available, otherwise, play downloaded global wait music.
        queueLevelAudioUrl?.let { queueLevelAudioUrl ->
            audioPlayer?.play(queueLevelAudioUrl, object : TaskCallback<Void?> {
                override fun onTaskSuccess(result: Void?) {
                    playStatus = PLAY_STATUS_STOPPED
                }

                override fun onTaskFailure() {
                    tryToPlayGlobalWaitMusic()
                    return
                }
            })
            // Call completion listener after queue-level audio starts playing
            completionListener?.onWaitingMessagePlay()
        } ?: run {
            tryToPlayGlobalWaitMusic()
        }
    }

    private fun tryToPlayGlobalWaitMusic() {
        if (waitingAudioFileStatus == FILE_STATUS_DOWNLOADED) {
            audioPlayer?.play(waitingAudioFileName, true, object : TaskCallback<Void?> {
                override fun onTaskSuccess(result: Void?) {
                    playStatus = PLAY_STATUS_STOPPED
                }

                override fun onTaskFailure() {
                    playStatus = PLAY_STATUS_STOPPED
                }
            })
            // Call completion listener after global wait music starts playing
            completionListener?.onWaitingMessagePlay()
        } else if (waitingAudioFileStatus == FILE_STATUS_DOWNLOAD_FAILED) {
            playStatus = PLAY_STATUS_STOPPED
            // Call completion listener even if waiting audio failed to download
            completionListener?.onWaitingMessagePlay()
        }
    }

    companion object {
        private const val PLAY_STATUS_READY = 1
        private const val PLAY_STATUS_PLAYING_CONNECTING = 2
        private const val PLAY_STATUS_PLAYING_RECORDING = 3
        private const val PLAY_STATUS_PLAYING_WAITING = 4
        private const val PLAY_STATUS_STOPPED = 5
        private const val FILE_STATUS_READY = 1
        private const val FILE_STATUS_DOWNLOADING = 2
        private const val FILE_STATUS_DOWNLOADED = 3
        private const val FILE_STATUS_DOWNLOAD_FAILED = 4
    }

    init {
        audioPlayer = AudioPlayer()
        playStatus = PLAY_STATUS_READY
        this.useCaseHandler = useCaseHandler
        this.getAudibleMessages = getAudibleMessages
        downloadAudibleMessages()
    }
}