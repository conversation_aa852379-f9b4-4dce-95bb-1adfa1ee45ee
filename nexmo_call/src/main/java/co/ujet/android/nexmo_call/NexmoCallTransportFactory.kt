package co.ujet.android.nexmo_call

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher
import co.ujet.android.modulemanager.entrypoints.call.CallTransport
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import co.ujet.android.modulemanager.entrypoints.call.CallTransportListener
import co.ujet.android.modulemanager.entrypoints.call.Constants.VOIP_PROVIDER_NEXMO
import com.nexmo.client.NexmoClient

object NexmoCallTransportFactory : CallTransportFactory {
    ******** val transportType = VOIP_PROVIDER_NEXMO
    ******** val transportVersion: String
        get() = NexmoClient.get()?.sdkVersion ?: "unknown"

    ******** fun createCallTransport(
        context: Context,
        region: String?,
        callId: Int,
        participantId: Int,
        callTransportListener: CallTransportListener,
        callAccessTokenFetcher: CallAccessTokenFetcher,
        logger: Logger
    ): CallTransport =
        NexmoCallTransport(context, region, callId, participantId, callTransportListener, callAccessTokenFetcher, logger)
}