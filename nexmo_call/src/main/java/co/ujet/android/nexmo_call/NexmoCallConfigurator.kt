package co.ujet.android.nexmo_call

import androidx.annotation.Keep
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory

@Keep
class NexmoCallConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        //[TODO:Nexmo Call] Skipping nexmo call module until it is release ready
        if (isEnabled().not()) {
            return
        }
        EntryPointFactory.registerEntryPoint(
            CallTransportFactory::class.java,
            NexmoCallTransportFactory
        )
        EntryPointFactory.registerEntryPoint(UjetModule::class.java, NexmoCallUjetModule)
    }

    override fun isEnabled() = false
}
