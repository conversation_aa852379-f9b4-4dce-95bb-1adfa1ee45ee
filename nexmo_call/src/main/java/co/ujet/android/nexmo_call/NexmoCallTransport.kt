package co.ujet.android.nexmo_call

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher
import co.ujet.android.modulemanager.entrypoints.call.*
import co.ujet.android.nexmo_call.ui.NexmoPermissionRequestActivity
import co.ujet.android.nexmo_call.utils.MainHandlerImmediate.runOnMain
import com.nexmo.client.*
import com.nexmo.client.request_listener.NexmoApiError
import com.nexmo.client.request_listener.NexmoConnectionListener
import com.nexmo.client.request_listener.NexmoRequestListener
import com.nexmo.utils.logger.ILogger

class NexmoCallTransport(
    private val context: Context,
    private val region: String?,
    private val callId: Int,
    private val participantId: Int,
    private val callTransportListener: CallTransportListener,
    private val callAccessTokenFetcher: CallAccessTokenFetcher,
    private val logger: Logger
) : CallTransport, NexmoConnectionListener {

    private var nexmoClient: NexmoClient? = null
    private var nexmoCall: NexmoCall? = null
    private var shouldBeConnected = false
    private var permissionDenied = false
    private var isFetchingAuthToken = false
    private var isMuted = false
    private var nexmoCallStatus: NexmoCallMemberStatus? = null

    ******** fun connect() {
        if (!hasReadPhoneStatePermission()) {
            requestPhoneStatePermission()
            return
        }

        if (shouldBeConnected) {
            logger.w("NexmoCallTransport.connect() called before ******** call was disconnected")
            return
        }
        shouldBeConnected = true
        fetchAccessToken(
            onSuccess = { accessToken ->
                nexmoClient = getOrCreateClient().apply {
                    setConnectionListener(this@NexmoCallTransport)
                    login(accessToken)
                }
            },
            onFailure = { disconnect() }
        )
    }

    ******** fun getState(): CallTransportState {
        return when {
            /* Twilio sends specific error code (example, CONNECTION_ERROR, Generic errors etc) in case
            * of call failure but nexmo does not send so we map all nexmo errors to show one error message
            * to end user and we decided to use "ujet_error_call_connection" string in this case so sending
            * CONNECTION_FAILURE failure reason when permission denied / nexmo call failed.
             */
            permissionDenied -> CallTransportState.Error(FailureReason.CONNECTION_FAILURE)
            !shouldBeConnected -> CallTransportState.Disconnected
            nexmoCallStatus == NexmoCallMemberStatus.ANSWERED ->  {
                //Connected state triggers CallManager#refresh() so reset to null to break triggering it in loop
                nexmoCallStatus = null
                CallTransportState.Connected
            }
            isNexmoCallFailed() -> CallTransportState.Failed(FailureReason.CONNECTION_FAILURE)
            else -> CallTransportState.Connecting
        }
    }

    //EndStatuses() -> returns [CANCELLED, COMPLETED, FAILED, TIMEOUT, REJECTED] so we need to check
    //if nexmoCallStatus is not completed and falls in any of the EndStatuses()
    private fun isNexmoCallFailed() = NexmoCallMemberStatus.EndStatuses().any { nexmoCallStatus != NexmoCallMemberStatus.COMPLETED && it == nexmoCallStatus }

    ******** fun mute() {
        logger.i("Muting Nexmo call (Ujet call ID $callId)")
        isMuted = true
        nexmoCall?.mute(true)
    }

    ******** fun unMute() {
        logger.i("Un-muting Nexmo call (Ujet call ID $callId)")
        isMuted = false
        nexmoCall?.mute(false)
    }

    ******** fun disconnect() {
        if (!shouldBeConnected) {
            logger.w("Can't disconnect an already disconnected Nexmo call")
            return
        }
        logger.i("Disconnecting Nexmo call (Ujet call ID $callId)")
        shouldBeConnected = false
        isFetchingAuthToken = false
        isMuted = false
        nexmoCall = null
        nexmoClient?.logout()
        nexmoClient = null
        callTransportListener.onTransportStateChanged(getState())
    }

    private fun hasReadPhoneStatePermission() = ActivityCompat.checkSelfPermission(
        context,
        Manifest.permission.READ_PHONE_STATE
    ) == PackageManager.PERMISSION_GRANTED

    private fun requestPhoneStatePermission() {
        logger.i("Missing required READ_PHONE_STATE permission for Nexmo SDK, requesting it")
        val localBroadcastManager = LocalBroadcastManager.getInstance(context)
        localBroadcastManager.registerReceiver(object : BroadcastReceiver() {
            ******** fun onReceive(context: Context?, intent: Intent?) {
                localBroadcastManager.unregisterReceiver(this)
                if (shouldBeConnected && hasReadPhoneStatePermission()) {
                    connect()
                } else if (!hasReadPhoneStatePermission()) {
                    logger.w("Required READ_PHONE_STATE permission not granted. The Nexmo SDK can not be started.")
                    permissionDenied = true
                    shouldBeConnected = true //Required to trigger disconnect() to end the call
                    disconnect()
                }
            }
        }, IntentFilter(NexmoPermissionRequestActivity.INTENT_PERMISSION_RESULT))
        context.startActivity(Intent(context, NexmoPermissionRequestActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        })
    }

    private fun fetchAccessToken(onSuccess: (String) -> Unit, onFailure: () -> Unit) {
        callTransportListener.onTransportStateChanged(getState())
        isFetchingAuthToken = true
        callAccessTokenFetcher.fetch("",
            onSuccess = { accessToken ->
                isFetchingAuthToken = false
                if (!shouldBeConnected) {
                    logger.i("Nexmo call token fetched, but transport should not be connected anymore")
                    return@fetch
                }
                onSuccess(accessToken)
            },
            onFailure = {
                isFetchingAuthToken = false
                logger.w("Failed to fetch Nexmo call access token")
                onFailure()
            }
        )
    }

    private fun getOrCreateClient(): NexmoClient {
        return NexmoClient.get()
            ?: run {
                val domain = if (vonageRegions.contains(region)) {
                    "vonage.com"
                } else {
                    "nexmo.com"
                }
                logger.d("Creating a new Nexmo client")
                NexmoClient
                    .Builder()
                    .logLevel(if (BuildConfig.DEBUG) ILogger.eLogLevel.DEBUG else ILogger.eLogLevel.CRITICAL)
                    .environmentHost("https://ws-$region.$domain/")
                    .restEnvironmentHost("https://api-$region.$domain/")
                    .imageProcessingServiceUrl("https://api-$region.$domain/v1/image/")
                    .build(context)
            }
    }

    ******** fun onConnectionStatusChange(
        status: NexmoConnectionListener.ConnectionStatus,
        reason: NexmoConnectionListener.ConnectionStatusReason
    ) = runOnMain {
        logger.i("Nexmo connection status changed: $status ($reason)")
        if (status == NexmoConnectionListener.ConnectionStatus.CONNECTED && nexmoCall == null) {
            createNexmoCall()
        }
        callTransportListener.onTransportStateChanged(getState())
    }

    private fun createNexmoCall() {
        val customData: HashMap<String, Any> = HashMap<String, Any>().apply {
            put("call_id", callId)
            put("from_participant_id", participantId)
        }
        nexmoClient?.serverCall(callId.toString(), customData,
            object: NexmoRequestListener<NexmoCall> {
                ******** fun onSuccess(response: NexmoCall?) {
                    logger.d("Nexmo call started: ${nexmoCall.toString()}")
                    nexmoCall = response
                    nexmoCall?.mute(isMuted)
                    nexmoCall?.addCallEventListener(callEventListener)
                }
                ******** fun onError(apiError: NexmoApiError) {
                    logger.w("Nexmo call error: Unable to start a call ${apiError.message}")
                }
            }
        )
    }

    private val callEventListener = object: NexmoCallEventListener {
        ******** fun onDTMF(digit: String?, nexmoMember: NexmoMember?) {
            logger.i("Nexmo call onDTMF(): digit: $digit, nexmoMember: $nexmoMember")
        }

        ******** fun onLegTransfer(event: NexmoLegTransferEvent?, member: NexmoMember?) {
            logger.i("Nexmo call transferred")
        }

        ******** fun onMemberStatusUpdated(callStatus: NexmoCallMemberStatus?, nexmoMember: NexmoMember?) {
            logger.i("Nexmo call onMemberStatusUpdated(): status: $callStatus, nexmoMember: $nexmoMember")
            nexmoCallStatus = callStatus
            callTransportListener.onTransportStateChanged(getState())
        }

        ******** fun onMuteChanged(muteState: NexmoMediaActionState?, nexmoMember: NexmoMember?) {
            logger.i("Nexmo call mute state changed to: $muteState, nexmoMember: $nexmoMember")
        }

        ******** fun onEarmuffChanged(earmuffState: NexmoMediaActionState?, nexmoMember: NexmoMember?) {
            logger.i("Nexmo call earmuff state changed to: $earmuffState, nexmoMember: $nexmoMember")
        }
    }

    companion object {
        private val vonageRegions = listOf("us-3", "us-4", "eu-3", "eu-4", "ap-3", "ap-4")
    }
}
