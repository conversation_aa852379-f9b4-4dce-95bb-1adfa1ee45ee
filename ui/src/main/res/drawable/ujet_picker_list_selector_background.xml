<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_window_focused="false" android:drawable="@android:color/transparent" />

    <!-- Even though these two point to the same resource, have two states so the drawable will invalidate itself when coming out of pressed state. -->
    <item android:state_focused="true"  android:state_enabled="false" android:state_pressed="true" android:drawable="@android:color/transparent" />
    <item android:state_focused="true"  android:state_enabled="false"                              android:drawable="@android:color/transparent" />
    <item android:state_focused="true"                                android:state_pressed="true" android:drawable="@android:color/holo_orange_dark" />
    <item android:state_focused="false"                               android:state_pressed="true" android:drawable="@android:color/holo_orange_dark" />
    <item android:state_focused="true"                                                             android:drawable="@android:color/holo_orange_dark" />
</selector>
