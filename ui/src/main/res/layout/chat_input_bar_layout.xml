<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:theme="@style/Theme.Ujet.Bridge">

    <HorizontalScrollView
        android:id="@+id/footer_quick_reply_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/quick_reply_list_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="26dp"
            android:paddingTop="4dp"
            android:paddingEnd="26dp"
            android:paddingBottom="26dp"
            android:orientation="horizontal">

        </LinearLayout>

    </HorizontalScrollView>

    <LinearLayout
        android:id="@+id/banner_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:id="@+id/banner_top_border"
            android:layout_width="match_parent"
            android:layout_height="1dp" />

        <TextView
            android:id="@+id/description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/ujet_dialog_margin"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:layout_marginEnd="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:textSize="@dimen/ujet_description" />

        <co.ujet.android.ui.widgets.UjetProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_margin="@dimen/ujet_dialog_margin" />

        <ImageView
            android:id="@+id/success_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:layout_margin="@dimen/ujet_dialog_margin"
            android:background="@drawable/ujet_smart_action_verification_background"
            app:srcCompat="@drawable/ujet_ic_csat_check"
            tools:ignore="ContentDescription" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_input_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:id="@+id/top_border"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/action_icons_layout"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/chat_actions_menu_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:scaleType="centerInside"
                app:srcCompat="@drawable/ujet_ic_chat_actions" />

            <ImageView
                android:id="@+id/escalate_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="bottom"
                android:layout_toEndOf="@+id/chat_actions_menu_button"
                android:scaleType="centerInside"
                app:srcCompat="@drawable/ujet_ic_escalate_chat" />

            <RelativeLayout
                android:id="@+id/message_edit_text_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/escalate_button" >

                <EditText
                    android:id="@+id/message_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:imeOptions="flagNoFullscreen"
                    android:inputType="textMultiLine"
                    android:maxLength="@integer/chat_input_max_length"
                    android:maxLines="4"
                    android:minHeight="40dp"
                    android:textCursorDrawable="@null" />

            </RelativeLayout>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>
