<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/hidden_panel"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <co.ujet.android.ui.picker.PickerListView
        android:id="@+id/picker_ui_listview"
        style="@style/Ujet.PickerUI.ListView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        tools:listitem="@layout/ujet_picker_item"
        android:layout_margin="0dp"
        android:overScrollMode="never"
        />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/ujet_picker_item_height"
        android:layout_centerVertical="true" >

        <View
            android:id="@+id/tv_top_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentTop="true"
            android:background="@color/ujet_primary" />

        <View
            android:id="@+id/tv_bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/ujet_primary" />

    </RelativeLayout>

</RelativeLayout>
