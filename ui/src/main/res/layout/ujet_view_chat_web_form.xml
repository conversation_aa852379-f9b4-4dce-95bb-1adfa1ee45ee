<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/UjetChatBounds"
    android:gravity="start">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/card_layout"
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:paddingBottom="16dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:scaleType="centerCrop"
            app:cornerFamily="rounded"
            app:cornerSize="4dp"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toTopOf="@id/card_layout"
            android:visibility="gone"
            tools:src="@drawable/ujet_file_pdf" />

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="#00FFFFFF"
            android:shadowDx="48"
            android:shadowDy="0"
            android:shadowRadius="1"
            android:textAlignment="viewStart"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Title" />

        <TextView
            android:id="@+id/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="#00FFFFFF"
            android:shadowDx="48"
            android:shadowDy="0"
            android:shadowRadius="1"
            android:textAlignment="viewStart"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:text="A Subtitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/card_layout"
        android:layout_marginTop="8dp"
        android:layout_toEndOf="@+id/dot_separator"
        android:importantForAccessibility="no"
        android:lineSpacingMultiplier="1.0"
        android:maxWidth="240dp"
        android:selectAllOnFocus="true"
        android:textColor="#4D000000"
        android:textIsSelectable="true"
        android:textSize="11sp"
        tools:text="Today 15:00" />

    <TextView
        android:id="@+id/dot_separator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@+id/status"
        android:layout_below="@+id/card_layout"
        android:textStyle="bold"
        android:lineHeight="20dp"
        android:text=" . "
        android:textSize="16sp" />

    <TextView
        android:id="@+id/status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/card_layout"
        android:layout_alignStart="@+id/card_layout"
        android:layout_marginTop="7dp"
        android:gravity="center_vertical"
        android:importantForAccessibility="no"
        android:lineSpacingMultiplier="1.0"
        android:maxWidth="240dp"
        android:selectAllOnFocus="true"
        android:textColor="#4D000000"
        android:textIsSelectable="true"
        android:textSize="12sp"
        tools:text="Completed" />
</RelativeLayout>
