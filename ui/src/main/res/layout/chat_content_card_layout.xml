<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/card_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:paddingBottom="16dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:scaleType="centerCrop"
            app:cornerFamily="rounded"
            app:cornerSize="4dp"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toTopOf="@id/card_layout"
            tools:src="@drawable/ujet_file_pdf" />

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="#00FFFFFF"
            android:shadowDx="48"
            android:shadowDy="0"
            android:shadowRadius="1"
            android:textAlignment="viewStart"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/image"
            tools:text="Title" />

        <TextView
            android:id="@+id/subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="#00FFFFFF"
            android:shadowDx="48"
            android:shadowDy="0"
            android:shadowRadius="1"
            android:textAlignment="viewStart"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:text="A Subtitle" />

        <TextView
            android:id="@+id/message_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:shadowColor="#00FFFFFF"
            android:shadowDx="48"
            android:shadowDy="0"
            android:shadowRadius="1"
            android:textAlignment="viewStart"
            app:layout_constraintEnd_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/subtitle"
            tools:text="Body - I can put whatever text I want here.\nIncluding new lines." />

        <LinearLayout
            android:id="@+id/button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            app:layout_constraintStart_toEndOf="@id/card_layout"
            app:layout_constraintStart_toStartOf="@id/card_layout"
            app:layout_constraintTop_toBottomOf="@id/message_body" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>
