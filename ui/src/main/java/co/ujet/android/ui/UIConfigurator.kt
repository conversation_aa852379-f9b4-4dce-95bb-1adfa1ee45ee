package co.ujet.android.ui

import androidx.annotation.Keep
import co.ujet.android.commons.libs.uson.Uson
import co.ujet.android.modulemanager.Configurable
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.ui.domain.*
import co.ujet.android.modulemanager.entrypoints.ui.UjetUI
import co.ujet.android.ui.domain.UjetStyles

@Keep
class UIConfigurator : Configurable {
    override fun configure(configurationsMap: Map<String, Any?>) {
        EntryPointFactory.registerEntryPoint(UjetUI::class.java, object : UjetUI {
            override fun buildChatStylesFromJson(chatStylesJson: String?): ChatStyles? {
                if (chatStylesJson == null) {
                    return null
                }
                val ujetStyles = Uson().fromJson(chatStylesJson, UjetStyles::class.java)
                val chatStyles = ujetStyles?.chat ?: return null
                return ChatStyles(
                    backButton = BackButtonStyle(
                        visible = chatStyles.backButton?.visible ?: true,
                        image = chatStyles.backButton?.image
                    ),
                    header = ChatHeaderStyle(
                        textContent = chatStyles.header?.textContent,
                        font = FontStyle(
                            colorReference = chatStyles.header?.font?.colorReference,
                            style = chatStyles.header?.font?.style,
                            family = chatStyles.header?.font?.family,
                            size = chatStyles.header?.font?.size
                        ),
                        messageTrayVisible = chatStyles.header?.messageTrayVisible,
                        agentIconVisible = chatStyles.header?.agentIconVisible,
                        divider = DividerStyle(
                            color = chatStyles.header?.divider?.color,
                            width = chatStyles.header?.divider?.width
                        )
                    ),
                    endChatButton = EndButtonStyle(
                        visible = chatStyles.endChatButton?.visible ?: true,
                        font = FontStyle(
                            colorReference = chatStyles.endChatButton?.font?.colorReference,
                            style = chatStyles.endChatButton?.font?.style,
                            family = chatStyles.endChatButton?.font?.family,
                            size = chatStyles.endChatButton?.font?.size
                        )
                    ),
                    systemMessages = SystemMessageStyle(
                        font = FontStyle(
                            colorReference = chatStyles.systemMessages?.font?.colorReference,
                            style = chatStyles.systemMessages?.font?.style,
                            family = chatStyles.systemMessages?.font?.family,
                            size = chatStyles.systemMessages?.font?.size
                        ),
                        backgroundColor = chatStyles.systemMessages?.backgroundColor,
                        cornerRadius = chatStyles.systemMessages?.cornerRadius,
                        border = BorderStyle(
                            color = chatStyles.systemMessages?.border?.color,
                            width = chatStyles.systemMessages?.border?.width
                        ),
                        buttonStyle = ButtonStyle(
                            backgroundColor = chatStyles.systemMessages?.buttonStyle?.backgroundColor,
                            cornerRadius = chatStyles.systemMessages?.buttonStyle?.cornerRadius,
                            border = BorderStyle(
                                color = chatStyles.systemMessages?.buttonStyle?.border?.color,
                                width = chatStyles.systemMessages?.buttonStyle?.border?.width
                            ),
                            font = FontStyle(
                                colorReference = chatStyles.systemMessages?.buttonStyle?.font?.colorReference,
                                style = chatStyles.systemMessages?.buttonStyle?.font?.style,
                                family = chatStyles.systemMessages?.buttonStyle?.font?.family,
                                size = chatStyles.systemMessages?.buttonStyle?.font?.size
                            )
                        )
                    ),
                    timeStamps = TimeStampStyle(
                        font = FontStyle(
                            colorReference = chatStyles.timeStamps?.font?.colorReference,
                            style = chatStyles.timeStamps?.font?.style,
                            family = chatStyles.timeStamps?.font?.family,
                            size = chatStyles.timeStamps?.font?.size
                        )
                    ),
                    agentMessageBubbles = AgentMessageStyle(
                        backgroundColor = chatStyles.agentMessageBubbles?.backgroundColor,
                        cornerRadius = chatStyles.agentMessageBubbles?.cornerRadius,
                        border = BorderStyle(
                            color = chatStyles.agentMessageBubbles?.border?.color,
                            width = chatStyles.agentMessageBubbles?.border?.width
                        ),
                        font = FontStyle(
                            colorReference = chatStyles.agentMessageBubbles?.font?.colorReference,
                            style = chatStyles.agentMessageBubbles?.font?.style,
                            family = chatStyles.agentMessageBubbles?.font?.family,
                            size = chatStyles.agentMessageBubbles?.font?.size
                        ),
                        icon = IconStyle(
                            visible = chatStyles.agentMessageBubbles?.icon?.visible ?: true,
                            icon = chatStyles.agentMessageBubbles?.icon?.icon,
                            size = chatStyles.agentMessageBubbles?.icon?.size,
                            position = chatStyles.agentMessageBubbles?.icon?.position
                        ),
                    ),
                    consumerMessageBubbles = ConsumerMessageStyle(
                        backgroundColor = chatStyles.consumerMessageBubbles?.backgroundColor,
                        cornerRadius = chatStyles.consumerMessageBubbles?.cornerRadius,
                        border = BorderStyle(
                            color = chatStyles.consumerMessageBubbles?.border?.color,
                            width = chatStyles.consumerMessageBubbles?.border?.width
                        ),
                        font = FontStyle(
                            colorReference = chatStyles.consumerMessageBubbles?.font?.colorReference,
                            style = chatStyles.consumerMessageBubbles?.font?.style,
                            family = chatStyles.consumerMessageBubbles?.font?.family,
                            size = chatStyles.consumerMessageBubbles?.font?.size
                        ),
                        icon = IconStyle(
                            visible = chatStyles.consumerMessageBubbles?.icon?.visible ?: true,
                            icon = chatStyles.consumerMessageBubbles?.icon?.icon,
                            size = chatStyles.consumerMessageBubbles?.icon?.size,
                            position = chatStyles.consumerMessageBubbles?.icon?.position
                        ),
                    ),
                    userInputBar = ChatInputBarStyle(
                        backgroundColor = chatStyles.userInputBar?.backgroundColor,
                        inputField = ChatInputFieldStyle(
                            cornerRadius = chatStyles.userInputBar?.inputField?.cornerRadius,
                            cursorColor = chatStyles.userInputBar?.inputField?.cursorColor,
                            placeholderText = chatStyles.userInputBar?.inputField?.placeholderText,
                            font = FontStyle(
                                colorReference = chatStyles.userInputBar?.inputField?.font?.colorReference,
                                style = chatStyles.userInputBar?.inputField?.font?.style,
                                family = chatStyles.userInputBar?.inputField?.font?.family,
                                size = chatStyles.userInputBar?.inputField?.font?.size
                            ),
                            border = BorderStyle(
                                color = chatStyles.userInputBar?.inputField?.border?.color,
                                width = chatStyles.userInputBar?.inputField?.border?.width
                            )
                        ),
                        topBorder = BorderStyle(
                            color = chatStyles.userInputBar?.topBorder?.color,
                            width = chatStyles.userInputBar?.topBorder?.width
                        ),
                        escalateIcon = IconStyle(
                            visible = chatStyles.userInputBar?.escalateIcon?.visible ?: true,
                            icon = chatStyles.userInputBar?.escalateIcon?.icon,
                            size = chatStyles.userInputBar?.escalateIcon?.size,
                        ),
                        chatActionsMenuIcon = IconStyle(
                            icon = chatStyles.userInputBar?.chatActionsMenuIcon?.icon,
                            size = chatStyles.userInputBar?.chatActionsMenuIcon?.size,
                        ),
                        sendButton = SendButtonStyle(
                            visible = chatStyles.userInputBar?.sendButton?.visible ?: true,
                            image = chatStyles.userInputBar?.sendButton?.image,
                            textColor = chatStyles.userInputBar?.sendButton?.textColor
                        )
                    ),
                    chatActionsMenuStyle = ChatActionsMenuStyle(
                        cameraIcon = IconStyle(
                            visible = chatStyles.chatActionsMenu?.cameraIcon?.visible ?: true,
                            icon = chatStyles.chatActionsMenu?.cameraIcon?.icon,
                            size = chatStyles.chatActionsMenu?.cameraIcon?.size,
                        ),
                        cobrowseIcon = IconStyle(
                            visible = chatStyles.chatActionsMenu?.cobrowseIcon?.visible ?: true,
                            icon = chatStyles.chatActionsMenu?.cobrowseIcon?.icon,
                            size = chatStyles.chatActionsMenu?.cobrowseIcon?.size,
                        ),
                        selectPhotoFromLibraryIcon = IconStyle(
                            visible = chatStyles.chatActionsMenu?.selectPhotoFromLibraryIcon?.visible ?: true,
                            icon = chatStyles.chatActionsMenu?.selectPhotoFromLibraryIcon?.icon,
                            size = chatStyles.chatActionsMenu?.selectPhotoFromLibraryIcon?.size,
                        )
                    ),
                    contentCard = ContentCardStyle(
                        backgroundColor = chatStyles.contentCard?.backgroundColor,
                        cornerRadius = chatStyles.contentCard?.cornerRadius,
                        font = FontStyle(
                            colorReference = chatStyles.contentCard?.font?.colorReference,
                            style = chatStyles.contentCard?.font?.style,
                            family = chatStyles.contentCard?.font?.family,
                            size = chatStyles.contentCard?.font?.size
                        ),
                        border = BorderStyle(
                            color = chatStyles.contentCard?.border?.color,
                            width = chatStyles.contentCard?.border?.width
                        ),
                        title = TextStyle(
                            font = FontStyle(
                                colorReference = chatStyles.contentCard?.title?.font?.colorReference,
                                style = chatStyles.contentCard?.title?.font?.style,
                                family = chatStyles.contentCard?.title?.font?.family,
                                size = chatStyles.contentCard?.title?.font?.size
                            )
                        ),
                        subtitle = TextStyle(
                            font = FontStyle(
                                colorReference = chatStyles.contentCard?.subtitle?.font?.colorReference,
                                style = chatStyles.contentCard?.subtitle?.font?.style,
                                family = chatStyles.contentCard?.subtitle?.font?.family,
                                size = chatStyles.contentCard?.subtitle?.font?.size
                            )
                        ),
                        body = TextStyle(
                            font = FontStyle(
                                colorReference = chatStyles.contentCard?.body?.font?.colorReference,
                                style = chatStyles.contentCard?.body?.font?.style,
                                family = chatStyles.contentCard?.body?.font?.family,
                                size = chatStyles.contentCard?.body?.font?.size
                            )
                        ),
                        image = ImageStyle(
                            height = chatStyles.contentCard?.image?.height
                        ),
                        primaryButton = ButtonStyle(
                            backgroundColor = chatStyles.contentCard?.primaryButton?.backgroundColor,
                            cornerRadius = chatStyles.contentCard?.primaryButton?.cornerRadius,
                            border = BorderStyle(
                                color = chatStyles.contentCard?.primaryButton?.border?.color,
                                width = chatStyles.contentCard?.primaryButton?.border?.width,
                            ),
                            font = FontStyle(
                                colorReference = chatStyles.contentCard?.primaryButton?.font?.colorReference,
                                size = chatStyles.contentCard?.primaryButton?.font?.size,
                                style = chatStyles.contentCard?.primaryButton?.font?.style,
                                family = chatStyles.contentCard?.primaryButton?.font?.family
                            )
                        ),
                        secondaryButton = ButtonStyle(
                            backgroundColor = chatStyles.contentCard?.secondaryButton?.backgroundColor,
                            cornerRadius = chatStyles.contentCard?.secondaryButton?.cornerRadius,
                            border = BorderStyle(
                                color = chatStyles.contentCard?.secondaryButton?.border?.color,
                                width = chatStyles.contentCard?.secondaryButton?.border?.width,
                            ),
                            font = FontStyle(
                                colorReference = chatStyles.contentCard?.secondaryButton?.font?.colorReference,
                                size = chatStyles.contentCard?.secondaryButton?.font?.size,
                                style = chatStyles.contentCard?.secondaryButton?.font?.style,
                                family = chatStyles.contentCard?.secondaryButton?.font?.family
                            )
                        ),
                    ),
                    formCard = FormCardStyle(
                        backgroundColor = chatStyles.formCard?.backgroundColor,
                        cornerRadius = chatStyles.formCard?.cornerRadius,
                        font = FontStyle(
                            colorReference = chatStyles.formCard?.font?.colorReference,
                            style = chatStyles.formCard?.font?.style,
                            family = chatStyles.formCard?.font?.family,
                            size = chatStyles.formCard?.font?.size
                        ),
                        border = BorderStyle(
                            color = chatStyles.formCard?.border?.color,
                            width = chatStyles.formCard?.border?.width
                        ),
                        title = TextStyle(
                            font = FontStyle(
                                colorReference = chatStyles.formCard?.title?.font?.colorReference,
                                style = chatStyles.formCard?.title?.font?.style,
                                family = chatStyles.formCard?.title?.font?.family,
                                size = chatStyles.formCard?.title?.font?.size
                            )
                        ),
                        subtitle = TextStyle(
                            font = FontStyle(
                                colorReference = chatStyles.formCard?.subtitle?.font?.colorReference,
                                style = chatStyles.formCard?.subtitle?.font?.style,
                                family = chatStyles.formCard?.subtitle?.font?.family,
                                size = chatStyles.formCard?.subtitle?.font?.size
                            )
                        ),
                        image = ImageStyle(
                            height = chatStyles.formCard?.image?.height
                        ),
                    ),
                    postSession = PostSessionStyle(
                        backgroundColor = chatStyles.postSession?.backgroundColor,
                        border = BorderStyle(
                            color = chatStyles.formCard?.border?.color,
                            width = chatStyles.formCard?.border?.width
                        ),
                    )
                )
            }
        })
    }
}
