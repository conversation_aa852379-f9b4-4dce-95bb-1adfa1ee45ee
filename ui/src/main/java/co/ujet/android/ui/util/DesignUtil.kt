package co.ujet.android.ui.util

import android.R.attr
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Typeface
import android.util.TypedValue
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.annotation.ColorInt
import androidx.annotation.AttrRes
import android.content.res.ColorStateList
import android.graphics.Bitmap.Config.ARGB_8888
import android.graphics.Canvas
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import androidx.annotation.DimenRes
import androidx.core.content.res.ResourcesCompat
import java.io.File
import java.lang.Exception
import java.util.HashMap
import kotlin.math.roundToInt

object DesignUtil {
    private val BITMAP_CONFIG = ARGB_8888
    private const val COLOR_DRAWABLE_DIMENSION = 2
    private val cachedFontMap: MutableMap<String, Typeface?> = HashMap()

    @JvmStatic
    fun pxToSp(context: Context, px: Float) = (px / context.resources.displayMetrics.scaledDensity).roundToInt()

    @JvmStatic
    fun spToPx(context: Context, sp: Float) = (sp * context.resources.displayMetrics.scaledDensity).roundToInt()

    @JvmStatic
    fun dpToPx(context: Context, dp: Float) = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, context.resources.displayMetrics)

    @JvmStatic
    fun dpToPx(context: Context, dp: Int) = dpToPx(context, dp.toFloat())

    @JvmStatic
    fun getDisplayMetrics(context: Context): DisplayMetrics {
        val metrics = DisplayMetrics()
        val wm = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = wm.defaultDisplay
        display.getMetrics(metrics)
        return metrics
    }

    @JvmStatic
    fun findFont(context: Context, fontPath: String, defaultFontPath: String?): Typeface? {
        if (fontPath.isEmpty()) {
            return Typeface.DEFAULT
        }
        var defaultFontName = ""
        if (!defaultFontPath.isNullOrEmpty()) {
            val defaultFontFile = File(defaultFontPath)
            defaultFontName = defaultFontFile.name
        }
        val fontFile = File(fontPath)
        val fontName = fontFile.name
        if (cachedFontMap.containsKey(fontName)) {
            return cachedFontMap[fontName]
        }
        if (cachedFontMap.containsKey(fontPath)) {
            return cachedFontMap[fontPath]
        }
        try {
            val assets = context.resources.assets
            if (assets.list("")?.asList()?.contains(fontPath) == true) {
                val typeface = Typeface.createFromAsset(context.assets, fontName)
                cachedFontMap[fontName] = typeface
                return typeface
            } else if (assets.list("fonts")?.asList()?.contains(fontName) == true) {
                val typeface = Typeface.createFromAsset(context.assets, String.format("fonts/%s", fontName))
                cachedFontMap[fontName] = typeface
                return typeface
            } else if (assets.list("iconfonts")?.asList()?.contains(fontName) == true) {
                val typeface = Typeface.createFromAsset(context.assets, String.format("iconfonts/%s", fontName))
                cachedFontMap[fontName] = typeface
                return typeface
            } else if (!defaultFontPath.isNullOrEmpty() && assets.list("")?.asList()?.contains(defaultFontPath) == true) {
                val typeface = Typeface.createFromAsset(context.assets, defaultFontPath)
                cachedFontMap[defaultFontName] = typeface
                return typeface
            }
        } catch (ignore: Exception) {
        }

        // find from resources
        try {
            if (fontPath.startsWith("res/font/")) {
                val fontResName = fontName.substring(0, fontName.lastIndexOf("."))
                val fontResId = context.resources.getIdentifier(fontResName, "font", context.packageName)
                if (fontResId > 0) {
                    val typeface = ResourcesCompat.getFont(context, fontResId)
                    cachedFontMap[fontPath] = typeface
                    return typeface
                }
            }
        } catch (ignore: Exception) {
        }
        return Typeface.DEFAULT
    }

    @JvmStatic
    fun getColor(context: Context, id: Int): Int {
        return if (VERSION.SDK_INT >= VERSION_CODES.M) {
            context.getColor(id)
        } else {
            context.resources.getColor(id)
        }
    }

    @ColorInt
    @JvmStatic
    fun resolveColor(context: Context, @AttrRes attr: Int) = resolveColor(context, attr, 0)

    @ColorInt
    fun resolveColor(context: Context, @AttrRes attr: Int, fallback: Int): Int {
        val attributes = context.theme.obtainStyledAttributes(intArrayOf(attr))
        return try {
            attributes.getColor(0, fallback)
        } finally {
            attributes.recycle()
        }
    }

    @JvmStatic
    fun getStateListDrawable(normalDrawable: Drawable, focusDrawable: Drawable): StateListDrawable {
        val status = StateListDrawable()
        status.addState(intArrayOf(attr.state_pressed), focusDrawable)
        status.addState(intArrayOf(attr.state_focused), normalDrawable)
        status.addState(intArrayOf(attr.state_enabled), normalDrawable)
        return status
    }

    @JvmStatic
    fun getColorStateList(defaultColor: Int, focusColor: Int): ColorStateList {
        val states = arrayOf(intArrayOf(attr.state_pressed), intArrayOf(attr.state_focused), intArrayOf(attr.state_enabled))
        val colors = intArrayOf(focusColor, focusColor, defaultColor)
        return ColorStateList(states, colors)
    }

    @JvmStatic
    fun createRoundedRectangleDrawable(color: Int, strokeColor: Int, strokeWidth: Int, cornerRadiusPx: Float): Drawable {
        val drawable = GradientDrawable()
        drawable.shape = GradientDrawable.RECTANGLE
        drawable.setColor(color)
        drawable.setStroke(strokeWidth, strokeColor)
        drawable.cornerRadius = cornerRadiusPx
        return drawable
    }

    @JvmStatic
    fun getBitmapFromDrawable(drawable: Drawable?): Bitmap? {
        if (drawable == null) {
            return null
        }
        return if (drawable is BitmapDrawable) {
            drawable.bitmap
        } else try {
            val bitmap = if (drawable is ColorDrawable) {
                Bitmap.createBitmap(COLOR_DRAWABLE_DIMENSION, COLOR_DRAWABLE_DIMENSION, BITMAP_CONFIG)
            } else {
                Bitmap.createBitmap(drawable.intrinsicWidth, drawable.intrinsicHeight, BITMAP_CONFIG)
            }
            val canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            bitmap
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun getDimensionPixelSize(context: Context, @DimenRes dimensionRes: Int): Int {
        return context.resources.getDimensionPixelSize(dimensionRes)
    }
}
