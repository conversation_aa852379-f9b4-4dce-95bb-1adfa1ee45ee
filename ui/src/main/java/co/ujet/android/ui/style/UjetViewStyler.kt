package co.ujet.android.ui.style

import android.R.attr
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.ShapeDrawable
import android.util.TypedValue
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.LinearLayout.LayoutParams
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat.SRC_IN
import androidx.core.graphics.ColorUtils
import co.ujet.android.ui.R
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.picker.Picker
import com.google.android.material.button.MaterialButton
import kotlin.math.max

object UjetViewStyler {
    @JvmStatic
    fun stylePrimaryButton(ujetStyle: UjetStyle, button: FancyButton) {
        styleButton(
            ujetStyle,
            button,
            ujetStyle.colorPrimary,
            ujetStyle.getColor(R.color.ujet_white)
        )
    }

    @JvmStatic
    fun stylePrimaryContentCardButton(ujetStyle: UjetStyle, button: FancyButton) {
        button.setBackgroundColor(ujetStyle.colorPrimary)
        button.setFocusBackgroundColor(getFocusColor(ujetStyle.colorPrimary))
        button.setDisabledBackgroundColor(getDisabledColor(ujetStyle.colorPrimary))
        button.setTextColor(ujetStyle.getColor(R.color.ujet_white))
        button.setFocusTextColor(getFocusColor(R.color.ujet_white))
        button.setDisabledTextColor(getDisabledColor(R.color.ujet_white))
        button.setTextSize(ujetStyle.pxToSp(16f))
        button.setCustomTypeFace(ujetStyle.typeFace)
        button.setRadius(ujetStyle.dpToPx(16).toInt())
    }

    @JvmStatic
    fun styleSecondaryContentCardButton(ujetStyle: UjetStyle, button: FancyButton) {
        button.setBackgroundColor(ujetStyle.contentCardButtonSecondaryBackgroundColor)
        button.setFocusBackgroundColor(getFocusColor(ujetStyle.contentCardButtonSecondaryBackgroundColor))
        button.setDisabledBackgroundColor(ujetStyle.contentCardButtonDisableSecondaryBackgroundColor)
        button.setTextColor(ujetStyle.colorPrimary)
        button.setFocusTextColor(getFocusColor(ujetStyle.colorPrimary))
        button.setDisabledTextColor(getDisabledColor(ujetStyle.colorPrimary))
        button.setBorderWidth(ujetStyle.dpToPx(2).toInt())
        button.setBorderColor(ujetStyle.colorPrimary)
        button.setFocusBorderColor(getFocusColor(ujetStyle.colorPrimary))
        button.setDisabledBorderColor(getDisabledColor(ujetStyle.colorPrimary))
        button.setTextSize(ujetStyle.pxToSp(16f))
        button.setCustomTypeFace(ujetStyle.typeFace)
        button.setRadius(ujetStyle.dpToPx(16).toInt())
    }

    fun stylePrimaryButton(ujetStyle: UjetStyle, button: MaterialButton) {
        button.typeface = ujetStyle.typeFace
        button.strokeColor = ColorStateList.valueOf(ujetStyle.colorPrimary)
        val states = arrayOf(intArrayOf(attr.state_pressed), intArrayOf())
        val textColors = intArrayOf(
            ujetStyle.getColor(R.color.ujet_white),
            ujetStyle.textPrimaryColor
        )
        button.setTextColor(ColorStateList(states, textColors))
        val backgroundColors = intArrayOf(
            ujetStyle.colorPrimary,
            ujetStyle.chatVirtualAgentInlineButtonBackgroundColor
        )
        button.backgroundTintList = ColorStateList(states, backgroundColors)
        button.rippleColor = ColorStateList.valueOf(ujetStyle.colorPrimary)
    }

    fun styleDownloadTranscriptButton(ujetStyle: UjetStyle, button: MaterialButton) {
        button.typeface = ujetStyle.typeFace
        button.strokeColor = ColorStateList.valueOf(ujetStyle.colorPrimary)
        val states = arrayOf(
            intArrayOf(attr.state_enabled, attr.state_pressed), // Pressed state
            intArrayOf(attr.state_enabled), // Normal state
            intArrayOf(-attr.state_enabled) // Disabled state
        )
        val textColors = intArrayOf(
            ujetStyle.getColor(R.color.ujet_white),
            ujetStyle.textPrimaryColor,
            ujetStyle.getColor(R.color.ujet_chat_quick_reply_buttons_disabled_text_color)
        )
        button.setTextColor(ColorStateList(states, textColors))
        val backgroundColors = intArrayOf(
            ujetStyle.colorPrimary,
            ujetStyle.chatVirtualAgentInlineButtonBackgroundColor,
            ujetStyle.chatVirtualAgentInlineButtonBackgroundColor
        )
        button.backgroundTintList = ColorStateList(states, backgroundColors)
        button.rippleColor = ColorStateList.valueOf(ujetStyle.colorPrimary)
    }

    fun styleSecondaryButton(ujetStyle: UjetStyle, button: MaterialButton) {
        button.typeface = ujetStyle.typeFace
        val states = arrayOf(intArrayOf(-attr.state_enabled), intArrayOf(attr.state_pressed), intArrayOf())
        val textColors = intArrayOf(
            ujetStyle.disabledTextColor,
            ujetStyle.textPrimaryColor,
            ujetStyle.textPrimaryColor
        )
        button.setTextColor(ColorStateList(states, textColors))
        button.setBackgroundColor(ujetStyle.primaryBackgroundColor)
        val buttonColors = intArrayOf(
            ujetStyle.getColorPrimary((255 * 0.3).toInt()),
            ujetStyle.colorPrimaryLight,
            ujetStyle.colorPrimaryLight
        )
        button.strokeColor = ColorStateList(states, buttonColors)
        button.rippleColor = ColorStateList.valueOf(ujetStyle.colorPrimary)
        button.iconTint = ColorStateList(states, buttonColors)
    }

    fun styleChatQuickReplyIndividualButton(ujetStyle: UjetStyle, index: Int, button: MaterialButton) {
        val container = button.parent as? LinearLayout
        container?.gravity = ujetStyle.getChatQuickReplyGravity()
        button.typeface = ujetStyle.getChatQuickReplyTypeFace()
        val states = arrayOf(intArrayOf(attr.state_pressed), intArrayOf())
        val textColors = intArrayOf(
            ujetStyle.getChatQuickReplyButtonPressedTextColor(),
            ujetStyle.getChatQuickReplyButtonTextColor()
        )
        button.setTextColor(ColorStateList(states, textColors))
        button.setBackgroundColor(ujetStyle.getChatQuickReplyBackgroundColor())
        val strokeColors = intArrayOf(
            ujetStyle.getChatQuickReplyButtonStrokeColor(),
            ujetStyle.getChatQuickReplyButtonStrokeColor()
        )
        button.strokeWidth = ujetStyle.getChatQuickReplyButtonStrokeWidth()
        button.cornerRadius = ujetStyle.getChatQuickReplyButtonCornerRadius()
        if (index > 0) {
            val buttonMargin: Int = ujetStyle.getChatQuickReplyButtonVerticalMargins()
            (button.layoutParams as? LayoutParams)?.setMargins(0, buttonMargin, 0, 0)
        }
        button.strokeColor = ColorStateList(states, strokeColors)
        button.rippleColor = ColorStateList.valueOf(ujetStyle.getChatQuickReplyButtonPressedBackgroundColorPrimary())
        val horizontalPadding: Int = ujetStyle.getChatQuickReplyHorizontalPadding()
        val verticalPadding: Int = ujetStyle.getChatQuickReplyVerticalPadding()
        button.setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding)
    }

    fun styleDangerButton(ujetStyle: UjetStyle, button: MaterialButton) {
        button.typeface = ujetStyle.typeFace
        val states = arrayOf(intArrayOf(-attr.state_enabled), intArrayOf(attr.state_pressed), intArrayOf())
        val textColors = intArrayOf(
            ujetStyle.getColor(R.color.ujet_white),
            ujetStyle.getColor(R.color.ujet_white),
            ujetStyle.getColor(R.color.ujet_white)
        )
        button.setTextColor(ColorStateList(states, textColors))
        button.setBackgroundColor(ujetStyle.getColor(R.color.ujet_danger))
        val buttonColors = intArrayOf(
            ujetStyle.getColor(R.color.ujet_danger),
            ColorUtils.setAlphaComponent(ujetStyle.getColor(R.color.ujet_danger), (255 * 0.3).toInt()),
            ujetStyle.getColor(R.color.ujet_danger)
        )
        button.strokeColor = ColorStateList(states, buttonColors)
        button.rippleColor = ColorStateList.valueOf(ujetStyle.getColor(R.color.ujet_white))
        button.iconTint = ColorStateList.valueOf(ujetStyle.getColor(R.color.ujet_white))
    }

    fun styleChatButton(ujetStyle: UjetStyle, button: ImageView, applyTint: Boolean) {
        if (applyTint) {
            val states = arrayOf(intArrayOf(-attr.state_enabled), intArrayOf())
            val imageColors = intArrayOf(
                ColorUtils.setAlphaComponent(ujetStyle.imageTintColor, (255 * 0.3).toInt()),
                ujetStyle.imageTintColor
            )
            button.imageTintList = ColorStateList(states, imageColors)
        }
        button.setBackgroundColor(ujetStyle.chatFooterBackgroundColor)
    }

    private fun styleButton(
        ujetStyle: UjetStyle,
        button: FancyButton,
        backgroundColor: Int,
        textColor: Int
    ) {
        button.setBackgroundColor(backgroundColor)
        button.setFocusBackgroundColor(getFocusColor(backgroundColor))
        button.setDisabledBackgroundColor(getDisabledColor(backgroundColor))
        button.setTextColor(textColor)
        button.setFocusTextColor(getFocusColor(textColor))
        button.setDisabledTextColor(getDisabledColor(textColor))
        button.setBorderWidth(0)
        button.setRadius(ujetStyle.buttonRadius.toInt())
        button.setCustomTypeFace(ujetStyle.typeFace)
    }

    @JvmStatic
    fun styleButtonIconColor(ujetStyle: UjetStyle, button: FancyButton) {
        button.setIconColor(
            ujetStyle.colorPrimary,
            getFocusColor(ujetStyle.colorPrimary)
        )
    }

    fun getDisabledColor(color: Int) = ColorUtils.setAlphaComponent(color, Color.alpha(color) / 2)

    fun getFocusColor(color: Int): Int {
        return if (ColorUtils.setAlphaComponent(color, 0xFF) == Color.BLACK) {
            ColorUtils.setAlphaComponent(color, (Color.alpha(color) * 0.5f).toInt())
        } else Color.argb(
            Color.alpha(color),
            max(0, Color.red(color) - 25),
            max(0, Color.green(color) - 25),
            max(0, Color.blue(color) - 25)
        )
    }

    @JvmStatic
    fun styleInvertedButton(ujetStyle: UjetStyle, button: FancyButton) {
        button.setBackgroundColor(ujetStyle.primaryBackgroundColor)
        button.setFocusBackgroundColor(ujetStyle.colorPrimary)
        button.setTextColor(ujetStyle.colorPrimary)
        button.setFocusTextColor(ujetStyle.primaryBackgroundColor)
        button.setDisabledTextColor(getDisabledColor(ujetStyle.colorPrimary))
        button.setRadius(ujetStyle.buttonRadius.toInt())
        button.setBorderWidth(ujetStyle.dpToPx(2f).toInt())
        button.setBorderColor(ujetStyle.colorPrimary)
        button.setFocusBorderColor(ujetStyle.colorPrimary)
        button.setDisabledBorderColor(getDisabledColor(ujetStyle.colorPrimary))
    }

    @JvmStatic
    fun styleFlatButton(ujetStyle: UjetStyle, button: FancyButton) {
        button.setBackgroundColor(ujetStyle.primaryBackgroundColor)
        button.setFocusBackgroundColor(ujetStyle.primaryBackgroundColor)
        button.setTextColor(ujetStyle.colorPrimary)
        button.setFocusTextColor(getFocusColor(ujetStyle.colorPrimary))
        button.setDisabledTextColor(getDisabledColor(ujetStyle.colorPrimary))
        button.setBorderWidth(0)
        button.setRadius(ujetStyle.buttonRadius.toInt())
    }

    @JvmStatic
    fun stylePickerView(ujetStyle: UjetStyle, pickerView: Picker) {
        pickerView.setColorTextCenter(ujetStyle.pickerTextCenterColor)
        pickerView.setColorTextNoCenter(ujetStyle.pickerTextNoCenterColor)
        pickerView.setSeparatorColor(ujetStyle.pickerSeparatorColor)
        pickerView.setTypeFace(ujetStyle.typeFace)
    }

    @JvmStatic
    fun overrideTypeface(ujetStyle: UjetStyle, textView: TextView) {
        if (textView.typeface != null) {
            textView.setTypeface(ujetStyle.typeFace, textView.typeface.style)
        } else {
            textView.typeface = ujetStyle.typeFace
        }
    }

    @JvmStatic
    fun stylePrimaryText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.textPrimaryColor)
    }

    @JvmStatic
    fun styleSecondaryText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.textSecondaryColor)
    }

    fun styleSecondaryLinkText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setLinkTextColor(ujetStyle.textSecondaryColor)
    }

    fun styleTertiaryText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.textTertiaryColor)
    }

    fun styleSecondaryText(ujetStyle: UjetStyle, textView: TextView, color: Int) {
        textView.setTextColor(if (ujetStyle.isDarkModeEnabled) ujetStyle.textSecondaryColor else color)
    }

    fun styleRemoteChatText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.remoteMessageTextColor)
        textView.typeface = ujetStyle.typeFace
    }

    fun styleRemoteChatLinkText(ujetStyle: UjetStyle, textView: TextView, isBackgroundDarkColored: Boolean = false) {
        textView.setLinkTextColor(ujetStyle.getRemoteMessageLinkTextColor(isBackgroundDarkColored))
    }

    fun styleLocalChatText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.localMessageTextColor)
        textView.typeface = ujetStyle.typeFace
    }

    fun styleLocalChatLinkText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setLinkTextColor(ujetStyle.localMessageTextColor)
    }

    fun stylePrimaryEditText(ujetStyle: UjetStyle, editText: EditText) {
        editText.setTextColor(ujetStyle.textPrimaryColor)
        editText.setHintTextColor(ujetStyle.pickerTextNoCenterColor)
        val colorStateList = ColorStateList.valueOf(ujetStyle.colorPrimary)
        editText.backgroundTintList = colorStateList
    }

    @JvmStatic
    fun styleSecondaryEditText(ujetStyle: UjetStyle, editText: EditText) {
        editText.setTextColor(ujetStyle.textSecondaryColor)
        editText.setHintTextColor(ujetStyle.pickerTextNoCenterColor)
        val colorStateList = ColorStateList.valueOf(ujetStyle.colorPrimary)
        editText.backgroundTintList = colorStateList
    }

    fun styleTertiaryEditText(ujetStyle: UjetStyle, editText: EditText) {
        editText.setTextColor(ujetStyle.textTertiaryColor)
        editText.setHintTextColor(ujetStyle.pickerTextNoCenterColor)
    }

    fun styleSurveyTitleText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.surveyTitleColor)
    }

    /**
     * Change the background composed of the bottom background (primary color) and
     * the white rounded rectangle background above on the bottom background
     *
     * @param ujetStyle UjetStyle
     * @param view      View
     * @link R.drawable.ujet_fragment_background
     */
    @JvmStatic
    fun styleFragmentBackground(ujetStyle: UjetStyle, view: View) {
        val context = view.context
        val background = ContextCompat.getDrawable(context, R.drawable.ujet_fragment_background) ?: return
        if (background is LayerDrawable) {
            if (background.numberOfLayers > 1) {
                setColor(background.getDrawable(0), ujetStyle.primaryBackgroundOverlayColor)
                setColor(background.getDrawable(1), ujetStyle.pickerBackgroundColor)
            } else if (background.numberOfLayers > 0) {
                setColor(background.getDrawable(0), ujetStyle.primaryBackgroundOverlayColor)
            }
        } else {
            setColor(background, ujetStyle.primaryBackgroundOverlayColor)
        }
        val paddingLeft = view.paddingLeft
        val paddingRight = view.paddingRight
        val paddingBottom = view.paddingBottom
        val paddingTop = view.paddingTop
        view.background = background
        view.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
    }

    fun styleChatCustomTitleText(ujetStyle: UjetStyle, textView: TextView) {
        textView.setTextColor(ujetStyle.getChatCustomTitleTextColor(ujetStyle.textPrimaryColor))
        val typefaceStyle: Int = ujetStyle.getChatCustomTitleTextStyle()
        val textSize: Float = ujetStyle.getChatCustomTitleTextSize()
        textView.setTypeface(ujetStyle.typeFace, typefaceStyle)
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
    }

    private fun setColor(drawable: Drawable, color: Int) {
        when (drawable) {
            is ShapeDrawable -> drawable.paint.color = color
            is GradientDrawable -> drawable.setColor(color)
            is ColorDrawable -> drawable.color = color
        }
    }

    /**
     * Change the background color
     *
     * @param ujetStyle UjetStyle
     * @param view      View
     */
    fun styleFragmentBottomBackground(ujetStyle: UjetStyle, view: View) {
        view.background = ColorDrawable(ujetStyle.primaryBackgroundOverlayColor)
    }

    /**
     * Change the background of Dialog
     */
    @JvmStatic
    fun styleDialogBackground(ujetStyle: UjetStyle, view: View) {
        view.setBackgroundColor(ujetStyle.dialogBackgroundColor)
    }

    fun applyAvatarBorderColor(ujetStyle: UjetStyle, @ColorInt color: Int, view: View) {
        val finalColor: Int = if (ujetStyle.isRemoveAgentIconBorderEnabled) {
            Color.argb(0, 0, 0, 0)
        } else {
            color
        }
        view.background.colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
            finalColor,
            SRC_IN
        )
    }

    fun applyAvatarBackgroundColor(ujetStyle: UjetStyle, view: View) {
        val finalColor = if (ujetStyle.isRemoveAgentIconBorderEnabled) {
            Color.argb(0, 0, 0, 0)
        } else {
            Color.WHITE
        }
        view.background.colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(finalColor, SRC_IN)
    }
}
