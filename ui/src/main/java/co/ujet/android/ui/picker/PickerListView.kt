package co.ujet.android.ui.picker

import android.content.Context
import android.util.AttributeSet
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.AbsListView
import android.widget.ListView
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.commons.util.MainLooper
import co.ujet.android.ui.R

class PickerListView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyle: Int = 0) :
    ListView(context, attrs, defStyle) {
    private var pickerUIItemClickListener: PickerUIItemClickListener? = null
    var pickerUIAdapter: PickerAdapter? = null
        private set
    var lastPositionBeforeConfigChanged = 0
    private var scrollEnabled = false
    private var lastPositionNotified = 0
    private var items: List<String>? = null
    private var which = 0

    init {
        if (isInEditMode) {
            createEditModeView(context)
        } else {
            init(items)
        }
    }

    /**
     * This method inflates the ListView to be visible from Preview Layout.
     * It use a mock list (item0, item1, item2...) and set the center in the middle of the list with
     * 'setSelection(...)'
     * It only applies in Preview Layout.
     *
     * @param context it's necessary to use in [PickerAdapter]
     */
    private fun createEditModeView(context: Context) {
        val entries = arrayOfNulls<String>(10)
        for (i in 0..9) {
            entries[i] = "item $i"
        }
        val entriesList = entries.asList()
        pickerUIAdapter = PickerAdapter(context, R.layout.ujet_picker_item, entriesList, entriesList.size / 2)
        adapter = pickerUIAdapter
        setSelection(entriesList.size / 2)
    }

    private fun init(items: List<String>?) {
        this.items = items
        val observer = viewTreeObserver
        observer.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                //This will be called as the layout is finished, prior to displaying.
                scrollEnabled = true
                viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
        setOnScrollListener(object : OnScrollListener {
            override fun onScrollStateChanged(view: AbsListView, scrollState: Int) {
                if (scrollState == 0) {
                    val position = 0.coerceAtLeast(itemInListCenter)
                    selectListItem(position)
                }
            }

            override fun onScroll(
                view: AbsListView, firstVisibleItem: Int, visibleItemCount: Int,
                totalItemCount: Int,
            ) {
                if (scrollEnabled) {
                    val position = 0.coerceAtLeast(itemInListCenter)
                    pickerUIItemClickListener?.onCenterItemChanged(position)
                }
            }
        })

        AccessibilityUtil.setupKeyboardAccessibility(this, onEnter = {
            val position = selectedItemPosition
            if (position != INVALID_POSITION) {
                setNewPositionCenter(position)
                onItemClickListener?.onItemClick(this, getChildAt(position - firstVisiblePosition), position, adapter?.getItemId(position) ?: -1)
            }
            true // now the event is handled and action is performed
        })
        onItemClickListener = OnItemClickListener { parent, view, position, id -> setNewPositionCenter(position) }
    }

    /**
     * This method is used by [Picker] to indicate to [PickerListView] the items to
     * display in the panel and a number of configurations.
     *
     * @param context           [PickerAdapter] needs a context to inflate the layout
     * @param items             elements to show in panel
     * @param idRequestPickerUI id of the element
     * @param position          position to set in the center of the list
     */
    fun setItems(context: Context, items: List<String>, idRequestPickerUI: Int, position: Int) {
        this.items = items
        which = idRequestPickerUI
        pickerUIAdapter = PickerAdapter(context, R.layout.ujet_picker_item, items, position)
        adapter = pickerUIAdapter
    }

    /**
     * Method to select an item from the list based on user selection and notifies [Picker] if necessary.
     *
     * @param position the position to select in the list and to set in the center
     * @param notify   indicates whether to notify the selection of an item
     */
    private fun selectListItem(position: Int, notify: Boolean = true) {
        setSelection(position)
        if (notify) {
            //We need to give the adapter time to draw the views
            MainLooper.postDelayed({
                checkNotNull(pickerUIItemClickListener) { "You must assign a valid PickerListView.PickerUIItemClickListener first!" }
                try {
                    pickerUIItemClickListener?.onItemClickItemPickerUI(which, position, items?.get(position))
                } catch (_: IndexOutOfBoundsException) { }
            }, 200)
        }
    }

    /**
     * Method to select an item and notify to [PickerAdapter] to set the style.
     *
     * @param position the position to select in the list and to set in the center
     */
    private fun setNewPositionCenter(position: Int) {
        pickerUIAdapter?.handleSelectEvent(position)
        selectListItem(position - 2)
    }

    /**
     * When the user is scrolling and stops, we need to get the item in the center of the list, save
     * this position and notify to adapter.
     */
    val itemInListCenter: Int
        get() {
            val position: Int
            if (lastPositionBeforeConfigChanged > 0) {
                position = lastPositionBeforeConfigChanged
                lastPositionBeforeConfigChanged = 0
            } else {
                position = pointToPosition(width / 2, height / 2)
            }
            //Only refresh adapter on different positions
            if (position != -1 && position != lastPositionNotified) {
                lastPositionNotified = position
                pickerUIAdapter?.handleSelectEvent(position)
            }
            return position - 2
        }

    /**
     * Set a callback listener for the item click.
     *
     * @param listener Callback instance.
     */
    fun setOnClickItemPickerUIListener(listener: PickerUIItemClickListener?) {
        pickerUIItemClickListener = listener
    }

    /**
     * Interface for a callback when the item has been clicked.
     */
    interface PickerUIItemClickListener {
        /**
         * Callback when the item has been clicked.
         *
         * @param which       id of the element has been clicked
         * @param position    Position of the current item.
         * @param valueResult Value of text of the current item.
         */
        fun onItemClickItemPickerUI(which: Int, position: Int, valueResult: String?)
        fun onCenterItemChanged(position: Int)
    }
}
