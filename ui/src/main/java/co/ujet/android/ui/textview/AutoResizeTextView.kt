package co.ujet.android.ui.textview

import android.content.Context
import android.text.Layout.Alignment.ALIGN_NORMAL
import kotlin.jvm.JvmOverloads
import android.widget.TextView
import android.util.TypedValue
import android.text.TextPaint
import android.text.StaticLayout
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import kotlin.math.max
import kotlin.math.min

class AutoResizeTextView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyle: Int = 0) :
    AppCompatTextView(context, attrs, defStyle) {

    // Interface for resize notifications
    interface OnTextResizeListener {
        fun onTextResize(textView: TextView?, oldSize: Float, newSize: Float)
    }

    // Registered resize listener
    private var textResizeListener: OnTextResizeListener? = null

    // Flag for text and/or size changes to force a resize
    private var needsResize = false

    // Text size that is set from code. This acts as a starting point for resizing
    private var mTextSize = textSize

    // Temporary upper bounds on the starting text size
    private var maxTextSize = 0f

    // Lower bounds for text size
    private var minTextSize = MIN_TEXT_SIZE

    // Text view line spacing multiplier
    private var spacingMult = 1.0f

    // Text view additional line spacing
    private var spacingAdd = 0.0f

    /**
     * Set flag to add ellipsis to text that overflows at the smallest text size
     */
    private var addEllipsis = true

    /**
     * When text changes, set the force resize flag to true and reset the text size.
     */
    override fun onTextChanged(text: CharSequence, start: Int, before: Int, after: Int) {
        needsResize = true
        // Since this view may be reused, it is good to reset the text size
        resetTextSize()
    }

    /**
     * If the text view size changed, set the force resize flag to true
     */
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        if (w != oldw || h != oldh) {
            needsResize = true
        }
    }

    /**
     * Override the set text size to update our internal reference values
     */
    override fun setTextSize(size: Float) {
        super.setTextSize(size)
        mTextSize = textSize
    }

    /**
     * Override the set text size to update our internal reference values
     */
    override fun setTextSize(unit: Int, size: Float) {
        super.setTextSize(unit, size)
        mTextSize = textSize
    }

    /**
     * Override the set line spacing to update our internal reference values
     */
    override fun setLineSpacing(add: Float, mult: Float) {
        super.setLineSpacing(add, mult)
        spacingMult = mult
        spacingAdd = add
    }

    /**
     * Reset the text to the original size
     */
    private fun resetTextSize() {
        if (mTextSize > 0) {
            super.setTextSize(TypedValue.COMPLEX_UNIT_PX, mTextSize)
            maxTextSize = mTextSize
        }
    }

    /**
     * Resize text after measuring
     */
    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        if (changed || needsResize) {
            val widthLimit = right - left - compoundPaddingLeft - compoundPaddingRight
            val heightLimit = bottom - top - compoundPaddingBottom - compoundPaddingTop
            resizeText(widthLimit, heightLimit)
        }
        super.onLayout(changed, left, top, right, bottom)
    }

    /**
     * Resize the text size with specified width and height
     */
    private fun resizeText(width: Int, height: Int) {
        // Do not resize if the view does not have dimensions or there is no text or larger text
        // accessibility setting is enabled
        if (text.isNullOrEmpty() || height <= 0 || width <= 0 || mTextSize == 0f) {
            return
        }
        if (transformationMethod != null) {
            text = transformationMethod.getTransformation(text, this)
        }

        // Store the current text size
        val oldTextSize = paint.textSize
        // If there is a max text size set, use the lesser of that and the default text size
        var targetTextSize = if (maxTextSize > 0) {
            min(mTextSize, maxTextSize)
        } else {
            mTextSize
        }

        // Get the required text height
        var textHeight = getTextHeight(text, paint, width, targetTextSize)

        // Until we either fit within our text view or we had reached our min text size, incrementally try smaller sizes
        while (textHeight > height && targetTextSize > minTextSize) {
            targetTextSize = max(targetTextSize - 2, minTextSize)
            textHeight = getTextHeight(text, paint, width, targetTextSize)
        }

        // Skip resizing the text when large text accessibility is enabled
        if (ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled(context)) {
            targetTextSize = oldTextSize
            textHeight = getTextHeight(text, paint, width, targetTextSize)
        }

        // Append an ellipsis, if we had reached our minimum text size and still don't fit or text does
        // not fit when large text accessibility is disabled
        if (!ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled(context) && addEllipsis &&
            textHeight > height && targetTextSize == minTextSize) {
            // Draw using a static layout
            // modified: use a copy of TextPaint for measuring
            val paint = TextPaint(paint)
            // Draw using a static layout
            val layout = StaticLayout(text, paint, width, ALIGN_NORMAL, spacingMult, spacingAdd, false)
            // Check that we have a least one line of rendered text
            if (layout.lineCount > 0) {
                // Since the line at the specific vertical position would be cut off,
                // we must trim up to the previous line
                val lastLine = layout.getLineForVertical(height) - 1
                // If the text would not even fit on a single line, clear it
                if (lastLine < 0) {
                    text = ""
                } else {
                    val start = layout.getLineStart(lastLine)
                    var end = layout.getLineEnd(lastLine)
                    var lineWidth = layout.getLineWidth(lastLine)
                    val ellipseWidth = paint.measureText(ELLIPSIS)

                    // Trim characters off until we have enough room to draw the ellipsis
                    while (width < lineWidth + ellipseWidth) {
                        lineWidth = paint.measureText(text?.subSequence(start, --end + 1).toString())
                    }
                    text = "${text?.subSequence(0, end).toString()}$ELLIPSIS"
                }
            }
        }

        // Some devices try to auto adjust line spacing, so force default line spacing
        // and invalidate the layout as a side effect
        setTextSize(TypedValue.COMPLEX_UNIT_PX, targetTextSize)
        setLineSpacing(spacingAdd, spacingMult)

        // Notify the listener if registered
        textResizeListener?.onTextResize(this, oldTextSize, targetTextSize)

        // Reset force resize flag
        needsResize = false
    }

    // Set the text size of the text paint object and use a static layout to render text off screen before measuring
    private fun getTextHeight(source: CharSequence?, paint: TextPaint, width: Int, textSize: Float): Int {
        // modified: make a copy of the original TextPaint object for measuring
        // (apparently the object gets modified while measuring, see also the
        // docs for TextView.getPaint() (which states to access it read-only)
        val paintCopy = TextPaint(paint)
        // Update the text paint object
        paintCopy.textSize = textSize
        // Measure using a static layout
        val layout = StaticLayout(source, paintCopy, width, ALIGN_NORMAL, spacingMult, spacingAdd, true)
        return layout.height
    }

    companion object {
        // Minimum text size for this text view
        const val MIN_TEXT_SIZE = 20f

        // Our ellipse string
        const val ELLIPSIS = "..."
    }
}
