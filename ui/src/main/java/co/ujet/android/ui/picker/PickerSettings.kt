package co.ujet.android.ui.picker

import android.os.Parcelable
import android.os.Parcel
import android.os.Parcelable.Creator
import co.ujet.android.ui.R
import java.util.ArrayList

class PickerSettings : Parcelable {
    var items: List<String?>? = null
    var colorTextCenter = 0
    var colorTextNoCenter = 0
    var backgroundColor = 0
    var position = 0
    var separatorColor = 0

    private constructor(builder: Builder) {
        items = builder.mItems
        colorTextCenter = builder.colorTextCenter
        colorTextNoCenter = builder.colorTextNoCenter
        backgroundColor = builder.backgroundColor
        position = builder.position
        separatorColor = builder.separatorColor
    }

    private constructor(input: Parcel) {
        val items = items
        if (items != null) {
            input.readStringList(items)
        } else {
            input.readStringList(ArrayList<String>().also { this.items = it })
        }
        colorTextCenter = input.readInt()
        colorTextNoCenter = input.readInt()
        backgroundColor = input.readInt()
        position = input.readInt()
        separatorColor = input.readInt()
    }

    override fun describeContents() = 0

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeStringList(items)
        dest.writeInt(colorTextCenter)
        dest.writeInt(colorTextNoCenter)
        dest.writeInt(backgroundColor)
        dest.writeInt(position)
        dest.writeInt(separatorColor)
    }

    class Builder {
        var mItems: List<String?>? = null
        var colorTextCenter = R.color.ujet_primary
        var colorTextNoCenter = R.color.ujet_disabled
        var backgroundColor = R.color.ujet_background
        var position = 0
        var separatorColor = 0

        fun withItems(mItems: List<String>, position: Int): Builder {
            this.mItems = mItems
            this.position = position
            return this
        }

        fun withItems(mItems: Array<String?>, position: Int): Builder {
            this.mItems = mItems.asList()
            this.position = position
            return this
        }

        fun withBackgroundColor(backgroundColor: Int): Builder {
            this.backgroundColor = backgroundColor
            return this
        }

        fun build(): PickerSettings {
            return PickerSettings(this)
        }
    }

    companion object {
        @JvmField
        val CREATOR: Creator<PickerSettings> = object : Creator<PickerSettings> {
            override fun createFromParcel(source: Parcel) = PickerSettings(source)

            override fun newArray(size: Int) = arrayOfNulls<PickerSettings>(size)
        }
    }
}
