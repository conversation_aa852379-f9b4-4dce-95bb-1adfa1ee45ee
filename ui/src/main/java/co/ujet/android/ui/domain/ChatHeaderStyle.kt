package co.ujet.android.ui.domain

import co.ujet.android.commons.libs.uson.SerializedName

data class ChatHeaderStyle(
    @SerializedName("text_content")
    var textContent: String? = null,

    @SerializedName("font")
    var font: FontStyle? = null,

    @SerializedName("visible")
    var messageTrayVisible: Boolean? = null,

    @SerializedName("show_avatar_icon")
    var agentIconVisible: Boolean? = null,

    @SerializedName("divider")
    var divider: DividerStyle? = null
)
