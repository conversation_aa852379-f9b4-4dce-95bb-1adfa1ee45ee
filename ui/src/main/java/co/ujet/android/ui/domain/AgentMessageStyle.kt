package co.ujet.android.ui.domain

import co.ujet.android.commons.libs.uson.SerializedName

data class AgentMessageStyle(
    @SerializedName("background_color_reference")
    var backgroundColor: String? = null,

    @SerializedName("corner_radius")
    var cornerRadius: String? = null,

    @SerializedName("border")
    var border: BorderStyle? = null,

    @SerializedName("font")
    var font: FontStyle? = null,

    @SerializedName("avatar")
    var icon: IconStyle? = null
)
