package co.ujet.android.ui.domain

import co.ujet.android.commons.libs.uson.SerializedName

data class ContentCardStyle(
    @SerializedName("background_color_reference")
    var backgroundColor: String? = null,

    @SerializedName("corner_radius")
    var cornerRadius: String? = null,

    @SerializedName("font")
    var font: FontStyle? = null,

    @SerializedName("border")
    var border: BorderStyle? = null,

    @SerializedName("title")
    var title: TextStyle? = null,

    @SerializedName("subtitle")
    var subtitle: TextStyle? = null,

    @SerializedName("body")
    var body: TextStyle? = null,

    @SerializedName("image")
    var image: ImageStyle? = null,

    @SerializedName("primary_button")
    var primaryButton: ButtonStyle? = null,

    @SerializedName("secondary_button")
    var secondaryButton: ButtonStyle? = null
)
