package co.ujet.android.ui.style

import android.content.Context
import android.content.res.Resources.NotFoundException
import android.content.res.TypedArray
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.util.TypedValue
import android.view.Gravity
import androidx.core.graphics.ColorUtils
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions
import co.ujet.android.modulemanager.entrypoints.configuration.Configuration
import co.ujet.android.ui.R
import co.ujet.android.ui.R.color
import co.ujet.android.ui.R.dimen
import co.ujet.android.ui.R.styleable
import co.ujet.android.ui.util.DesignUtil.findFont
import co.ujet.android.ui.util.DesignUtil.getColor
import java.lang.ref.WeakReference

class UjetStyle private constructor(
    context: Context,
    private val configuration: Configuration,
    private val attrsArrayLight: TypedArray,
    private val attrsArrayDark: TypedArray
) {
    private val applicationContext = context.applicationContext
    private val displayMetrics = applicationContext.resources.displayMetrics
    private val cachedDimensions = mutableMapOf<Float, Float>()

    fun dpToPx(dp: Float): Float {
        return cachedDimensions[dp] ?: TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, displayMetrics).apply {
            cachedDimensions[dp] = this
        }
    }

    fun dpToPx(dp: Int) = dpToPx(dp.toFloat())
    fun pxToSp(px: Float) = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_PX, px, displayMetrics)


    // TODO: Use DesignUtil.getColor
    fun getColor(colorResource: Int): Int {
        return getColor(applicationContext, colorResource)
    }

    // TODO: Use DesignUtil.findFont
    val typeFace: Typeface?
        get() {
            val typeFace = ujetAttrs.getString(R.styleable.UjetAttrs_ujet_typeFace) ?: return null
            return findFont(applicationContext, typeFace, null)
        }
    val colorPrimary: Int
        get() = ujetAttrs.getColor(R.styleable.UjetAttrs_ujet_colorPrimary, getColor(R.color.ujet_primary))

    fun getColorPrimary(alpha: Int): Int {
        return ujetAttrs.getColor(R.styleable.UjetAttrs_ujet_colorBackground, ColorUtils.setAlphaComponent(colorPrimary, alpha))
    }

    val colorPrimaryDark: Int
        get() = ujetAttrs.getColor(R.styleable.UjetAttrs_ujet_colorPrimaryDark, getColor(R.color.ujet_primary_dark))
    private val colorBackground: Int
        get() = ujetAttrs.getColor(
            R.styleable.UjetAttrs_ujet_colorBackground,
            ((colorPrimary and 0xFFFFFF) + 0x88000000).toInt()
        )
    private val colorBackgroundDark: Int
        get() = ujetAttrs.getColor(
            R.styleable.UjetAttrs_ujet_colorBackgroundDark,
            getColor(applicationContext, R.color.ujet_background_overlay_color_dark)
        )
    private val colorWindowBackground: Int
        get() = ujetAttrs.getColor(
            R.styleable.UjetAttrs_ujet_colorWindowBackground,
            getColor(applicationContext, R.color.ujet_white)
        )
    private val colorWindowBackgroundDark: Int
        get() = ujetAttrs.getColor(
            R.styleable.UjetAttrs_ujet_colorWindowBackgroundDark,
            getColor(applicationContext, R.color.ujet_picker_background_color_dark)
        )
    val colorText: Int
        get() = ujetAttrs.getColor(R.styleable.UjetAttrs_ujet_colorText, getColor(R.color.ujet_text_secondary_light))
    val colorError: Int
        get() = getColor(R.color.ujet_error)
    val colorDanger: Int
        get() = getColor(R.color.ujet_danger)
    val companyLogo: Drawable?
        get() = loadDrawable(ujetAttrs, R.styleable.UjetAttrs_ujet_companyLogo)
    val defaultAvatar: Drawable?
        get() = loadDrawable(ujetAttrs, R.styleable.UjetAttrs_ujet_defaultAvatar)
    val colorPrimaryLight: Int
        get() = ColorUtils.blendARGB(colorPrimary, Color.WHITE, 0.2f)
    val buttonRadius: Float
        get() = ujetAttrs.getDimension(
            R.styleable.UjetAttrs_ujet_buttonRadius,
            applicationContext.resources.getDimension(R.dimen.ujet_button_radius)
        )

    val primaryBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_background_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_white)
        }

    val contentCardButtonSecondaryBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_content_card_button_secondary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_content_card_button_secondary_light)
        }
    val contentCardButtonDisableSecondaryBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_content_card_button_secondary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_content_card_button_secondary_light)
        }
    val primaryBackgroundOverlayColor: Int
        get() = if (isDarkModeEnabled) {
            colorBackgroundDark
        } else {
            colorBackground
        }
    val dialogBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            colorWindowBackgroundDark
        } else {
            colorWindowBackground
        }
    val textPrimaryColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_primary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_primary_light)
        }
    val textSecondaryColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_secondary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_secondary_light)
        }
    val textTertiaryColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_tertiary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_tertiary_light)
        }
    val remoteMessageTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_remote_message_text_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_chat_remote_message_text_color)
        }
    val localMessageTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_local_message_text_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_chat_local_message_text_color)
        }
    val textFocusBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_focus_background_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_focus_background)
        }
    val pickerBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            colorWindowBackgroundDark
        } else {
            colorWindowBackground
        }
    val pickerTextCenterColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_picker_highlighted_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_primary_light)
        }
    val pickerTextNoCenterColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_picker_disabled_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_disabled)
        }
    val pickerSeparatorColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_picker_separator_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_disabled)
        }
    val channelIconColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_channel_icon_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_white)
        }
    val channelBorderColor: Int
        get() = if (isDarkModeEnabled) {
            getDarkModePrimaryColor(R.color.ujet_channel_border_color_dark)
        } else {
            colorPrimary
        }
    val ratingStarBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_picker_disabled_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_picker_disabled_color_light)
        }
    val chatFooterBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_message_background_dark)
        } else {
            getColor(applicationContext, R.color.ujet_chat_message_background)
        }
    val chatHumanAgentMessageBubbleColor: Int
        get() = if (isDarkModeEnabled) {
            colorPrimary
        } else {
            getColorPrimary(25)
        }
    val chatHumanAgentMessageBubbleBorderColor: Int
        get() = colorPrimary
    val chatVirtualAgentMessageBubbleColor: Int
        get() = if (isDarkModeEnabled) getColor(applicationContext, R.color.ujet_chat_message_background_dark) else getColor(
            applicationContext,
            R.color.ujet_white
        )
    val chatVirtualAgentInlineButtonBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_message_background_dark)
        } else {
            getColorPrimary(10)
        }
    val chatVirtualAgentMessageBubbleBorderColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_remote_virtual_agent_message_border_dark)
        } else {
            colorPrimary
        }
    val chatEndUserMessageTopBubbleColor: Int
        get() = if (isDarkModeEnabled) getColor(applicationContext, R.color.ujet_chat_local_message_background_dark) else getColor(
            applicationContext,
            R.color.ujet_chat_local_message_background
        )
    val chatTypingIndicatorColor: Int
        get() = if (isDarkModeEnabled) getColor(applicationContext, R.color.ujet_white) else getColor(
            applicationContext,
            R.color.ujet_black
        )
    val imageTintColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_channel_icon_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_text_secondary_light)
        }
    val dividerBackgroundColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_gray)
        } else {
            getColor(applicationContext, R.color.ujet_gray_light)
        }
    val textLoadingViewColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_tertiary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_disabled_text_mask)
        }
    val disabledChannelViewColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_focus_background_dark)
        } else {
            getColor(applicationContext, R.color.ujet_disabled_background)
        }
    val disabledTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_text_tertiary_dark)
        } else {
            getColor(applicationContext, R.color.ujet_disabled_text)
        }
    val surveyTitleColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_survey_gray)
        } else {
            getColor(applicationContext, R.color.ujet_text_secondary_light)
        }
    val surveyStarColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_survey_gray_dark)
        } else {
            getColor(applicationContext, R.color.ujet_survey_stars_gray)
        }
    val surveyOutlineColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_survey_gray_dark)
        } else {
            getColor(applicationContext, R.color.ujet_survey_gray)
        }
    val surveyFreeFormBackgroundColor: Int
        get() = if (isDarkModeEnabled) getColor(applicationContext, R.color.ujet_survey_free_form_gray_dark) else getColor(
            applicationContext,
            R.color.ujet_survey_free_form_gray_light
        )
    val dialogTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_gray_light)
        } else {
            getColor(applicationContext, R.color.ujet_gray)
        }
    val emailSuccessIcon: Int
        get() = if (isDarkModeEnabled) {
            R.drawable.ujet_email_success_icon_dark_mode
        } else {
            R.drawable.ujet_email_success_icon_light_mode
        }
    val isDarkModeEnabled: Boolean
        get() {
            val currentNightMode =
                applicationContext.resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
            return configuration.isDarkModeEnabled() && currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
        }
    val isRemoveAgentIconBorderEnabled: Boolean
        get() = configuration.isRemoveAgentIconBorderEnabled()

    val customStylesOptions: UjetStylesOptions?
        get() = configuration.getUjetStylesOptions()

    val webFormTitleTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_web_form_title_text_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_chat_web_form_title_text_color)
        }
    val webFormErrorTextColor: Int
        get() = if (isDarkModeEnabled) {
            getColor(applicationContext, R.color.ujet_chat_web_form_error_text_color_dark)
        } else {
            getColor(applicationContext, R.color.ujet_chat_web_form_error_text_color)
        }

    fun getRemoteMessageLinkTextColor(isBackgroundDarkColored: Boolean): Int {
        return if (isDarkModeEnabled) {
            val darkModeColor = if (isBackgroundDarkColored) {
                color.ujet_message_link_text_color
            } else {
                color.ujet_message_link_text_color_dark
            }
            getColor(applicationContext, darkModeColor)
        } else {
            getColor(applicationContext, color.ujet_message_link_text_color)
        }
    }

    fun getChatQuickReplyTypeFace(): Typeface? {
        val fontName = ujetAttrs.getString(styleable.UjetAttrs_ujet_chatQuickReplyButtonTypeFace) ?: return null
        return findFont(applicationContext, fontName, null)
    }

    fun getChatQuickReplyButtonTextColor(): Int {
        return ujetAttrs.getColor(
            if (isDarkModeEnabled) styleable.UjetAttrs_ujet_colorChatQuickReplyButtonTextDark else styleable.UjetAttrs_ujet_colorChatQuickReplyButtonText,
            getColor(applicationContext, color.ujet_chat_quick_reply_buttons_text_color)
        )
    }

    fun getChatQuickReplyButtonPressedTextColor(): Int {
        return ujetAttrs.getColor(
            if (isDarkModeEnabled) styleable.UjetAttrs_ujet_colorChatQuickReplyButtonPressedTextDark else styleable.UjetAttrs_ujet_colorChatQuickReplyButtonPressedText,
            getColor(applicationContext, color.ujet_chat_quick_reply_buttons_text_color)
        )
    }

    fun getChatQuickReplyBackgroundColor(): Int {
        return ujetAttrs.getColor(
            if (isDarkModeEnabled) styleable.UjetAttrs_ujet_colorChatQuickReplyButtonBackgroundDark else styleable.UjetAttrs_ujet_colorChatQuickReplyButtonBackground,
            getColor(applicationContext, color.ujet_chat_quick_reply_buttons_background_color)
        )
    }

    fun getChatQuickReplyButtonPressedBackgroundColorPrimary(): Int {
        return ujetAttrs.getColor(
            if (isDarkModeEnabled) styleable.UjetAttrs_ujet_colorChatQuickReplyButtonPressedBackgroundDark else styleable.UjetAttrs_ujet_colorChatQuickReplyButtonPressedBackground,
            getColor(applicationContext, color.ujet_chat_quick_reply_buttons_background_color)
        )
    }

    fun getChatQuickReplyButtonStrokeColor(): Int {
        return ujetAttrs.getColor(
            if (isDarkModeEnabled) styleable.UjetAttrs_ujet_colorChatQuickReplyButtonStrokeDark else styleable.UjetAttrs_ujet_colorChatQuickReplyButtonStroke,
            getColor(applicationContext, color.ujet_chat_quick_reply_buttons_stroke_color)
        )
    }

    fun getChatQuickReplyButtonStrokeWidth(): Int {
        return ujetAttrs.getDimensionPixelSize(styleable.UjetAttrs_ujet_chatQuickReplyButtonStrokeWidth, dpToPx(1f).toInt())
    }

    fun getChatQuickReplyButtonCornerRadius(): Int {
        return ujetAttrs.getDimensionPixelSize(
            styleable.UjetAttrs_ujet_chatQuickReplyButtonCornerRadius,
            applicationContext.resources.getDimensionPixelSize(dimen.ujet_chat_quick_reply_buttons_corner_radius)
        )
    }

    fun getChatQuickReplyButtonVerticalMargins(): Int {
        return ujetAttrs.getDimensionPixelSize(
            styleable.UjetAttrs_ujet_chatQuickReplyButtonVerticalMargin,
            applicationContext.resources.getDimensionPixelSize(dimen.ujet_chat_quick_reply_buttons_vertical_margin)
        )
    }

    fun getChatQuickReplyGravity(): Int {
        return ujetAttrs.getInt(styleable.UjetAttrs_ujet_chatQuickReplyButtonAlignment, Gravity.LEFT)
    }

    fun getChatQuickReplyHorizontalPadding(): Int {
        return ujetAttrs.getDimensionPixelSize(
            styleable.UjetAttrs_ujet_chatQuickReplyButtonHorizontalPadding,
            applicationContext.resources.getDimensionPixelSize(dimen.ujet_chat_quick_reply_buttons_horizontal_padding)
        )
    }

    fun getChatQuickReplyVerticalPadding(): Int {
        return ujetAttrs.getDimensionPixelSize(
            styleable.UjetAttrs_ujet_chatQuickReplyButtonVerticalPadding,
            applicationContext.resources.getDimensionPixelSize(dimen.ujet_chat_quick_reply_buttons_vertical_padding)
        )
    }

    private fun getDarkModePrimaryColor(colorResourceId: Int): Int {
        return attrsArrayDark.getColor(R.styleable.UjetAttrs_ujet_colorPrimary, getColor(applicationContext, colorResourceId))
    }

    fun getChatCustomTitleTextColor(defaultColor: Int): Int {
        return if (isDarkModeEnabled) ujetAttrs.getColor(
            styleable.UjetAttrs_ujet_chatCustomHeaderTextColorDark,
            defaultColor
        ) else ujetAttrs.getColor(
            styleable.UjetAttrs_ujet_chatCustomHeaderTextColor, defaultColor
        )
    }

    fun getChatCustomTitleTextStyle(): Int {
        return ujetAttrs.getInt(styleable.UjetAttrs_ujet_chatCustomHeaderTextStyle, 0)
    }

    fun getChatCustomTitleTextSize(): Float {
        return ujetAttrs.getDimension(
            styleable.UjetAttrs_ujet_chatCustomHeaderTextSize,
            applicationContext.resources.getDimensionPixelSize(dimen.ujet_heading_3)
                .toFloat()
        )
    }

    private val ujetAttrs: TypedArray
        get() = if (isDarkModeEnabled) {
            attrsArrayDark
        } else {
            attrsArrayLight
        }

    private fun loadDrawable(typedArray: TypedArray, resId: Int): Drawable? {
        return try {
            typedArray.getDrawable(resId)
        } catch (e: NotFoundException) {
            null
        }
    }

    companion object {
        private var reference: WeakReference<UjetStyle>? = null
        private const val LIGHT_THEME = "Light"
        private const val DARK_THEME = "Dark"

        @JvmStatic
        fun getInstance(context: Context, configuration: Configuration): UjetStyle {
            val instance = reference?.get() ?: UjetStyle(
                context, configuration,
                context.obtainStyledAttributes(getUjetStyleId(context, LIGHT_THEME, configuration), R.styleable.UjetAttrs),
                context.obtainStyledAttributes(getUjetStyleId(context, DARK_THEME, configuration), R.styleable.UjetAttrs)
            ).apply {
                reference = WeakReference(this)
            }
            return instance
        }

        private fun getUjetStyleId(context: Context, theme: String, configuration: Configuration): Int {
            val defaultUjetStyleId = configuration.getUjetStyleId()
            val ujetStyleId = context.resources.getIdentifier(
                String.format("%s.%s", context.resources.getResourceName(defaultUjetStyleId), theme),
                "style", context.packageName
            )
            return if (ujetStyleId == 0) {
                defaultUjetStyleId
            } else {
                ujetStyleId
            }
        }
    }
}
