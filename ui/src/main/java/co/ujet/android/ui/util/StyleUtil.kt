package co.ujet.android.ui.util

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.PorterDuff.Mode.SRC_IN
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.MetricAffectingSpan
import android.util.TypedValue
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.modulemanager.common.ui.domain.BorderStyle
import co.ujet.android.modulemanager.common.ui.domain.FontStyle
import co.ujet.android.ui.R
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.style.UjetViewStyler.getDisabledColor
import co.ujet.android.ui.style.UjetViewStyler.getFocusColor
import com.google.android.material.button.MaterialButton
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.shape.CornerFamily

object StyleUtil {
    const val RESOURCE_NOT_FOUND = 0
    private val cachedColorResIds = mutableMapOf<String?, Int>()
    private val cachedDrawableResIds = mutableMapOf<String?, Int>()

    fun updateFontStyle(context: Context, textView: TextView, font: FontStyle?) {
        updateFontStyle(context, textView, font?.style, font?.family, font?.size, font?.colorReference)
    }

    fun updateFontStyle(
        context: Context,
        textView: TextView,
        fontStyle: String?,
        fontFamily: String?,
        fontSize: Int?,
        fontColor: String?
    ) {
        val colorResId = getColorResIdByName(context, fontColor)
        if (colorResId != RESOURCE_NOT_FOUND) {
            textView.setTextColor(ContextCompat.getColor(context, colorResId))
        }
        val textStyle = extractFontStyle(fontStyle)
        val typefaceFamily = fontFamily?.let {
            DesignUtil.findFont(context, it, null)
        }
        textView.typeface = Typeface.create(typefaceFamily, textStyle)
        fontSize?.toFloat()?.let { textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, it) }
    }

    fun updateFontStyle(context: Context, editText: EditText, font: FontStyle?) {
        updateFontStyle(context, editText as TextView, font)
        val colorResId = getColorResIdByName(context, font?.colorReference)
        if (colorResId != RESOURCE_NOT_FOUND) {
            editText.setHintTextColor(ContextCompat.getColor(context, colorResId))
        }
    }

    fun updateFontStyle(context: Context, fancyButton: FancyButton, font: FontStyle?) {
        val colorResId = getColorResIdByName(context, font?.colorReference)
        if (colorResId != RESOURCE_NOT_FOUND) {
            fancyButton.setTextColor(ContextCompat.getColor(context, colorResId))
            fancyButton.setFocusTextColor(getFocusColor(ContextCompat.getColor(context, colorResId)))
            fancyButton.setDisabledTextColor(getDisabledColor(ContextCompat.getColor(context, colorResId)))
        }
        val textStyle = extractFontStyle(font?.style)
        val typefaceFamily = font?.family?.let { DesignUtil.findFont(context, it, null) }
        fancyButton.setCustomTypeFace(Typeface.create(typefaceFamily, textStyle))
        font?.size?.toFloat()?.let { fancyButton.setTextSize(it) }
    }

    fun updateFontStyle(context: Context, materialButton: MaterialButton, font: FontStyle?) {
        val colorResId = getColorResIdByName(context, font?.colorReference)
        if (colorResId != RESOURCE_NOT_FOUND) {
            materialButton.setTextColor(ContextCompat.getColor(context, colorResId))
        }
        val textStyle = extractFontStyle(font?.style)
        val typefaceFamily = font?.family?.let { DesignUtil.findFont(context, it, null) }
        materialButton.typeface = Typeface.create(typefaceFamily, textStyle)
        font?.size?.toFloat()?.let { materialButton.textSize = it }
    }

    fun updateFontStyle(context: Context, menuItem: MenuItem, font: FontStyle?) {
        val textStyle = extractFontStyle(font?.style)
        val menuText = when (textStyle) {
            in listOf(Typeface.ITALIC, Typeface.BOLD_ITALIC) -> "${menuItem.title}  "
            else -> menuItem.title
        }
        val spannableText = SpannableString(menuText)
        val startIndex = 0
        val endIndex = spannableText.length
        val colorResId = getResIdByName(context, font?.colorReference, "color")
        if (colorResId != RESOURCE_NOT_FOUND) {
            spannableText.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(context, colorResId)),
                startIndex,
                endIndex,
                Spannable.SPAN_INCLUSIVE_INCLUSIVE
            )
        }
        val typefaceFamily = font?.family?.let { DesignUtil.findFont(context, it, null) }
        val typeFace = Typeface.create(typefaceFamily, textStyle)
        spannableText.setSpan(CustomTypefaceSpan(typeFace), startIndex, endIndex, 0)
        font?.size?.toFloat()?.let {
            val fontSize = DesignUtil.spToPx(context, it)
            spannableText.setSpan(AbsoluteSizeSpan(fontSize, false), startIndex, endIndex, 0)
        }
        menuItem.title = spannableText
    }

    fun updateBackgroundStyle(textView: TextView, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?) {
        if (isBackgroundStyleAvailable(textView.context, backgroundColor, cornerRadius, borderStyle)) {
            textView.background = getBackgroundDrawable(textView.context, backgroundColor, cornerRadius, borderStyle)
            textView.context.resources.getDimensionPixelSize(R.dimen.ujet_chat_message_padding).let {
                textView.setPaddingRelative(it, it, it, it)
            }
        }
    }

    fun updateBackgroundStyle(imageView: ImageView, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?) {
        if (isBackgroundStyleAvailable(imageView.context, backgroundColor, cornerRadius, borderStyle)) {
            imageView.background = getBackgroundDrawable(imageView.context, backgroundColor, cornerRadius, borderStyle)
            getTextPaddingWithInBorder(imageView.context, cornerRadius, borderStyle)?.let {
                imageView.setPaddingRelative(it, it, it, it)
            }
        }
    }

    fun updateBackgroundStyle(view: View, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?) {
        updateBackgroundStyle(view, getColorResIdByName(view.context, backgroundColor), cornerRadius, borderStyle)
    }

    fun updateBackgroundStyle(view: View, @ColorRes backgroundColorRes: Int, cornerRadius: String?, borderStyle: BorderStyle?) {
        if (isBackgroundStyleAvailable(view.context, backgroundColorRes, cornerRadius, borderStyle)) {
            view.background = getBackgroundDrawable(view.context, backgroundColorRes, cornerRadius, borderStyle)
        }
        getTextPaddingWithInBorder(view.context, cornerRadius, borderStyle)?.let {
            view.setPaddingRelative(it, it, it, it)
        }
    }

    fun updateBackgroundStyle(fancyButton: FancyButton, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?) {
        val backgroundColorResId = getColorResIdByName(fancyButton.context, backgroundColor)
        if (backgroundColorResId != RESOURCE_NOT_FOUND) {
            fancyButton.setBackgroundColor(ContextCompat.getColor(fancyButton.context, backgroundColorResId))
            fancyButton.setFocusBackgroundColor(getFocusColor(ContextCompat.getColor(fancyButton.context, backgroundColorResId)))
            fancyButton.setDisabledBackgroundColor(getDisabledColor(ContextCompat.getColor(fancyButton.context, backgroundColorResId)))
        }
        cornerRadius?.toInt()?.let { fancyButton.setRadius(it) }
        borderStyle?.width?.let { fancyButton.setBorderWidth(it) }
        val borderColorResId = getColorResIdByName(fancyButton.context, borderStyle?.color)
        if (borderColorResId != RESOURCE_NOT_FOUND) {
            fancyButton.setBorderColor(ContextCompat.getColor(fancyButton.context, borderColorResId))
            fancyButton.setFocusBorderColor(getFocusColor(ContextCompat.getColor(fancyButton.context, borderColorResId)))
            fancyButton.setDisabledBorderColor(getDisabledColor(ContextCompat.getColor(fancyButton.context, borderColorResId)))
        }

        getTextPaddingWithInBorder(fancyButton.context, cornerRadius, borderStyle)?.let {
            fancyButton.setPaddingRelative(it, it, it, it)
        }
    }

    fun updateBackgroundStyle(materialButton: MaterialButton, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?) {
        val backgroundColorResId = getColorResIdByName(materialButton.context, backgroundColor)
        if (backgroundColorResId != RESOURCE_NOT_FOUND) {
            materialButton.setBackgroundColor(ContextCompat.getColor(materialButton.context, backgroundColorResId))
        }
        cornerRadius?.toInt()?.let { materialButton.cornerRadius = it }
        borderStyle?.width?.let { materialButton.strokeWidth = it }
        val borderColorResId = getColorResIdByName(materialButton.context, borderStyle?.color)
        if (borderColorResId != RESOURCE_NOT_FOUND) {
            materialButton.strokeColor = ColorStateList.valueOf(ContextCompat.getColor(materialButton.context, borderColorResId))
        }

        getTextPaddingWithInBorder(materialButton.context, cornerRadius, borderStyle)?.let {
            materialButton.setPaddingRelative(it, it, it, it)
        }
    }

    fun applyAvatarBorderColor(ujetStyle: UjetStyle, borderColor: String?, view: View) {
        val borderColorResId = getColorResIdByName(view.context, borderColor)
        if (borderColorResId != RESOURCE_NOT_FOUND) {
            UjetViewStyler.applyAvatarBorderColor(ujetStyle, ContextCompat.getColor(view.context, borderColorResId), view)
        }
    }

    fun applyPaddingToCardLayout(context: Context,
                                 cornerRadius: Float,
                                 ujetStyle: UjetStyle,
                                 borderStyle: BorderStyle?,
                                 cardLayout: View,
                                 imageNotAvailable: Boolean) {
        val borderWidth = if (getColorResIdByName(context, borderStyle?.color) == RESOURCE_NOT_FOUND)
            0
        else
            ujetStyle.dpToPx(borderStyle?.width?.toFloat() ?: 0f).toInt()

        if (imageNotAvailable) {
            cardLayout.setPaddingRelative(
                borderWidth,
                (cornerRadius / 5f).toInt() + borderWidth / 2,
                borderWidth,
                ((cornerRadius / 3f).toInt() + borderWidth).coerceAtLeast(ujetStyle.dpToPx(16f).toInt() + borderWidth)
            )
        } else {
            cardLayout.setPaddingRelative(
                borderWidth,
                borderWidth,
                borderWidth,
                ((cornerRadius / 2f).toInt() + borderWidth).coerceAtLeast(ujetStyle.dpToPx(16f).toInt() + borderWidth)
            )
        }
    }

    fun applyCornerRadiusToImageView(image: ShapeableImageView, cornerRadius: Float) {
        image.shapeAppearanceModel = image.shapeAppearanceModel
            .toBuilder()
            .setTopLeftCorner(CornerFamily.ROUNDED, cornerRadius)
            .setTopRightCorner(CornerFamily.ROUNDED, cornerRadius)
            .build()
    }

    private fun getResIdByName(context: Context, resIdName: String?, resType: String): Int {
        if (resIdName == null) {
            return RESOURCE_NOT_FOUND
        }
        return context.resources.getIdentifier(resIdName, resType, context.packageName)
    }

    fun getColorResIdByName(context: Context, resIdName: String?): Int {
        return cachedColorResIds[resIdName] ?: (resIdName?.let { getResIdByName(context, it, "color") } ?: RESOURCE_NOT_FOUND).apply {
            cachedColorResIds[resIdName] = this
        }
    }

    fun getDrawableResIdByName(context: Context, resIdName: String?): Int {
        return cachedDrawableResIds[resIdName] ?: (resIdName?.let { getResIdByName(context, it, "drawable") } ?: RESOURCE_NOT_FOUND).apply {
            cachedDrawableResIds[resIdName] = this
        }
    }

    fun setCursorColor(view: EditText, @ColorRes color: Int) {
        try {
            // Get the cursor resource id
            var field = TextView::class.java.getDeclaredField("mCursorDrawableRes")
            field.isAccessible = true
            val drawableResId = field.getInt(view)

            // Get the editor
            field = TextView::class.java.getDeclaredField("mEditor")
            field.isAccessible = true
            val editor: Any = field.get(view)

            // Get the drawable and set a color filter
            val drawable = ContextCompat.getDrawable(view.context, drawableResId)
            drawable?.setColorFilter(ContextCompat.getColor(view.context, color), SRC_IN)
            val drawables = arrayOf(drawable, drawable)

            // Set the drawables
            field = editor.javaClass.getDeclaredField("mCursorDrawable")
            field.isAccessible = true
            field.set(editor, drawables)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Returns true if the default styles have been applied
     */
    fun applyDefaultStyle(context: Context, ujetStyle: UjetStyle, containerLayout: View, backgroundColor: String?,
                          cornerRadius: String?, borderStyle: BorderStyle?, background: Drawable?,
                          backgroundColorFilter: Int, layoutParams: ViewGroup.LayoutParams?): Boolean {
        //When there is no background style available use default existing style
        val shouldApplyDefaultStyles = !isBackgroundStyleAvailable(context, backgroundColor, cornerRadius, borderStyle)
        if (shouldApplyDefaultStyles) {
            background?.let {
                containerLayout.background = it
                containerLayout.background.colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                    backgroundColorFilter, BlendModeCompat.SRC_IN
                )
            }
            layoutParams?.let { containerLayout.layoutParams = it }
            val padding = ujetStyle.dpToPx(10f).toInt()
            containerLayout.setPadding(padding, padding, padding, padding)
            containerLayout.minimumHeight = ujetStyle.dpToPx(32f).toInt()
            applyCornerRadiusToDefaultStyle(cornerRadius, containerLayout.background as? GradientDrawable)
        }
        return shouldApplyDefaultStyles
    }

    fun isBackgroundStyleAvailable(context: Context, backgroundColor: String?, cornerRadius: String?, borderStyle: BorderStyle?): Boolean {
        return isBackgroundStyleAvailable(context, getColorResIdByName(context, backgroundColor), cornerRadius, borderStyle)
    }

    private fun isBackgroundStyleAvailable(context: Context,
                                           @ColorRes backgroundColorRes: Int,
                                           cornerRadius: String?,
                                           borderStyle: BorderStyle?): Boolean {
        return when {
            backgroundColorRes == RESOURCE_NOT_FOUND && getColorResIdByName(context, borderStyle?.color) == RESOURCE_NOT_FOUND -> false
            backgroundColorRes != RESOURCE_NOT_FOUND -> true
            (cornerRadius?.toFloat() ?: 0f) > 0 -> true
            (borderStyle?.width ?: 0) > 0 -> true
            getColorResIdByName(context, borderStyle?.color) != RESOURCE_NOT_FOUND -> true
            else -> false
        }
    }

    //If there is only corner radius available then apply it to default background
    fun applyCornerRadiusToDefaultStyle(cornerRadius: String?, drawable: GradientDrawable?) {
        cornerRadius?.toFloat()?.let { drawable?.setCornerRadius(it) }
    }

    //We need border width and border color to apply border for consumer and human agent messages, but
    // virtual agent message has default border so If there is only border width available then apply it to.
    fun applyBorderWidthToDefaultStyle(context: Context, borderColor: String?, borderWidth: Int?,
                                       defaultBorderColor: Int, drawable: GradientDrawable?) {
        //When there is no border color style available use default border color
        val strokeColorResId = getColorResIdByName(context, borderColor)
        val strokeColor = if (strokeColorResId != RESOURCE_NOT_FOUND) {
            ContextCompat.getColor(context, strokeColorResId)
        } else {
            defaultBorderColor
        }
        borderWidth?.let { drawable?.setStroke(it, strokeColor) }
    }

    private fun getBackgroundDrawable(
        context: Context,
        backgroundColor: String?,
        cornerRadius: String?,
        borderStyle: BorderStyle?
    ): GradientDrawable {
        return getBackgroundDrawable(context, getColorResIdByName(context, backgroundColor), cornerRadius, borderStyle)
    }

    private fun getBackgroundDrawable(
        context: Context,
        @ColorRes backgroundColorRes: Int,
        cornerRadius: String?,
        borderStyle: BorderStyle?
    ): GradientDrawable {
        val drawable = GradientDrawable()
        if (backgroundColorRes != RESOURCE_NOT_FOUND) {
            drawable.setColor(ContextCompat.getColor(context, backgroundColorRes))
        }
        cornerRadius?.toFloat()?.let { drawable.cornerRadius = DesignUtil.dpToPx(context, it) }
        val strokeColorResId = getColorResIdByName(context, borderStyle?.color)
        if (strokeColorResId != RESOURCE_NOT_FOUND) {
            borderStyle?.width?.let {
                val borderWidth = DesignUtil.dpToPx(context, it).toInt()
                drawable.setStroke(borderWidth, ContextCompat.getColor(context, strokeColorResId))
            }
        }
        return drawable
    }

    private fun extractFontStyle(style: String?): Int {
        return when {
            style == null -> Typeface.NORMAL
            style.contains(FontStyle.Style.BOLD.value, true) -> when {
                style.contains(FontStyle.Style.ITALIC.value, true) -> Typeface.BOLD_ITALIC
                else -> Typeface.BOLD
            }

            style.contains(FontStyle.Style.ITALIC.value, true) -> Typeface.ITALIC
            else -> Typeface.NORMAL
        }
    }

    fun createBorderedBackground(
        context: Context,
        @ColorInt backgroundColor: Int,
        borderWidth: Float,
        borderColor: String?,
        borderRadius: Float
    ): GradientDrawable {
        val shape = GradientDrawable()
        shape.shape = GradientDrawable.RECTANGLE
        shape.cornerRadius = borderRadius
        shape.setColor(backgroundColor)
        val strokeColorResId = getColorResIdByName(context, borderColor)
        if (strokeColorResId != RESOURCE_NOT_FOUND) {
            shape.setStroke(borderWidth.toInt(), ContextCompat.getColor(context, strokeColorResId))
        }
        return shape
    }

    private class CustomTypefaceSpan(private val typeface: Typeface?) : MetricAffectingSpan() {
        override fun updateDrawState(paint: TextPaint) {
            paint.typeface = typeface
        }

        override fun updateMeasureState(paint: TextPaint) {
            paint.typeface = typeface
        }
    }

    // To avoid text going out of border, need to apply Padding = BorderWidth + (cornerRadius/4)
    // Apply only if border has background color set
    fun getTextPaddingWithInBorder(context: Context, cornerRadius: String?, borderStyle: BorderStyle?): Int? {
        return if (getColorResIdByName(context, borderStyle?.color) == RESOURCE_NOT_FOUND) {
            null
        } else {
            (borderStyle?.width)?.plus(cornerRadius?.toInt()?.div(4) ?: 0)
        }
    }

    fun setButtonImageOfDefault(context: Context,
                                ujetStyle: UjetStyle?,
                                button: ImageView,
                                drawableResName: String?,
                                @DrawableRes defaultIconResId: Int,
                                applyChatStyle: Boolean = true) {
        val iconResId = getDrawableResIdByName(context, drawableResName)
        if (iconResId == RESOURCE_NOT_FOUND) {
            button.setImageResource(defaultIconResId)
        } else {
            button.setImageResource(iconResId)
        }
        if (applyChatStyle) {
            UjetViewStyler.styleChatButton(ujetStyle ?: return, button, iconResId == RESOURCE_NOT_FOUND)
        }
    }
}
