package co.ujet.android.ui.util.viewpager

import android.view.View
import android.view.View.MeasureSpec
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.PageTransformer
import kotlin.math.max

class DynamicHeightPageTransformer(private val viewPager: ViewPager2) : PageTransformer {
    private var maxHeight = 0

    override fun transformPage(page: View, position: Float) {
        page.post {
            val wMeasureSpec = MeasureSpec.makeMeasureSpec(page.width, MeasureSpec.EXACTLY)
            val hMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            page.measure(wMeasureSpec, hMeasureSpec)
            viewPager.layoutParams = viewPager.layoutParams.also { lp ->
                lp.height = max(maxHeight, page.measuredHeight).also {
                    maxHeight = it
                }
            }
            viewPager.invalidate()
        }
    }
}
