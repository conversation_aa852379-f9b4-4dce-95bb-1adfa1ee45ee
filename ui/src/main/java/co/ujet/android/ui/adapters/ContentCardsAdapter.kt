package co.ujet.android.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.ui.R
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.ui.adapters.viewholders.ContentCardViewHolder
import co.ujet.android.modulemanager.common.ui.domain.ContentCardStyle
import co.ujet.android.ui.adapters.viewholders.ContentCardViewHolder.CardClickedCallback
import co.ujet.android.ui.style.UjetStyle

class ContentCardsAdapter(
    private val contentCards: List<ContentCard>,
    private val ujetStyle: UjetStyle,
    private val contentCardStyle: ContentCardStyle?,
    private val adapterPosition: Int,
    private val callback: CardClickedCallback?
) : RecyclerView.Adapter<ContentCardViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ContentCardViewHolder {
        return ContentCardViewHolder(
            LayoutInflater
                .from(parent.context)
                .inflate(R.layout.chat_content_card_layout, parent, false),
            contentCardStyle,
            ujetStyle,
            adapterPosition
        )
    }

    override fun getItemCount() = contentCards.size

    override fun onBindViewHolder(holder: ContentCardViewHolder, position: Int) {
        val contentCard = contentCards[position]
        val context = holder.itemView.context
        holder.bind(context, contentCard, callback)
    }
}
