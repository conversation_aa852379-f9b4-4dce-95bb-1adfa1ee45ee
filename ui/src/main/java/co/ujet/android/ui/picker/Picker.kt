package co.ujet.android.ui.picker

import android.content.Context
import android.content.res.Resources.NotFoundException
import android.graphics.Typeface
import android.os.Bundle
import android.os.Parcelable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.LocaleSpan
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import co.ujet.android.commons.util.AccessibilityUtil.CUSTOM_PICKER_VIEW_ACTION
import co.ujet.android.commons.util.AccessibilityUtil.CUSTOM_PICKER_VIEW_ROLE
import co.ujet.android.commons.util.AccessibilityUtil.isBackButtonClicked
import co.ujet.android.ui.BuildConfig
import co.ujet.android.ui.R
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.picker.PickerListView.PickerUIItemClickListener
import java.util.Locale
import kotlin.math.ceil

class Picker : RelativeLayout {
    private var pickerListener: PickerItemClickListener? = null
    private var pickerListView: PickerListView? = null
    private var hiddenPanelView: View? = null
    private var position = 0
    private var backgroundColorPanel = 0
    private var colorTextCenterListView = 0
    private var colorTextNoCenterListView = 0
    private var pickerUISettings: PickerSettings? = null
    private var separatorColor = 0
    private var topSeparator: View? = null
    private var bottomSeparator: View? = null
    private var announcedContent: Boolean = false
    private var previousPickerViewContent: String? = null
    private var nextView: View? = null

    constructor(context: Context) : super(context) {
        if (isInEditMode) {
            createEditModeView()
        } else {
            createView(null)
        }
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        if (isInEditMode) {
            createEditModeView()
        } else {
            createView(attrs)
            getAttributes(attrs)
        }
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        if (isInEditMode) {
            createEditModeView()
        } else {
            createView(attrs)
            getAttributes(attrs)
        }
    }

    /**
     * This method inflates the panel to be visible from Preview Layout
     */
    private fun createEditModeView() {
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        inflater.inflate(R.layout.ujet_picker, this, true)
    }

    private fun createView(attrs: AttributeSet?) {
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = inflater.inflate(R.layout.ujet_picker, this, true)
        hiddenPanelView = view.findViewById(R.id.hidden_panel)
        pickerListView = view.findViewById<View>(R.id.picker_ui_listview) as PickerListView
        val screenHeight = context.resources.displayMetrics.heightPixels
        val pickerHeight = getPixelSize(R.dimen.ujet_picker_height)
        // Top padding should be half of the screen size so that picker list view is centered vertically
        // and horizontally to the screen and bottom padding should subtract 3 picker items shown
        // during initialization (cached menu queues) from top padding to align the items at the center.
        val topPadding = screenHeight / 2
        val bottomPadding = topPadding - (pickerHeight * 3)
        // Ignore applying padding when going back to previous screen by clicking back button to select
        // the previously selected menu queue (not the default first menu queue)
        if (!isBackButtonClicked) {
            pickerListView?.setPadding(0, topPadding, 0, bottomPadding)
        }
        topSeparator = view.findViewById(R.id.tv_top_separator)
        bottomSeparator = view.findViewById(R.id.tv_bottom_separator)
    }

    fun registerGlobalLayoutListener(view: RelativeLayout? = null, titleView: TextView? = null,
                                     descriptionView: TextView? = null, nextButton: FancyButton? = null,
                                     pickerView: View? = null, isLandScapeModeEnabled: Boolean = false) {
        // In landscape mode, we add 50dp padding for left and right side for white background
        // and 40dp padding in portrait mode, you can refer ujet_fragment_background.
        val startEndMargin = if (isLandScapeModeEnabled) {
            getPixelSize(R.dimen.ujet_picker_white_background_start_end_margin_landscape)
        } else {
            getPixelSize(R.dimen.ujet_picker_white_background_start_end_margin)
        }
        val params: LayoutParams? = pickerView?.layoutParams as? LayoutParams
        params?.marginStart = startEndMargin
        params?.marginEnd = startEndMargin
        // Make pickerView invisible when coming from previous screen to fix alignment issue.
        if (isBackButtonClicked) {
            pickerView?.visibility = INVISIBLE
        }
        viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val pickerHeightPx = getPixelSize(R.dimen.ujet_picker_height)
                // We add 40dp padding between tool bar and the white background at the top and bottom of the screen
                val topBottomViewMargin = getPixelSize(R.dimen.ujet_picker_white_background_top_bottom_margin) * VIEW_TOP_BOTTOM_COUNT
                // Additional margin height is padding added at the top and bottom of the UI components in the screen
                val additionalMarginHeightPx = getPixelSize(R.dimen.ujet_picker_title_top_margin) +
                        getPixelSize(R.dimen.ujet_picker_description_top_margin) +
                        getPixelSize(R.dimen.ujet_picker_next_button_bottom_margin)
                val actualScreenHeight = view?.height?.minus(topBottomViewMargin) ?: 0
                val titleViewHeight = titleView?.height ?: 0
                val descriptionViewHeight = descriptionView?.height ?: 0
                val nextButtonHeight = nextButton?.height ?: 0
                val pickerViewHeight = pickerView?.height ?: 0
                val currentViewHeight = titleViewHeight + descriptionViewHeight + pickerHeightPx +
                        nextButtonHeight + additionalMarginHeightPx
                // If current view height exceeds screen height then we need to expand the screen
                // to make it scrollable to accommodate all views.
                if (actualScreenHeight < currentViewHeight) {
                    view?.apply {
                        // We need to append topBottomViewMargin at the top and bottom of the screen to
                        // current view height to accommodate all the views and make it scrollable.
                        layoutParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                            currentViewHeight + topBottomViewMargin)
                    }
                }
                /**
                 * Set vertical padding for 'pickerListView' based on screen size dynamically.
                 * Note: PickerListView height must be 5x of the listItemView(43dp) height which is 215dp.
                 * To maintain that height vertical padding is being added based on screen size.
                 */
                var topBottomPadding = ceil(((pickerViewHeight - pickerHeightPx) / 2).toDouble()).toInt()
                if (topBottomPadding < 0) {
                    topBottomPadding = 0
                }
                pickerListView?.setPadding(0, topBottomPadding, 0, topBottomPadding)
                viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
    }

    fun setNestedScrollable(boolean: Boolean) {
        pickerListView?.isNestedScrollingEnabled = boolean
    }

    private fun getPixelSize(dimenResId: Int): Int {
        return resources.getDimensionPixelSize(dimenResId)
    }

    /**
     * Retrieve styles attributes
     */
    private fun getAttributes(attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.UjetPickerUI, 0, 0)
        try {
            backgroundColorPanel = typedArray
                .getColor(R.styleable.UjetPickerUI_backgroundColor, ContextCompat.getColor(context, R.color.ujet_background))
            colorTextCenterListView = typedArray
                .getColor(R.styleable.UjetPickerUI_textCenterColor, ContextCompat.getColor(context, R.color.ujet_primary))
            colorTextNoCenterListView = typedArray
                .getColor(R.styleable.UjetPickerUI_textNoCenterColor, ContextCompat.getColor(context, R.color.ujet_disabled))
            val idItems: Int = typedArray.getResourceId(R.styleable.UjetPickerUI_entries, -1)
            if (idItems != -1) {
                setItems(context, resources.getStringArray(idItems).asList())
            }
        } catch (e: Exception) {
            Log.e(LOG_TAG, "Error while creating the view PickerUI: " + e.message)
        } finally {
            typedArray.recycle()
        }
    }

    /**
     * Sets the background color for the panel.
     *
     * @param color the color of the background
     */
    private fun setBackgroundColorPanel(color: Int) {
        backgroundColorPanel = color
    }

    private fun setTextColorsListView() {
        setColorTextCenter(colorTextCenterListView)
        setColorTextNoCenter(colorTextNoCenterListView)
    }

    private val isPanelShown: Boolean
        get() = hiddenPanelView?.visibility == VISIBLE

    /**
     * Method to set items to show in panel.
     * In this method, by default, the 'which' is 0 and the position is the half of the elements.
     *
     * @param items elements to show in panel
     */
    private fun setItems(context: Context, items: List<String>) {
        setItems(context, items, 0, items.size / 2)
    }

    /**
     * Method to set items to show in panel.
     * In this method, by default, the 'which' is 0 and the position is the half of the elements.
     *
     * @param items    elements to show in panel
     * @param position the position to set in the center of the panel.
     */
    private fun setItems(context: Context, items: List<String?>?, position: Int) {
        setItems(context, items ?: return, 0, position)
    }

    /**
     * Method to set items to show in panel.
     *
     * @param context  [PickerListView] needs a context
     * @param items    elements to show in panel
     * @param which    id of the element has been clicked
     * @param position the position to set in the center of the panel.
     */
    private fun setItems(context: Context, items: List<String?>, which: Int, position: Int) {
        pickerListView?.setItems(context, items.filterNotNull(), which, position)
        setTextColorsListView()
    }

    /**
     * Sets the text color for the item of the center.
     *
     * @param color the color of the text
     */
    fun setColorTextCenter(color: Int) {
        if (pickerListView != null) {
            val newColor: Int = try {
                ContextCompat.getColor(context, color)
            } catch (e: NotFoundException) {
                color
            }
            colorTextCenterListView = newColor
            pickerListView?.pickerUIAdapter?.setColorTextCenter(newColor)
        }
    }

    /**
     * Sets the text color for the items which aren't in the center.
     *
     * @param color the color of the text
     */
    fun setColorTextNoCenter(color: Int) {
        if (pickerListView != null) {
            val newColor: Int = try {
                ContextCompat.getColor(context, color)
            } catch (e: NotFoundException) {
                color
            }
            colorTextNoCenterListView = newColor
            pickerListView?.pickerUIAdapter?.setColorTextNoCenter(newColor)
        }
    }

    fun setTypeFace(typeface: Typeface?) {
        pickerListView?.pickerUIAdapter?.setTypeFace(typeface)
    }

    fun setSeparatorColor(color: Int) {
        val newColor: Int = try {
            ContextCompat.getColor(context, color)
        } catch (e: NotFoundException) {
            color
        }
        separatorColor = newColor
        topSeparator?.setBackgroundColor(newColor)
        bottomSeparator?.setBackgroundColor(newColor)
    }

    /**
     * Method to slide up the panel. Panel displays with an animation, and when it starts, the item
     * of the center is
     * selected.
     */
    private fun showPanelPicker() {
        hiddenPanelView?.visibility = VISIBLE
        setBackgroundPanel()
        pickerListView?.pickerUIAdapter?.handleSelectEvent(position + 2)
        pickerListView?.setSelection(position)
    }

    private fun setBackgroundPanel() {
        val color: Int = try {
            ContextCompat.getColor(context, backgroundColorPanel)
        } catch (e: NotFoundException) {
            backgroundColorPanel
        }
        hiddenPanelView?.setBackgroundColor(color)
    }

    /**
     * Set a callback listener for the item click.
     *
     * @param listener Callback instance.
     */
    fun setOnClickItemPickerListener(listener: PickerItemClickListener?) {
        pickerListener = listener
        pickerListView?.setOnClickItemPickerUIListener(
            object : PickerUIItemClickListener {
                override fun onItemClickItemPickerUI(which: Int, position: Int, valueResult: String?) {
                    checkNotNull(pickerListener) { "You must assign a valid PickerUI.PickerUIItemClickListener first!" }
                    <EMAIL> = position //update current position
                    announcedContent = false //reset as position is changed
                    setFocusedPickerItemContent()
                    pickerListener?.onItemClickPicker(which, position, valueResult ?: return)
                }

                override fun onCenterItemChanged(position: Int) {
                    checkNotNull(pickerListener) { "You must assign a valid PickerUI.PickerUIItemClickListener first!" }
                    pickerListener?.onCenterItemChanged(position)
                }
            })
    }

    /**
     * This method sets the desired functionalities of panel to make easy.
     *
     * @param pickerUISettings Object with all functionalities to make easy.
     */
    fun setSettings(pickerUISettings: PickerSettings, view: View?) {
        this.pickerUISettings = pickerUISettings
        setColorTextCenter(pickerUISettings.colorTextCenter)
        setColorTextNoCenter(pickerUISettings.colorTextNoCenter)
        setItems(context, pickerUISettings.items, pickerUISettings.position)
        setBackgroundColorPanel(pickerUISettings.backgroundColor)
        position = pickerUISettings.position
        showPanelPicker()
        setSeparatorColor(pickerUISettings.separatorColor)
        nextView = view
        val content = getFocusedPickerViewContent()
        previousPickerViewContent = content
        this.contentDescription = content
    }

    fun clearResources() {
        nextView = null
    }

    // content to pronounce when list view is focused
    private fun getFocusedPickerViewContent(): String {
        val currentPickerItem = if (position < (pickerUISettings?.items?.size ?: 0)) {
            pickerUISettings?.items?.get(position)
        } else {
            null
        }
        return currentPickerItem?.plus(" $CUSTOM_PICKER_VIEW_ROLE ${position + 1} of ${pickerUISettings?.items?.size} $CUSTOM_PICKER_VIEW_ACTION") ?: ""
    }

    // content to pronounce when picker items are scrolled up/down
    private fun setFocusedPickerItemContent() {
        val currentPickerItem = pickerUISettings?.items?.get(position)
        val languageCode = pickerListener?.getLanguageCode(currentPickerItem)
        currentPickerItem?.let {
            val currentPickerItemText = "$currentPickerItem ${position + 1} of ${pickerUISettings?.items?.size}" // Example : English (3 of 10)
            pickerListView?.contentDescription = if (languageCode.isNullOrEmpty()) {
                currentPickerItemText
            } else {
                // To let talkback read language names in different languages wrap text using LocaleSpan
                // according to https://theappbusiness.github.io/accessibility-guidelines/guidelines/3.1.2.html#guidance-for-android
                val localeSpan = LocaleSpan(Locale.forLanguageTag(languageCode))
                val spannableText = SpannableString(currentPickerItemText)
                spannableText.setSpan(localeSpan, 0, currentPickerItem.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                spannableText
            }
            pickerListView?.sendAccessibilityEvent(AccessibilityEvent.CONTENT_CHANGE_TYPE_CONTENT_DESCRIPTION)
        }
    }

    override fun onInitializeAccessibilityNodeInfo(info: AccessibilityNodeInfo?) {
        super.onInitializeAccessibilityNodeInfo(info)

        val currentPickerViewContent = getFocusedPickerViewContent()
        val pickerContentChanged = currentPickerViewContent != previousPickerViewContent
        val nextButtonFocused = nextView?.isAccessibilityFocused
        if (nextButtonFocused == true && pickerContentChanged && !announcedContent) {
            // Need to update content when talkback focus moved back to picker view from next button
            this.contentDescription = currentPickerViewContent
            announcedContent = true
        }
    }

    /**
     * Save the state of the panel when orientation screen changed.
     */
    public override fun onSaveInstanceState(): Parcelable {
        val bundle = Bundle()
        bundle.putParcelable("instanceState", super.onSaveInstanceState())
        bundle.putParcelable("stateSettings", pickerUISettings)
        //save everything
        bundle.putBoolean("stateIsPanelShown", isPanelShown)
        bundle.putInt("statePosition", pickerListView?.itemInListCenter ?: 0)
        return bundle
    }

    /**
     * Retrieve the state of the panel when orientation screen changed.
     */
    public override fun onRestoreInstanceState(state: Parcelable) {
        var restoreState: Parcelable? = state
        if (restoreState is Bundle) {
            val bundle = restoreState

            //load everything
            try {
                restoreState = bundle.getParcelable("instanceState")
                val pickerUISettings = bundle.getParcelable<PickerSettings>("stateSettings")
                if (pickerUISettings != null) {
                    val statePosition = bundle.getInt("statePosition", 0)
                    pickerUISettings.position = statePosition
                    // Count initial empty rows added in PickerAdapter.setItems() for list view.
                    pickerListView?.lastPositionBeforeConfigChanged = statePosition + EMPTY_ROWS_COUNT
                    setSettings(pickerUISettings, null)
                }
                val stateIsPanelShown = bundle.getBoolean("stateIsPanelShown")
                if (stateIsPanelShown) {
                    val observer = viewTreeObserver
                    observer.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            viewTreeObserver.removeOnGlobalLayoutListener(this)
                        }
                    })
                }
            } catch (e: Exception) {
                if (BuildConfig.DEBUG) {
                    e.printStackTrace()
                }
            }
        }
        super.onRestoreInstanceState(restoreState)
    }

    /**
     * Interface for a callback when the item has been clicked.
     */
    interface PickerItemClickListener {
        /**
         * Callback when the item has been clicked.
         *
         * @param which       id of the element has been clicked
         * @param position    Position of the current item.
         * @param valueResult Value of text of the current item.
         */
        fun onItemClickPicker(which: Int, position: Int, valueResult: String)
        fun onCenterItemChanged(position: Int)
        fun getLanguageCode(languageName: String?): String?
    }

    companion object {
        private val LOG_TAG = Picker::class.java.simpleName
        private const val EMPTY_ROWS_COUNT = 2
        private const val VIEW_TOP_BOTTOM_COUNT = 2
    }
}
