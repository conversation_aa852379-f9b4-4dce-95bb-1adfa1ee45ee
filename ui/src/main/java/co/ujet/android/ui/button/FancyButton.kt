package co.ujet.android.ui.button

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.TypedArray
import android.graphics.Color
import android.graphics.PorterDuff.Mode.SRC_ATOP
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.RippleDrawable
import android.graphics.drawable.StateListDrawable
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.Spanned
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityManager
import android.widget.*
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.text.HtmlCompat
import androidx.core.text.HtmlCompat.FROM_HTML_MODE_LEGACY
import co.ujet.android.commons.util.AccessibilityUtil
import co.ujet.android.ui.R
import co.ujet.android.ui.extensions.addNotNull
import co.ujet.android.ui.textview.AutoResizeTextView
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.findFont
import co.ujet.android.ui.util.DesignUtil.getColorStateList
import co.ujet.android.ui.util.DesignUtil.getStateListDrawable
import co.ujet.android.ui.util.DesignUtil.pxToSp
import co.ujet.android.ui.util.DesignUtil.spToPx
import co.ujet.android.ui.util.TextViewWithoutClipping
import co.ujet.android.ui.widgets.UjetProgressBar
import java.util.*

class FancyButton : RelativeLayout {
    // # Content Attributes
    private var mContentGravity = 0x11 // Gravity.CENTER

    // # Background Attributes
    private var mDefaultBackgroundColor = Color.BLACK
    private var mFocusBackgroundColor = 0
    private var mDisabledBackgroundColor = 0
    private var mEnableRipple = true

    // # Text Attributes
    private var mDefaultTextColor = Color.WHITE
    private var mDefaultIconColor = Color.WHITE
    private var mFocusTextColor = Color.WHITE
    private var mFocusIconColor = Color.WHITE
    private var mDisabledTextColor = Color.WHITE
    private var mDefaultTextSizeInSp = DEFAULT_TEXT_SIZE
    private val mSmallTextSizeInSp = DEFAULT_SMALL_TEXT_SIZE
    private var mDefaultTextGravity = Gravity.CENTER_HORIZONTAL or Gravity.CENTER_VERTICAL
    private var mText: String? = null
    private var mSubText: String? = null

    // # Icon Attributes
    private var mIconResource: Drawable? = null
    private var mFontIconSizeInSp = DEFAULT_ICON_SIZE
    private var mFontIcon: String? = null
    private var mIconPosition = 1
    private var mIconPaddingLeft = 10
    private var mIconPaddingRight = 10
    private var mIconPaddingTop = 0
    private var mIconPaddingBottom = 0
    private var mBorderColor = Color.TRANSPARENT
    private var mBorderWidth = 0
    private var mFocusBorderColor = Color.TRANSPARENT
    private var mDisabledBorderColor = Color.TRANSPARENT
    private var mRadius = 0
    private var mTextAllCaps = false
    private var mTextBold = false
    private var mTextTypeFace: Typeface? = null
    private var mIconTypeFace: Typeface? = null
    private var mIndicatorVisible = false
    private var mAutoResizeText = false
    private var mIndicatorSize = spToPx(context, 25f)
    private var mTextPadding = 0
    private val mDefaultIconFont = "fontawesome.ttf"
    private val mDefaultTextFont = "robotoregular.ttf"
    private var mButtonContainer: LinearLayout? = null
    private var mIconView: AppCompatImageView? = null
    private val handler = Handler(Looper.getMainLooper())

    /**
     * Return Icon Font of the FancyButton
     *
     * @return TextView Object
     */
    var iconFontObject: TextView? = null
        private set

    /**
     * Return TextView Object of the FancyButton
     *
     * @return TextView Object
     */
    var textViewObject: TextView? = null
        private set
    private var mSubTextView: TextView? = null
    private var mIndicator: UjetProgressBar? = null
    private var mGhost = false // Default is a solid button !

    /**
     * Set the capitalization of text
     *
     * @param textAllCaps : is text to be capitalized
     */
    var isTextAllCaps: Boolean
        get() = mTextAllCaps
        set(textAllCaps) {
            mTextAllCaps = textAllCaps
            setText(mText)
        }

    /**
     * Return Text of the button
     *
     * @return Text
     */
    var text: CharSequence
        get() = textViewObject?.text ?: ""
        set(value) {
            textViewObject?.text = value
        }

    constructor(context: Context?) : super(context) {
        mTextTypeFace = findFont(getContext(), mDefaultTextFont, null)
        mIconTypeFace = findFont(getContext(), mDefaultIconFont, null)
        mEnableRipple = false
        mTextPadding = 12
        initializeFancyButton()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        val attrsArray = context.obtainStyledAttributes(attrs, R.styleable.UjetFancyButtonsAttrs, 0, 0)
        initAttributesArray(attrsArray)
        attrsArray.recycle()
        initializeFancyButton()
    }

    /**
     * Initialize Button dependencies
     * - Initialize Button Container : The LinearLayout
     * - Initialize Button TextView
     * - Initialize Button Icon
     * - Initialize Button Font Icon
     */
    private fun initializeFancyButton() {
        initializeButtonContainer()
        removeAllViews()
        this.addView(mButtonContainer)
        textViewObject = setupTextView()
        mSubTextView = setupSubTextView()
        mIconView = setupIconView()
        iconFontObject = setupFontIconView()
        mIndicator = setupIndicator()
        if (mIconView == null && iconFontObject == null && textViewObject == null) {
            val tempTextView = Button(context)
            tempTextView.text = "Fancy Button"
            mButtonContainer?.addView(tempTextView)
        } else {
            setupBackground()
            val views = mutableListOf<View>()
            if (mIconPosition == POSITION_LEFT || mIconPosition == POSITION_TOP) {
                views.addNotNull(mIconView)
                views.addNotNull(iconFontObject)
                if (textViewObject != null && mSubTextView != null) {
                    views.addNotNull(setupTextContainer())
                } else {
                    views.addNotNull(textViewObject)
                    views.addNotNull(mSubTextView)
                }
            } else {
                if (textViewObject != null && mSubTextView != null) {
                    views.addNotNull(setupTextContainer())
                } else {
                    views.addNotNull(textViewObject)
                    views.addNotNull(mSubTextView)
                }
                views.addNotNull(mIconView)
                views.addNotNull(iconFontObject)
            }
            views.forEach {
                mButtonContainer?.addView(it)
            }
        }
        addView(mIndicator)
        setIndicatorVisible(mIndicatorVisible)
        handleAccessibilityChanges()
    }

    private fun handleAccessibilityChanges() {
        // Add button role to fancy button view
        AccessibilityUtil.addButtonRole(this)
        // We are setting toolbar/actionbar back button as initial focus in BaseFragment#onResume() and
        // when going back to previous screen talkback always focuses back button and does not announce anything,
        // to make experience better, updating initial focus to Next button on loading previous screen as it was last view
        // user clicked. To do so, we need to wait for a second so that base fragment finishes its execution
        // and then change initial focus to next button and reset isBackButtonClicked to focus respective view.
        handler.postDelayed({
            if (AccessibilityUtil.isBackButtonClicked) {
                // when going back from language screen to entry screen, focus should be on language name
                // menu item at the top of the screen instead of next button so when ignoreNextButtonFocus
                // is true just reset its value and return without setting focus to next button.
                // EntryFragment#onResume() takes care of focusing language name menu item. 
                if (AccessibilityUtil.ignoreNextButtonFocus) {
                    AccessibilityUtil.ignoreNextButtonFocus = false //reset
                    return@postDelayed
                }
                this.requestFocus()
                AccessibilityUtil.setupInitialFocus(context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager,
                    this)
                AccessibilityUtil.addButtonRole(this)
                AccessibilityUtil.isBackButtonClicked = false
            }
        }, AccessibilityUtil.ACCESSIBILITY_WAIT_TIMER)
    }

    private fun setupTextContainer(): LinearLayout {
        val textContainer = LinearLayout(context)
        textContainer.orientation = LinearLayout.VERTICAL
        val viewParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        textContainer.layoutParams = viewParams
        textContainer.addView(textViewObject)
        textContainer.addView(mSubTextView)
        return textContainer
    }

    /**
     * Setup Text View
     *
     * @return : TextView
     */
    private fun setupTextView(): TextView? {
        if (mText != null) {
            //Will reach here when button text is set through xml attributes, set text as contentDescription so that
            //talkback can read contents as label
            this.contentDescription = mText
            val textView = if (mAutoResizeText) {
                AutoResizeTextView(context)
            } else {
                TextViewWithoutClipping(context)
            }
            textView.text = mText?.let { getDisplayableTextFromHtml(it) }
            textView.gravity = mDefaultTextGravity
            textView.isFocusable = true
            textView.isDuplicateParentStateEnabled = true
            textView.setTextColor(getColorStateList(mDefaultTextColor, mFocusTextColor, mDisabledTextColor))
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, mDefaultTextSizeInSp)
            textView.setPadding(mTextPadding, 0, mTextPadding, 0)
            textView.layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            if (!isInEditMode && mTextTypeFace != null) {
                textView.typeface = mTextTypeFace
            }
            if (mTextBold) {
                textView.setTypeface(null, Typeface.BOLD)
            }
            textView.importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_NO
            return textView
        }
        return null
    }

    /**
     * Setup Text View
     *
     * @return : TextView
     */
    private fun setupSubTextView(): TextView? {
        if (mSubText != null) {
            val textView = TextView(context)
            textView.text = mSubText?.let { getDisplayableTextFromHtml(it) }
            textView.gravity = mDefaultTextGravity
            textView.isFocusable = true
            textView.isDuplicateParentStateEnabled = true
            textView.setTextColor(getColorStateList(mDefaultTextColor, mFocusTextColor, mDisabledTextColor))
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, mSmallTextSizeInSp)
            textView.layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            if (!isInEditMode && mTextTypeFace != null) {
                textView.typeface = mTextTypeFace
            }
            textView.importantForAccessibility = IMPORTANT_FOR_ACCESSIBILITY_NO
            return textView
        }
        return null
    }

    /**
     * Setup Font Icon View
     *
     * @return : TextView
     */
    private fun setupFontIconView(): TextView? {
        if (mFontIcon != null) {
            val fontIconView = TextView(context)
            fontIconView.isFocusable = true
            fontIconView.isDuplicateParentStateEnabled = true
            fontIconView.setTextColor(getColorStateList(mDefaultIconColor, mFocusIconColor))
            val iconTextViewParams = LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
            iconTextViewParams.rightMargin = mIconPaddingRight
            iconTextViewParams.leftMargin = mIconPaddingLeft
            iconTextViewParams.topMargin = mIconPaddingTop
            iconTextViewParams.bottomMargin = mIconPaddingBottom
            if (textViewObject != null) {
                if (mIconPosition == POSITION_TOP || mIconPosition == POSITION_BOTTOM) {
                    iconTextViewParams.gravity = Gravity.CENTER
                    fontIconView.gravity = Gravity.CENTER
                } else {
                    fontIconView.gravity = Gravity.CENTER_VERTICAL
                    iconTextViewParams.gravity = Gravity.CENTER_VERTICAL
                }
            } else {
                iconTextViewParams.gravity = Gravity.CENTER
                fontIconView.gravity = Gravity.CENTER_VERTICAL
            }
            fontIconView.layoutParams = iconTextViewParams
            if (!isInEditMode) {
                fontIconView.text = mFontIcon
                fontIconView.typeface = mIconTypeFace
            } else {
                fontIconView.text = "O"
            }
            fontIconView.setTextSize(TypedValue.COMPLEX_UNIT_SP, mFontIconSizeInSp)
            return fontIconView
        }
        return null
    }

    /**
     * Text Icon resource view
     *
     * @return : ImageView
     */
    private fun setupIconView(): AppCompatImageView? {
        val normalDrawable = mIconResource ?: return null
        val iconView = AppCompatImageView(context)
        mIconResource?.constantState?.newDrawable()?.apply {
            setColorFilter(mFocusIconColor, SRC_ATOP)
            iconView.setImageDrawable(getStateListDrawable(normalDrawable, this))
        }
        iconView.isFocusable = true
        iconView.isDuplicateParentStateEnabled = true
        iconView.setPadding(mIconPaddingLeft, mIconPaddingTop, mIconPaddingRight, mIconPaddingBottom)
        val iconViewParams = LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        if (textViewObject != null) {
            if (mIconPosition == POSITION_TOP || mIconPosition == POSITION_BOTTOM) iconViewParams.gravity =
                Gravity.CENTER else iconViewParams.gravity = Gravity.START or Gravity.CENTER_VERTICAL
            iconViewParams.rightMargin = 10
            iconViewParams.leftMargin = 10
        } else {
            iconViewParams.gravity = Gravity.CENTER_VERTICAL
        }
        iconView.layoutParams = iconViewParams
        return iconView
    }

    private fun setupIndicator(): UjetProgressBar {
        val indicatorParams = LayoutParams(mIndicatorSize, mIndicatorSize)
        indicatorParams.addRule(CENTER_IN_PARENT)
        val progressBar = UjetProgressBar(context)
        progressBar.visibility = GONE
        progressBar.layoutParams = indicatorParams
        progressBar.isClickable = false
        progressBar.isFocusable = false
        return progressBar
    }

    /**
     * Initialize Attributes arrays
     *
     * @param attrsArray : Attributes array
     */
    private fun initAttributesArray(attrsArray: TypedArray) {
        mContentGravity = attrsArray.getInt(R.styleable.UjetFancyButtonsAttrs_ujet_fb_contentGravity, Gravity.CENTER)
        mDefaultBackgroundColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_defaultColor, mDefaultBackgroundColor)
        mFocusBackgroundColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_focusColor, mFocusBackgroundColor)
        mEnableRipple = attrsArray.getBoolean(R.styleable.UjetFancyButtonsAttrs_ujet_fb_enableRipple, true)
        mDefaultTextColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textColor, mDefaultTextColor)
        // if default color is set then the icon's color is the same (the default for icon's color)
        mDefaultIconColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconColor, mDefaultTextColor)
        mFocusTextColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_focusTextColor, mDefaultTextColor)
        mFocusIconColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_focusIconColor, mDefaultTextColor)
        mDisabledTextColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_disabledTextColor, mDefaultTextColor)
        val defaultTextSizeInPx = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textSize,
            spToPx(context, mDefaultTextSizeInSp).toFloat())
        mDefaultTextSizeInSp = pxToSp(context, defaultTextSizeInPx).toFloat()
        mDefaultTextGravity = attrsArray.getInt(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textGravity, mDefaultTextGravity)
        mBorderColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_borderColor, mBorderColor)
        mBorderWidth = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_borderWidth, mBorderWidth.toFloat()).toInt()
        mFocusBorderColor = attrsArray.getColor(R.styleable.UjetFancyButtonsAttrs_ujet_fb_focusBorderColor, mBorderColor)
        mRadius = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_radius, mRadius.toFloat()).toInt()
        val fontIconSizeInPx = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_fontIconSize,
            spToPx(context, mFontIconSizeInSp).toFloat())
        mFontIconSizeInSp = pxToSp(context, fontIconSizeInPx).toFloat()
        mIconPaddingLeft =
            attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconPaddingLeft, mIconPaddingLeft.toFloat()).toInt()
        mIconPaddingRight =
            attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconPaddingRight, mIconPaddingRight.toFloat()).toInt()
        mIconPaddingTop =
            attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconPaddingTop, mIconPaddingTop.toFloat()).toInt()
        mIconPaddingBottom =
            attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconPaddingBottom, mIconPaddingBottom.toFloat()).toInt()
        mTextAllCaps = attrsArray.getBoolean(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textAllCaps, false)
        mTextBold = attrsArray.getBoolean(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textBold, true)
        mAutoResizeText = attrsArray.getBoolean(R.styleable.UjetFancyButtonsAttrs_ujet_fb_autoResizeText, false)
        val text = attrsArray.getString(R.styleable.UjetFancyButtonsAttrs_ujet_fb_text)
        val subText = attrsArray.getString(R.styleable.UjetFancyButtonsAttrs_ujet_fb_subText)
        mIconPosition = attrsArray.getInt(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconPosition, mIconPosition)
        val fontIcon = attrsArray.getString(R.styleable.UjetFancyButtonsAttrs_ujet_fb_fontIconResource)
        val iconFontFamily = attrsArray.getString(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconFont)
        val textFontFamily = attrsArray.getString(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textFont)
        mTextPadding = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_textPadding, mTextPadding.toFloat()).toInt()
        mIconResource = try {
            attrsArray.getDrawable(R.styleable.UjetFancyButtonsAttrs_ujet_fb_iconResource)
        } catch (e: Exception) {
            null
        }
        if (fontIcon != null) {
            mFontIcon = fontIcon
        }
        if (text != null) {
            mText = if (mTextAllCaps) {
                text.uppercase(Locale.getDefault())
            } else {
                text
            }
        }
        if (subText != null) {
            mSubText = subText
        }
        if (!isInEditMode) {
            mIconTypeFace = if (iconFontFamily != null) {
                findFont(context, iconFontFamily, mDefaultIconFont)
            } else {
                findFont(context, mDefaultIconFont, null)
            }
            mTextTypeFace = if (textFontFamily != null) {
                findFont(context, textFontFamily, mDefaultTextFont)
            } else {
                findFont(context, mDefaultTextFont, null)
            }
        }
        mIndicatorVisible = attrsArray.getBoolean(R.styleable.UjetFancyButtonsAttrs_ujet_fb_indicator_visible, false)
        mIndicatorSize = attrsArray.getDimension(R.styleable.UjetFancyButtonsAttrs_ujet_fb_indicator_size, mIndicatorSize.toFloat()).toInt()
    }

    private fun getRippleDrawable(defaultDrawable: Drawable, focusDrawable: Drawable): Drawable {
        val ripple = RippleDrawable(ColorStateList.valueOf(mFocusBackgroundColor), defaultDrawable, focusDrawable)
        ripple.opacity = 255
        return ripple
    }

    private fun getColorStateList(defaultColor: Int, focusColor: Int, disabledColor: Int): ColorStateList {
        val states = arrayOf(
            intArrayOf(android.R.attr.state_pressed),
            intArrayOf(android.R.attr.state_focused),
            intArrayOf(android.R.attr.state_enabled),
            intArrayOf(-android.R.attr.state_enabled)
        )
        val colors = intArrayOf(focusColor, focusColor, defaultColor, disabledColor)
        return ColorStateList(states, colors)
    }

    @SuppressLint("NewApi")
    private fun setupBackground() {
        // Default Drawable
        val defaultDrawable = GradientDrawable()
        defaultDrawable.cornerRadius = mRadius.toFloat()
        if (mGhost) {
            defaultDrawable.setColor(DesignUtil.getColor(context, android.R.color.transparent)) // Hollow Background
        } else {
            defaultDrawable.setColor(mDefaultBackgroundColor)
        }

        //Focus Drawable
        val focusDrawable = GradientDrawable()
        focusDrawable.cornerRadius = mRadius.toFloat()
        focusDrawable.setColor(mFocusBackgroundColor)

        // Handle Border
        if (mBorderColor != 0) {
            defaultDrawable.setStroke(mBorderWidth, mBorderColor)
        }
        if (mFocusBorderColor != 0) {
            focusDrawable.setStroke(mBorderWidth, mFocusBorderColor)
        }
        if (mEnableRipple) {
            this.background = getRippleDrawable(defaultDrawable, focusDrawable)
        } else {
            val states = StateListDrawable()
            // Focus/Pressed Drawable
            val drawable2 = createBackgroundDrawable(mFocusBackgroundColor, mFocusBorderColor)
            if (mFocusBackgroundColor != 0) {
                states.addState(intArrayOf(android.R.attr.state_pressed), drawable2)
                states.addState(intArrayOf(android.R.attr.state_focused), drawable2)
            }
            if (mDisabledBackgroundColor != 0 || mDisabledBorderColor != 0) {
                states.addState(
                    intArrayOf(-android.R.attr.state_enabled),
                    createBackgroundDrawable(mDisabledBackgroundColor, mDisabledBorderColor)
                )
            }
            states.addState(intArrayOf(), defaultDrawable)
            this.background = states
        }
    }

    private fun createBackgroundDrawable(color: Int, borderColor: Int): GradientDrawable {
        val drawable = GradientDrawable()
        drawable.cornerRadius = mRadius.toFloat()
        if (mGhost) {
            drawable.setColor(DesignUtil.getColor(context, android.R.color.transparent)) // No focus color
        } else {
            drawable.setColor(color)
        }
        if (borderColor != 0) {
            if (mGhost) {
                drawable.setStroke(mBorderWidth, color) // Border is the main part of button now
            } else {
                drawable.setStroke(mBorderWidth, borderColor)
            }
        }
        return drawable
    }

    /**
     * Initialize button container
     */
    private fun initializeButtonContainer() {
        this.isClickable = true
        this.isFocusable = true
        val containerParams = LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        layoutParams = containerParams
        when (mContentGravity) {
            0x03 -> { // LEFT
                containerParams.addRule(ALIGN_PARENT_LEFT)
            }
            0x05 -> { // RIGHT
                containerParams.addRule(ALIGN_PARENT_RIGHT)
            }
            else -> { // CENTER
                containerParams.addRule(CENTER_IN_PARENT)
            }
        }
        mButtonContainer = LinearLayout(context)
        mButtonContainer?.layoutParams = containerParams
        mButtonContainer?.isClickable = false
        mButtonContainer?.isFocusable = false
        mButtonContainer?.isDuplicateParentStateEnabled = true
        if (mIconPosition == POSITION_TOP || mIconPosition == POSITION_BOTTOM) {
            mButtonContainer?.orientation = LinearLayout.VERTICAL
        } else {
            mButtonContainer?.orientation = LinearLayout.HORIZONTAL
        }
        if (mIconResource == null && mFontIcon == null && paddingLeft == 0 && paddingRight == 0 && paddingTop == 0 && paddingBottom == 0) {
            setPadding(20, 20, 20, 20)
        }
    }

    /**
     * Set Text of the button
     *
     * @param text : Text
     */
    fun setText(text: String?) {
        mText = if (mTextAllCaps) {
            text?.uppercase(Locale.getDefault())
        } else {
            text
        }
        //Will reach here when button text is set programmatically, set text as contentDescription so that
        //talkback can read contents as label
        this.contentDescription = text
        if (textViewObject == null) {
            initializeFancyButton()
        } else {
            textViewObject?.text = mText
        }
    }

    /**
     * Set the color of text
     *
     * @param color : Color
     * use Color.parse('#code')
     */
    fun setTextColor(color: Int) {
        mDefaultTextColor = color
        if (textViewObject == null) {
            initializeFancyButton()
        } else {
            textViewObject?.setTextColor(getColorStateList(mDefaultTextColor, mFocusTextColor, mDisabledTextColor))
        }
    }

    /**
     * Set the size of Text
     *
     * @param textSize : Text Size in sp
     */
    fun setTextSize(textSize: Float) {
        mDefaultTextSizeInSp = textSize
        if (textViewObject != null) {
            textViewObject?.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize)
        }
    }

    /**
     * Set the focused color of text
     *
     * @param color : Color
     * use Color.parse('#code')
     */
    fun setFocusTextColor(color: Int) {
        mFocusTextColor = color
        if (textViewObject == null) {
            initializeFancyButton()
        } else {
            textViewObject?.setTextColor(getColorStateList(mDefaultTextColor, mFocusTextColor, mDisabledTextColor))
        }
    }

    /**
     * Set the color of disabled text
     *
     * @param color : Color
     * use Color.parse('#code')
     */
    fun setDisabledTextColor(color: Int) {
        mDisabledTextColor = color
        if (textViewObject == null) {
            initializeFancyButton()
        } else {
            textViewObject?.setTextColor(getColorStateList(mDefaultTextColor, mFocusTextColor, mDisabledTextColor))
        }
    }

    /**
     * Setting the icon's color independent of the text color
     *
     * @param color : Color
     */
    fun setIconColor(color: Int, focusColor: Int) {
        mDefaultIconColor = color
        mFocusIconColor = focusColor
        iconFontObject?.setTextColor(getColorStateList(mDefaultIconColor, mFocusIconColor))
        setDrawableIconColor()
    }

    /**
     * Set Background color of the button
     *
     * @param color : use Color.parse('#code')
     */
    override fun setBackgroundColor(color: Int) {
        mDefaultBackgroundColor = color
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    /**
     * Set Focus color of the button
     *
     * @param color : use Color.parse('#code')
     */
    fun setFocusBackgroundColor(color: Int) {
        mFocusBackgroundColor = color
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    fun setDisabledBackgroundColor(mDisabledBackgroundColor: Int) {
        this.mDisabledBackgroundColor = mDisabledBackgroundColor
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    private fun setDrawableIconColor() {
        val defaultDrawable = mIconResource?.constantState?.newDrawable() ?: return
        val focusDrawable = mIconResource?.constantState?.newDrawable() ?: return
        defaultDrawable.setColorFilter(mDefaultIconColor, SRC_ATOP)
        focusDrawable.setColorFilter(mFocusIconColor, SRC_ATOP)
        mIconView?.setImageDrawable(getStateListDrawable(defaultDrawable, focusDrawable))
    }

    /**
     * Set color of the button border
     *
     * @param color : Color
     * use Color.parse('#code')
     */
    fun setBorderColor(color: Int) {
        mBorderColor = color
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    /**
     * Set color of the focused button border
     *
     * @param color : Color
     * use Color.parse('#code')
     */
    fun setFocusBorderColor(color: Int) {
        mFocusBorderColor = color
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    fun setDisabledBorderColor(mDisabledBorderColor: Int) {
        this.mDisabledBorderColor = mDisabledBorderColor
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    /**
     * Set Width of the button
     *
     * @param width : Width
     */
    fun setBorderWidth(width: Int) {
        mBorderWidth = width
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    /**
     * Set Border Radius of the button
     *
     * @param radius : Radius
     */
    fun setRadius(radius: Int) {
        mRadius = radius
        if (mIconView != null || iconFontObject != null || textViewObject != null) {
            setupBackground()
        }
    }

    fun setCustomTypeFace(typeFace: Typeface?) {
        mTextTypeFace = typeFace
        if (textViewObject == null) {
            initializeFancyButton()
        } else {
            textViewObject?.typeface = mTextTypeFace
            if (mTextBold) {
                textViewObject?.setTypeface(mTextTypeFace, Typeface.BOLD)
            }
        }
    }

    fun setIndicatorVisible(visible: Boolean) {
        mIndicatorVisible = visible
        if (mIndicator == null) {
            return
        }
        mButtonContainer?.visibility = if (visible) INVISIBLE else VISIBLE
        mIndicator?.visibility = if (visible) VISIBLE else INVISIBLE
        //request accessibility info when button is disabled/enabled and update accordingly
        AccessibilityUtil.updateAccessibilityAction(this, visible)
    }

    private fun getDisplayableTextFromHtml(htmlText: String): Spanned {
        return if (Build.VERSION.SDK_INT >= 24) {
            Html.fromHtml(htmlText, FROM_HTML_MODE_LEGACY) // For API level 24 and above
        } else {
            Html.fromHtml(htmlText) // For below API level 24
        }
    }

    companion object {
        /**
         * Tags to identify icon position
         */
        private const val POSITION_LEFT = 1
        private const val POSITION_TOP = 3
        private const val POSITION_BOTTOM = 4

        private const val DEFAULT_TEXT_SIZE = 15f
        private const val DEFAULT_SMALL_TEXT_SIZE = 12f
        private const val DEFAULT_ICON_SIZE = 15f
    }
}
