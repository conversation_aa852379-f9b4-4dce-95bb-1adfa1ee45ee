package co.ujet.android.ui.picker

import android.content.Context
import android.graphics.Paint
import android.graphics.Typeface
import android.util.SparseArray
import android.util.SparseIntArray
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.core.widget.TextViewCompat
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.modulemanager.EntryPointFactory.provideEntryPoint
import co.ujet.android.modulemanager.entrypoints.configuration.Configuration
import co.ujet.android.ui.R

class PickerAdapter(context: Context, resource: Int, private var items: List<String?>, position: Int) :
    ArrayAdapter<String?>(context, resource, items) {
    private var centerPosition = 0
    private val positionsNoClickables: SparseIntArray = SparseIntArray(items.size)
    private var colorTextCenter = -1
    private var colorTextNoCenter = -1
    private var typeFace: Typeface? = null

    init {
        setItems(items, position)
        setPositonsNoClickables()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val resultView = convertView ?: run {
            val vi = parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            vi.inflate(R.layout.ujet_picker_item, parent, false).apply {
                ViewHolder.get<TextView>(this, R.id.tv_item)?.apply {
                    TextViewCompat.setTextAppearance(this, R.style.Ujet_PickerUI_Center_Item)
                }
            }
        }
        val textItem = ViewHolder.get<TextView>(resultView, R.id.tv_item) ?: return resultView
        val option = items[position]
        textItem.text = option
        // Determine if it's an empty row and adjust properties
        if (option == EMPTY_STRING) {
            resultView.isClickable = false
            resultView.isFocusable = false
            resultView.isEnabled = false
            resultView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        } else {
            resultView.isEnabled = true
        }
        if (textItem.width == 0) {
            /**
             * It's a trick to calculate the width of text view.
             * Because we know how the width will be, we can calculate it without considering other's padding or margin.
             * Just measure convertView's width and minus that's padding. (padding is defined in layout)
             * When layout was changed, you should change below too.
             */
            resultView.measure(
                MeasureSpec.makeMeasureSpec(parent.width, MeasureSpec.EXACTLY),
                MeasureSpec.makeMeasureSpec(parent.height, MeasureSpec.EXACTLY)
            )
            val textViewWidth = resultView.measuredWidth - resultView.paddingRight - resultView.paddingLeft
            setTextItemStyle(textItem, position, textViewWidth)
        } else {
            setTextItemStyle(textItem, position, textItem.width)
        }
        return resultView
    }

    /**
     * This method sets the appropriate style to each of the components to get a carousel effect.
     *
     * @param textItem the TextView of the current position of the actual item
     * @param position the current position of the actual item
     */
    private fun setTextItemStyle(textItem: TextView, position: Int, textViewWidth: Int) {
        var styleId = R.style.Ujet_PickerUI_Small_Item
        var textColor = colorTextNoCenter
        if (position == centerPosition) {
            styleId = R.style.Ujet_PickerUI_Center_Item
            textColor = colorTextCenter
        } else if (position - 1 == centerPosition || position + 1 == centerPosition) {
            styleId = R.style.Ujet_PickerUI_Near_Center_Item
            textColor = colorTextNoCenter
        } else if (position - 1 == centerPosition || position + 1 == centerPosition) {
            styleId = R.style.Ujet_PickerUI_Far_Center_Item
            textColor = colorTextNoCenter
        }
        TextViewCompat.setTextAppearance(textItem, styleId)
        if (textColor != 0) {
            textItem.setTextColor(textColor)
        }
        if (typeFace != null) {
            textItem.typeface = typeFace
        }
        if (isAutoAdjustTextEnabled()) {
            setTextSize(textItem, textViewWidth)
        }
    }

    // Skip auto adjust text in picker view when customers enabled getStaticFontSizeInPickerView()
    // ujet option or end user enabled larger text accessibility setting.
    private fun isAutoAdjustTextEnabled(): Boolean {
        return !provideEntryPoint(Configuration::class.java).getStaticFontSizeInPickerView() &&
                !ResizeTextAccessibilityUtil.isLargeTextAccessibilityEnabled(context)
    }

    private fun setTextSize(textItem: TextView, parentWidth: Int) {
        val text = textItem.text.toString()
        if (textItem.paint.measureText(text) < parentWidth) return
        val targetWidth = parentWidth - textItem.paddingLeft - textItem.paddingRight
        var high = textItem.textSize
        var low = 2f
        val measuringPainter = Paint()
        measuringPainter.set(textItem.paint)
        while (high - low > 0.5f) {
            val textSize = (high + low) / 2
            measuringPainter.textSize = textSize
            if (measuringPainter.measureText(text) >= targetWidth) {
                high = textSize
            } else {
                low = textSize
            }
        }
        textItem.setTextSize(TypedValue.COMPLEX_UNIT_PX, low)
    }

    /**
     * Sets the text color for the item of the center.
     *
     * @param color the color of the text
     */
    fun setColorTextCenter(color: Int) {
        colorTextCenter = color
    }

    /**
     * Sets the text color for the items which aren't in the center.
     *
     * @param color the color of the text
     */
    fun setColorTextNoCenter(color: Int) {
        colorTextNoCenter = color
    }

    /**
     * Sets the text type face for items
     * @param typeFace
     */
    fun setTypeFace(typeFace: Typeface?) {
        this.typeFace = typeFace
    }

    /**
     * This method is used to set the items to display in the panel and the empty rows in the
     * beginning and in the end.
     *
     * @param rawItems elements to show in panel
     * @param position position to set in the center of the list. By default, is the half of items.
     */
    private fun setItems(rawItems: List<String?>, position: Int) {
        addEmptyRows(rawItems)
        centerPosition = if (position == -1) {
            2
        } else {
            position + 2
        }
    }

    /**
     * It saves in [PickerAdapter.centerPosition] and notify adapter. Then, the adapter in
     * [ ][PickerAdapter.setTextItemStyle] (TextView, int)} set the appropriate style.
     *
     * @param position this is the position in the center of the list
     */
    fun handleSelectEvent(position: Int) {
        centerPosition = position
        notifyDataSetChanged()
    }

    /**
     * The first two positions must be empty.
     * The last 2 positions must be empty too.
     */
    private fun setPositonsNoClickables() {
        positionsNoClickables.put(0, 0)
        positionsNoClickables.put(1, 1)
        positionsNoClickables.put(items.size - 2, items.size - 2)
        positionsNoClickables.put(items.size - 1, items.size - 1)
    }

    /**
     * The first two positions must be empty.
     * The last 2 positions must be empty too.
     *
     * @param rawItems the items to show in panel
     */
    private fun addEmptyRows(rawItems: List<String?>) {
        val emptyRows = listOf(EMPTY_STRING, EMPTY_STRING)
        val items: MutableList<String?> = ArrayList()
        items.addAll(emptyRows)
        items.addAll(rawItems)
        items.addAll(emptyRows)
        this.items = items
    }

    override fun getCount() = items.size

    /**
     * This method indicates whether items can be clicked.
     *
     * if it is allowed that elements can be clicked, the first two positions and the last 2
     * positions can not be
     * pressed.
     *
     * @param position the current position of the actual item
     * @return if this item is clickable
     */
    override fun isEnabled(position: Int) = false

    object ViewHolder {
        operator fun <T : View?> get(view: View, id: Int): T? {
            var viewHolder = view.tag as SparseArray<View?>?
            if (viewHolder == null) {
                viewHolder = SparseArray()
                view.tag = viewHolder
            }
            var childView = viewHolder[id]
            if (childView == null) {
                childView = view.findViewById(id)
                viewHolder.put(id, childView)
            }
            return childView as T?
        }
    }

    companion object {
        private const val EMPTY_STRING = ""
    }
}
