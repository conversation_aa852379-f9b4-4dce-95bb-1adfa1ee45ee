package co.ujet.android.ui.widgets

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityEvent
import android.view.animation.TranslateAnimation
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.commons.domain.chat.message.QuickReplyButton
import co.ujet.android.commons.domain.chat.message.QuickReplyLinkButton
import co.ujet.android.commons.domain.chat.message.VirtualAgentQuickReplyButtonsChatMessage
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil
import co.ujet.android.commons.util.ResizeTextAccessibilityUtil.MAX_FONT_SIZE
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions
import co.ujet.android.modulemanager.common.ui.domain.ChatActionsMenuStyle
import co.ujet.android.modulemanager.common.ui.domain.ChatInputBarStyle
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.ui.R
import co.ujet.android.ui.extensions.setVisibility
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.StyleUtil
import co.ujet.android.ui.util.StyleUtil.setButtonImageOfDefault
import com.google.android.material.button.MaterialButton
import java.util.concurrent.TimeUnit

class ChatInputBarLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : LinearLayout(context, attrs) {
    private var inputBarStyles: ChatInputBarStyle? = null
    private var chatActionsMenuStyle: ChatActionsMenuStyle? = null
    private var listener: ChatInputBarListener? = null
    private val rootView: LinearLayout
    private val bannerContainer: LinearLayout
    private var bannerTopBorder: View? = null
    private var descriptionText: TextView? = null
    private var progressBar: UjetProgressBar? = null
    private var successIcon: ImageView? = null
    private val mainInputContainer: LinearLayout
    private val chatActionsMenuButton: ImageView
    private val escalateButton: ImageView
    private val messageEditTextLayout: RelativeLayout
    private val messageEditText: EditText
    private var ongoingInput: String = ""
    private var footerViewHeight = 0
    private var quickReplyButtons: View? = null
    private var ujetStyle: UjetStyle? = null
    private var textSizeWhenIconsAlignedVertically: Int? = null
    private var isQuickReplyButtonsVisible = false
    private var textCountPerLine = 0
    private val messageUpdateHandler = Handler(Looper.getMainLooper()) {
        if (it.what == MSG_UPDATE_MESSAGE_PREVIEW) {
            listener?.onSendChatMessagePreview(getOngoingInputMessage())
            true
        } else {
            false
        }
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.chat_input_bar_layout, this).apply {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        }
        rootView = findViewById(R.id.root_view)
        bannerContainer = findViewById(R.id.banner_container)
        mainInputContainer = findViewById(R.id.main_input_container)
        chatActionsMenuButton = findViewById(R.id.chat_actions_menu_button)
        escalateButton = findViewById(R.id.escalate_button)
        messageEditTextLayout = findViewById(R.id.message_edit_text_layout)
        messageEditText = findViewById(R.id.message_edit_text)
    }

    fun init(
        ujetStyle: UjetStyle, listener: ChatInputBarListener, hideMediaAttachment: Boolean,
        ujetStylesOptions: UjetStylesOptions?, escalateButtonText: String
    ) {
        this.ujetStyle = ujetStyle
        this.listener = listener
        inputBarStyles = ujetStylesOptions?.chatStyles?.userInputBar
        chatActionsMenuStyle = ujetStylesOptions?.chatStyles?.chatActionsMenuStyle
        rootView.apply {
            footerViewHeight = layoutParams.height
            quickReplyButtons = findViewById(R.id.footer_quick_reply_buttons)
        }
        quickReplyButtons?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
        applyBannerContainerStyles()
        applyMainInputContainerStyles(inputBarStyles)
        applyChatActionsMenuButtonStyles(hideMediaAttachment, inputBarStyles, chatActionsMenuStyle)
        applyEscalateButtonStyles(escalateButtonText)
        applyMessageBoxStyles(inputBarStyles)
    }

    fun getMainInputContainerHeight() = mainInputContainer.height

    fun setQuickReplyButtonsBottomMargin(margin: Int) {
        quickReplyButtons?.updateLayoutParams<MarginLayoutParams> {
            bottomMargin = margin
        }
    }

    fun setFocus(value: Boolean) {
        val accessibilityImportanceValue = if (value) IMPORTANT_FOR_ACCESSIBILITY_YES else IMPORTANT_FOR_ACCESSIBILITY_NO
        messageEditText.importantForAccessibility = accessibilityImportanceValue
        listOf(escalateButton, chatActionsMenuButton).forEach { view ->
            view.apply {
                isFocusable = value
                importantForAccessibility = accessibilityImportanceValue
            }
        }
    }

    private fun applyBannerContainerStyles() {
        val ujetStyle = ujetStyle ?: return
        bannerContainer.visibility = GONE
        bannerContainer.setBackgroundColor(ujetStyle.primaryBackgroundColor)
        findViewById<View>(R.id.banner_top_border)?.apply {
            setBackgroundColor(ujetStyle.dividerBackgroundColor)
        }
        bannerTopBorder = bannerContainer.findViewById(R.id.banner_top_border)
        descriptionText = bannerContainer.findViewById<TextView>(R.id.description)?.apply {
            UjetViewStyler.overrideTypeface(ujetStyle, this)
            UjetViewStyler.styleSecondaryText(ujetStyle, this)
        }
        progressBar = bannerContainer.findViewById<UjetProgressBar>(R.id.progressBar).apply {
            val spinnerColor = ContextCompat.getColor(context, R.color.ujet_gray_lighter)
            setColorFilter(BlendModeColorFilterCompat.createBlendModeColorFilterCompat(spinnerColor, BlendModeCompat.SRC_IN))
        }
        successIcon = bannerContainer.findViewById<ImageView>(R.id.success_icon)?.apply {
            visibility = GONE
        }
    }

    private fun applyMainInputContainerStyles(inputBarStyles: ChatInputBarStyle?) {
        val ujetStyle = ujetStyle ?: return
        val topBorderView: View = mainInputContainer.findViewById(R.id.top_border)
        val topBorderColor = StyleUtil.getColorResIdByName(context, inputBarStyles?.topBorder?.color)
        if (topBorderColor == StyleUtil.RESOURCE_NOT_FOUND) {
            topBorderView.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, 0)
        } else {
            topBorderView.setBackgroundColor(DesignUtil.getColor(context, topBorderColor))
            val borderWidth = DesignUtil.dpToPx(context, inputBarStyles?.topBorder?.width ?: 0).toInt()
            topBorderView.layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, borderWidth)
        }
        val backgroundColorResId = StyleUtil.getColorResIdByName(context, inputBarStyles?.backgroundColor)
        val backgroundColor = if (backgroundColorResId == StyleUtil.RESOURCE_NOT_FOUND) {
            ujetStyle.chatFooterBackgroundColor
        } else {
            ContextCompat.getColor(context, backgroundColorResId)
        }
        mainInputContainer.setBackgroundColor(backgroundColor)
    }

    private fun applyChatActionsMenuButtonStyles(hideMediaAttachment: Boolean,
                                                 inputBarStyles: ChatInputBarStyle?,
                                                 chatActionsMenuStyle: ChatActionsMenuStyle?) {
        with(chatActionsMenuButton) {
            //If hideMediaAttachmentInChat is enabled then we should not show the send photo icon.
            setVisibility(
                (hideMediaAttachment || (chatActionsMenuStyle?.cameraIcon?.visible == false && chatActionsMenuStyle?.selectPhotoFromLibraryIcon?.visible == false))
                        && chatActionsMenuStyle?.cobrowseIcon?.visible == false,
                GONE,
                VISIBLE
            )
            setButtonImageOfDefault(context, ujetStyle, this, inputBarStyles?.chatActionsMenuIcon?.icon, R.drawable.ujet_ic_chat_actions)
            setOnClickListener {
                listener?.onChatActionsMenuIconClicked()
                hideKeyboard(context)
            }
        }
    }

    fun setActionsMenuContentDescription(description:String){
        chatActionsMenuButton.contentDescription = description
    }

    private fun applyEscalateButtonStyles(escalateButtonText: String) {
        with(escalateButton) {
            contentDescription = escalateButtonText
            setVisibility(inputBarStyles?.escalateIcon?.visible == false, GONE, VISIBLE)
            setButtonImageOfDefault(context, ujetStyle, this, inputBarStyles?.escalateIcon?.icon, R.drawable.ujet_ic_escalate_chat)
            setOnClickListener {
                listener?.showEscalateConfirmation()
            }
        }
    }

    private fun applyMessageBoxStyles(inputBarStyles: ChatInputBarStyle?) {
        val ujetStyle = ujetStyle ?: return
        UjetViewStyler.overrideTypeface(ujetStyle, messageEditText)
        UjetViewStyler.styleTertiaryEditText(ujetStyle, messageEditText)

        val cursorColorResId = StyleUtil.getColorResIdByName(context, inputBarStyles?.inputField?.cursorColor)
        if (cursorColorResId != StyleUtil.RESOURCE_NOT_FOUND) {
            if (VERSION.SDK_INT >= VERSION_CODES.Q) {
                val cursorWidth = DesignUtil.dpToPx(context, 2).toInt()
                val color = ContextCompat.getColor(context, cursorColorResId)
                messageEditText.textCursorDrawable =
                    GradientDrawable(GradientDrawable.Orientation.BOTTOM_TOP, intArrayOf(color, color)).apply {
                        setSize(cursorWidth, messageEditText.textSize.toInt())
                    }
            } else {
                StyleUtil.setCursorColor(messageEditText, cursorColorResId)
            }
        }
        inputBarStyles?.inputField?.placeholderText?.let {
            setHint(it)
        }
        StyleUtil.updateFontStyle(context, messageEditText, inputBarStyles?.inputField?.font)

        messageEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                val input = s.toString()
                ongoingInput = input
                // Reset when user clears the text and to re-calculate textSizeWhenIconsAlignedVertically
                // when font size is changed.
                if (input.isEmpty()) {
                    textSizeWhenIconsAlignedVertically = null
                }
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable) {
                val input = s.toString()
                listener?.onSetChatInput(input)
                if (input.isBlank()) {
                    return
                }
                if (listener?.isChatPreviewAvailable() == true) {
                    if (ongoingInput != input && !messageUpdateHandler.hasMessages(MSG_UPDATE_MESSAGE_PREVIEW)) {
                        ongoingInput = input
                        messageUpdateHandler.sendMessageDelayed(
                            messageUpdateHandler.obtainMessage(MSG_UPDATE_MESSAGE_PREVIEW),
                            MESSAGE_PREVIEW_DELAY
                        )
                    }
                }
                if (messageEditText.lineCount == 1) {
                    textCountPerLine = input.length
                }
                alignActionIcons(input)
            }
        })
        messageEditText.imeOptions = EditorInfo.IME_ACTION_SEND
        messageEditText.setRawInputType(InputType.TYPE_CLASS_TEXT)
        messageEditText.maxLines = INPUT_FIELD_MAX_LINES
        messageEditText.setImeActionLabel("Send", KeyEvent.KEYCODE_ENTER)
        messageEditText.setOnEditorActionListener(TextView.OnEditorActionListener { v, _, event ->
            if (event == null) {
                return@OnEditorActionListener false
            }
            if (messageEditText.text.isNotEmpty() && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                listener?.onChatMessageSend(v.text.toString())
                messageEditText.setText("")
            }
            true
        })
        messageEditText.setText("")

        val backgroundColorResId = StyleUtil.getColorResIdByName(context, inputBarStyles?.backgroundColor)
        val backgroundColor = if (backgroundColorResId == StyleUtil.RESOURCE_NOT_FOUND) {
            ujetStyle.chatFooterBackgroundColor
        } else {
            ContextCompat.getColor(context, backgroundColorResId)
        }
        val borderWidth = DesignUtil.dpToPx(context, inputBarStyles?.inputField?.border?.width ?: 0)
        val borderColor = inputBarStyles?.inputField?.border?.color
        val cornerRadius = DesignUtil.dpToPx(context, inputBarStyles?.inputField?.cornerRadius?.toInt() ?: 0)
        messageEditText.background = StyleUtil.createBorderedBackground(context, backgroundColor, borderWidth, borderColor, cornerRadius)
        val defaultInputBarPadding = DesignUtil.getDimensionPixelSize(context, R.dimen.ujet_chat_input_bar_layout_padding)
        val horizontalPadding = (borderWidth + defaultInputBarPadding).toInt()
        val verticalPadding = horizontalPadding + cornerRadius.div(4).toInt()
        messageEditText.setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding)
    }

    fun requestFocusOfChatActionsMenu() {
        chatActionsMenuButton.requestFocus()
    }

    private fun alignActionIcons(input: String) {
        // Align action icons when font size is at 200% or more
        if (!(ResizeTextAccessibilityUtil.getMaxFontSize(context) >= MAX_FONT_SIZE ||
                    ResizeTextAccessibilityUtil.isFontSizeExceededLimit(context))
        ) {
            return
        }
        val messageLinesCount = messageEditText.lineCount
        // When action icons are aligned vertically, we get additional space in edit text so
        // line count will change from 2 to 1 and icons will switch from vertical to horizontal
        // alignment. To address this edge case, we saved text size when icons aligned vertically,
        // after that if user started typing additional texts in edit text, we still need to align icons
        // vertically so save it in areIconsAlignedVertically and when user started removing
        // texts from edit text, we need to switch icons alignment from vertical to horizontal
        // so save it in areIconsAlignedHorizontally.
        val (areIconsAlignedVertically, areIconsAlignedHorizontally) = textSizeWhenIconsAlignedVertically?.let {
            return@let Pair(it <= input.length, it > input.length)
        } ?: run {
            Pair(false, false)
        }
        // When areIconsAlignedHorizontally is true, align icons horizontally and otherwise,
        // check if areIconsAlignedVertically is true or not. When both of these are false,
        // check edit text lines count to determine to icons alignment.
        val alignIconsVertically = if (areIconsAlignedHorizontally) {
            false
        } else {
            areIconsAlignedVertically || (messageLinesCount > 1)
        }
        listener?.setActionIconAlignment(alignIconsVertically)
        updateActionIconsAlignment()
        // Store text size when edit text aligned vertically to keep action icons stay aligned
        // vertically or horizontally based on user typed more texts or removed texts from edit text.
        if (alignIconsVertically && textSizeWhenIconsAlignedVertically == null) {
            /* When user changed font size while typing text during chat, we can not calculate correct text
            count per line as it varies based on font size and since it is highly unlikely edge case
            and we are going to redesign action icons in future, we can ignore this case. For now, we
            store previous text count per line and if it exists then we use it as it is closely matches.
            If user clears the text then we always re-calculate correct text count per line.
            * */
            if (messageEditText.lineCount > 1 && (listener?.getTextCountPerLine() ?: 0) > 0) {
                textSizeWhenIconsAlignedVertically = listener?.getTextCountPerLine()
            } else {
                textSizeWhenIconsAlignedVertically = textCountPerLine
                listener?.setTextCountPerLine(textCountPerLine)
            }
        }
    }

    fun updateActionIconsAlignment() {
        val areIconsAlignedVertically = listener?.getActionIconAlignment() == true
        if (chatActionsMenuButton.visibility == View.VISIBLE) {
            updateChatActionsMenuButtonParams(areIconsAlignedVertically)
        }
        if (escalateButton.visibility == View.VISIBLE) {
            updateEscalationButtonParams(areIconsAlignedVertically)
        }
        updateMessageEditTextParams(areIconsAlignedVertically)
    }

    private fun updateChatActionsMenuButtonParams(areIconsAlignedVertically: Boolean) {
        val chatActionsMenuButtonParams = RelativeLayout.LayoutParams(
            getIconLayoutParams(),
            getIconLayoutParams()
        )
        chatActionsMenuButtonParams.topMargin = getChatActionsMenuButtonTopMargin(areIconsAlignedVertically)
        chatActionsMenuButtonParams.bottomMargin = getIconMargin()
        chatActionsMenuButtonParams.marginStart = getIconMargin()
        chatActionsMenuButtonParams.marginEnd = getIconMargin()
        chatActionsMenuButton.layoutParams = chatActionsMenuButtonParams
        chatActionsMenuButton.setPadding(getIconMargin(), getIconMargin(), getIconMargin(), getIconMargin())
    }

    private fun updateEscalationButtonParams(areIconsAlignedVertically: Boolean) {
        val escalateButtonParams = RelativeLayout.LayoutParams(
            getIconLayoutParams(),
            getIconLayoutParams()
        )
        escalateButtonParams.addRule(getLayoutParamsRule(areIconsAlignedVertically), R.id.chat_actions_menu_button)
        if (areIconsAlignedVertically) {
            escalateButtonParams.marginStart = getIconMargin()
        } else {
            escalateButtonParams.topMargin = getIconMargin()
        }
        escalateButtonParams.bottomMargin = getIconMargin()
        escalateButtonParams.marginEnd = getIconMargin()
        escalateButton.layoutParams = escalateButtonParams
        escalateButton.setPadding(getIconMargin(), getIconMargin(), getIconMargin(), getIconMargin())
    }

    private fun updateMessageEditTextParams(areIconsAlignedVertically: Boolean) {
        val endOfResId = when {
            areIconsAlignedVertically -> {
                R.id.chat_actions_menu_button
            }

            escalateButton.visibility == View.VISIBLE -> {
                R.id.escalate_button
            }

            else -> {
                R.id.chat_actions_menu_button
            }
        }
        val params = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        )
        params.addRule(RelativeLayout.END_OF, endOfResId)
        params.topMargin = getIconMargin()
        params.bottomMargin = getIconMargin()
        params.marginEnd = getIconMargin()
        messageEditTextLayout.layoutParams = params
    }

    private fun getLayoutParamsRule(areIconsAlignedVertically: Boolean): Int {
        return if (areIconsAlignedVertically) {
            RelativeLayout.BELOW
        } else {
            RelativeLayout.END_OF
        }
    }

    private fun getChatActionsMenuButtonTopMargin(areIconsAlignedVertically: Boolean): Int {
        var topMargin = getIconMargin()
        if (areIconsAlignedVertically) {
            // Calculate vertical padding to be added to top margin so that icons are centered vertically
            val actualScreenHeight = mainInputContainer.height + (getIconMargin() * 2)
            var currentViewHeight = 0
            var iconsCount = 0
            if (chatActionsMenuButton.visibility == View.VISIBLE) {
                currentViewHeight += chatActionsMenuButton.height
                iconsCount += 1
            }
            if (escalateButton.visibility == View.VISIBLE) {
                currentViewHeight += escalateButton.height
                iconsCount += 1
            }
            // Add top and bottom margin between action icons which are visible to current view height
            currentViewHeight += (iconsCount + 1) * getIconMargin()
            val padding = (actualScreenHeight - currentViewHeight) / 2
            if (padding >= 0) {
                topMargin = padding
            }
        }
        return topMargin
    }

    private fun getIconMargin() = getPixelSize(R.dimen.ujet_chat_input_bar_icon_margin)

    private fun getIconLayoutParams() = getPixelSize(R.dimen.ujet_chat_input_bar_icon_params)

    private fun getPixelSize(dimenResId: Int): Int {
        return resources.getDimensionPixelSize(dimenResId)
    }

    fun setChatInputVisible(activity: Activity, isVisible: Boolean) {
        val layoutParams = rootView.layoutParams ?: return
        messageEditText.setVisibility(isVisible, VISIBLE, GONE)
        if (isVisible) {
            layoutParams.height = footerViewHeight
            rootView.layoutParams = layoutParams
        } else {
            layoutParams.height = 0
            rootView.layoutParams = layoutParams
            hideKeyboard(activity)
        }
    }

    fun setHint(hint: String?) {
        messageEditText.hint = hint
    }

    fun setImeActionLabel(label: String) {
        messageEditText.setImeActionLabel(label, EditorInfo.IME_ACTION_SEND)
    }

    fun hideKeyboard(context: Context) {
        val imm = context.applicationContext?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
        imm?.hideSoftInputFromWindow(messageEditText.windowToken ?: return, 0)
    }

    fun updateQuickReplyButtons(lastMessage: ChatMessage?,
                                postSessionQuickReplyListContainer: LinearLayout? = null,
                                postSessionQuickReplyButtons: HorizontalScrollView? = null,
                                chatMessageRecyclerView: RecyclerView? = null,
                                isPostSessionChatInProgress: Boolean = false) {
        var mainContainer = rootView.findViewById<LinearLayout>(R.id.quick_reply_list_view)
        if (isPostSessionChatInProgress) {
            mainContainer = postSessionQuickReplyListContainer
            this.quickReplyButtons = postSessionQuickReplyButtons
        }
        if (lastMessage is VirtualAgentQuickReplyButtonsChatMessage && lastMessage.quickReplyButtonsVisible) {
            this.quickReplyButtons?.visibility = VISIBLE
            mainContainer?.removeAllViews()
            lastMessage.quickReplyButtons.forEachIndexed { index, quickReply ->
                val ujetStyle = ujetStyle ?: return
                val stickyButtonView = MaterialButton(
                    context,
                    null,
                    R.style.UjetButton_QuickReplyButton
                )
                val stickyButtonParams = RelativeLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                stickyButtonParams.marginStart = getPixelSize(R.dimen.ujet_chat_message_margin)
                stickyButtonParams.marginEnd = getPixelSize(R.dimen.ujet_chat_message_margin)
                stickyButtonView.layoutParams = stickyButtonParams

                val screenWidthDp = resources.configuration.screenWidthDp
                val maxWidth = DesignUtil.dpToPx(context, DesignUtil.dpToPx(context, screenWidthDp / 4.5f).toInt()).toInt()
                stickyButtonView.maxWidth = maxWidth
                stickyButtonView.minWidth = getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_min_width)

                stickyButtonView.text = quickReply.title
                stickyButtonView.strokeWidth = getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_stroke)
                stickyButtonView.insetTop = getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_inset_top_bottom)
                stickyButtonView.insetBottom = getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_inset_top_bottom)
                stickyButtonView.cornerRadius = getPixelSize(R.dimen.ujet_chat_sticky_buttons_corner_radius)
                stickyButtonView.minHeight = getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_min_height)
                stickyButtonView.gravity = Gravity.CENTER
                stickyButtonView.textSize = 14f
                stickyButtonView.isAllCaps = false
                stickyButtonView.setPaddingRelative(
                    getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_horizontal_padding_start_end),
                    getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_horizontal_padding_top_bottom),
                    getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_horizontal_padding_start_end),
                    getPixelSize(R.dimen.ujet_chat_quick_reply_buttons_horizontal_padding_top_bottom),
                )

                if (quickReply is QuickReplyLinkButton) {
                    stickyButtonView.paintFlags = Paint.UNDERLINE_TEXT_FLAG
                }

                UjetViewStyler.stylePrimaryButton(ujetStyle, stickyButtonView)
                stickyButtonView.setOnClickListener {
                    if (quickReply is QuickReplyLinkButton && quickReply.link != null) {
                        try {
                            context.startActivity(
                                Intent(
                                    Intent.ACTION_VIEW,
                                    Uri.parse(quickReply.link)
                                )
                            )
                        } catch (e: ActivityNotFoundException) {
                            Logger.w(e, e.message)
                        }
                    }
                    listener?.onQuickReplyClicked(lastMessage, quickReply)
                }
                mainContainer.addView(stickyButtonView)
                isQuickReplyButtonsVisible = true
            }
            if (isPostSessionChatInProgress) {
                mainContainer?.post {
                    chatMessageRecyclerView?.updatePadding(bottom = mainContainer.height)
                }
            }
        } else {
            mainContainer.removeAllViews()
            isQuickReplyButtonsVisible = false
            this.quickReplyButtons?.visibility = GONE
        }
    }

    fun isQuickReplyButtonsVisible() = isQuickReplyButtonsVisible

    fun onStop() {
        messageUpdateHandler.removeMessages(MSG_UPDATE_MESSAGE_PREVIEW)
    }


    fun displaySavedChatInput(message: String) {
        if (message.isNotEmpty() && message != getOngoingInputMessage()) {
            messageEditText.setText(message)
        }
    }

    fun isEscalateButtonVisible() = escalateButton.isVisible

    fun setEscalateEnabled(enabled: Boolean) {
        escalateButton.setVisibility(enabled && inputBarStyles?.escalateIcon?.visible != false, VISIBLE, GONE)
        updateActionIconsAlignment()
    }

    fun clearResources() {
        // Force hide transfer banner when UI is destroyed
        hideTransferBanner()
        bannerTopBorder = null
        descriptionText = null
        progressBar = null
    }

    // slide the view from below
    fun showTransferBanner(message: String) {
        setMainContainerEnabled(false)
        setBannerViewVisibility(showBannerViews = true, successViewVisible = false)
        descriptionText?.text = message
        val animate = TranslateAnimation(
            0F,  // fromXDelta
            0F,  // toXDelta
            bannerContainer.height.toFloat(),  // fromYDelta
            0F
        ) // toYDelta
        animate.duration = 300 // 1000
        animate.fillAfter = true
        bannerContainer.startAnimation(animate)
    }

    fun showTransferFinishedView(message: String) {
        descriptionText?.text = message
        setBannerViewVisibility(successViewVisible = true)
        // Add 2 sec delay to show finished/success view before banner closed down
        Handler(Looper.getMainLooper()).postDelayed({
            hideTransferBanner()
        }, TimeUnit.SECONDS.toMillis(2))
    }

    // Since animation sliding over message input area, switching to animation disappearing instead for now
    private fun hideTransferBanner() {
        setBannerViewVisibility(showBannerViews = false)
        setMainContainerEnabled(true)
    }

    private fun setMainContainerEnabled(isEnabled: Boolean) {
        messageEditText.isEnabled = isEnabled
        chatActionsMenuButton.isEnabled = isEnabled
        escalateButton.isEnabled = isEnabled
    }

    private fun setBannerViewVisibility(showBannerViews: Boolean? = null, successViewVisible: Boolean? = null) {
        showBannerViews?.let {
            bannerTopBorder?.visibility = if (it) VISIBLE else GONE
            descriptionText?.visibility = if (it) VISIBLE else GONE
            bannerContainer.visibility = if (it) VISIBLE else GONE
        }
        successViewVisible?.let {
            progressBar?.visibility = if (it) GONE else VISIBLE
            successIcon?.visibility = if (it) VISIBLE else GONE
        }
    }

    fun invokeFocusEventOnEscalateButton() {
        handler.postDelayed({
            escalateButton.requestFocus()
            escalateButton.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED)
        }, ESCALATE_BUTTON_ANNOUNCEMENT_DELAY_DURATION)
    }

    private fun getOngoingInputMessage() = messageEditText.text.toString()

    interface ChatInputBarListener {
        fun onQuickReplyClicked(lastMessage: VirtualAgentQuickReplyButtonsChatMessage, quickReply: QuickReplyButton)
        fun onSendChatMessagePreview(ongoingInputMessage: String)
        fun onChatMessageSend(message: String)
        fun onSetChatInput(input: String)
        fun isChatPreviewAvailable(): Boolean?
        fun onChatActionsMenuIconClicked()
        fun showEscalateConfirmation()
        fun setActionIconAlignment(alignIconsVertically: Boolean)
        fun getActionIconAlignment(): Boolean
        fun setTextCountPerLine(textCountPerLine: Int)
        fun getTextCountPerLine(): Int
    }

    companion object {
        private const val MSG_UPDATE_MESSAGE_PREVIEW = 1
        private const val INPUT_FIELD_MAX_LINES = 4
        private const val MESSAGE_PREVIEW_DELAY = 2000L
        private const val ESCALATE_BUTTON_ANNOUNCEMENT_DELAY_DURATION = 300L

    }
}
