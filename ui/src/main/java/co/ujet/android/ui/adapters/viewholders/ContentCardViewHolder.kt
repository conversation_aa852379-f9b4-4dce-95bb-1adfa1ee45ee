package co.ujet.android.ui.adapters.viewholders

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.LinearLayout
import android.widget.RelativeLayout.LayoutParams
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.ContentCardButton
import co.ujet.android.commons.extensions.loadRemoteImage
import co.ujet.android.modulemanager.common.ui.domain.ContentCardStyle
import co.ujet.android.modulemanager.common.ui.domain.FontStyle
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.ui.R
import co.ujet.android.ui.button.FancyButton
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.StyleUtil
import co.ujet.android.ui.util.StyleUtil.applyCornerRadiusToImageView
import co.ujet.android.ui.util.StyleUtil.applyPaddingToCardLayout
import com.google.android.material.imageview.ShapeableImageView

class ContentCardViewHolder(
    itemView: View,
    private val contentCardStyle: ContentCardStyle?,
    private val ujetStyle: UjetStyle,
    private val adapterPosition: Int
) : RecyclerView.ViewHolder(itemView) {
    private val cardLayout: View = itemView.findViewById(R.id.card_layout)
    private val buttonLayout: LinearLayout = itemView.findViewById(R.id.button_container)
    private val image: ShapeableImageView = itemView.findViewById(R.id.image)
    private val title: TextView = itemView.findViewById(R.id.title)
    private val subtitle: TextView = itemView.findViewById(R.id.subtitle)
    private val messageBody: TextView = itemView.findViewById(R.id.message_body)
    private val buttonMap = hashMapOf<String, ArrayList<FancyButton>>()
    private var isButtonContainsLink = false

    fun bind(context: Context, contentCard: ContentCard, callback: CardClickedCallback?) {
        image.visibility = View.VISIBLE
        title.text = contentCard.title
        subtitle.text = contentCard.subtitle
        messageBody.text = contentCard.body
        UjetViewStyler.stylePrimaryText(ujetStyle, title)
        UjetViewStyler.stylePrimaryText(ujetStyle, subtitle)
        UjetViewStyler.stylePrimaryText(ujetStyle, messageBody)
        title.textSize = ujetStyle.pxToSp(20f)
        subtitle.textSize = ujetStyle.pxToSp(14f)
        messageBody.textSize = ujetStyle.pxToSp(14f)
        contentCard.buttons?.forEach { contentCardButton ->
            if (!contentCardButton.title.isNullOrEmpty()) {
                val button = FancyButton(context)
                button.setText(contentCardButton.title)
                applyButtonStyle(button, contentCardButton.style)
                button.setOnClickListener {
                    callback?.onCardButtonClicked(contentCardButton, contentCard, adapterPosition)
                    contentCardButton.link?.let { url ->
                        openUrlInBrowser(context, url)
                    }
                    if (contentCardButton.autoReply == true) {
                        autoReplyTriggerMap[adapterPosition] = true
                    }
                }
                if (autoReplyTriggerMap.contains(adapterPosition) && contentCardButton.link.isNullOrEmpty() && contentCardButton.autoReply == true) {
                    button.isEnabled = false
                }
                isButtonContainsLink = !contentCardButton.link.isNullOrEmpty() || isButtonContainsLink
                buttonLayout.addView(button)
            }
        }
        applyCustomStyles(context, contentCard)
        subtitle.visibility = if (contentCard.subtitle.isNullOrBlank()) {
            View.GONE
        } else {
            View.VISIBLE
        }

        cardLayout.setOnClickListener {
            if (isButtonContainsLink) {
                return@setOnClickListener
            }
            callback?.onCardClicked(contentCard)
            contentCard.link?.let { url ->
                openUrlInBrowser(context, url)

            }
        }
    }

    private fun openUrlInBrowser(context: Context, url: String) {
        try {
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
        } catch (e: ActivityNotFoundException) {
            Logger.w(e, e.message)
        }
    }


    private fun applyButtonStyle(button: FancyButton, style: String?) {
        when (style) {
            PRIMARY_BUTTON -> {
                UjetViewStyler.stylePrimaryContentCardButton(ujetStyle, button)
                updateButtonMap(button, PRIMARY_BUTTON)
            }

            SECONDARY_BUTTON -> {
                UjetViewStyler.styleSecondaryContentCardButton(ujetStyle, button)
                updateButtonMap(button, SECONDARY_BUTTON)
            }
        }
        button.setCustomTypeFace(ujetStyle.typeFace)
        button.layoutParams = LayoutParams(MATCH_PARENT, WRAP_CONTENT).apply {
            topMargin = DesignUtil.dpToPx(button.context, 8).toInt()
        }
        button.minimumHeight = ujetStyle.dpToPx(48f).toInt()
    }

    @SuppressLint("DiscouragedApi")
    private fun applyCustomStyles(context: Context, contentCard: ContentCard) {
        // Hero image
        val defaultCornerRadius = 16
        val defaultImageHeight = 160
        val cornerRadius = DesignUtil.dpToPx(context, contentCardStyle?.cornerRadius?.toInt() ?: defaultCornerRadius)
        val imageHeight = ujetStyle.dpToPx(contentCardStyle?.image?.height ?: defaultImageHeight).toInt()
        applyCornerRadiusToImageView(image,cornerRadius)
        val imagePath = contentCard.images?.get(0)
        var resId = 0
        imagePath?.let { resId = context.resources.getIdentifier(it, "drawable", context.packageName) }
        if (resId != 0) {
            val drawable = ContextCompat.getDrawable(context, resId)
            image.setImageDrawable(drawable)
        } else {
            image.loadRemoteImage(
                path = imagePath,
                onError = {
                    image.visibility = View.GONE
                }
            )
        }

        image.layoutParams = ConstraintLayout.LayoutParams(MATCH_PARENT, imageHeight)

        // Background color and Corner radius
        var backgroundColorResId = StyleUtil.getColorResIdByName(context, contentCardStyle?.backgroundColor)
        if (backgroundColorResId == StyleUtil.RESOURCE_NOT_FOUND) {
            backgroundColorResId = if (ujetStyle.isDarkModeEnabled) {
                R.color.ujet_content_card_background_color_dark
            } else {
                R.color.ujet_content_card_background_color
            }
        }
        StyleUtil.updateBackgroundStyle(
            cardLayout,
            backgroundColorResId,
            contentCardStyle?.cornerRadius ?: defaultCornerRadius.toString(),
            contentCardStyle?.border
        )
        if (StyleUtil.getColorResIdByName(context, contentCardStyle?.border?.color) != StyleUtil.RESOURCE_NOT_FOUND) {
            val borderWidth = ujetStyle.dpToPx(contentCardStyle?.border?.width?.toFloat() ?: 0f).toInt()
            //Calculate inner radius of the card border(cornerRadius-(borderWidth/2f)) and apply it to image as top corner radius
            if (borderWidth != 0) {
                applyCornerRadiusToImageView(image,cornerRadius - (borderWidth / 2f))
            }
        }
        applyPaddingToCardLayout(context, cornerRadius, ujetStyle, contentCardStyle?.border, cardLayout, contentCard.images.isNullOrEmpty())
        // Card font styles (inherited)
        StyleUtil.updateFontStyle(
            context,
            title,
            contentCardStyle?.font?.style,
            contentCardStyle?.font?.family,
            contentCardStyle?.font?.size,
            contentCardStyle?.font?.colorReference
        )

        // Title font styles
        StyleUtil.updateFontStyle(
            context,
            title,
            contentCardStyle?.title?.font?.style ?: contentCardStyle?.font?.style ?: FontStyle.Style.BOLD.value,
            contentCardStyle?.title?.font?.family ?: contentCardStyle?.font?.family,
            contentCardStyle?.title?.font?.size ?: contentCardStyle?.font?.size,
            contentCardStyle?.title?.font?.colorReference ?: contentCardStyle?.font?.colorReference
        )

        // Subtitle font styles
        StyleUtil.updateFontStyle(
            context,
            subtitle,
            contentCardStyle?.subtitle?.font?.style ?: contentCardStyle?.font?.style ?: FontStyle.Style.BOLD.value,
            contentCardStyle?.subtitle?.font?.family ?: contentCardStyle?.font?.family,
            contentCardStyle?.subtitle?.font?.size ?: contentCardStyle?.font?.size,
            contentCardStyle?.subtitle?.font?.colorReference ?: contentCardStyle?.font?.colorReference
        )

        // Body font styles
        StyleUtil.updateFontStyle(
            context,
            messageBody,
            contentCardStyle?.body?.font?.style ?: contentCardStyle?.font?.style,
            contentCardStyle?.body?.font?.family ?: contentCardStyle?.font?.family,
            contentCardStyle?.body?.font?.size ?: contentCardStyle?.font?.size,
            contentCardStyle?.body?.font?.colorReference ?: contentCardStyle?.font?.colorReference
        )
        // Primary button custom style
        buttonMap[PRIMARY_BUTTON]?.forEach { button ->
            StyleUtil.updateFontStyle(context, button, contentCardStyle?.primaryButton?.font)
            StyleUtil.updateBackgroundStyle(
                button,
                contentCardStyle?.primaryButton?.backgroundColor,
                contentCardStyle?.primaryButton?.cornerRadius,
                contentCardStyle?.primaryButton?.border
            )
        }
        // Secondary button custom style
        buttonMap[SECONDARY_BUTTON]?.forEach { button ->
            StyleUtil.updateFontStyle(context, button, contentCardStyle?.secondaryButton?.font)
            StyleUtil.updateBackgroundStyle(
                button,
                contentCardStyle?.secondaryButton?.backgroundColor,
                contentCardStyle?.secondaryButton?.cornerRadius,
                contentCardStyle?.secondaryButton?.border
            )
        }
    }

    private fun updateButtonMap(button: FancyButton, buttonIndex: String) {
        if (buttonMap[buttonIndex] == null) {
            buttonMap[buttonIndex] = arrayListOf(button)
        } else {
            buttonMap[buttonIndex]?.add(button)
        }
    }

    interface CardClickedCallback {
        fun onCardClicked(contentCard: ContentCard)
        fun onCardButtonClicked(button: ContentCardButton, contentCard: ContentCard, adapterPosition: Int)
    }

    companion object {
        const val PRIMARY_BUTTON = "primary"
        const val SECONDARY_BUTTON = "secondary"
        var autoReplyTriggerMap = linkedMapOf<Int, Boolean>()
    }
}
