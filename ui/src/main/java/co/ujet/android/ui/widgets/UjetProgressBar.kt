package co.ujet.android.ui.widgets

import android.content.Context
import android.content.res.Resources.NotFoundException
import android.graphics.ColorFilter
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.entrypoints.configuration.Configuration
import co.ujet.android.modulemanager.entrypoints.log.Logger

class UjetProgressBar @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : ProgressBar(context, attrs) {

    private var customSpinnerDrawable: Drawable? = null

    init {
        EntryPointFactory
            .provideEntryPoint(Configuration::class.java)
            .getSpinnerDrawableRes()?.let { spinnerDrawableRes ->
                try {
                    ContextCompat.getDrawable(context, spinnerDrawableRes)?.let { spinnerDrawable ->
                        customSpinnerDrawable = spinnerDrawable
                        indeterminateDrawable = customSpinnerDrawable
                    }
                } catch (e: NotFoundException) {
                    Logger.w(e, e.message)
                }
            }
    }

    fun setColorFilter(colorFilter: ColorFilter?) {
        if (customSpinnerDrawable == null) {
            indeterminateDrawable.colorFilter = colorFilter
        }
    }
}
