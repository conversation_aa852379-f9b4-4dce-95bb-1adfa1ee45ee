package co.ujet.android.ui.domain

import co.ujet.android.commons.libs.uson.SerializedName
import co.ujet.android.modulemanager.common.ui.domain.ChatActionsMenuStyle
import co.ujet.android.modulemanager.common.ui.domain.FormCardStyle
import co.ujet.android.modulemanager.common.ui.domain.PostSessionStyle

data class ChatStyles(
    @SerializedName("back_button")
    var backButton: BackButtonStyle? = null,

    @SerializedName("header")
    var header: ChatHeaderStyle? = null,

    @SerializedName("end_chat_button")
    var endChatButton: EndButtonStyle? = null,

    @SerializedName("timestamp")
    var timeStamps: TimeStampStyle? = null,

    @SerializedName("system_message")
    var systemMessages: SystemMessageStyle? = null,

    @SerializedName("agent_message_bubble")
    var agentMessageBubbles: AgentMessageStyle? = null,

    @SerializedName("consumer_message_bubble")
    var consumerMessageBubbles: ConsumerMessageStyle? = null,

    @SerializedName("user_input_bar")
    var userInputBar: ChatInputBarStyle? = null,

    @SerializedName("chat_actions_menu")
    var chatActionsMenu: ChatActionsMenuStyle? = null,

    @SerializedName("content_card")
    var contentCard: ContentCardStyle? = null,

    @SerializedName("form_card")
    var formCard: FormCardStyle? = null,

    @SerializedName("post_session")
    var postSession: PostSessionStyle? = null
)
