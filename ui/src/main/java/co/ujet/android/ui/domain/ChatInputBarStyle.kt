package co.ujet.android.ui.domain

import co.ujet.android.commons.libs.uson.SerializedName

data class ChatInputBarStyle(
    @SerializedName("background_color_reference")
    var backgroundColor: String? = null,

    @SerializedName("input_field")
    var inputField: ChatInputFieldStyle? = null,

    @SerializedName("top_border")
    var topBorder: BorderStyle? = null,

    @SerializedName("escalate_icon")
    var escalateIcon: IconStyle? = null,

    @SerializedName("chat_actions_menu_icon")
    var chatActionsMenuIcon: IconStyle? = null,

    @SerializedName("send_button")
    var sendButton: SendButtonStyle? = null
)
