###############################################################
# Java JWT
# - Ignore warnings.
# - Rules of Jackson. JJWT depends on <PERSON>.
###############################################################

#-keep class io.jsonwebtoken.** {
#  public protected private *;
#}
-keepattributes *Annotation*,EnclosingMethod

-keepnames class com.fasterxml.jackson.** { *; }
-keepnames interface com.fasterxml.jackson.** { *; }

-dontwarn javax.xml.**
-dontwarn javax.xml.stream.events.**
-dontwarn com.fasterxml.jackson.databind.**

-dontwarn org.bouncycastle.jce.ECNamedCurveTable

###############################################################
# Crashlytics rules
# - Skip to build faster
# - https://docs.fabric.io/android/crashlytics/dex-and-proguard.html#exclude-crashlytics-with-proguard
###############################################################
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**

-keep class io.fabric.sdk.android.** { *; }
-keep interface io.fabric.sdk.android.** { *; }

-keep class io.fabric.** { *; }
-keep interface io.fabric.** { *; }

-keepattributes SourceFile,LineNumberTable

###############################################################
# Twilio rules
# - https://www.twilio.com/docs/api/client/android
# - https://www.twilio.com/docs/voice/voip-sdk/android#setting-up-proguard-rules
###############################################################
-keep class com.twilio.** { *; }
-dontwarn org.apache.http.**
-keep class tvo.webrtc.** { *; }
-dontwarn tvo.webrtc.**
-keep class com.twilio.voice.** { *; }
-keepattributes InnerClasses

###############################################################
# - Rules of Web Form mock server data classes.
###############################################################
-keep class co.ujet.ujetandroidsdk.mock.FormDataRequest { *; }
-keep class co.ujet.ujetandroidsdk.mock.FormDataResponse { *; }
-keep class co.ujet.ujetandroidsdk.mock.FormData { *; }

# Gson
-keep class com.google.gson.** { *; }
-keep class sun.misc.Unsafe { *; }
-dontwarn sun.misc.Unsafe
-dontwarn com.google.gson.**
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type


-keep class co.ujet.ujetandroidsdk.env.Env { *; }
-keep class co.ujet.ujetandroidsdk.env.Tenant { *; }

# Retrofit
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions
-dontwarn retrofit2.**
-keep class okhttp3.** { *; }
-dontwarn okhttp3.**

# coroutine, reference: https://r8.googlesource.com/r8/+/refs/heads/main/compatibility-faq.md#kotlin-suspend-functions-and-generic-signatures
-keepattributes Signature
-keep class kotlin.coroutines.Continuation


# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn org.bouncycastle.jsse.BCSSLParameters
-dontwarn org.bouncycastle.jsse.BCSSLSocket
-dontwarn org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
-dontwarn org.conscrypt.Conscrypt$Version
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn org.openjsse.javax.net.ssl.SSLParameters
-dontwarn org.openjsse.javax.net.ssl.SSLSocket
-dontwarn org.openjsse.net.ssl.OpenJSSE
-dontwarn org.slf4j.impl.StaticLoggerBinder
