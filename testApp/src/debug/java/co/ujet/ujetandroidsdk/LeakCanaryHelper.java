package co.ujet.ujetandroidsdk;

import java.util.EnumSet;

import leakcanary.LeakCanary;
import shark.AndroidReferenceMatchers;

class LeakCanaryHelper {
    public static void setupLeakCanary() {
        //Reference from https://square.github.io/leakcanary/upgrading-to-leakcanary-2.0/
        LeakCanary.Config leakConfig = LeakCanary.getConfig().newBuilder()
                .referenceMatchers(AndroidReferenceMatchers.Companion.buildKnownReferences
                        (EnumSet.of(AndroidReferenceMatchers.SEM_CLIPBOARD_MANAGER__MCONTEXT,
                                AndroidReferenceMatchers.REFERENCES)))
                .build();
        LeakCanary.setConfig(leakConfig);
    }
}