{"chat": {"back_button": {"visible": true, "image_reference": "image_reference"}, "header": {"text_content": "some text here", "font": {"color_reference": "custom_color_2", "size": 14, "family": "<PERSON><PERSON>", "style": "bold|italic"}, "visible": true, "show_avatar_icon": true, "divider": {"width": 8, "color_reference": "custom_color_10"}}, "end_chat_button": {"visible": true, "font": {"color_reference": "custom_color_3", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}}, "timestamp": {"font": {"color_reference": "custom_color_4", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}}, "system_message": {"background_color_reference": "custom_color_2", "corner_radius": 10, "border": {"color_reference": "custom_color_6", "width": 2}, "font": {"color_reference": "custom_color_3", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}, "button_style": {"background_color_reference": "custom_color_4", "corner_radius": 10, "border": {"color_reference": "custom_color_5", "width": 2}, "font": {"color_reference": "custom_color_5", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}}}, "agent_message_bubble": {"background_color_reference": "custom_color_6", "corner_radius": 10, "border": {"color_reference": "custom_color_5", "width": 2}, "font": {"color_reference": "custom_color_7", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}, "avatar": {"visible": true, "size": 24, "position": "left|overlay"}}, "consumer_message_bubble": {"background_color_reference": "custom_color_8", "corner_radius": 10, "border": {"color_reference": "custom_color_4", "width": 2}, "font": {"color_reference": "custom_color_9", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 14}, "avatar": {"visible": true, "size": 24, "image_reference": "image_reference", "position": "right|overlay"}}, "user_input_bar": {"background_color_reference": "custom_color_10", "input_field": {"cursor_color_reference": "custom_color_9", "placeholder_text": "Some text here...", "corner_radius": 10, "border": {"color_reference": "custom_color_3", "width": 2}, "font": {"color_reference": "custom_color_2", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 14}}, "top_border": {"color_reference": "custom_color_8", "width": 2}, "menu_action_icon": {"image_reference": "image_reference"}, "escalate_icon": {"visible": true, "image_reference": "image_reference"}, "send_button": {"image_reference": "image_reference"}}, "chat_actions_menu": {"photo_library_icon": {"visible": true, "image_reference": "image_reference"}, "camera_icon": {"visible": true, "image_reference": "image_reference"}, "cobrowse_icon": {"visible": true, "image_reference": "image_reference"}}, "content_card": {"background_color_reference": "custom_color_11", "corner_radius": 6, "font": {"color_reference": "custom_color_5", "style": "bold|italic", "family": "Roboto-Black.ttf"}, "border": {"color_reference": "custom_color_12", "width": 1}, "title": {"font": {"color_reference": "custom_color_3", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 20}}, "subtitle": {"font": {"color_reference": "custom_color_4", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 16}}, "body": {"font": {"color_reference": "custom_color_5", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 14}}, "image": {"height": 94}, "primary_button": {"background_color_reference": "custom_color_13", "corner_radius": 16, "border": {"color_reference": "custom_color_13", "width": 2}, "font": {"color_reference": "custom_color_14", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 16}}, "secondary_button": {"background_color_reference": "custom_color_14", "corner_radius": 16, "border": {"color_reference": "custom_color_13", "width": 2}, "font": {"color_reference": "custom_color_13", "style": "bold|italic", "family": "<PERSON><PERSON>", "size": 16}}}, "form_card": {"background_color_reference": "custom_color_11", "corner_radius": 6, "font": {"color_reference": "custom_color_5", "style": "bold|italic", "family": "Roboto-Black.ttf"}, "border": {"color_reference": "custom_color_12", "width": 1}, "title": {"font": {"color_reference": "custom_color_3", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 20}}, "subtitle": {"font": {"color_reference": "custom_color_4", "style": "bold|italic", "family": "Roboto-Black.ttf", "size": 16}}, "image": {"height": 94}}, "post_session": {"background_color_reference": "custom_color_11", "border": {"color_reference": "custom_color_13", "width": 2}}}}