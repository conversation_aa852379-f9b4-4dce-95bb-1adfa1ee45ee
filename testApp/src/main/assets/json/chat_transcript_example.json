{"greeting_override": "Please hold while we connect you with a human agent.", "agent": {"name": "Solvvy", "avatar": "http://solvvy.com/wp-content/uploads/2020/09/solvvy-web-header.svg"}, "transcript": [{"sender": "agent", "timestamp": "2021-03-15 12:00:00Z", "content": [{"type": "text", "text": "[Solvvy launched]"}]}, {"sender": "agent", "timestamp": "2021-03-15 12:00:15Z", "content": [{"type": "text", "text": "**Suggestions shown:**\n\n* Help with batch or delivery\n* Help with metrics or order feedback\n* Help with Instant Cashout"}]}, {"sender": "end_user", "timestamp": "2021-03-15 12:00:16Z", "content": [{"type": "text", "text": "Help with batch or delivery"}]}, {"sender": "agent", "timestamp": "2021-03-15 12:00:18Z", "content": [{"type": "text", "text": "*What do you need help with?*\n\n* Issue with batch selection\n* App freezing during delivery\n* Customer can't be reached for delivery\n* ID issue during alcohol delivery"}]}, {"sender": "end_user", "timestamp": "2021-03-15 12:00:19Z", "content": [{"type": "text", "text": "Issue with batch selection"}]}, {"sender": "agent", "timestamp": "2021-03-15 12:00:20Z", "content": [{"type": "text", "text": "How can I help you today?\n\n* The app freezes when I try to start a batch\n* A batch is stuck on my dashboard\n* Is there an app outage"}]}, {"sender": "end_user", "timestamp": "2021-03-15 12:00:21Z", "content": [{"type": "text", "text": "A batch is stuck on my dashboard"}]}, {"sender": "agent", "timestamp": "2021-03-15 12:00:21Z", "content": [{"type": "text", "text": "Try wiping your batch state  \nIf there's an inactive batch on your Dashboard, you can safely ignore it.  The batch shouldn't interfere with receiving or completing active batches.\n\nIf the inactive batch interferes with your current batch, use the in-app Wipe batch state feature to reset your batch state--\n\n1. Tap Settings to go to the Instacart Shopper app.\n2. Under the App section, tap Wipe batch state.\n3. Tap Confirm if you want re-download all of your batches from the server.\n\nNote: A Batch state wiped message will appear once complete.\n * Settings\n\nDid this resolve your issue?\n\n* This worked, thanks!\n* This didn't work"}]}, {"sender": "end_user", "timestamp": "2021-03-15 12:00:22Z", "content": [{"type": "text", "text": "That didn't work"}]}]}