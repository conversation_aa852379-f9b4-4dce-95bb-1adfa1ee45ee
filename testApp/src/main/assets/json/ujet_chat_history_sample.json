{"pagination": {"per_page": 1, "next_page": 3}, "chats": [{"comm_type": "chat", "comm_id": 2, "transcript_version": "1.0", "assigned_at": "2022-12-31T08:58:04.000-07:00", "timezone": "America/Los_Angeles", "entries": [{"timestamp": 1672521500, "body": {"content": "Hi, @{<PERSON><PERSON>_USER}. Sorry to hear about the issue you are experiencing. Let’s take a few steps together to determine how to fix it as soon as possible.", "type": "text_template"}, "role": "system", "user_data": {}}, {"timestamp": 1672521600, "body": {"content": "test", "type": "text"}, "role": "end_user", "user_data": {}}, {"timestamp": 1672521700, "body": {"content": "message", "type": "text"}, "role": "agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/239/239735.png"}}, {"timestamp": 1672521800, "body": {"document": {"url": "https://MyShoppingList.doc", "text": "text"}, "type": "document"}, "role": "agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/239/239735.png"}}, {"timestamp": 1672521900, "body": {"type": "content_card", "cards": [{"title": "Title 1", "subtitle": "Subtitle 1", "body": "This card has subtitle 1", "link": "ujetqa://contentCard/parameters", "image": "https://avatarfiles.alphacoders.com/239/239735.png"}, {"title": "Title 2", "subtitle": "Subtitle 2", "body": "This card has subtitle 2", "link": "ujetqa://contentCard/parameters", "image": "https://avatarfiles.alphacoders.com/239/239735.png"}, {"title": "Title 3", "subtitle": "Subtitle 3", "body": "This card has subtitle 3", "link": "ujetqa://contentCard/parameters", "image": "https://avatarfiles.alphacoders.com/239/239735.png"}]}, "role": "system", "user_data": {}}, {"timestamp": 1672522000, "body": {"event": "chatEnded", "type": "noti"}, "role": "system", "user_data": {}}, {"timestamp": 1672522000, "body": {"event": "memberLeft", "memberName": "Admin U.", "type": "noti"}, "role": "system", "user_data": {}}]}, {"comm_type": "chat", "comm_id": 3, "transcript_version": "1.0", "assigned_at": "2023-01-01T00:00:00.000-00:00", "timezone": "America/Los_Angeles", "entries": [{"timestamp": 1672631200, "body": {"content": "Hi, @{<PERSON><PERSON>_USER}. Sorry to hear about the issue you are experiencing. Let’s take a few steps together to determine how to fix it as soon as possible.", "type": "text_template"}, "role": "system", "user_data": {}}, {"timestamp": 1672631300, "body": {"content": "message", "type": "text"}, "role": "virtual_agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/239/239735.png"}}, {"timestamp": 1672631400, "body": {"document": {"url": "https://MyShoppingList.doc", "text": "text"}, "type": "document"}, "role": "end_user", "user_data": {}}, {"timestamp": 1672631600, "body": {"document": {"url": "https://MyShoppingList.doc", "text": "text"}, "type": "document"}, "role": "virtual_agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/239/239735.png"}}, {"timestamp": 1672631700, "body": {"event": "chatEnded", "type": "noti"}, "role": "system", "user_data": {}}, {"timestamp": 1672631800, "body": {"event": "memberLeft", "memberName": "Admin U.", "type": "noti"}, "role": "system", "user_data": {}}]}, {"comm_type": "chat", "comm_id": 4, "transcript_version": "1.0", "assigned_at": "2023-09-27T08:58:04.000-07:00", "timezone": "America/Los_Angeles", "entries": [{"timestamp": 1695815440, "body": {"content": "Hi, @{<PERSON><PERSON>_USER}. Sorry to hear about the issue you are experiencing. Let’s take a few steps together to determine how to fix it as soon as possible.", "type": "text_template"}, "role": "system", "user_data": {}}, {"timestamp": 1695815550, "body": {"content": "test", "type": "text"}, "role": "end_user", "user_data": {}}, {"timestamp": 1695815600, "body": {"content": "message", "type": "text"}, "role": "agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/239/239735.png"}}, {"timestamp": 1695815700, "body": {"event": "chatEnded", "type": "noti"}, "role": "system", "user_data": {}}, {"timestamp": 1695815700, "body": {"event": "memberLeft", "memberName": "Admin U.", "type": "noti"}, "role": "system", "user_data": {}}]}, {"comm_type": "chat", "comm_id": 5, "transcript_version": "1.0", "assigned_at": "2023-09-27T08:58:04.000-07:00", "timezone": "America/Los_Angeles", "entries": [{"timestamp": 1695865000, "body": {"content": "Hi, @{<PERSON><PERSON>_USER}. Sorry to hear about the issue you are experiencing. Let’s take a few steps together to determine how to fix it as soon as possible.", "type": "text_template"}, "role": "system", "user_data": {}}, {"timestamp": 1695865100, "body": {"content": "test", "type": "text"}, "role": "end_user", "user_data": {}}, {"timestamp": 1695865200, "body": {"content": "message", "type": "text"}, "role": "agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/160/160477.png"}}, {"timestamp": 1695865300, "body": {"event": "chatEnded", "type": "noti"}, "role": "system", "user_data": {}}, {"timestamp": 1695865400, "body": {"event": "memberLeft", "memberName": "Admin U.", "type": "noti"}, "role": "system", "user_data": {}}]}, {"comm_type": "chat", "comm_id": 6, "transcript_version": "1.0", "assigned_at": "2023-10-20T08:58:04.000-07:00", "timezone": "America/Los_Angeles", "entries": [{"timestamp": 1697817469, "body": {"content": "Hi, @{<PERSON><PERSON>_USER}. Sorry to hear about the issue you are experiencing. Let’s take a few steps together to determine how to fix it as soon as possible.", "type": "text_template"}, "role": "system", "user_data": {}}, {"timestamp": 1697817492, "body": {"content": "test", "type": "text"}, "role": "end_user", "user_data": {}}, {"timestamp": 1697817497, "body": {"content": "message", "type": "text"}, "role": "agent", "user_data": {"name": "Admin U.", "id": 1, "avatar_url": "https://avatarfiles.alphacoders.com/427/42731.jpg"}}, {"timestamp": 1697817540, "body": {"type": "photo", "media_id": "1"}, "role": "end_user", "user_data": {}}, {"timestamp": 1697817640, "body": {"type": "video", "media_id": "4"}, "role": "end_user", "user_data": {}}, {"timestamp": 1697817740, "body": {"event": "chatEnded", "type": "noti"}, "role": "system", "user_data": {}}, {"timestamp": 1697817740, "body": {"event": "memberLeft", "memberName": "Admin U.", "type": "noti"}, "role": "system", "user_data": {}}]}]}