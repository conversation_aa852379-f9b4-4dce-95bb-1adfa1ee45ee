package co.ujet.ujetandroidsdk;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import java.util.HashMap;
import java.util.Map;

import co.ujet.android.Ujet;
import co.ujet.android.UjetStartOptions;

/**
 * For testing the push notification without a server.
 * <p>
 * Use this command to send a broadcast to this receiver
 * adb shell am broadcast -a co.ujet.android.push_test -n co.ujet.localqa.debug/co.ujet.ujetandroidsdk.PushTestBroadcastReceiver --es type "type"
 * <p>
 * For example, testing a scheduled call on LocalQa Test App for Pro SDK
 * adb shell am broadcast -a co.ujet.android.push_test -n co.ujet.localqa.debug/co.ujet.ujetandroidsdk.PushTestBroadcastReceiver --es type "scheduled_call"
 */
public class PushTestBroadcastReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        String type = intent.getStringExtra("type");
        if (type == null)
            return;

        switch (type) {
            case "start":
                startUjet();
                break;

            case "scheduled_call":
                handleScheduledCall();
                break;
        }
    }

    private void handleScheduledCall() {
        Map<String, String> pushEventData = new HashMap<>();
        pushEventData.put("call_id", String.valueOf(0x7fffffff));
        pushEventData.put("ujet_noti_type", "connect_call");
        pushEventData.put("call_type", "ScheduledCall");
        pushEventData.put("fail_reason", "none");
        pushEventData.put("fail_details", "none");

        Ujet.canHandlePush(pushEventData);
    }

    private void startUjet() {
        Ujet.start(new UjetStartOptions.Builder().build());
    }
}
