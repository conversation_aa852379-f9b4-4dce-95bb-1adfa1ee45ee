package co.ujet.ujetandroidsdk

import android.content.Context
import androidx.fragment.app.DialogFragment
import co.ujet.android.CoBrowseAlertProvider

/**
 * Customer will implement this to show their own co-browse consent dialog UI.
 */
class CoBrowseAlertProviderImpl : CoBrowseAlertProvider() {
    override fun showCoBrowseSessionActivationRequestDialog(context: Context,
                                                            dialogInstance: (DialogFragment) -> Unit,
                                                            consentStatus: (Boolean) -> Unit) {
        val dialog =
            ConsentDialog.newInstance(
                title = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_request_alert_title),
                message = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_request_alert_message),
                positiveButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_request_alert_button_allow),
                negativeButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_request_alert_button_deny),
                cancelable = false,
                onNegativeClick = consentStatus,
                onPositiveClick = consentStatus
            )
        dialogInstance(dialog)
    }

    override fun showCoBrowseSessionRemoteControlRequestDialog(context: Context,
                                                               dialogInstance: (DialogFragment) -> Unit,
                                                               consentStatus: (Boolean) -> Unit) {
        val dialog =
            ConsentDialog.newInstance(
                title = context.getString(co.ujet.android.R.string.ujet_cobrowse_remote_access_request_alert_title),
                message = context.getString(co.ujet.android.R.string.ujet_cobrowse_remote_access_request_alert_message),
                positiveButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_remote_access_request_alert_button_allow),
                negativeButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_remote_access_request_alert_button_deny),
                cancelable = false,
                onNegativeClick = consentStatus,
                onPositiveClick = consentStatus
            )
        dialogInstance(dialog)
    }

    override fun showCoBrowseSessionFullDeviceRequestDialog(context: Context,
                                                            dialogInstance: (DialogFragment) -> Unit,
                                                            consentStatus: (Boolean) -> Unit) {
        val dialog =
            ConsentDialog.newInstance(
                title = context.getString(co.ujet.android.R.string.ujet_cobrowse_screen_share_request_alert_title),
                message = context.getString(co.ujet.android.R.string.ujet_cobrowse_screen_share_request_alert_message),
                positiveButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_screen_share_request_alert_button_allow),
                negativeButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_screen_share_request_alert_button_deny),
                cancelable = false,
                onNegativeClick = consentStatus,
                onPositiveClick = consentStatus
            )
        dialogInstance(dialog)
    }

    override fun showCoBrowseConfirmationDialog(context: Context, dialogInstance: (DialogFragment) -> Unit,
                                                consentStatus: (Boolean) -> Unit) {
        val dialog =
            ConsentDialog.newInstance(
                title = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_initiate_alert_title),
                message = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_initiate_alert_message),
                positiveButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_initiate_alert_button_yes),
                negativeButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_initiate_alert_button_no),
                onNegativeClick = consentStatus,
                onPositiveClick = consentStatus
            )
        dialogInstance(dialog)
    }

    override fun showStopCoBrowseConfirmationDialog(context: Context,
                                                    dialogInstance: (DialogFragment) -> Unit,
                                                    consentStatus: (Boolean) -> Unit) {
        val dialog =
            ConsentDialog.newInstance(
                title = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_end_alert_title),
                message = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_end_alert_message),
                positiveButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_end_alert_button_yes),
                negativeButton = context.getString(co.ujet.android.R.string.ujet_cobrowse_session_end_alert_button_no),
                onNegativeClick = consentStatus,
                onPositiveClick = consentStatus
            )
        dialogInstance(dialog)
    }


}
