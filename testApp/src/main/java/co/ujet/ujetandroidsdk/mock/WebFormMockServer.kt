package co.ujet.ujetandroidsdk.mock


import com.google.gson.annotations.SerializedName
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * Provides a Retrofit-based API service to fetch "formData" from a mock server for "Web Form" feature.
 * Calls the "form/uri" API and uses a singleton instance with lazy initialization and logging.
 */
object WebFormMockServer {
    interface FormApiService {
        @POST("form/uri")
        suspend fun fetchFormData(@Body request: FormDataRequest): Response<FormDataResponse>
    }

    private val retrofit by lazy {
        Retrofit.Builder()
            .baseUrl("https://form-demo-24b4f.web.app/")
            .client(
                OkHttpClient.Builder()
                    .addInterceptor(HttpLoggingInterceptor().apply { level = HttpLoggingInterceptor.Level.BODY })
                    .build()
            )
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    val formApiService: FormApiService by lazy { retrofit.create(FormApiService::class.java) }
}

data class FormDataRequest(
    @SerializedName("external_form_id")
    val externalFormId: String,
    @SerializedName("smart_action_id")
    val smartActionId: Int
)

data class FormDataResponse(
    @SerializedName("type")
    val type: String,
    @SerializedName("signature")
    val signature: String,
    @SerializedName("data")
    val data: FormData
)

data class FormData(
    @SerializedName("smart_action_id")
    val smartActionId: Int,
    @SerializedName("external_form_id")
    val externalFormId: String,
    @SerializedName("uri")
    val uri: String
)
