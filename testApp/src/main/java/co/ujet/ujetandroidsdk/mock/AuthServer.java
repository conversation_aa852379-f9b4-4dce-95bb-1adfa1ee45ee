package co.ujet.ujetandroidsdk.mock;

import android.os.Handler;
import android.os.Looper;

import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import co.ujet.android.UjetTokenCallback;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * Created by mimu on 4/26/17.
 */

public class AuthServer {
    private int delayMilliseconds;
    private Timer authTimer;
    private Timer customDataTimer;
    private String companySecret;

    public AuthServer(String companySecret, int delayMilliseconds) {
        this.companySecret = companySecret;
        this.delayMilliseconds = delayMilliseconds;
    }

    public void signAuthToken(final Map<String, Object> payload, final UjetTokenCallback callback) {
        if (authTimer != null) {
            return;
        }

        authTimer = new Timer();
        authTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                final String token = Jwts.builder().setClaims(payload).signWith(SignatureAlgorithm.HS256, companySecret.getBytes()).compact();
                authTimer.cancel();
                authTimer = null;

                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        callback.onToken(token);
                    }});
            }
        }, delayMilliseconds);
    }

    public void signCustomData(final Map<String, Object> payload, final UjetTokenCallback callback) {
        if (customDataTimer != null) {
            return;
        }

        customDataTimer = new Timer();
        customDataTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                final String token = Jwts.builder().setClaims(payload).signWith(SignatureAlgorithm.HS256, companySecret.getBytes()).compact();
                customDataTimer.cancel();
                customDataTimer = null;

                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        callback.onToken(token);
                    }});
            }
        }, delayMilliseconds);
    }
}
