package co.ujet.ujetandroidsdk

import android.app.Dialog
import android.os.Bundle
import android.widget.TextView
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import co.ujet.android.ui.button.FancyButton
import java.io.Serializable

internal class ConsentDialog : DialogFragment() {
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        isCancelable = arguments?.getBoolean(ARG_CANCELABLE) ?: true
        return android.app.AlertDialog.Builder(activity).apply {
            val view = layoutInflater.inflate(R.layout.custom_alert_dialog_layout, null)
            val positiveButton = view.findViewById<FancyButton>(R.id.positive_button)
            val negativeButton = view.findViewById<FancyButton>(R.id.negative_button)
            setView(view)
            arguments?.getString(ARG_TITLE)?.let {
                view.findViewById<TextView>(R.id.title).apply {
                    text = it
                }
            }
            arguments?.getString(ARG_MESSAGE)?.let {
                view.findViewById<TextView>(R.id.message_body).apply {
                    text = it
                }
            }
            arguments?.getString(ARG_POSITIVE_BUTTON)?.let {
                positiveButton.apply {
                    text = it
                    setOnClickListener {
                        val consentStatus = arguments?.getSerializable(ARG_ON_POSITIVE_CLICK) as (Boolean) -> Unit
                        consentStatus(true)
                        dismiss()
                    }
                }
            }

            arguments?.getString(ARG_NEGATIVE_BUTTON)?.let {
                negativeButton.apply {
                    text = it
                    setOnClickListener {
                        val consentStatus = arguments?.getSerializable(ARG_ON_NEGATIVE_CLICK) as (Boolean) -> Unit
                        consentStatus(false)
                        dismiss()
                    }
                }
            }
        }.create()

    }

    companion object {
        const val ARG_REQUEST_KEY = "ARG_REQUEST_KEY"
        private const val ARG_TITLE = "ARG_TITLE"
        private const val ARG_MESSAGE = "ARG_MESSAGE"
        private const val ARG_POSITIVE_BUTTON = "ARG_POSITIVE_BUTTON"
        private const val ARG_NEGATIVE_BUTTON = "ARG_NEGATIVE_BUTTON"
        private const val ARG_CANCELABLE = "ARG_CANCELABLE"
        private const val ARG_ON_POSITIVE_CLICK = "ARG_ON_POSITIVE_CLICK"
        private const val ARG_ON_NEGATIVE_CLICK = "ARG_ON_NEGATIVE_CLICK"

        fun newInstance(
            fragmentRequestKey: String? = null,
            title: String? = null,
            message: String,
            positiveButton: String? = null,
            negativeButton: String? = null,
            onNegativeClick: (Boolean) -> Unit,
            onPositiveClick: (Boolean) -> Unit,
            cancelable: Boolean = true
        ): ConsentDialog {
            return ConsentDialog().apply {
                arguments = bundleOf(
                    ARG_REQUEST_KEY to fragmentRequestKey,
                    ARG_TITLE to title,
                    ARG_MESSAGE to message,
                    ARG_POSITIVE_BUTTON to positiveButton,
                    ARG_NEGATIVE_BUTTON to negativeButton,
                    ARG_CANCELABLE to cancelable,
                    ARG_ON_POSITIVE_CLICK to onPositiveClick as Serializable,
                    ARG_ON_NEGATIVE_CLICK to onNegativeClick as Serializable
                )
            }
        }
    }
}
