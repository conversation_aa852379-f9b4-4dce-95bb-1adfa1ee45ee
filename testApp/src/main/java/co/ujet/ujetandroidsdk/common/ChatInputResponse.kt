package co.ujet.ujetandroidsdk.common

import android.widget.EditText

class ChatInputResponse(
    var font: FontResponse? = null,
    var backgroundColorEditText: EditText? = null,
    var cornerRadiusEditText: EditText? = null,
    var cursorColorEditText: EditText? = null,
    var border: BorderResponse? = null,
    var topBorder: BorderResponse? = null,
    var placeHolderEditText: EditText? = null,
    var escalateIcon: IconResponse? = null,
    var chatActionsMenuIcon: IconResponse? = null,
    val sendButton: IconResponse? = null
)
