package co.ujet.ujetandroidsdk.common

import co.ujet.android.modulemanager.common.ui.domain.ChatActionsMenuStyle

data class UserResponse(
    var backButton: BackButtonResponse = BackButtonResponse(),
    var endChatButton: EndChatButtonResponse = EndChatButtonResponse(true, FontResponse()),
    var header: HeaderResponse = HeaderResponse(FontResponse()),
    var welcomeMessageTray: WelcomeMessageTrayResponse = WelcomeMessageTrayResponse(true, true, DividerResponse()),
    var systemMessages: SystemMessageResponse = SystemMessageResponse(
        MessageBubbleResponse(
            FontResponse(), null, null,
            BorderResponse()
        ), ButtonResponse(FontResponse(), null, null, BorderResponse())
    ),
    var consumerMessageBubbles: ConsumerMessageResponse = ConsumerMessageResponse(
        MessageBubbleResponse(
            FontResponse(),
            null, null, BorderResponse()
        ), IconResponse()
    ),
    var agentMessageBubbles: AgentMessageResponse = AgentMessageResponse(
        MessageBubbleResponse(
            FontResponse(),
            null, null, BorderResponse()
        ), IconResponse()
    ),
    var timeStamps: TimeStampsResponse = TimeStampsResponse(FontResponse()),
    var chatInputFieldStyle: ChatInputResponse = ChatInputResponse(
        font = FontResponse(),
        border = BorderResponse(),
        topBorder = BorderResponse(),
        escalateIcon = IconResponse(),
        chatActionsMenuIcon = IconResponse()
    ),
    var chatActionsMenuStyle: ChatActionsMenuResponse = ChatActionsMenuResponse(
        cameraIcon = IconResponse(),
        selectPhotoFromLibraryIcon = IconResponse(),
        cobrowseIcon = IconResponse()
    ),
    var contentCard: ContentCardResponse = ContentCardResponse(),
    var webFormCard: WebFormCardResponse = WebFormCardResponse(),
    var postSession: PostSessionResponse = PostSessionResponse()
)
