package co.ujet.ujetandroidsdk.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.hardware.SensorManager;

import com.squareup.seismic.ShakeDetector;

import co.ujet.ujetandroidsdk.activity.SettingsActivity;


/**
 * Created by mimu on 5/7/16.
 */
public class ShakeToSettings implements ShakeDetector.Listener {
    private Activity activity;
    private ShakeDetector sd;

    public ShakeToSettings(Activity activity) {
        this.activity = activity;

        sd = new ShakeDetector(this);
        start();
    }

    public void start() {
        SensorManager sensorManager = (SensorManager) activity.getApplicationContext().getSystemService(Context.SENSOR_SERVICE);
        sd.setSensitivity(ShakeDetector.SENSITIVITY_MEDIUM);
        //Reference from https://github.com/square/seismic/issues/24 to support Android 12 phones
        sd.start(sensorManager, SensorManager.SENSOR_DELAY_GAME);
    }

    public void stop() {
        sd.stop();
    }

    public void launchSettingActivity() {
        Intent intent = new Intent(activity, SettingsActivity.class);
        activity.startActivityForResult(intent, SettingsActivity.REQUEST_FOR_SETTING);
    }

    public void clear() {
        activity = null;
    }

    @Override
    public void hearShake() {
        sd.stop(); // Prevent detect twice
        launchSettingActivity();
    }
}
