package co.ujet.ujetandroidsdk.common

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.drawable.StateListDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.android.ui.util.DesignUtil.getColor
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings
import com.joanzapata.iconify.IconDrawable
import com.joanzapata.iconify.fonts.FontAwesomeIcons

class CustomDataListAdapter internal constructor(context: Context, private val customDataType: CustomDataType,
                                                 customDataList: List<CustomData>
) : ArrayAdapter<CustomData?>(context, 0, ArrayList(customDataList)) {

    private var onClickListener: OnClickListener? = null
    private var eyeSlashIconList = mutableListOf<ImageView>()

    fun setOnClickListener(onClickListener: OnClickListener) {
        this.onClickListener = onClickListener
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        return getCustomDataView(position, getItem(position), convertView, parent)
    }

    private fun getCustomDataView(
        position: Int,
        customData: CustomData?,
        convertView: View?,
        parent: ViewGroup
    ): View {
        val resultView = convertView ?: run {
            LayoutInflater.from(context).inflate(R.layout.custom_data_list_item, parent, false)
        }
        resultView.findViewById<View>(R.id.root_view).apply {
            this.setBackgroundColor(getUjetStyle(context).primaryBackgroundColor)
        }
        resultView.findViewById<View>(R.id.list_view_layout).apply {
            setOnClickListener {
                setEyeSlashIconVisibility(position)
            }
        }
        val eyeSlashIcon = resultView.findViewById<ImageView>(R.id.eye_slash_icon)
        eyeSlashIcon?.setImageDrawable(IconDrawable(context, FontAwesomeIcons.fa_eye_slash))
        eyeSlashIcon?.setColorFilter(getColor(context, co.ujet.android.ui.R.color.ujet_primary))
        // Set visibility from cached unsigned custom data
        eyeSlashIcon?.visibility = if (customData?.invisibleToAgent == true) {
            VISIBLE
        } else {
            GONE
        }
        // Avoid duplicates
        if (position >= eyeSlashIconList.size) {
            eyeSlashIconList.add(position, eyeSlashIcon)
        }
        resultView.findViewById<TextView>(R.id.custom_data_key)?.apply {
            text = "Key: ${customData?.key}"
            UjetViewStyler.stylePrimaryText(getUjetStyle(context), this)
        }
        resultView.findViewById<TextView>(R.id.custom_data_label)?.apply {
            text = "Label: ${customData?.label}"
            UjetViewStyler.stylePrimaryText(getUjetStyle(context), this)
        }
        resultView.findViewById<TextView>(R.id.custom_data_type)?.apply {
            text = "Type: ${customData?.type}"
            UjetViewStyler.stylePrimaryText(getUjetStyle(context), this)
        }
        resultView.findViewById<TextView>(R.id.custom_data_value)?.apply {
            text = "Value: ${customData?.value}"
            UjetViewStyler.stylePrimaryText(getUjetStyle(context), this)
        }
        resultView.findViewById<ImageButton>(R.id.remove_button)?.apply {
            visibility = if (customDataType == CustomDataType.Signed) {
                GONE
            } else {
                VISIBLE
            }
            background = getButtonDrawable(resultView.context)
            setImageResource(co.ujet.android.ui.R.drawable.ujet_ic_close_gray)
            setColorFilter(ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_red), PorterDuff.Mode.MULTIPLY)
            setOnClickListener {
                onClickListener?.onClick(position)
            }
        }
        return resultView
    }

    private fun setEyeSlashIconVisibility(position: Int) {
        val eyeSlashIcon = eyeSlashIconList[position]
        val invisibleToAgent = if (eyeSlashIcon.isVisible) {
            eyeSlashIcon.visibility = GONE
            false
        } else {
            eyeSlashIcon.visibility = VISIBLE
            true
        }
        eyeSlashIconList[position] = eyeSlashIcon
        val currentCustomData = getItem(position)
        currentCustomData?.let { data ->
            data.invisibleToAgent = invisibleToAgent
            if (customDataType == CustomDataType.Unsigned) {
                val updatedCustomData = TestAppSettings.customUnsignedData
                updatedCustomData?.set(position, data)
                TestAppSettings.customUnsignedData = updatedCustomData
            } else if (customDataType == CustomDataType.Signed) {
                val updatedCustomData = TestAppSettings.customSignedData
                updatedCustomData?.set(position, data)
                TestAppSettings.customSignedData = updatedCustomData
            }
        }
    }

    private fun getButtonDrawable(context: Context): StateListDrawable {
        val backgroundColor = getUjetStyle(context).primaryBackgroundColor
        val primaryColor = getUjetStyle(context).colorPrimary
        val strokePx = context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
        val cornerRadiusPx = DesignUtil.dpToPx(context, getUjetStyle(context).buttonRadius).toInt()
        val normalDrawable = DesignUtil.createRoundedRectangleDrawable(backgroundColor, primaryColor, strokePx, cornerRadiusPx.toFloat())
        val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(primaryColor, primaryColor, strokePx, cornerRadiusPx.toFloat())
        return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
    }

    private fun getUjetStyle(context: Context) = Injection.provideUjetStyle(context)

    interface OnClickListener {
        fun onClick(position: Int)
    }
}
