package co.ujet.ujetandroidsdk.common

data class CustomDataParams(
    // Enable signed custom data by default to match with iOS
    var isSignedCustomDataEnabled: Boolean = true,
    var isUnsignedCustomDataEnabled: Boolean = false,
    var isReservedDataConfigEnabled: <PERSON>olean = false,
    var isCustomerVerified: <PERSON>olean = false,
    var isCustomerBadActor: <PERSON>olean = false,
    var isCustomerRepeated: Boolean = false,
)

enum class CustomDataType {
    None,
    Signed,
    Unsigned,
    SignedAndUnsigned
}
