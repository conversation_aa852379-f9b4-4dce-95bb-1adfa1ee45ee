package co.ujet.ujetandroidsdk.common

import android.widget.EditText

class ContentCardResponse(
    var backgroundColorEditText: EditText? = null,
    var cornerRadiusEditText: EditText? = null,
    var font: FontResponse? = FontResponse(),
    var border: BorderResponse? = BorderResponse(),
    var title: ContentCardTitleResponse? = ContentCardTitleResponse(),
    var subtitle: ContentCardSubtitleResponse? = ContentCardSubtitleResponse(),
    var body: ContentCardBodyResponse? = ContentCardBodyResponse(),
    var image: ContentCardImageResponse? = ContentCardImageResponse(),
    var primaryButton: ContentCardButtonResponse? = ContentCardButtonResponse(),
    var secondaryButton: ContentCardButtonResponse? = ContentCardButtonResponse()
)
