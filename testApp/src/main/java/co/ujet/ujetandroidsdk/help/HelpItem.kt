package co.ujet.ujetandroidsdk.help

class HelpItem {
    @JvmField
    var title: String?
    @JvmField
    var image: String?
    var type: Int

    constructor(title: String?, image: String?) {
        this.title = title
        this.image = image
        type = HELP_ITEM_TYPE_NONE
    }

    constructor(title: String?, image: String?, type: Int) {
        this.title = title
        this.image = image
        this.type = type
    }

    companion object {
        const val HELP_ITEM_TYPE_NONE = 10001
        const val HELP_ITEM_TYPE_LAUNCH = 10002
        const val HELP_ITEM_TYPE_LAUNCH_WITH_KEY = 10003
        const val HELP_ITEM_TYPE_LAUNCH_WITH_IVR_PHONE_NUMBER = 10004
        const val HELP_ITEM_TYPE_LAUNCH_WITHOUT_SPLASH = 10005
        const val HELP_ITEM_CHANGE_DEFAULT_LANGAUGE = 20001
        const val HELP_ITEM_CLEAR_USER_PREFERRED_LANGUAGE = 20002
        const val HELP_ITEM_CLEAR_DEFAULT_LANGUAGE = 20003
        const val HELP_ITEM_CLEAR_DEVICE_UUID = 20004
        const val HELP_ITEM_SET_SIGN_DELAY = 30001
        const val HELP_ITEM_RESET_SIGN_DELAY = 30002
        const val HELP_ITEM_FCM_KEY = 9
        const val HELP_ITEM_CONFIGURE_TICKET_ID = 1000
        const val HELP_ITEM_CHAT_BUBBLES_TEST = 1001
        const val HELP_ITEM_CHAT_TRANSCRIPT_EXAMPLE_JSON = 1002
        const val HELP_ITEM_MARKDOWN_TEST = 1003
        const val HELP_ITEM_SET_FALLBACK_PHONE_NUMBER = 1006
        const val HELP_ITEM_CONFIGURE_PREFERRED_CHANNEL = 1100
        const val HELP_ITEM_SURVEY_PAGE_TEST = 1011
        const val HELP_ITEM_STYLE_OPTIONS = 1012
        const val HELP_ITEM_CUSTOM_COBROWSE_LICENSE_KEY = 1013
        const val HELP_ITEM_CUSTOM_COBROWSE_URL = 1014
        const val HELP_ITEM_CONFIGURE_CUSTOM_DATA = 1015
    }
}
