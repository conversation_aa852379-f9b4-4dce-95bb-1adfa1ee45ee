package co.ujet.ujetandroidsdk.help;

import java.util.List;

import android.content.Context;
import android.content.res.Configuration;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import co.ujet.ujetandroidsdk.R;

/**
 * Created by mimu on 5/3/16.
 */
public class HelpListAdapter extends ArrayAdapter<HelpItem> {
    private Context context;

    List<HelpItem> items;

    public HelpListAdapter(Context context, int resource, List<HelpItem> objects) {
        super(context, resource);
        this.context = context;
        items = objects;
    }

    public int getCount() {
        return this.items.size();
    }

    public HelpItem getItem(int index) {
        return this.items.get(index);
    }

    public View getView(int position, View convertView, ViewGroup parent) {
        View row = convertView;
        if (row == null) {
            LayoutInflater inflater = (LayoutInflater) this.getContext()
                    .getApplicationContext()
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            row = inflater.inflate(R.layout.help_list_item, parent, false);
        }

        ImageView icon = row.findViewById(R.id.icon);
        TextView title = row.findViewById(R.id.title);

        HelpItem item = getItem(position);
        if (item == null) {
            return row;
        }

        int resId = 0;
        if (item.image != null) {
            resId = this.context.getResources().getIdentifier(item.image, "mipmap", this.context.getPackageName());
        }

        if (resId != 0) {
            icon.setImageResource(resId);
        } else {
            icon.setImageDrawable(null);
        }

        title.setText(item.title);
        setTitleTextColor(title);

        return row;
    }

    private void setTitleTextColor(TextView title){
        Configuration configuration = context.getResources().getConfiguration();

        int currentNightMode = configuration.uiMode & Configuration.UI_MODE_NIGHT_MASK;
        switch (currentNightMode) {
            case Configuration.UI_MODE_NIGHT_NO:
                // Night mode is not active, we're using the light theme
                title.setTextColor(ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_black));
                break;
            case Configuration.UI_MODE_NIGHT_YES:
                // Night mode is active, we're using dark theme
                title.setTextColor(ContextCompat.getColor(context, co.ujet.android.ui.R.color.ujet_white));
                break;
        }
    }
}
