package co.ujet.ujetandroidsdk

import android.content.*
import android.util.Log
import co.ujet.ujetandroidsdk.util.EnvironmentUtil.Companion.load
import co.ujet.ujetandroidsdk.UjetInitializer.setup
import co.ujet.ujetandroidsdk.UjetInitializer.init
import co.ujet.ujetandroidsdk.TestAppSettings.isEventDataNotificationEnabled
import co.ujet.ujetandroidsdk.TestAppSettings.setEventData
import co.ujet.ujetandroidsdk.TestAppSettings.companySecret
import co.ujet.ujetandroidsdk.TestAppSettings.getSignDelay
import androidx.multidex.MultiDexApplication
import co.ujet.android.UjetRequestListener
import co.ujet.ujetandroidsdk.fcm.FcmTokenManager
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.FirebaseCrashlytics
import co.ujet.android.Ujet
import com.joanzapata.iconify.Iconify
import com.joanzapata.iconify.fonts.FontAwesomeModule
import co.ujet.android.UjetPayloadType
import co.ujet.android.UjetTokenCallback
import co.ujet.ujetandroidsdk.common.CustomDataType
import co.ujet.ujetandroidsdk.mock.AuthServer
import co.ujet.ujetandroidsdk.util.CustomDataHelper
import co.ujet.ujetandroidsdk.common.EventData
import java.util.*

class Application : MultiDexApplication(), UjetRequestListener {
    var batteryLevel = 0
        private set
    private var fcmTokenManager: FcmTokenManager? = null
    private var preferences: SharedPreferences? = null
    override fun onCreate() {
        super.onCreate()
        preferences = applicationContext.getSharedPreferences(
            TestAppSettings.TEST_APP_PREFERENCES_NAME,
            MODE_PRIVATE
        )
        load(this)
        Log.d("UJET", "Application.onCreate")
        this.registerReceiver(batteryLevelReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        FirebaseApp.initializeApp(this)
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)
        setup(this)
        init()
        Ujet.setUjetErrorListener { false }
        Iconify.with(FontAwesomeModule())
        LeakCanaryHelper.setupLeakCanary()
        fcmTokenManager = FcmTokenManager(this)

        //Register Event
        if (isEventDataNotificationEnabled) {
            Ujet.setUjetEventNotificationListener { eventType, eventData ->
                Log.d("UJET", "Event Data:$eventData")
                val eventDataList: ArrayList<EventData?> = TestAppSettings.eventData
                eventDataList.add(EventData(eventType.value, getFormattedData(eventData)))
                setEventData(eventDataList)
            }
        }
    }

    override fun onSignPayloadRequest(payload: MutableMap<String, Any>, payloadType: UjetPayloadType,
        callback: UjetTokenCallback) {
        val authServer = AuthServer(companySecret, getSignDelay())
        if (payloadType == UjetPayloadType.AuthToken) {
            setCommonPayload(payload)
            setAuthTokenPayload(payload)
            authServer.signAuthToken(payload, callback)
        } else if (payloadType == UjetPayloadType.CustomData) {
            val cachedCustomData = TestAppSettings.customDataParams
            // Return with null token when signed custom data type disabled
            if (!cachedCustomData.isSignedCustomDataEnabled) {
                callback.onToken(null)
                return
            }
            setCommonPayload(payload)
            setCustomDataPayload(payload)
            authServer.signCustomData(payload, callback)
        }
    }

    override fun onRequestPushToken(): String? {
        return fcmTokenManager?.token
    }

    private fun setAuthTokenPayload(payload: MutableMap<String, Any>) {
        val identifier = TestAppSettings.identifier
        if (identifier?.isNotEmpty() == true) {
            payload["identifier"] = identifier
        }
        val email = TestAppSettings.email
        if (email?.isNotEmpty() == true) {
            payload["email"] = email
        }
        val name = TestAppSettings.name
        if (name?.isNotEmpty() == true) {
            payload["name"] = name
        }
        val phoneNumber = TestAppSettings.phoneNumber
        if (phoneNumber?.isNotEmpty() == true) {
            payload["phone"] = phoneNumber
        }
    }

    private fun setCustomDataPayload(payload: MutableMap<String, Any>) {
        val data = CustomDataHelper.getSignedCustomData(this)
        payload["custom_data"] = data.getData()
    }

    private fun setCommonPayload(payload: MutableMap<String, Any>) {
        val now = Date().time / 1000L
        payload["iss"] = "ujet"
        // This value should be inserted by customer's server on production
        payload["iat"] = now
        // Expiration is 10 min. This value should be inserted by customer's server on production.
        payload["exp"] = now + 60 * 10
    }

    override fun onTerminate() {
        super.onTerminate()
        Log.d("UJET", "Application.onTerminate")
    }

    private val batteryLevelReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            batteryLevel = intent.getIntExtra("level", 0)
        }
    }

    private fun getFormattedData(eventData: HashMap<String, Any>) = eventData.map { "${it.key} : ${it.value}" }.joinToString("\n")
}
