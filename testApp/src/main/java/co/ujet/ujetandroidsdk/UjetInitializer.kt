package co.ujet.ujetandroidsdk

import android.content.Context
import android.util.Log
import co.ujet.android.Ujet
import co.ujet.android.UjetOption
import co.ujet.android.UjetOption.Builder
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.UjetModule
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions.Companion.QuickReplyButtonsStyle
import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory
import co.ujet.android.modulemanager.entrypoints.cobrowse.Cobrowse
import java.lang.reflect.Field

object UjetInitializer {
    private lateinit var application: Application

    @JvmStatic
    fun setup(application: Application) {
        UjetInitializer.application = application
    }

    @JvmStatic
    fun init() {
        TestAppSettings.init(application)
        if (!TestAppSettings.isIsInitialized) return
        val ujetOption = createUjetOptions("+1 205-555-0123")
        val preferences = application.getSharedPreferences(
            "co.ujet.android.internal.config", Context.MODE_PRIVATE
        )
        val editor = preferences.edit()
            .putString("company_key", TestAppSettings.companyKey)
            .putString("company_name", TestAppSettings.companyName)
            .putString("subdomain", TestAppSettings.subdomain)
            .putString("endpoint_url", TestAppSettings.endpointUrl)
        val attrName = String.format("Ujet.%s", TestAppSettings.theme)
        val ujetStyleId = application.resources.getIdentifier(attrName, "style", application.packageName)
        if (ujetStyleId != 0) {
            editor.putInt("ujet_style", ujetStyleId)
        } else {
            editor.remove("ujet_style")
        }
        editor.apply()
        Ujet.init(application, ujetOption)

        // ⚠️ Load modules selectively, HACKS AHEAD ⚠️
        rewriteModulesEntryPoints()
        // ⚠️ Load modules selectively ⚠️
    }

    // ⚠️ Re-write modules entry points, HACKS AHEAD ⚠️
    private fun rewriteModulesEntryPoints() {
        val registeredEntryPointsField = getField(EntryPointFactory::class.java, "registeredEntryPoints") ?: return
        val registeredEntryPoints = registeredEntryPointsField[EntryPointFactory] as? MutableMap<Class<*>, MutableList<Any>> ?: return

        // Cobrowse
        if (!TestAppSettings.isCobrowseEnabled()) {
            val instanceField = getField(Cobrowse.Companion::class.java, "instance\$delegate") ?: return
            setField(instanceField, Cobrowse.Companion, lazy { EntryPointFactory.provideEntryPoint(Cobrowse::class.java) })
            removeEntryPoint(registeredEntryPoints, Cobrowse::class.java, "CobrowseEntryPoint")
            removeEntryPoint(registeredEntryPoints, UjetModule::class.java, "CobrowseUjetModule")
        }
        // Nexmo Chat
        if (!TestAppSettings.isNexmoChatEnabled()) {
            removeEntryPoint(registeredEntryPoints, ChatTransportFactory::class.java, "NexmoChatTransportFactory")
            removeEntryPoint(registeredEntryPoints, UjetModule::class.java, "NexmoChatUjetModule")
        }
        // Nexmo Call
        if (!TestAppSettings.isNexmoCallEnabled()) {
            removeEntryPoint(registeredEntryPoints, CallTransportFactory::class.java, "NexmoCallTransportFactory")
            removeEntryPoint(registeredEntryPoints, UjetModule::class.java, "NexmoCallUjetModule")
        }
        // Twilio Chat
        if (!TestAppSettings.isTwilioChatEnabled()) {
            removeEntryPoint(registeredEntryPoints, ChatTransportFactory::class.java, "TwilioChatTransportFactory")
            removeEntryPoint(registeredEntryPoints, UjetModule::class.java, "TwilioChatUjetModule")
        }
        // Twilio Call
        if (!TestAppSettings.isTwilioCallEnabled()) {
            removeEntryPoint(registeredEntryPoints, CallTransportFactory::class.java, "TwilioCallTransportFactory")
            removeEntryPoint(registeredEntryPoints, UjetModule::class.java, "TwilioCallUjetModule")
        }

        setField(registeredEntryPointsField, EntryPointFactory, registeredEntryPoints)
    }

    // ⚠️ Remove entry points from the EntryPoints Map, HACKS AHEAD ⚠️
    private fun removeEntryPoint(registeredEntryPoints: MutableMap<Class<*>, MutableList<Any>>, clazz: Class<*>, entryPointName: String) {
        registeredEntryPoints[clazz] = registeredEntryPoints[clazz]?.filterNot {
            it.javaClass.simpleName == entryPointName
        }?.toMutableList() ?: return
        Log.i(javaClass.simpleName, "Entry point: $entryPointName removed")
    }

    // ⚠️ Get a field from an object using reflection, HACKS AHEAD ⚠️
    private fun getField(clazz: Class<*>, fieldName: String): Field? {
        try {
            val field = clazz.getDeclaredField(fieldName)
            field.isAccessible = true
            return field
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        }
        return null
    }

    // ⚠️ Overwrite a field value from an object using reflection, HACKS AHEAD ⚠️
    private fun setField(field: Field, obj: Any, value: Any?) {
        try {
            field.set(obj, value)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        }
    }

    fun setOptions(fallbackPhoneNumber: String) {
        Ujet.setOptions(createUjetOptions(fallbackPhoneNumber))
    }

    private fun createUjetOptions(fallbackPhoneNumber: String): UjetOption {
        val builder = Builder()
            .setFallbackPhoneNumber(fallbackPhoneNumber)
            .setLogLevel(Log.VERBOSE)
            .setDefaultLanguage(TestAppSettings.language)
            .setUncaughtExceptionHandlerEnabled(true)
            .setNetworkSensitivity(TestAppSettings.networkSensitivity)
            .setDarkModeEnabled(TestAppSettings.isDarkModeEnabled)
            .setShowSingleChannelEnabled(TestAppSettings.isShowSingleChannelEnabled)
            .setAutoMinimizeCallView(TestAppSettings.autoMinimizeCallView())
            .setShowAgentIconBorderEnabled(TestAppSettings.isRemoveAgentIconBorderEnabled)
            .setStaticFontSizeInPickerView(TestAppSettings.staticFontSizeInPickerView())
            .setIgnoreReadPhoneStatePermission(TestAppSettings.ignoreReadPhoneStatePermission())
            .setHideMediaAttachmentInChat(TestAppSettings.hideMediaAttachmentInChat())
            .setShowCsatSkipButton(TestAppSettings.showCsatSkipButton())
            .setUjetStylesOptions(
                UjetStylesOptions
                    .Builder()
                    .setChatQuickReplyButtonsStyle(
                        if (TestAppSettings.quickReplyButtonsGrouped) {
                            QuickReplyButtonsStyle.GROUPED
                        } else {
                            QuickReplyButtonsStyle.INDIVIDUAL
                        }
                    )
                    .setChatStyles(TestAppSettings.chatStyles)
                    .build()
            )
            .setBlockChatTerminationByEndUser(TestAppSettings.blockChatTerminationByEndUser())
            .setHideStatusBar(TestAppSettings.hideStatusBar())
            .setHideStartNewConversation(TestAppSettings.hideStartNewConversation())
            .setLoadingSpinnerDrawableRes(TestAppSettings.getSpinnerDrawableRes(application))
            .setLandscapeOrientationDisabled(TestAppSettings.isLandscapeModeDisabled)
        if (isClassPresent("co.ujet.android.cobrowse.CobrowseEntryPoint")) {
            builder.setCobrowseLicenseKey(TestAppSettings.customCobrowseLicenseKey)
                   .setCobrowseURL(TestAppSettings.customCobrowseUrl)
        }
        return builder.build()
    }

    private fun isClassPresent(className: String): Boolean {
        return try {
            Class.forName(className)
            true
        } catch (e: ClassNotFoundException) {
            false
        }
    }
}
