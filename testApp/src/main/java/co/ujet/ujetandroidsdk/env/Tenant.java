package co.ujet.ujetandroidsdk.env;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

public class Tenant {

    @NonNull
    @SerializedName("subdomain")
    private String subdomain;

    @NonNull
    @SerializedName("key")
    private String key;

    @NonNull
    @SerializedName("secret")
    private String secret;

    @NonNull
    @SerializedName("host")
    private String host;

    private int port;

    public Tenant(@NonNull String subdomain,
                  @NonNull String key,
                  @NonNull String secret,
                  @NonNull String host,
                  int port) {
        this.subdomain = subdomain;
        this.key = key;
        this.secret = secret;
        this.host = host;
        this.port = port;
    }

    @NonNull
    public String getSubdomain() {
        return subdomain;
    }

    @NonNull
    public String getKey() {
        return key;
    }

    @NonNull
    public String getSecret() {
        return secret;
    }

    @NonNull
    public String getHost() {
        return host;
    }

    public int getPort() {
        return port > 0 ? port : 443;
    }

    public boolean isLocal() {
        return "local".equals(subdomain);
    }

    public boolean isNotCustom() {
        return !"custom".equals(subdomain) && !"local".equals(subdomain);
    }

    public void validate() {
        checkNotEmpty(subdomain, "Subdomain");
        checkNotEmpty(key, "Company key");
        checkNotEmpty(secret, "Company secret");
        checkNotEmpty(host, "Host");
    }

    private void checkNotEmpty(String value, String name) {
        if (TextUtils.isEmpty(value)) {
            if (TextUtils.isEmpty(subdomain))
                throw new IllegalArgumentException(name + " doesn't exists");
            else
                throw new IllegalArgumentException(name + " doesn't exists for " + subdomain);
        }
    }
}

