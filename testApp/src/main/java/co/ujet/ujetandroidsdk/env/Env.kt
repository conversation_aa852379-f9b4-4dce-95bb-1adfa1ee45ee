package co.ujet.ujetandroidsdk.env

import android.text.TextUtils
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class Env @Keep constructor(
        @SerializedName("name")
        val name: String? = null,

        @SerializedName("tenants")
        val tenants: List<Tenant>
) : Serializable {
        val tenantNames: Array<String?>
                get() {
                        val names = arrayOfNulls<String>(tenants.size + 2)
                        tenants.forEachIndexed { index, tenant ->
                                names[index] = tenant.subdomain
                        }
                        names[tenants.size] = "custom"
                        names[tenants.size + 1] = "local"
                        return names
                }

        fun validate() {
                require(!TextUtils.isEmpty(name)) { "Env name doesn't exists" }
                require(tenants.isNotEmpty()) { "Tenants doesn't exists for $name" }
        }

        fun pickTenant(tenantName: String?): Tenant {
                for (tenant in tenants) {
                        if (tenant.subdomain == tenantName) {
                                return tenant
                        }
                }
                return tenants[0]
        }
}
