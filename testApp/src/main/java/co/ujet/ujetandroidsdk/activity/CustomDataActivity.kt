package co.ujet.ujetandroidsdk.activity

import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import co.ujet.android.app.FragmentHelper
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetStyle
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.fragment.CustomDataFragment

class CustomDataActivity: BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_custom_data)
        setToolBar()
        registerOnBackPressedCallback { back() }
        showCustomDataFragment()
    }

    override fun onSupportNavigateUp(): Boolean {
        back()
        return true
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            back()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        getUjetStyle()?.colorPrimary?.let { toolbar.setBackgroundColor(it) }
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)
    }

    private fun getUjetStyle(): UjetStyle? {
        return applicationContext?.let { Injection.provideUjetStyle(it) }
    }

    private fun registerOnBackPressedCallback(callback: () -> Unit) {
        val onBackPressedCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                callback.invoke()
            }
        }
        onBackPressedDispatcher.addCallback(onBackPressedCallback)
    }

    private fun back() {
        val fragmentManager = supportFragmentManager
        if (fragmentManager.backStackEntryCount > 1) {
            fragmentManager.popBackStack()
        } else {
            finish()
        }
    }

    private fun showCustomDataFragment() {
        FragmentHelper.showAsTop(
            this,
            CustomDataFragment.newInstance(),
            CustomDataFragment.TAG
        )
    }
}
