package co.ujet.ujetandroidsdk.activity

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.TextView
import co.ujet.ujetandroidsdk.R

class EventParametersActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_event_parameters)
        val deeplink = intent?.data
        val eventDataString = StringBuilder()
        eventDataString
            .append("Full deep-link")
            .append("\n")
            .append(deeplink?.toString())
            .append("\n\n")
            .append("Query params")
            .append("\n")
        val queryParams = deeplink?.queryParameterNames
        if (queryParams.isNullOrEmpty()) {
            eventDataString.append("No query params")
        } else {
            queryParams.forEach { paramKey ->
                val paramValue = deeplink.getQueryParameter(paramKey)
                eventDataString
                    .append("Key: ")
                    .append(paramKey)
                    .append(" | ")
                    .append("Value: ")
                    .append(paramValue)
                    .append("\n")
            }
        }
        findViewById<TextView>(R.id.event_data).text = eventDataString.toString()
    }
}
