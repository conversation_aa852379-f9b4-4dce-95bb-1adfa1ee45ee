package co.ujet.ujetandroidsdk.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.SwitchCompat
import androidx.appcompat.widget.Toolbar
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.internal.Injection
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.ui.domain.*
import co.ujet.android.modulemanager.entrypoints.ui.UjetUI
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.android.ui.util.DesignUtil
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.common.*
import co.ujet.ujetandroidsdk.util.AssetsUtil
import co.ujet.ujetandroidsdk.util.StyleUtil
import mehdi.sakout.fancybuttons.FancyButton

class UjetStyleOptionsActivity : BaseActivity() {
    private var userResponse = UserResponse()
    private var cachedChatStyles = TestAppSettings.chatStyles

    @SuppressLint("ResourceType")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ujet_start_options)

        findViewById<View>(R.id.root_view).apply {
            setBackgroundColor(getUjetStyle().primaryBackgroundColor)
        }
        setToolBar()

        //Get cached chat styles to update UI
        userResponse.backButton.visible = cachedChatStyles.backButton?.visible == true
        //Quick reply button styles
        setTextViewColor(R.id.quick_reply_buttons_title)
        val quickReplyButtonsGrouped = findViewById<RadioButton>(R.id.quick_reply_buttons_grouped).apply {
            buttonTintList = getColorStateList()
            isChecked = TestAppSettings.quickReplyButtonsGrouped
            setTextColor(getUjetStyle().textPrimaryColor)
        }
        findViewById<RadioButton?>(R.id.quick_reply_buttons_individual).apply {
            buttonTintList = getColorStateList()
            isChecked = TestAppSettings.quickReplyButtonsGrouped.not()
            setTextColor(getUjetStyle().textPrimaryColor)
        }

        //Chat input bar styles
        setTextViewColor(R.id.chat_input_bar_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_input_bar_styles_box_label)
        setTextViewColor(R.id.chat_input_bar_background_color_label)
        setTextViewColor(R.id.chat_agent_message_corner_radius_label)
        setTextViewColor(R.id.chat_input_bar_cursor_color_label)
        updateEditTextView(cachedChatStyles, FontSupportedViews.INPUT_BAR, EditTextTypes.CURSOR_COLOR)
        setTextViewColor(R.id.chat_input_bar_placeholder_label)
        updateEditTextView(cachedChatStyles, FontSupportedViews.INPUT_BAR, EditTextTypes.PLACEHOLDER)
        setTextViewColor(R.id.chat_input_bar_border_color_label)
        setTextViewColor(R.id.chat_input_bar_border_width)
        updateEditTextView(FontSupportedViews.INPUT_BAR)
        updateSwitchView(IconSupportedViews.ESCALATE_ICON)
        updateFontView(cachedChatStyles.userInputBar?.inputField?.font, FontSupportedViews.INPUT_BAR)
        updateEditTextView(FontSupportedViews.INPUT_TRAY)
        //Chat actions menu style
        setTextViewColor(R.id.chat_actions_menu_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_actions_menu_styles_box_label)
        updateSwitchView(IconSupportedViews.CAMERA_ICON)
        updateSwitchView(IconSupportedViews.COBROWSE_ICON)
        updateSwitchView(IconSupportedViews.SELECT_PHOTO_FROM_LIBRARY_ICON)
        //Chat header styles
        setTextViewColor(R.id.chat_header_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_header_styles_box_label)
        setTextViewColor(R.id.chat_header_text_label)
        val chatHeaderEditText = findViewById<EditText?>(R.id.chat_header_text)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            setText(cachedChatStyles.header?.textContent)
            setTextColor(getUjetStyle().textSecondaryColor)
            setHintTextColor(getUjetStyle().pickerTextNoCenterColor)
        }

        /*Moved all font related logic to common place so make sure to follow format CHAT_STYLE_TYPE_FONT_TYPE to name new UI components
         and to apply font to it. You can checkout getChatStyleType() on how we get it and below example for reference.

        Resource ids used for chat header UI view ->
        Font Color label view id: chat_header_font_color_label [here chat_header is chat style type and then append "_FONT_TYPE" and "_LABEL" because it is label]
        Font color Edit text view id: chat_header_font_color [here chat_header is chat style type and then append _FONT_TYPE]
        Font style label view id: chat_header_font_style_label
        Font style spinner view id: chat_header_font_style

        Resource ids used for chat system message UI view ->
        Font Color label view id: chat_system_message_font_color_label [here chat_system_message is chat style type and then append "_FONT_TYPE" and "_LABEL" because it is label]
        Font color Edit text view id: chat_system_message_font_color [here chat_system_message is chat style type and then append _FONT_TYPE]
        Font style label view id: chat_system_message_font_style_label
        Font style spinner view id: chat_system_message_font_style
        */
        updateFontView(cachedChatStyles.header?.font, FontSupportedViews.HEADER)

        //Chat back button styles
        setTextViewColor(R.id.chat_back_button_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_back_button_styles_box_label)
        setTextViewColor(R.id.chat_back_button_image_label)
        updateSwitchView(IconSupportedViews.BACK_BUTTON) //to show visible style option
        updateSpinnerView(IconSupportedViews.BACK_BUTTON, IconTypes.ICON) //to show icon options

        //Chat end button styles
        setTextViewColor(R.id.chat_end_button_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_end_button_styles_box_label)
        updateSwitchView(IconSupportedViews.END_BUTTON) //to show visible style option
        updateFontView(cachedChatStyles.endChatButton?.font, FontSupportedViews.END_BUTTON)

        //Chat welcome message tray styles
        setTextViewColor(R.id.chat_welcome_message_tray_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_welcome_message_tray_styles_box_label)
        updateSwitchView(IconSupportedViews.WELCOME_MESSAGE_TRAY) //to show visible style option
        updateSwitchView(IconSupportedViews.WELCOME_MESSAGE_TRAY_AGENT_ICON) //to show visible style option
        updateDividerView() //to show divider width and color style options

        //Chat system messages styles
        setTextViewColor(R.id.chat_system_message_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_system_message_styles_box_label)
        setTextViewColor(R.id.chat_system_message_title)
        updateFontView(cachedChatStyles.systemMessages?.font, FontSupportedViews.SYSTEM_MESSAGES)
        setTextViewColor(R.id.chat_system_message_button_title)
        updateFontView(cachedChatStyles.systemMessages?.buttonStyle?.font, FontSupportedViews.SYSTEM_MESSAGES_BUTTON)
        updateEditTextView(FontSupportedViews.SYSTEM_MESSAGES) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.SYSTEM_MESSAGES_BUTTON) //to show border, background, corner radius style options for button

        //Chat time stamp styles
        setTextViewColor(R.id.chat_time_stamp_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_time_stamp_styles_box_label)
        updateFontView(cachedChatStyles.timeStamps?.font, FontSupportedViews.TIME_STAMP)

        //Chat consumer messages styles
        setTextViewColor(R.id.chat_consumer_message_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_consumer_message_styles_box_label)
        setTextViewColor(R.id.chat_consumer_message_title)
        updateFontView(cachedChatStyles.consumerMessageBubbles?.font, FontSupportedViews.CONSUMER_MESSAGES)
        updateEditTextView(FontSupportedViews.CONSUMER_MESSAGES) //to show border, background, corner radius style options for text
        setTextViewColor(R.id.chat_consumer_message_icon_title)
        updateSwitchView(IconSupportedViews.CONSUMER_MESSAGES) //to show visible style option
        updateEditTextView(cachedChatStyles, FontSupportedViews.CONSUMER_MESSAGES, EditTextTypes.ICON_SIZE) //to show icon size option
        updateSpinnerView(IconSupportedViews.CONSUMER_MESSAGES, IconTypes.POSITION) //to show icon position options
        updateSpinnerView(IconSupportedViews.CONSUMER_MESSAGES, IconTypes.ICON) //to show icon options

        //Chat agent messages styles
        setTextViewColor(R.id.chat_agent_message_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_agent_message_styles_box_label)
        setTextViewColor(R.id.chat_agent_message_title)
        updateFontView(cachedChatStyles.agentMessageBubbles?.font, FontSupportedViews.AGENT_MESSAGES)
        updateEditTextView(FontSupportedViews.AGENT_MESSAGES) //to show border, background, corner radius style options for text
        setTextViewColor(R.id.chat_agent_message_icon_title)
        updateSwitchView(IconSupportedViews.AGENT_MESSAGES) //to show visible style option
        updateEditTextView(cachedChatStyles, FontSupportedViews.AGENT_MESSAGES, EditTextTypes.ICON_SIZE) //to show icon size option
        updateSpinnerView(IconSupportedViews.AGENT_MESSAGES, IconTypes.POSITION) //to show icon position options

        //Chat content cards styles
        setTextViewColor(R.id.chat_content_cards_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_content_cards_styles_box_label)
        setTextViewColor(R.id.chat_content_cards)
        setTextViewColor(R.id.chat_content_cards_title)
        setTextViewColor(R.id.chat_content_cards_subtitle)
        setTextViewColor(R.id.chat_content_cards_body)
        setTextViewColor(R.id.chat_content_cards_background_image)
        setTextViewColor(R.id.chat_content_cards_primary_button)
        setTextViewColor(R.id.chat_content_cards_secondary_button)
        updateFontView(cachedChatStyles.contentCard?.font, FontSupportedViews.CONTENT_CARDS)
        updateFontView(cachedChatStyles.contentCard?.title?.font, FontSupportedViews.CONTENT_CARDS_TITLE)
        updateFontView(cachedChatStyles.contentCard?.subtitle?.font, FontSupportedViews.CONTENT_CARDS_SUBTITLE)
        updateFontView(cachedChatStyles.contentCard?.body?.font, FontSupportedViews.CONTENT_CARDS_BODY)
        updateEditTextView(FontSupportedViews.CONTENT_CARDS) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.CONTENT_CARDS_TITLE) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.CONTENT_CARDS_SUBTITLE) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.CONTENT_CARDS_BODY) //to show border, background, corner radius style options for text
        updateEditTextView(cachedChatStyles, FontSupportedViews.CONTENT_CARDS_IMAGE, EditTextTypes.IMAGE_HEIGHT) //to show image height option
        updateContentCardButtonView(cachedChatStyles.contentCard?.primaryButton?.font, FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON)
        updateContentCardButtonView(cachedChatStyles.contentCard?.secondaryButton?.font, FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON)

        //Web from content card styles
        setTextViewColor(R.id.chat_web_form_card_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_web_form_card_styles_box_label)
        setTextViewColor(R.id.chat_web_form_card)
        setTextViewColor(R.id.chat_web_form_card_title)
        setTextViewColor(R.id.chat_web_form_card_subtitle)
        setTextViewColor(R.id.chat_web_form_card_background_image)
        updateFontView(cachedChatStyles.formCard?.font, FontSupportedViews.WEB_FORM_CARD)
        updateFontView(cachedChatStyles.formCard?.title?.font, FontSupportedViews.WEB_FORM_CARD_TITLE)
        updateFontView(cachedChatStyles.formCard?.subtitle?.font, FontSupportedViews.WEB_FORM_CARD_SUBTITLE)
        updateEditTextView(FontSupportedViews.WEB_FORM_CARD) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.WEB_FORM_CARD_TITLE) //to show border, background, corner radius style options for text
        updateEditTextView(FontSupportedViews.WEB_FORM_CARD_SUBTITLE) //to show border, background, corner radius style options for text
        updateEditTextView(cachedChatStyles, FontSupportedViews.WEB_FORM_CARD_IMAGE, EditTextTypes.IMAGE_HEIGHT) //to show image height option

        //Post-session styles
        setTextViewColor(R.id.chat_post_session_styles_box_label)
        setTextViewBackgroundColor(R.id.chat_post_session_styles_box_label)
        setTextViewColor(R.id.chat_post_session)
        updateEditTextView(FontSupportedViews.POST_SESSION)

        findViewById<FancyButton?>(R.id.save_options_button).apply {
            setOnClickListener {
                val chatHeaderTitle = getEditTextResponse(chatHeaderEditText)
                TestAppSettings.customChatHeaderTitle = chatHeaderTitle // We need this for backwards compatibility?
                TestAppSettings.quickReplyButtonsGrouped = quickReplyButtonsGrouped.isChecked
                TestAppSettings.chatStyles = ChatStyles(
                    backButton = BackButtonStyle(
                        visible = userResponse.backButton.visible,
                        image = userResponse.backButton.image
                    ),
                    endChatButton = EndButtonStyle(
                        visible = userResponse.endChatButton.visible,
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.endChatButton.font?.colorEditText),
                            size = getEditTextResponse(userResponse.endChatButton.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.endChatButton.font?.styleEditText),
                            family = Family.values()[userResponse.endChatButton.font?.familySelected ?: DEFAULT_INDEX].value,
                        )
                    ),
                    header = ChatHeaderStyle(
                        textContent = chatHeaderTitle,
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.header.font?.colorEditText),
                            size = getEditTextResponse(userResponse.header.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.header.font?.styleEditText),
                            family = Family.values()[userResponse.header.font?.familySelected ?: DEFAULT_INDEX].value
                        ),
                        messageTrayVisible = userResponse.welcomeMessageTray.messageTrayVisible,
                        agentIconVisible = userResponse.welcomeMessageTray.agentIconVisible,
                        divider = DividerStyle(
                            color = getEditTextResponse(userResponse.welcomeMessageTray.divider?.colorEditText),
                            width = getEditTextResponse(userResponse.welcomeMessageTray.divider?.widthEditText)?.toInt(),
                        )
                    ),
                    systemMessages = SystemMessageStyle(
                        backgroundColor = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.backgroundColorEditText),
                        cornerRadius = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.cornerRadiusEditText),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.border?.colorEditText),
                            width = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.border?.widthEditText)?.toInt(),
                        ),
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.font?.colorEditText),
                            size = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.systemMessages.messageBubbleResponse?.font?.styleEditText),
                            family = Family.values()[userResponse.systemMessages.messageBubbleResponse?.font?.familySelected ?: DEFAULT_INDEX].value,
                        ),
                        buttonStyle = ButtonStyle(
                            backgroundColor = getEditTextResponse(userResponse.systemMessages.buttonResponse?.backgroundColorEditText),
                            cornerRadius = getEditTextResponse(userResponse.systemMessages.buttonResponse?.cornerRadiusEditText),
                            border = BorderStyle(
                                color = getEditTextResponse(userResponse.systemMessages.buttonResponse?.border?.colorEditText),
                                width = getEditTextResponse(userResponse.systemMessages.buttonResponse?.border?.widthEditText)?.toInt(),
                            ),
                            font = FontStyle(
                                colorReference = getEditTextResponse(userResponse.systemMessages.buttonResponse?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.systemMessages.buttonResponse?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.systemMessages.buttonResponse?.font?.styleEditText),
                                family = Family.values()[userResponse.systemMessages.buttonResponse?.font?.familySelected ?: DEFAULT_INDEX].value
                            )
                        )
                    ),
                    timeStamps = TimeStampStyle(
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.timeStamps.font?.colorEditText),
                            size = getEditTextResponse(userResponse.timeStamps.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.timeStamps.font?.styleEditText),
                            family = Family.values()[userResponse.timeStamps.font?.familySelected ?: DEFAULT_INDEX].value
                        )
                    ),
                    consumerMessageBubbles = ConsumerMessageStyle(
                        backgroundColor = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.backgroundColorEditText),
                        cornerRadius = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.cornerRadiusEditText),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.border?.colorEditText),
                            width = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.border?.widthEditText)?.toInt(),
                        ),
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.font?.colorEditText),
                            size = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.consumerMessageBubbles.messageBubbleResponse?.font?.styleEditText),
                            family = Family.values()[userResponse.consumerMessageBubbles.messageBubbleResponse?.font?.familySelected
                                ?: DEFAULT_INDEX].value,
                        ),
                        icon = IconStyle(
                            visible = userResponse.consumerMessageBubbles.icon?.visible ?: true,
                            icon = IconReferences.values()[userResponse.consumerMessageBubbles.icon?.iconSelected ?: DEFAULT_INDEX].value,
                            size = getEditTextResponse(userResponse.consumerMessageBubbles.icon?.sizeEditText)?.toInt(),
                            position = ConsumerMessageStyle.Position.values()[userResponse.consumerMessageBubbles.positionSelected].value
                        ),
                    ),
                    agentMessageBubbles = AgentMessageStyle(
                        backgroundColor = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.backgroundColorEditText),
                        cornerRadius = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.cornerRadiusEditText),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.border?.colorEditText),
                            width = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.border?.widthEditText)?.toInt(),
                        ),
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.font?.colorEditText),
                            size = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.agentMessageBubbles.messageBubbleResponse?.font?.styleEditText),
                            family = Family.values()[userResponse.agentMessageBubbles.messageBubbleResponse?.font?.familySelected
                                ?: DEFAULT_INDEX].value,
                        ),
                        icon = IconStyle(
                            visible = userResponse.agentMessageBubbles.icon?.visible ?: true,
                            icon = null,
                            size = getEditTextResponse(userResponse.agentMessageBubbles.icon?.sizeEditText)?.toInt(),
                            position = AgentMessageStyle.Position.values()[userResponse.agentMessageBubbles.positionSelected].value
                        ),
                    ),
                    userInputBar = ChatInputBarStyle(
                        backgroundColor = getEditTextResponse(userResponse.chatInputFieldStyle.backgroundColorEditText),
                        inputField = ChatInputFieldStyle(
                            cornerRadius = getEditTextResponse(userResponse.chatInputFieldStyle.cornerRadiusEditText),
                            cursorColor = getEditTextResponse(userResponse.chatInputFieldStyle.cursorColorEditText),
                            placeholderText = getEditTextResponse(userResponse.chatInputFieldStyle.placeHolderEditText),
                            border = BorderStyle(
                                color = getEditTextResponse(userResponse.chatInputFieldStyle.border?.colorEditText),
                                width = getEditTextResponse(userResponse.chatInputFieldStyle.border?.widthEditText)?.toInt(),
                            ),
                            font = FontStyle(
                                colorReference = getEditTextResponse(userResponse.chatInputFieldStyle.font?.colorEditText),
                                size = getEditTextResponse(userResponse.chatInputFieldStyle.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.chatInputFieldStyle.font?.styleEditText),
                                family = Family.values()[userResponse.chatInputFieldStyle.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        topBorder = BorderStyle(
                            color = getEditTextResponse(userResponse.chatInputFieldStyle.topBorder?.colorEditText),
                            width = getEditTextResponse(userResponse.chatInputFieldStyle.topBorder?.widthEditText)?.toInt(),
                        ),

                        escalateIcon = IconStyle(
                            visible = userResponse.chatInputFieldStyle.escalateIcon?.visible ?: true,
                            icon = userResponse.chatInputFieldStyle.escalateIcon?.image,
                            size = getEditTextResponse(userResponse.chatInputFieldStyle.escalateIcon?.sizeEditText)?.toInt()
                        ),

                        chatActionsMenuIcon = IconStyle(
                            icon = userResponse.chatInputFieldStyle.chatActionsMenuIcon?.image,
                            size = getEditTextResponse(userResponse.chatInputFieldStyle.chatActionsMenuIcon?.sizeEditText)?.toInt()
                        ),
                        sendButton = SendButtonStyle(
                            image = userResponse.chatInputFieldStyle.sendButton?.image
                        )
                    ),
                    chatActionsMenuStyle = ChatActionsMenuStyle(
                        cameraIcon = IconStyle(
                            visible = userResponse.chatActionsMenuStyle.cameraIcon?.visible ?: true,
                            icon = userResponse.chatActionsMenuStyle.cameraIcon?.image,
                            size = getEditTextResponse(userResponse.chatActionsMenuStyle.cameraIcon?.sizeEditText)?.toInt()
                        ),
                        cobrowseIcon = IconStyle(
                            visible = userResponse.chatActionsMenuStyle.cobrowseIcon?.visible ?: true,
                            icon = userResponse.chatActionsMenuStyle.cobrowseIcon?.image,
                            size = getEditTextResponse(userResponse.chatActionsMenuStyle.cobrowseIcon?.sizeEditText)?.toInt()
                        ),
                        selectPhotoFromLibraryIcon = IconStyle(
                            visible = userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.visible ?: true,
                            icon = userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.image,
                            size = getEditTextResponse(userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.sizeEditText)?.toInt()
                        )
                    ),
                    contentCard = ContentCardStyle(
                        backgroundColor = getEditTextResponse(userResponse.contentCard.backgroundColorEditText),
                        cornerRadius = getEditTextResponse(userResponse.contentCard.cornerRadiusEditText),
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.contentCard.font?.colorEditText),
                            size = getEditTextResponse(userResponse.contentCard.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.contentCard.font?.styleEditText),
                            family = Family.values()[userResponse.contentCard.font?.familySelected ?: DEFAULT_INDEX].value,
                        ),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.contentCard.border?.colorEditText),
                            width = getEditTextResponse(userResponse.contentCard.border?.widthEditText)?.toInt(),
                        ),
                        title = TextStyle(
                            FontStyle(
                                colorReference = getEditTextResponse(userResponse.contentCard.title?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.contentCard.title?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.contentCard.title?.font?.styleEditText),
                                family = Family.values()[userResponse.contentCard.title?.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        subtitle = TextStyle(
                            FontStyle(
                                colorReference = getEditTextResponse(userResponse.contentCard.subtitle?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.contentCard.subtitle?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.contentCard.subtitle?.font?.styleEditText),
                                family = Family.values()[userResponse.contentCard.subtitle?.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        body = TextStyle(
                            FontStyle(
                                colorReference = getEditTextResponse(userResponse.contentCard.body?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.contentCard.body?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.contentCard.body?.font?.styleEditText),
                                family = Family.values()[userResponse.contentCard.body?.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        image = ImageStyle(
                            height = getEditTextResponse(userResponse.contentCard.image?.heightEditText)?.toInt()
                        ),
                        primaryButton = ButtonStyle(
                            backgroundColor = getEditTextResponse(userResponse.contentCard.primaryButton?.backgroundColorEditText),
                            cornerRadius = getEditTextResponse(userResponse.contentCard.primaryButton?.cornerRadiusEditText),
                            border = BorderStyle(
                                color = getEditTextResponse(userResponse.contentCard.primaryButton?.border?.colorEditText),
                                width = getEditTextResponse(userResponse.contentCard.primaryButton?.border?.widthEditText)?.toInt(),
                            ),
                            font = FontStyle(
                                colorReference = getEditTextResponse(userResponse.contentCard.primaryButton?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.contentCard.primaryButton?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.contentCard.primaryButton?.font?.styleEditText),
                                family = Family.values()[userResponse.contentCard.primaryButton?.font?.familySelected ?: DEFAULT_INDEX].value
                            )
                        ),
                        secondaryButton = ButtonStyle(
                            backgroundColor = getEditTextResponse(userResponse.contentCard.secondaryButton?.backgroundColorEditText),
                            cornerRadius = getEditTextResponse(userResponse.contentCard.secondaryButton?.cornerRadiusEditText),
                            border = BorderStyle(
                                color = getEditTextResponse(userResponse.contentCard.secondaryButton?.border?.colorEditText),
                                width = getEditTextResponse(userResponse.contentCard.secondaryButton?.border?.widthEditText)?.toInt(),
                            ),
                            font = FontStyle(
                                colorReference = getEditTextResponse(userResponse.contentCard.secondaryButton?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.contentCard.secondaryButton?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.contentCard.secondaryButton?.font?.styleEditText),
                                family = Family.values()[userResponse.contentCard.secondaryButton?.font?.familySelected ?: DEFAULT_INDEX].value
                            )
                        ),
                    ),
                    formCard = FormCardStyle(
                        backgroundColor = getEditTextResponse(userResponse.webFormCard.backgroundColorEditText),
                        cornerRadius = getEditTextResponse(userResponse.webFormCard.cornerRadiusEditText),
                        font = FontStyle(
                            colorReference = getEditTextResponse(userResponse.webFormCard.font?.colorEditText),
                            size = getEditTextResponse(userResponse.webFormCard.font?.sizeEditText)?.toInt(),
                            style = getEditTextResponse(userResponse.webFormCard.font?.styleEditText),
                            family = Family.values()[userResponse.webFormCard.font?.familySelected ?: DEFAULT_INDEX].value,
                        ),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.webFormCard.border?.colorEditText),
                            width = getEditTextResponse(userResponse.webFormCard.border?.widthEditText)?.toInt(),
                        ),
                        title = TextStyle(
                            FontStyle(
                                colorReference = getEditTextResponse(userResponse.webFormCard.title?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.webFormCard.title?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.webFormCard.title?.font?.styleEditText),
                                family = Family.values()[userResponse.webFormCard.title?.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        subtitle = TextStyle(
                            FontStyle(
                                colorReference = getEditTextResponse(userResponse.webFormCard.subtitle?.font?.colorEditText),
                                size = getEditTextResponse(userResponse.webFormCard.subtitle?.font?.sizeEditText)?.toInt(),
                                style = getEditTextResponse(userResponse.webFormCard.subtitle?.font?.styleEditText),
                                family = Family.values()[userResponse.webFormCard.subtitle?.font?.familySelected ?: DEFAULT_INDEX].value,
                            )
                        ),
                        image = ImageStyle(
                            height = getEditTextResponse(userResponse.webFormCard.image?.heightEditText)?.toInt()
                        ),
                    ),
                    postSession = PostSessionStyle(
                        backgroundColor = getEditTextResponse(userResponse.postSession.backgroundColorEditText),
                        border = BorderStyle(
                            color = getEditTextResponse(userResponse.postSession.border?.colorEditText),
                            width = getEditTextResponse(userResponse.postSession.border?.widthEditText)?.toInt(),
                        ),
                    )
                )
                Toast.makeText(this@UjetStyleOptionsActivity, "Start options saved!", Toast.LENGTH_SHORT).show()
                restartApplication()
            }
        }
    }

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        toolbar.setBackgroundColor(getUjetStyle().colorPrimary)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)
        toolbar?.setNavigationOnClickListener {
            finish()
        }
    }

    private fun getUjetStyle() = Injection.provideUjetStyle(this)

    private fun setTextViewColor(resId: Int) {
        findViewById<TextView>(resId)?.apply {
            UjetViewStyler.stylePrimaryText(getUjetStyle(), this)
        }
    }

    private fun setTextViewBackgroundColor(resId: Int) {
        findViewById<TextView>(resId)?.apply {
            setBackgroundColor(getUjetStyle().primaryBackgroundColor)
        }
    }

    private fun updateFontView(font: FontStyle?, fontSupportedViews: FontSupportedViews) {
        updateFontEditTextView(font, fontSupportedViews, FontEditTextTypes.COLOR)
        updateFontEditTextView(font, fontSupportedViews, FontEditTextTypes.SIZE)
        updateFontEditTextView(font, fontSupportedViews, FontEditTextTypes.STYLE)
        updateFontSpinnerViews(font, fontSupportedViews, FontSpinnerViewTypes.FAMILY)
    }

    private fun updateEditTextView(fontSupportedViews: FontSupportedViews) {
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BACKGROUND_COLOR)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.CORNER_RADIUS)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BORDER_WIDTH)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BORDER_COLOR)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.IMAGE)
    }

    private fun updateContentCardButtonView(font: FontStyle?, fontSupportedViews: FontSupportedViews) {
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BACKGROUND_COLOR)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.CORNER_RADIUS)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BORDER_COLOR)
        updateEditTextView(cachedChatStyles, fontSupportedViews, EditTextTypes.BORDER_WIDTH)
        updateFontView(font, fontSupportedViews)
    }

    private fun updateDividerView() {
        updateEditTextView(cachedChatStyles, FontSupportedViews.WELCOME_MESSAGE_TRAY, EditTextTypes.DIVIDER_WIDTH)
        updateEditTextView(cachedChatStyles, FontSupportedViews.WELCOME_MESSAGE_TRAY, EditTextTypes.DIVIDER_COLOR)
    }

    private fun updateSwitchView(iconSupportedViews: IconSupportedViews) {
        var resourceName: String? = null
        var cachedSwitchEnabled = true

        when (iconSupportedViews) {
            IconSupportedViews.BACK_BUTTON -> {
                resourceName = "chat_back_button_visible"
                cachedSwitchEnabled = cachedChatStyles.backButton?.visible ?: true
                userResponse.backButton.visible = cachedSwitchEnabled
            }

            IconSupportedViews.END_BUTTON -> {
                resourceName = "chat_end_button_visible"
                cachedSwitchEnabled = cachedChatStyles.endChatButton?.visible ?: true
                userResponse.endChatButton.visible = cachedSwitchEnabled
            }

            IconSupportedViews.WELCOME_MESSAGE_TRAY -> {
                resourceName = "chat_welcome_message_tray_visible"
                cachedSwitchEnabled = cachedChatStyles.header?.messageTrayVisible ?: true
                userResponse.welcomeMessageTray.messageTrayVisible = cachedSwitchEnabled
            }

            IconSupportedViews.WELCOME_MESSAGE_TRAY_AGENT_ICON -> {
                resourceName = "chat_agent_avatar_icon_visible"
                cachedSwitchEnabled = cachedChatStyles.header?.agentIconVisible ?: true
                userResponse.welcomeMessageTray.agentIconVisible = cachedSwitchEnabled
            }

            IconSupportedViews.CONSUMER_MESSAGES -> {
                resourceName = "chat_consumer_message_visible"
                cachedSwitchEnabled = cachedChatStyles.consumerMessageBubbles?.icon?.visible ?: true
                userResponse.consumerMessageBubbles.icon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.AGENT_MESSAGES -> {
                resourceName = "chat_agent_message_visible"
                cachedSwitchEnabled = cachedChatStyles.agentMessageBubbles?.icon?.visible ?: true
                userResponse.agentMessageBubbles.icon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.CAMERA_ICON -> {
                resourceName = "chat_actions_menu_camera_icon_visible"
                cachedSwitchEnabled = cachedChatStyles.chatActionsMenuStyle?.cameraIcon?.visible ?: true
                userResponse.chatActionsMenuStyle.cameraIcon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.COBROWSE_ICON -> {
                resourceName = "chat_actions_menu_cobrowse_icon_visible"
                cachedSwitchEnabled = cachedChatStyles.chatActionsMenuStyle?.cobrowseIcon?.visible ?: true
                userResponse.chatActionsMenuStyle.cobrowseIcon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.SELECT_PHOTO_FROM_LIBRARY_ICON -> {
                resourceName = "chat_actions_menu_photo_library_icon_visible"
                cachedSwitchEnabled = cachedChatStyles.chatActionsMenuStyle?.selectPhotoFromLibraryIcon?.visible ?: true
                userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.ESCALATE_ICON -> {
                resourceName = "chat_input_bar_escalate_icon_visible"
                cachedSwitchEnabled = cachedChatStyles.userInputBar?.escalateIcon?.visible ?: true
                userResponse.chatInputFieldStyle.escalateIcon?.visible = cachedSwitchEnabled
            }

            IconSupportedViews.CHAT_ACTIONS_MENU_ICON -> {
                return
            }
        }

        val textViewId: Int = getResourceId("${resourceName}_label")
        val switchViewId: Int = getResourceId(resourceName)
        setTextViewColor(textViewId)
        findViewById<SwitchCompat?>(switchViewId).apply {
            isChecked = cachedSwitchEnabled
            setOnClickListener {
                when (iconSupportedViews) {
                    IconSupportedViews.BACK_BUTTON -> userResponse.backButton.visible = userResponse.backButton.visible.not()
                    IconSupportedViews.END_BUTTON -> userResponse.endChatButton.visible = userResponse.endChatButton.visible.not()
                    IconSupportedViews.WELCOME_MESSAGE_TRAY -> userResponse.welcomeMessageTray.messageTrayVisible =
                        userResponse.welcomeMessageTray.messageTrayVisible.not()

                    IconSupportedViews.WELCOME_MESSAGE_TRAY_AGENT_ICON -> userResponse.welcomeMessageTray.agentIconVisible =
                        userResponse.welcomeMessageTray.agentIconVisible.not()

                    IconSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.icon?.visible =
                        userResponse.consumerMessageBubbles.icon?.visible?.not() == true

                    IconSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.icon?.visible =
                        userResponse.agentMessageBubbles.icon?.visible?.not() == true

                    IconSupportedViews.CAMERA_ICON -> userResponse.chatActionsMenuStyle.cameraIcon?.visible =
                        userResponse.chatActionsMenuStyle.cameraIcon?.visible?.not() == true

                    IconSupportedViews.COBROWSE_ICON -> userResponse.chatActionsMenuStyle.cobrowseIcon?.visible =
                        userResponse.chatActionsMenuStyle.cobrowseIcon?.visible?.not() == true

                    IconSupportedViews.SELECT_PHOTO_FROM_LIBRARY_ICON -> userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.visible =
                        userResponse.chatActionsMenuStyle.selectPhotoFromLibraryIcon?.visible?.not() == true

                    IconSupportedViews.ESCALATE_ICON -> userResponse.chatInputFieldStyle.escalateIcon?.visible =
                        userResponse.chatInputFieldStyle.escalateIcon?.visible?.not() == true

                    else -> {}
                }
            }
        }
    }

    private fun updateSpinnerView(iconSupportedViews: IconSupportedViews, iconTypes: IconTypes) {
        val arrayResId: Int
        val resourceName: String?

        when (iconSupportedViews) {
            IconSupportedViews.BACK_BUTTON -> {
                resourceName = "chat_back_button_image"
                arrayResId = R.array.back_button_icons
            }

            IconSupportedViews.CONSUMER_MESSAGES -> {
                when (iconTypes) {
                    IconTypes.POSITION -> {
                        resourceName = "chat_consumer_message_icon_position"
                        arrayResId = R.array.consumer_message_icon_position
                    }

                    IconTypes.ICON -> {
                        resourceName = "chat_consumer_icon_options"
                        arrayResId = R.array.logo_icons
                    }

                    else -> return
                }
            }

            IconSupportedViews.AGENT_MESSAGES -> {
                resourceName = "chat_agent_message_icon_position"
                arrayResId = R.array.agent_message_icon_position
            }

            else -> return //do not have any icon support in this case so exit
        }

        val textViewId: Int = getResourceId("${resourceName}_label")
        val spinnerViewId: Int = getResourceId(resourceName)

        setTextViewColor(textViewId)
        findViewById<Spinner?>(spinnerViewId)?.apply {
            //for dropdown icon color
            background.colorFilter =
                BlendModeColorFilterCompat.createBlendModeColorFilterCompat(getUjetStyle().textPrimaryColor, BlendModeCompat.SRC_ATOP)
            adapter = getSpinnerAdapter(arrayResId)
            setSelection(getSelectedSpinnerPosition(iconSupportedViews, iconTypes))
            onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>, view: View, position: Int, id: Long) {
                    val response = parent.getItemAtPosition(position).toString()
                    when (iconSupportedViews) {
                        IconSupportedViews.BACK_BUTTON -> userResponse.backButton.image = response
                        IconSupportedViews.CONSUMER_MESSAGES -> {
                            when (iconTypes) {
                                IconTypes.POSITION -> userResponse.consumerMessageBubbles.positionSelected = position
                                IconTypes.ICON -> userResponse.consumerMessageBubbles.icon?.iconSelected = position
                                else -> {}
                            }
                        }

                        IconSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.positionSelected = position
                        else -> {} //do not have any icon support in this case so exit
                    }
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {
                }
            }
        }
    }

    private fun updateFontSpinnerViews(font: FontStyle?, fontSupportedViews: FontSupportedViews, fontSpinnerViewTypes: FontSpinnerViewTypes) {
        var arrayResId: Int = DEFAULT_INDEX
        var cachedSelectedIndex: Int = DEFAULT_INDEX

        var resourceName = getChatStyleType(fontSupportedViews)

        when (fontSpinnerViewTypes) {
            FontSpinnerViewTypes.FAMILY -> {
                arrayResId = R.array.font_family
                cachedSelectedIndex = font?.family?.let { Family.getEnumFromValue(it)?.ordinal } ?: DEFAULT_INDEX
                resourceName = "${resourceName}font_family"
            }
        }
        val textViewId: Int = getResourceId("${resourceName}_label")
        val spinnerId: Int = getResourceId(resourceName)

        setTextViewColor(textViewId)
        findViewById<Spinner>(spinnerId).apply {
            //for dropdown icon color
            background.colorFilter =
                BlendModeColorFilterCompat.createBlendModeColorFilterCompat(getUjetStyle().textPrimaryColor, BlendModeCompat.SRC_ATOP)
            adapter = getSpinnerAdapter(arrayResId)
            setSelection(cachedSelectedIndex)
            onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>, view: View, position: Int, id: Long) {
                    onUserSelected(fontSupportedViews, fontSpinnerViewTypes, position)
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {
                }
            }
        }
    }

    private fun updateFontEditTextView(font: FontStyle?, fontSupportedViews: FontSupportedViews, fontEditTextTypes: FontEditTextTypes) {
        var resourceName = getChatStyleType(fontSupportedViews)

        resourceName = when (fontEditTextTypes) {
            FontEditTextTypes.COLOR -> "${resourceName}font_color"
            FontEditTextTypes.SIZE -> "${resourceName}font_size"
            FontEditTextTypes.STYLE -> "${resourceName}font_style"
        }

        val textViewId: Int = getResourceId("${resourceName}_label")
        val editTextId: Int = getResourceId(resourceName)
        setTextViewColor(textViewId)
        val editTextView = findViewById<EditText?>(editTextId)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            when (fontEditTextTypes) {
                FontEditTextTypes.COLOR -> setText(font?.colorReference)
                FontEditTextTypes.SIZE -> setText(font?.size?.toString())
                FontEditTextTypes.STYLE -> setText(font?.style)
            }
            setTextColor(getUjetStyle().textSecondaryColor)
            setHintTextColor(getUjetStyle().pickerTextNoCenterColor)
        }
        when (fontSupportedViews) {
            FontSupportedViews.HEADER -> saveResponse(userResponse.header.font, fontEditTextTypes, editTextView)
            FontSupportedViews.END_BUTTON -> saveResponse(userResponse.endChatButton.font, fontEditTextTypes, editTextView)
            FontSupportedViews.SYSTEM_MESSAGES -> saveResponse(
                userResponse.systemMessages.messageBubbleResponse?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> saveResponse(
                userResponse.systemMessages.buttonResponse?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.TIME_STAMP -> saveResponse(userResponse.timeStamps.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONSUMER_MESSAGES -> saveResponse(
                userResponse.consumerMessageBubbles.messageBubbleResponse?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.AGENT_MESSAGES -> saveResponse(
                userResponse.agentMessageBubbles.messageBubbleResponse?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.INPUT_BAR -> saveResponse(userResponse.chatInputFieldStyle.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONTENT_CARDS -> saveResponse(userResponse.contentCard.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONTENT_CARDS_TITLE -> saveResponse(userResponse.contentCard.title?.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONTENT_CARDS_SUBTITLE -> saveResponse(userResponse.contentCard.subtitle?.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONTENT_CARDS_BODY -> saveResponse(userResponse.contentCard.body?.font, fontEditTextTypes, editTextView)
            FontSupportedViews.WEB_FORM_CARD -> saveResponse(userResponse.webFormCard.font, fontEditTextTypes, editTextView)
            FontSupportedViews.WEB_FORM_CARD_TITLE -> saveResponse(userResponse.webFormCard.title?.font, fontEditTextTypes, editTextView)
            FontSupportedViews.WEB_FORM_CARD_SUBTITLE -> saveResponse(userResponse.webFormCard.subtitle?.font, fontEditTextTypes, editTextView)
            FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> saveResponse(
                userResponse.contentCard.primaryButton?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> saveResponse(
                userResponse.contentCard.secondaryButton?.font,
                fontEditTextTypes,
                editTextView
            )

            FontSupportedViews.WELCOME_MESSAGE_TRAY, FontSupportedViews.BACK_BUTTON, FontSupportedViews.INPUT_TRAY, FontSupportedViews.CONTENT_CARDS_IMAGE,
            FontSupportedViews.WEB_FORM_CARD_IMAGE, FontSupportedViews.POST_SESSION -> return
        }
    }

    private fun updateEditTextView(cachedChatStyles: ChatStyles?, fontSupportedViews: FontSupportedViews, editTextTypes: EditTextTypes) {
        var resourceName = getChatStyleType(fontSupportedViews)

        resourceName = when (editTextTypes) {
            EditTextTypes.BACKGROUND_COLOR -> "${resourceName}background_color"
            EditTextTypes.CORNER_RADIUS -> "${resourceName}corner_radius"
            EditTextTypes.BORDER_WIDTH -> "${resourceName}border_width"
            EditTextTypes.BORDER_COLOR -> "${resourceName}border_color"
            EditTextTypes.ICON_SIZE -> "${resourceName}icon_size"
            EditTextTypes.IMAGE -> "${resourceName}image"
            EditTextTypes.DIVIDER_WIDTH -> "${resourceName}divider_width"
            EditTextTypes.DIVIDER_COLOR -> "${resourceName}divider_color"
            EditTextTypes.PLACEHOLDER -> "${resourceName}placeholder"
            EditTextTypes.CURSOR_COLOR -> "${resourceName}cursor_color"
            EditTextTypes.IMAGE_HEIGHT -> "${resourceName}height"
        }

        val textViewId: Int = getResourceId("${resourceName}_label")
        val editTextId: Int = getResourceId(resourceName)
        setTextViewColor(textViewId)
        val editTextView = findViewById<EditText?>(editTextId)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            val cachedText: String? = when (editTextTypes) {
                EditTextTypes.BACKGROUND_COLOR -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.SYSTEM_MESSAGES -> cachedChatStyles?.systemMessages?.backgroundColor
                        FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> cachedChatStyles?.systemMessages?.buttonStyle?.backgroundColor
                        FontSupportedViews.CONSUMER_MESSAGES -> cachedChatStyles?.consumerMessageBubbles?.backgroundColor
                        FontSupportedViews.AGENT_MESSAGES -> cachedChatStyles?.agentMessageBubbles?.backgroundColor
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.backgroundColor
                        FontSupportedViews.CONTENT_CARDS -> cachedChatStyles?.contentCard?.backgroundColor
                        FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> cachedChatStyles?.contentCard?.primaryButton?.backgroundColor
                        FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> cachedChatStyles?.contentCard?.secondaryButton?.backgroundColor
                        FontSupportedViews.WEB_FORM_CARD -> cachedChatStyles?.formCard?.backgroundColor
                        FontSupportedViews.POST_SESSION -> cachedChatStyles?.postSession?.backgroundColor
                        else -> null
                    }
                }

                EditTextTypes.CORNER_RADIUS -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.SYSTEM_MESSAGES -> cachedChatStyles?.systemMessages?.cornerRadius
                        FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> cachedChatStyles?.systemMessages?.buttonStyle?.cornerRadius
                        FontSupportedViews.CONSUMER_MESSAGES -> cachedChatStyles?.consumerMessageBubbles?.cornerRadius
                        FontSupportedViews.AGENT_MESSAGES -> cachedChatStyles?.agentMessageBubbles?.cornerRadius
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.inputField?.cornerRadius
                        FontSupportedViews.CONTENT_CARDS -> cachedChatStyles?.contentCard?.cornerRadius
                        FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> cachedChatStyles?.contentCard?.primaryButton?.cornerRadius
                        FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> cachedChatStyles?.contentCard?.secondaryButton?.cornerRadius
                        FontSupportedViews.WEB_FORM_CARD -> cachedChatStyles?.formCard?.cornerRadius
                        else -> null
                    }
                }

                EditTextTypes.BORDER_WIDTH -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.SYSTEM_MESSAGES -> cachedChatStyles?.systemMessages?.border?.width?.toString()
                        FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> cachedChatStyles?.systemMessages?.buttonStyle?.border?.width?.toString()
                        FontSupportedViews.CONSUMER_MESSAGES -> cachedChatStyles?.consumerMessageBubbles?.border?.width?.toString()
                        FontSupportedViews.AGENT_MESSAGES -> cachedChatStyles?.agentMessageBubbles?.border?.width?.toString()
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.inputField?.border?.width?.toString()
                        FontSupportedViews.INPUT_TRAY -> cachedChatStyles?.userInputBar?.topBorder?.width?.toString()
                        FontSupportedViews.CONTENT_CARDS -> cachedChatStyles?.contentCard?.border?.width?.toString()
                        FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> cachedChatStyles?.contentCard?.primaryButton?.border?.width?.toString()
                        FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> cachedChatStyles?.contentCard?.secondaryButton?.border?.width?.toString()
                        FontSupportedViews.WEB_FORM_CARD -> cachedChatStyles?.formCard?.border?.width?.toString()
                        FontSupportedViews.POST_SESSION -> cachedChatStyles?.postSession?.border?.width?.toString()
                        else -> null
                    }
                }

                EditTextTypes.BORDER_COLOR -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.SYSTEM_MESSAGES -> cachedChatStyles?.systemMessages?.border?.color
                        FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> cachedChatStyles?.systemMessages?.buttonStyle?.border?.color
                        FontSupportedViews.CONSUMER_MESSAGES -> cachedChatStyles?.consumerMessageBubbles?.border?.color
                        FontSupportedViews.AGENT_MESSAGES -> cachedChatStyles?.agentMessageBubbles?.border?.color
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.inputField?.border?.color
                        FontSupportedViews.INPUT_TRAY -> cachedChatStyles?.userInputBar?.topBorder?.color
                        FontSupportedViews.CONTENT_CARDS -> cachedChatStyles?.contentCard?.border?.color
                        FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> cachedChatStyles?.contentCard?.primaryButton?.border?.color
                        FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> cachedChatStyles?.contentCard?.secondaryButton?.border?.color
                        FontSupportedViews.WEB_FORM_CARD -> cachedChatStyles?.formCard?.border?.color
                        FontSupportedViews.POST_SESSION -> cachedChatStyles?.postSession?.border?.color
                        else -> null
                    }
                }

                EditTextTypes.ICON_SIZE -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.CONSUMER_MESSAGES -> cachedChatStyles?.consumerMessageBubbles?.icon?.size.toString()
                        FontSupportedViews.AGENT_MESSAGES -> cachedChatStyles?.agentMessageBubbles?.icon?.size.toString()
                        else -> null
                    }
                }

                EditTextTypes.IMAGE -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.BACK_BUTTON -> cachedChatStyles?.backButton?.image
                        else -> null
                    }
                }

                EditTextTypes.DIVIDER_WIDTH -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.WELCOME_MESSAGE_TRAY -> cachedChatStyles?.header?.divider?.width?.toString()
                        else -> null
                    }
                }

                EditTextTypes.DIVIDER_COLOR -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.WELCOME_MESSAGE_TRAY -> cachedChatStyles?.header?.divider?.color
                        else -> null
                    }
                }

                EditTextTypes.PLACEHOLDER -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.inputField?.placeholderText
                        else -> null
                    }
                }

                EditTextTypes.CURSOR_COLOR -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.INPUT_BAR -> cachedChatStyles?.userInputBar?.inputField?.cursorColor
                        else -> null
                    }
                }

                EditTextTypes.IMAGE_HEIGHT -> {
                    when (fontSupportedViews) {
                        FontSupportedViews.CONTENT_CARDS_IMAGE -> cachedChatStyles?.contentCard?.image?.height.toString()
                        FontSupportedViews.WEB_FORM_CARD_IMAGE -> cachedChatStyles?.formCard?.image?.height.toString()
                        else -> null
                    }
                }
            }
            if (cachedText?.isNotEmpty() == true && cachedText != "null") {
                setText(cachedText)
            }
            setTextColor(getUjetStyle().textSecondaryColor)
            setHintTextColor(getUjetStyle().pickerTextNoCenterColor)
        }
        when (fontSupportedViews) {
            FontSupportedViews.SYSTEM_MESSAGES, FontSupportedViews.SYSTEM_MESSAGES_BUTTON, FontSupportedViews.CONSUMER_MESSAGES,
            FontSupportedViews.AGENT_MESSAGES, FontSupportedViews.WELCOME_MESSAGE_TRAY, FontSupportedViews.INPUT_BAR,
            FontSupportedViews.INPUT_TRAY, FontSupportedViews.CONTENT_CARDS, FontSupportedViews.CONTENT_CARDS_TITLE,
            FontSupportedViews.CONTENT_CARDS_SUBTITLE, FontSupportedViews.CONTENT_CARDS_BODY, FontSupportedViews.CONTENT_CARDS_IMAGE,
            FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON,
            FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON,
            FontSupportedViews.WEB_FORM_CARD, FontSupportedViews.WEB_FORM_CARD_TITLE, FontSupportedViews.WEB_FORM_CARD_SUBTITLE,
            FontSupportedViews.WEB_FORM_CARD_IMAGE, FontSupportedViews.POST_SESSION -> {
                saveResponse(fontSupportedViews, editTextTypes, editTextView)
            }

            FontSupportedViews.HEADER, FontSupportedViews.END_BUTTON, FontSupportedViews.TIME_STAMP, FontSupportedViews.BACK_BUTTON -> {
                return
            }
        }
    }

    private fun getChatStyleType(fontSupportedViews: FontSupportedViews): String {
        return when (fontSupportedViews) {
            FontSupportedViews.HEADER -> "chat_header_"
            FontSupportedViews.INPUT_BAR -> "chat_input_bar_"
            FontSupportedViews.INPUT_TRAY -> "chat_input_tray_"
            FontSupportedViews.END_BUTTON -> "chat_end_button_"
            FontSupportedViews.WELCOME_MESSAGE_TRAY -> "chat_welcome_message_tray_"
            FontSupportedViews.SYSTEM_MESSAGES -> "chat_system_message_"
            FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> "chat_system_message_button_"
            FontSupportedViews.TIME_STAMP -> "chat_time_stamp_"
            FontSupportedViews.CONSUMER_MESSAGES -> "chat_consumer_message_"
            FontSupportedViews.AGENT_MESSAGES -> "chat_agent_message_"
            FontSupportedViews.BACK_BUTTON -> "chat_back_button_"
            FontSupportedViews.CONTENT_CARDS -> "chat_content_cards_"
            FontSupportedViews.CONTENT_CARDS_TITLE -> "chat_content_cards_title_"
            FontSupportedViews.CONTENT_CARDS_SUBTITLE -> "chat_content_cards_subtitle_"
            FontSupportedViews.CONTENT_CARDS_BODY -> "chat_content_cards_body_"
            FontSupportedViews.CONTENT_CARDS_IMAGE -> "chat_content_cards_background_image_"
            FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> "chat_content_cards_primary_button_"
            FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> "chat_content_cards_secondary_button_"
            FontSupportedViews.WEB_FORM_CARD -> "chat_web_form_card_"
            FontSupportedViews.WEB_FORM_CARD_TITLE -> "chat_web_form_card_title_"
            FontSupportedViews.WEB_FORM_CARD_SUBTITLE -> "chat_web_form_card_subtitle_"
            FontSupportedViews.WEB_FORM_CARD_IMAGE -> "chat_web_form_card_background_image_"
            FontSupportedViews.POST_SESSION -> "chat_post_session_"
        }
    }

    private fun onUserSelected(fontSupportedViews: FontSupportedViews, fontSpinnerViewTypes: FontSpinnerViewTypes, position: Int) {
        val fontResponse = when (fontSupportedViews) {
            FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.font
            FontSupportedViews.HEADER -> userResponse.header.font
            FontSupportedViews.END_BUTTON -> userResponse.endChatButton.font
            FontSupportedViews.SYSTEM_MESSAGES -> userResponse.systemMessages.messageBubbleResponse?.font
            FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> userResponse.systemMessages.buttonResponse?.font
            FontSupportedViews.TIME_STAMP -> userResponse.timeStamps.font
            FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.messageBubbleResponse?.font
            FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.messageBubbleResponse?.font
            FontSupportedViews.CONTENT_CARDS -> userResponse.contentCard.font
            FontSupportedViews.CONTENT_CARDS_TITLE -> userResponse.contentCard.title?.font
            FontSupportedViews.CONTENT_CARDS_SUBTITLE -> userResponse.contentCard.subtitle?.font
            FontSupportedViews.CONTENT_CARDS_BODY -> userResponse.contentCard.body?.font
            FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> userResponse.contentCard.primaryButton?.font
            FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> userResponse.contentCard.secondaryButton?.font
            FontSupportedViews.WEB_FORM_CARD -> userResponse.webFormCard.font
            FontSupportedViews.WEB_FORM_CARD_TITLE -> userResponse.webFormCard.title?.font
            FontSupportedViews.WEB_FORM_CARD_SUBTITLE -> userResponse.webFormCard.subtitle?.font
            else -> return
        }
        saveResponse(fontResponse, fontSpinnerViewTypes, position)
    }

    private fun saveResponse(response: FontResponse?, fontSpinnerViewTypes: FontSpinnerViewTypes, position: Int) {
        when (fontSpinnerViewTypes) {
            FontSpinnerViewTypes.FAMILY -> {
                response?.familySelected = position
            }
        }
    }

    private fun saveResponse(response: FontResponse?, fontEditTextTypes: FontEditTextTypes, editTextView: EditText?) {
        when (fontEditTextTypes) {
            FontEditTextTypes.COLOR -> {
                response?.colorEditText = editTextView
            }

            FontEditTextTypes.SIZE -> {
                response?.sizeEditText = editTextView
            }

            FontEditTextTypes.STYLE -> {
                response?.styleEditText = editTextView
            }
        }
    }

    private fun saveResponse(fontSupportedViews: FontSupportedViews, editTextTypes: EditTextTypes, editTextView: EditText?) {
        when (editTextTypes) {
            EditTextTypes.BACKGROUND_COLOR -> {
                when (fontSupportedViews) {
                    FontSupportedViews.SYSTEM_MESSAGES -> userResponse.systemMessages.messageBubbleResponse?.backgroundColorEditText = editTextView
                    FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> userResponse.systemMessages.buttonResponse?.backgroundColorEditText = editTextView
                    FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.messageBubbleResponse?.backgroundColorEditText =
                        editTextView

                    FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.messageBubbleResponse?.backgroundColorEditText =
                        editTextView

                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.backgroundColorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS -> userResponse.contentCard.backgroundColorEditText = editTextView
                    FontSupportedViews.WEB_FORM_CARD -> userResponse.webFormCard.backgroundColorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> userResponse.contentCard.primaryButton?.backgroundColorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> userResponse.contentCard.secondaryButton?.backgroundColorEditText =
                        editTextView

                    FontSupportedViews.POST_SESSION -> userResponse.postSession.backgroundColorEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.CORNER_RADIUS -> {
                when (fontSupportedViews) {
                    FontSupportedViews.SYSTEM_MESSAGES -> userResponse.systemMessages.messageBubbleResponse?.cornerRadiusEditText = editTextView
                    FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> userResponse.systemMessages.buttonResponse?.cornerRadiusEditText = editTextView
                    FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.messageBubbleResponse?.cornerRadiusEditText =
                        editTextView

                    FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.messageBubbleResponse?.cornerRadiusEditText = editTextView
                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.cornerRadiusEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS -> userResponse.contentCard.cornerRadiusEditText = editTextView
                    FontSupportedViews.WEB_FORM_CARD -> userResponse.webFormCard.cornerRadiusEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> userResponse.contentCard.primaryButton?.cornerRadiusEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> userResponse.contentCard.secondaryButton?.cornerRadiusEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.BORDER_WIDTH -> {
                when (fontSupportedViews) {
                    FontSupportedViews.SYSTEM_MESSAGES -> userResponse.systemMessages.messageBubbleResponse?.border?.widthEditText = editTextView
                    FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> userResponse.systemMessages.buttonResponse?.border?.widthEditText = editTextView
                    FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.messageBubbleResponse?.border?.widthEditText =
                        editTextView

                    FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.messageBubbleResponse?.border?.widthEditText = editTextView
                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.border?.widthEditText = editTextView
                    FontSupportedViews.INPUT_TRAY -> userResponse.chatInputFieldStyle.topBorder?.widthEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS -> userResponse.contentCard.border?.widthEditText = editTextView
                    FontSupportedViews.WEB_FORM_CARD -> userResponse.webFormCard.border?.widthEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> userResponse.contentCard.primaryButton?.border?.widthEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> userResponse.contentCard.secondaryButton?.border?.widthEditText =
                        editTextView

                    FontSupportedViews.POST_SESSION -> userResponse.postSession.border?.widthEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.BORDER_COLOR -> {
                when (fontSupportedViews) {
                    FontSupportedViews.SYSTEM_MESSAGES -> userResponse.systemMessages.messageBubbleResponse?.border?.colorEditText = editTextView
                    FontSupportedViews.SYSTEM_MESSAGES_BUTTON -> userResponse.systemMessages.buttonResponse?.border?.colorEditText = editTextView
                    FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.messageBubbleResponse?.border?.colorEditText =
                        editTextView

                    FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.messageBubbleResponse?.border?.colorEditText = editTextView
                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.border?.colorEditText = editTextView
                    FontSupportedViews.INPUT_TRAY -> userResponse.chatInputFieldStyle.topBorder?.colorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS -> userResponse.contentCard.border?.colorEditText = editTextView
                    FontSupportedViews.WEB_FORM_CARD -> userResponse.webFormCard.border?.colorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_PRIMARY_BUTTON -> userResponse.contentCard.primaryButton?.border?.colorEditText = editTextView
                    FontSupportedViews.CONTENT_CARDS_SECONDARY_BUTTON -> userResponse.contentCard.secondaryButton?.border?.colorEditText =
                        editTextView

                    FontSupportedViews.POST_SESSION -> userResponse.postSession.border?.colorEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.ICON_SIZE -> {
                when (fontSupportedViews) {
                    FontSupportedViews.CONSUMER_MESSAGES -> userResponse.consumerMessageBubbles.icon?.sizeEditText = editTextView
                    FontSupportedViews.AGENT_MESSAGES -> userResponse.agentMessageBubbles.icon?.sizeEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.IMAGE -> {

            }

            EditTextTypes.DIVIDER_WIDTH -> {
                when (fontSupportedViews) {
                    FontSupportedViews.WELCOME_MESSAGE_TRAY -> userResponse.welcomeMessageTray.divider?.widthEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.DIVIDER_COLOR -> {
                when (fontSupportedViews) {
                    FontSupportedViews.WELCOME_MESSAGE_TRAY -> userResponse.welcomeMessageTray.divider?.colorEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.PLACEHOLDER -> {
                when (fontSupportedViews) {
                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.placeHolderEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.CURSOR_COLOR -> {
                when (fontSupportedViews) {
                    FontSupportedViews.INPUT_BAR -> userResponse.chatInputFieldStyle.cursorColorEditText = editTextView
                    else -> {}
                }
            }

            EditTextTypes.IMAGE_HEIGHT -> {
                when (fontSupportedViews) {
                    FontSupportedViews.CONTENT_CARDS_IMAGE -> userResponse.contentCard.image?.heightEditText = editTextView
                    FontSupportedViews.WEB_FORM_CARD_IMAGE -> userResponse.webFormCard.image?.heightEditText = editTextView
                    else -> {}
                }
            }
        }
    }

    private fun getEditTextResponse(editTextView: EditText?) = editTextView?.text?.toString()?.ifEmpty { null }

    private fun getResourceId(resourceName: String) = resources.getIdentifier(resourceName, "id", packageName)

    private fun getSpinnerAdapter(arrayResId: Int): SpinnerAdapter {
        val spinnerAdapter = ArrayAdapter.createFromResource(this, arrayResId, R.layout.spinner_list_item)
        spinnerAdapter.setDropDownViewResource(R.layout.spinner_list_item)
        return spinnerAdapter
    }

    private fun getColorStateList() = DesignUtil.getColorStateList(
        getUjetStyle().colorPrimary,
        DesignUtil.getColor(this.applicationContext, co.ujet.android.ui.R.color.ujet_disabled)
    )

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.style_options_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_from_json -> showJsonInputDialog()
            R.id.action_clear_styles -> clearStyleDefinitions()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun getSelectedSpinnerPosition(iconSupportedViews: IconSupportedViews, iconTypes: IconTypes): Int {
        val index = when (iconSupportedViews) {
            IconSupportedViews.BACK_BUTTON -> resources.getStringArray(R.array.back_button_icons)
                .indexOf(cachedChatStyles.backButton?.image ?: DEFAULT_INDEX)

            IconSupportedViews.CONSUMER_MESSAGES -> {
                when (iconTypes) {
                    IconTypes.POSITION -> resources.getStringArray(R.array.consumer_message_icon_position)
                        .indexOf(cachedChatStyles.consumerMessageBubbles?.icon?.position)

                    IconTypes.ICON -> cachedChatStyles.consumerMessageBubbles?.icon?.icon?.let { IconReferences.getEnumFromValue(it)?.ordinal }
                        ?: DEFAULT_INDEX

                    else -> DEFAULT_INDEX
                }
            }

            IconSupportedViews.AGENT_MESSAGES -> {
                val array = resources.getStringArray(R.array.agent_message_icon_position)
                array.indexOf(cachedChatStyles.agentMessageBubbles?.icon?.position)
            }

            else -> DEFAULT_INDEX //do not have any icon support in this case so return default value
        }

        return if (index == -1) {
            DEFAULT_INDEX
        } else {
            index
        }
    }

    private fun showJsonInputDialog() {
        val jsonFileContent = AssetsUtil.getAssetsFileContent(this, "json/ujet_styles.json")
        val view = LayoutInflater.from(this).inflate(R.layout.dialog_text_input, null)
        val inputTextField = view.findViewById<EditText>(R.id.input)
        inputTextField.setText(jsonFileContent)
        AlertDialog
            .Builder(this)
            .setView(view)
            .setPositiveButton(co.ujet.android.R.string.ujet_common_ok) { _, _ ->
                TestAppSettings.chatStyles = EntryPointFactory
                    .provideEntryPoint(UjetUI::class.java)
                    .buildChatStylesFromJson(inputTextField.text.toString())
                    ?: run {
                        Toast.makeText(this, "Unable to parse JSON file", Toast.LENGTH_SHORT).show()
                        return@setPositiveButton
                    }
                restartApplication()
                Toast.makeText(this, "Saved styles JSON", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton(co.ujet.android.R.string.ujet_common_cancel) { _, _ ->

            }
            .show()
    }

    private fun clearStyleDefinitions() {
        TestAppSettings.chatStyles = ChatStyles()
        restartApplication()
        Toast.makeText(this, "Saved styles JSON", Toast.LENGTH_SHORT).show()
    }

    enum class FontSupportedViews {
        HEADER, END_BUTTON, WELCOME_MESSAGE_TRAY, SYSTEM_MESSAGES, SYSTEM_MESSAGES_BUTTON, TIME_STAMP, CONSUMER_MESSAGES, AGENT_MESSAGES,
        BACK_BUTTON, INPUT_BAR, INPUT_TRAY, CONTENT_CARDS, CONTENT_CARDS_TITLE, CONTENT_CARDS_SUBTITLE, CONTENT_CARDS_BODY, CONTENT_CARDS_IMAGE,
        CONTENT_CARDS_PRIMARY_BUTTON, CONTENT_CARDS_SECONDARY_BUTTON, WEB_FORM_CARD, WEB_FORM_CARD_TITLE, WEB_FORM_CARD_SUBTITLE, WEB_FORM_CARD_IMAGE, POST_SESSION
    }

    enum class FontSpinnerViewTypes {
        FAMILY
    }

    enum class FontEditTextTypes {
        COLOR, SIZE, STYLE
    }

    enum class EditTextTypes {
        BACKGROUND_COLOR, CORNER_RADIUS, BORDER_WIDTH, BORDER_COLOR, ICON_SIZE, IMAGE, DIVIDER_WIDTH, DIVIDER_COLOR, CURSOR_COLOR, PLACEHOLDER, IMAGE_HEIGHT
    }

    enum class IconSupportedViews {
        BACK_BUTTON, END_BUTTON, WELCOME_MESSAGE_TRAY, WELCOME_MESSAGE_TRAY_AGENT_ICON,
        CONSUMER_MESSAGES, AGENT_MESSAGES, CAMERA_ICON, COBROWSE_ICON, ESCALATE_ICON, SELECT_PHOTO_FROM_LIBRARY_ICON, CHAT_ACTIONS_MENU_ICON
    }

    enum class IconTypes {
        NONE, POSITION, ICON
    }

    enum class Family(val value: String?) {
        None(null),
        SansSerif("Roboto-Regular.ttf"),
        SansSerifLight("Roboto-Light.ttf"),
        SansSerifMedium("Roboto-Medium.ttf"),
        SansSerifBlack("Roboto-Black.ttf"),
        SansSerifCondensedLight("RobotoCondensed-Light.ttf"),
        SansSerifCondensed("RobotoCondensed-Regular.ttf"),
        SansSerifCondensedMedium("RobotoCondensed-Medium.ttf"),
        SerifMonospace("RobotoMono-Regular.ttf"),
        Casual("ComingSoon-Regular.ttf"),
        Cursive("DancingScript-Regular.ttf"),
        SansSerifSmallCaps("CarroisGothicSC-Regular.ttf");

        companion object {
            fun getEnumFromValue(value: String) = values().firstOrNull { it.value == value }
        }
    }

    enum class IconReferences(val value: String) {
        None(""),
        Fitbit("ujet_fitbit_logo"),
        Netflix("ujet_netflix_logo"),
        Ring("ujet_ring_logo"),
        Insta("ujet_insta_logo");

        companion object {
            fun getEnumFromValue(value: String) = values().firstOrNull { it.value == value }
        }
    }

    companion object {
        private const val DEFAULT_INDEX = 0
    }
}
