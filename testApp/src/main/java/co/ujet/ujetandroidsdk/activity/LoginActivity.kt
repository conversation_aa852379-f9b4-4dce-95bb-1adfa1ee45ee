package co.ujet.ujetandroidsdk.activity

import android.Manifest.permission
import android.content.Intent
import android.content.IntentSender.SendIntentException
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Looper
import android.os.Process
import android.util.Log
import android.util.Patterns
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.app.ActivityCompat
import co.ujet.android.Ujet
import co.ujet.android.UjetStartOptions
import co.ujet.android.UjetStatus
import co.ujet.android.common.util.PermissionUtil.isWholePermissionsGranted
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.TestAppSettings.isIsInitialized
import co.ujet.ujetandroidsdk.UjetInitializer.init
import co.ujet.ujetandroidsdk.common.ShakeToSettings
import co.ujet.ujetandroidsdk.util.AddressUtil
import co.ujet.ujetandroidsdk.util.WeakReferenceLocationCallback
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.*
import mehdi.sakout.fancybuttons.FancyButton
import org.json.JSONException
import org.json.JSONObject
import co.ujet.ujetandroidsdk.R

class LoginActivity : BaseActivity() {
    private var shakeToSettings: ShakeToSettings? = null
    private var hiddenUjetAutoEnvInvoker: HiddenUjetAutoEnvInvoker? = null
    private var identifierEditText: EditText? = null
    private var emailEditText: EditText? = null
    private var nameEditText: EditText? = null
    private var phoneEditText: EditText? = null
    private var settingsEditText: EditText? = null
    private var ujetPreferences: SharedPreferences? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        shakeToSettings = ShakeToSettings(this)
        hiddenUjetAutoEnvInvoker = HiddenUjetAutoEnvInvoker()
        ujetPreferences = getSharedPreferences("UjetPreferences", MODE_PRIVATE)
        setContentView(R.layout.activity_login)
        setupLayout()
        startSettingActivityIfNotInitialized()
        val resultCode = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this)
        if (resultCode != ConnectionResult.SUCCESS) {
            GoogleApiAvailability.getInstance().makeGooglePlayServicesAvailable(this)
        }
    }

    override fun onResume() {
        super.onResume()
        shakeToSettings?.start()
    }

    override fun onPause() {
        super.onPause()
        shakeToSettings?.stop()
    }

    override fun onDestroy() {
        super.onDestroy()
        identifierEditText = null
        emailEditText = null
        nameEditText = null
        phoneEditText = null
        settingsEditText = null
        shakeToSettings?.clear()
    }

    private fun setupLayout() {
        identifierEditText = findViewById<EditText?>(R.id.identifier).apply {
            setText(ujetPreferences?.getString("identifier", null))
        }
        emailEditText = findViewById<EditText?>(R.id.email).apply {
            setText(ujetPreferences?.getString("userId", null))
        }
        nameEditText = findViewById<EditText?>(R.id.name).apply {
            setText(ujetPreferences?.getString("name", null))
        }
        phoneEditText = findViewById<EditText?>(R.id.phone_number).apply {
            setText(ujetPreferences?.getString("phoneNumber", null))
        }
        settingsEditText = findViewById(R.id.configuration_settings)
        findViewById<FancyButton>(R.id.btn_login).apply {
            isLongClickable = true
            setOnClickListener { onLoginClick() }
            setOnLongClickListener {
                startSettingActivity()
                true
            }
        }
        findViewById<View>(R.id.hiddenButton).setOnClickListener { hiddenUjetAutoEnvInvoker?.trigger() }
    }

    private fun onLoginClick() {
        if (startSettingActivityIfNotInitialized() || requestLocationPermission()) {
            return
        }
        checkLocationSettings()
    }

    private fun validateEmail(emailString: String?): Boolean {
        if (!emailString.isNullOrEmpty() && !Patterns.EMAIL_ADDRESS.matcher(emailString).matches()) {
            Toast.makeText(applicationContext, "Enter valid email", Toast.LENGTH_SHORT).show()
            emailEditText?.requestFocus()
            val inputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.showSoftInput(emailEditText, InputMethodManager.SHOW_IMPLICIT)
            return false
        }
        return true
    }

    private fun startSettingActivityIfNotInitialized(): Boolean {
        if (isIsInitialized) return false
        Toast.makeText(this, "Please input Company Key and Company Secret", Toast.LENGTH_LONG).show()
        startSettingActivity()
        return true
    }

    private fun startSettingActivity() {
        val intent = Intent(this@LoginActivity, SettingsActivity::class.java)
        startActivityForResult(intent, SettingsActivity.REQUEST_FOR_SETTING)
    }

    private fun requestLocationPermission(): Boolean {
        val fineLocationPermissionState = ActivityCompat.checkSelfPermission(this, permission.ACCESS_FINE_LOCATION)
        val coarseLocationPermissionState = ActivityCompat.checkSelfPermission(this, permission.ACCESS_COARSE_LOCATION)
        if (fineLocationPermissionState == PackageManager.PERMISSION_GRANTED &&
            coarseLocationPermissionState == PackageManager.PERMISSION_GRANTED) {
            return false
        }
        ActivityCompat.requestPermissions(
            this,
            arrayOf(permission.ACCESS_FINE_LOCATION, permission.ACCESS_COARSE_LOCATION),
            LOCATION_PERMISSION_REQUEST_CODE
        )
        return true
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (isWholePermissionsGranted(grantResults)) {
                startDashboardActivity()
            } else {
                Toast.makeText(applicationContext, "Location permissions needed", Toast.LENGTH_LONG).show()
                finish()
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    /**
     * https://developer.android.com/training/location/change-location-settings.html
     */
    private fun checkLocationSettings() {
        val locationRequest = LocationRequest()
        locationRequest.numUpdates = 1
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        val builder = LocationSettingsRequest.Builder()
            .addLocationRequest(locationRequest)
        LocationServices.getSettingsClient(this)
            .checkLocationSettings(builder.build())
            .addOnSuccessListener(this) {
                updateLocation()
                startDashboardActivity()
            }
            .addOnFailureListener { e ->
                when ((e as ApiException).statusCode) {
                    CommonStatusCodes.RESOLUTION_REQUIRED ->
                        // Location settings are not satisfied, but this can be fixed by showing the user a dialog.
                        try {
                            // Show the dialog by calling startResolutionForResult(), and check the result in onActivityResult().
                            val resolvable = e as ResolvableApiException
                            resolvable.startResolutionForResult(
                                this@LoginActivity,
                                CHECK_SETTINGS_REQUEST_CODE
                            )
                        } catch (sendEx: SendIntentException) {
                            // Ignore the error.
                        }
                    LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE ->                                 // Location settings are not satisfied. However, we have no way
                        // to fix the settings so we won't show the dialog.
                        startDashboardActivity()
                    CommonStatusCodes.API_NOT_CONNECTED -> startDashboardActivity()
                }
            }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == CHECK_SETTINGS_REQUEST_CODE) {
            updateLocation()
            startDashboardActivity()
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun updateLocation() {
        if (ActivityCompat.checkSelfPermission(this, permission.ACCESS_FINE_LOCATION) !=
            PackageManager.PERMISSION_GRANTED) {
            startDashboardActivity()
            return
        }
        val locationRequest = LocationRequest()
        locationRequest.numUpdates = 1
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        val locationCallback: LocationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                val location = locationResult.lastLocation
                AddressUtil.setLocation(location)
                Log.d("Location", AddressUtil.getLastKnownLocation(applicationContext))
            }
        }
        LocationServices.getFusedLocationProviderClient(applicationContext).requestLocationUpdates(
            locationRequest,
            WeakReferenceLocationCallback(locationCallback),
            Looper.getMainLooper()
        )
    }

    private fun startDashboardActivity() {
        val identifier = identifierEditText?.text.toString()
        val email = emailEditText?.text.toString()
        val name = nameEditText?.text.toString()
        val phone = phoneEditText?.text.toString()
        val savedEmail = ujetPreferences?.getString("userId", null)
        val savedName = ujetPreferences?.getString("name", null)
        val savedPhone = ujetPreferences?.getString("phoneNumber", null)
        if (email != savedEmail || name != savedName || phone != savedPhone) {
            Ujet.clearUserData()
        }
        if (!validateEmail(email)) return
        val status = Ujet.getStatus()
        if (status != UjetStatus.None) {
            val message = if (status == UjetStatus.InVoiceCall) "Previous logged in user is still in the call, continue?"
                          else "Previous logged in user is still in the chat, continue?"
            val builder = AlertDialog.Builder(
                ContextThemeWrapper(this@LoginActivity, R.style.GenericoTheme)
            )
            builder.setTitle(message)

            // Set up the buttons
            builder.setPositiveButton("OK") { dialog, _ ->
                dialog.cancel()
                Ujet.start(UjetStartOptions.Builder().build())
            }
            builder.setNegativeButton("Clear") { dialog, _ ->
                dialog.cancel()
                Ujet.disconnect {
                    Ujet.clearUserData()
                    Toast.makeText(
                        applicationContext,
                        "Cleared the session data and user logged out successfully",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
            builder.show()
            return
        }
        val configurationSettings = settingsEditText?.text.toString()
        ujetPreferences?.edit()
            ?.putString("identifier", identifier)
            ?.putString("userId", email)
            ?.putString("name", name)
            ?.putString("phoneNumber", phone)
            ?.apply()
        if (!configurationSettings.isNullOrEmpty()) {
            if (parseConfigurationSettings(configurationSettings)) {
                restart()
            }
        } else {
            val intent = Intent(this@LoginActivity, DashboardActivity::class.java)
            startActivity(intent)
        }
    }

    private fun parseConfigurationSettings(configurationSettings: String): Boolean {
        val settingsPreferences = getSharedPreferences(TestAppSettings.TEST_APP_PREFERENCES_NAME, MODE_PRIVATE)
        try {
            val mainObject = JSONObject(configurationSettings)
            val settingsArray = mainObject.getJSONArray("settings")
            for (i in 0 until settingsArray.length()) {
                val settingsObject = settingsArray.getJSONObject(i)
                settingsPreferences.edit()
                    .putString("env", settingsObject.getString("environment"))
                    .putString("tenant", settingsObject.getString("company"))
                    .putString("company_name", settingsObject.optString("company_name"))
                    .putString("company_key", settingsObject.optString("company_key"))
                    .putString("company_secret", settingsObject.optString("company_secret"))
                    .putString("host", settingsObject.optString("host"))
                    .putString("port", settingsObject.optString("port"))
                    .putString("networkSensitivity", settingsObject.optString("networkSensitivity"))
                    .putString("theme", settingsObject.optString("theme"))
                    .apply()
                Thread.sleep(1000) //Devices with API level 28 could not save settings in cache before restart but delaying restart did save it in cache.
            }
        } catch (e: JSONException) {
            Toast.makeText(applicationContext,"Can not parse configuration settings. Please check again.",
                Toast.LENGTH_LONG).show()
            return false
        } catch (e: InterruptedException) {
            Toast.makeText(applicationContext, "Can not parse configuration settings. Please check again.",
                Toast.LENGTH_LONG).show()
            return false
        }
        return true
    }

    private fun restart() {
        finish()
        startActivity(intent)
        killCurrentProcess()
    }

    private fun killCurrentProcess() {
        Process.killProcess(Process.myPid())
        Runtime.getRuntime().exit(10)
    }

    private inner class HiddenUjetAutoEnvInvoker {
        private var triggerCount = 0
        fun trigger() {
            triggerCount += 1
            if (triggerCount >= InvokeCounter) {
                invoke()
            }
        }

        private operator fun invoke() {
            triggerCount = 0
            init()
            Toast.makeText(applicationContext, "Changed to UjetAuto env", Toast.LENGTH_LONG).show()
        }
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 12501
        private const val CHECK_SETTINGS_REQUEST_CODE = 12511
        private const val InvokeCounter = 4
    }
}