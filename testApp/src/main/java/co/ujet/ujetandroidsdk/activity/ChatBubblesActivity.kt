package co.ujet.ujetandroidsdk.activity

import android.os.Bundle
import android.view.Menu
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.RecyclerView
import co.ujet.android.app.chat.ChatAdapter
import co.ujet.android.app.chat.data.ChatMessageChangeListener
import co.ujet.android.app.chat.data.ChatMessageDataSource
import co.ujet.android.app.chat.view.ChatStatusView
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.MediaFile.Companion.create
import co.ujet.android.commons.domain.MediaFile.Companion.createRemote
import co.ujet.android.commons.domain.MediaFile.Status.Uploaded
import co.ujet.android.commons.domain.MediaFile.Type
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.agent.VirtualAgent
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.ContentCardButton
import co.ujet.android.commons.domain.chat.message.*
import co.ujet.android.commons.domain.chat.message.EndUserChatMessage.Companion.createSent
import co.ujet.android.commons.domain.chat.message.HumanAgentVideoChatMessage.Companion.createSent
import co.ujet.android.commons.domain.chat.message.VirtualAgentVideoChatMessage.Companion.createSent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import co.ujet.android.data.chat.ChatMessageParser
import co.ujet.android.data.model.ChatHistory
import co.ujet.android.data.repository.ChatLocalIdGenerator
import co.ujet.android.internal.Injection
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions.Builder
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions.Companion.QuickReplyButtonsStyle
import co.ujet.android.service.chat.ChatFactory
import co.ujet.android.service.chat.ChatHistoryManager
import co.ujet.android.service.chat.ChatManager
import co.ujet.android.service.chat.ChatMessageManager
import co.ujet.android.service.common.AgentManager
import co.ujet.android.service.common.CustomDataDelegate
import co.ujet.android.ui.util.StyleUtil.updateFontStyle
import co.ujet.android.ui.widgets.ChatInputBarLayout
import co.ujet.android.ui.widgets.ChatInputBarLayout.ChatInputBarListener
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings.chatStyles
import co.ujet.ujetandroidsdk.TestAppSettings.quickReplyButtonsGrouped
import co.ujet.ujetandroidsdk.util.AssetsUtil.getAssetsFileContent
import java.util.*

class ChatBubblesActivity : AppCompatActivity() {
    var quickStickyReplyList: ChatMessage? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_chat_bubbles)
        setTheme(co.ujet.android.R.style.Theme_UjetButton_GroupedQuickReplyListButton)
        setToolBar()
        window.statusBarColor = getUjetStyle().colorPrimaryDark
        setupMessagesListView()
        val rootView = findViewById<View>(R.id.root_view)
        rootView.setBackgroundColor(getUjetStyle().primaryBackgroundColor)
        val inputBarLayout = findViewById<ChatInputBarLayout>(R.id.input_bar)
        inputBarLayout.setHint("Type Message")
        inputBarLayout.init(
            getUjetStyle(),
            object : ChatInputBarListener {
                override fun setActionIconAlignment(alignIconsVertically: Boolean) {
                    // No action here, just a dummy container
                }

                override fun getActionIconAlignment(): Boolean {
                    // No action here, just a dummy container
                    return false
                }

                override fun setTextCountPerLine(textCountPerLine: Int) {
                    // No action here, just a dummy container
                }

                override fun getTextCountPerLine(): Int {
                    // No action here, just a dummy container
                    return 0
                }

                override fun onQuickReplyClicked(lastMessage: VirtualAgentQuickReplyButtonsChatMessage, quickReply: QuickReplyButton) {
                    // No action here, just a dummy container
                }

                override fun onSendChatMessagePreview(ongoingInputMessage: String) {
                    // No action here, just a dummy container
                }

                override fun onChatMessageSend(message: String) {
                    // No action here, just a dummy container
                }

                override fun onSetChatInput(input: String) {
                    // No action here, just a dummy container
                }

                override fun isChatPreviewAvailable(): Boolean? {
                    // No action here, just a dummy container
                    return null
                }

                override fun onChatActionsMenuIconClicked() {
                    // No action here, just a dummy container
                }

                override fun showEscalateConfirmation() {
                    // No action here, just a dummy container
                }
            },
            false,
            Builder()
                .setChatQuickReplyButtonsStyle(
                    if (quickReplyButtonsGrouped) QuickReplyButtonsStyle.GROUPED else QuickReplyButtonsStyle.INDIVIDUAL
                )
                .setChatStyles(chatStyles)
                .build(),
            getString(co.ujet.android.R.string.ujet_escalation_to_human_agent),
        )
        quickStickyReplyList.let {
            inputBarLayout.updateQuickReplyButtons(it)
        }

    }

    private fun getUjetStyle() = Injection.provideUjetStyle(this)

    private fun setToolBar() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)
        toolbar.setNavigationOnClickListener { v: View? -> finish() }
        toolbar.setBackgroundColor(getUjetStyle().colorPrimary)
    }

    private fun setupMessagesListView() {
        val json = getAssetsFileContent(this, "json/ujet_chat_history_sample.json")
        val chatHistory: ChatHistory? = Injection.provideSerializer().deserialize(json, ChatHistory::class.java)
        val chatHistoryManager = getChatHistoryManager()
        val messages = chatHistoryManager.getChatHistoryMessages(true, chatHistory, 1)
        val updatedMessages = getDummyMessages(messages as MutableList<ChatMessage>)
        val chatMessageAdapter = ChatAdapter(this, getUjetStyle(), this)
        chatMessageAdapter.setDataSource(DummyMessagesDataSource(updatedMessages))
        chatMessageAdapter.addHeaderView(ChatStatusView(this, getUjetStyle(), chatStyles.header, chatStyles.header?.textContent, ""))
        findViewById<RecyclerView>(R.id.message_recycler_view)?.apply {
            adapter = chatMessageAdapter
            importantForAccessibility = RecyclerView.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
    }

    private fun getChatHistoryManager(): ChatHistoryManager {
        val localIdGenerator = ChatLocalIdGenerator()
        val chatManager = ChatManager(
            Injection.provideApiManager(this),
            ChatFactory(
                applicationContext,
                Injection.provideUjetContext(this),
                Injection.provideApiManager(this),
                Injection.provideChooseLanguage(this),
                Injection.provideUseCaseHandler(),
                Injection.provideLocalRepository(this),
                CustomDataDelegate()
            )
        )
        val chatMessageManager = ChatMessageManager()
        val chatMessageParser = ChatMessageParser(
            this,
            Injection.provideSerializer(),
            chatMessageManager,
            Injection.provideUploadRepository(this),
            Injection.provideUjetContext(this),
            AgentManager(),
            chatManager,
            localIdGenerator
        )
        return ChatHistoryManager(
            this, Injection.provideApiManager(this), chatMessageManager,
            localIdGenerator, chatMessageParser
        )
    }

    private fun getDummyMessages(messages: MutableList<ChatMessage>): List<ChatMessage> {
        val timeNow: Calendar = GregorianCalendar()
        val virtualAgent = VirtualAgent()
        var id = messages.size + 1
        val greetingMessage: ChatMessage = GreetingChatMessage(id, id.toString(), timeNow.time, "Greeting message!", id.toLong())
        messages.add(greetingMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val virtualAgentChatMessage: ChatMessage = VirtualAgentChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Virtual agent message!\nwith multiple lines",
            virtualAgent,
            id.toLong()
        )
        messages.add(virtualAgentChatMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val virtualAgentChatMessage2: ChatMessage = VirtualAgentChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Virtual agent message!\nwith multiple lines",
            virtualAgent,
            id.toLong()
        )
        messages.add(virtualAgentChatMessage2)

        id++
        timeNow.add(Calendar.MINUTE, 5)
        var contentCard: ChatMessage = ContentCardChatMessage.createSent(
            id,
            id.toString(),
            timeNow.time,
            listOf(
                ContentCard(
                    title = "Title 1",
                    subtitle = "",
                    body = "This card has an empty subtitle",
                    link = "ujetqa://contentCard/parameters",
                    eventParams = hashMapOf(
                        "title" to "Title 1",
                        "subtitle" to "Subtitle 1",
                        "body" to "Body body body 1",
                        "has_button" to false,
                        "parameter 1" to "value 1.1",
                        "parameter 2" to "value 1.2"
                    )
                ),
                ContentCard(
                    title = "Title 2",
                    body = "This card has no subtitle",
                    link = "ujetqa://contentCard/parameters?param1=value1",
                    eventParams = hashMapOf(
                        "title" to "Title 2",
                        "subtitle" to "Subtitle 2",
                        "body" to "Body body body 2",
                        "has_button" to false,
                        "parameter 1" to "value 2.1",
                        "parameter 2" to "value 2.2"
                    )
                ),
                ContentCard(
                    title = "Title 3",
                    subtitle = "Subtitle 3\nSubtitle 3\nSubtitle 3",
                    body = "Body body body 3\nBody body body 3\nBody body body 3",
                    link = "ujetqa://contentCard/parameters?param1=value1&param2=value2",
                    eventParams = hashMapOf(
                        "title" to "Title 3",
                        "subtitle" to "Subtitle 3",
                        "body" to "Body body body 3",
                        "has_button" to false,
                        "parameter 1" to "value 3.1",
                        "parameter 2" to "value 3.2"
                    )
                )
            ),
            "",
            id.toLong()
        )
        messages.add(contentCard)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val virtualAgentChatMessage3: ChatMessage =
            VirtualAgentChatMessage(id, id.toString(), timeNow.time, "Virtual agent message!", virtualAgent, id.toLong())
        messages.add(virtualAgentChatMessage3)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val virtualAgentChatMessage4: ChatMessage = VirtualAgentChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Virtual agent message!\nwith multiple lines",
            virtualAgent,
            id.toLong()
        )
        messages.add(virtualAgentChatMessage4)

        id++
        timeNow.add(Calendar.MINUTE, 5)
        var image1 = getRemoteImageUrl()
        var image2 = getRemoteImageUrl()
        var image3 = getRemoteImageUrl()
        contentCard = ContentCardChatMessage.createSent(
            id,
            id.toString(),
            timeNow.time,
            listOf(
                ContentCard(
                    title = "Title 1",
                    subtitle = "Subtitle 1",
                    body = "Body body body 1\nBody body body 1\nBody body body 1\nBody body body 1\nBody body body 1\nBody body body 1",
                    images = listOf(image1),
                    buttons = listOf(
                        ContentCardButton(
                            title = "Btn1",
                            style = "primary"
                        ),
                        ContentCardButton(
                            title = "Btn2",
                            style = "secondary"
                        ),
                    ),
                    link = "https://www.google.com/search?q=frank+sinatra",
                    eventParams = hashMapOf(
                        "title" to "Title 1",
                        "subtitle" to "Subtitle 1",
                        "body" to "Body body body 1",
                        "image" to image1,
                        "link" to "https://www.google.com/search?q=frank+sinatra",
                        "has_button" to true,
                        "parameter 1" to "value 1.1",
                        "parameter 2" to "value 1.2"
                    )
                ),
                ContentCard(
                    title = "Title 2",
                    subtitle = "Subtitle 2",
                    body = "Body body body 2",
                    images = listOf(image2),
                    buttons = listOf(
                        ContentCardButton(
                            title = "Btn1",
                            style = "primary"
                        ),
                        ContentCardButton(
                            title = "Btn2",
                            style = "secondary"
                        ),
                    ),
                    link = "https://www.google.com/search?q=ujet",
                    eventParams = hashMapOf(
                        "title" to "Title 2",
                        "subtitle" to "Subtitle 2",
                        "body" to "Body body body 2",
                        "image" to image2,
                        "link" to "https://www.google.com/search?q=ujet",
                        "has_button" to true,
                        "parameter 1" to "value 2.1",
                        "parameter 2" to "value 2.2"
                    )
                ),
                ContentCard(
                    title = "Title 3",
                    subtitle = "Subtitle 3",
                    body = "Body body body 3",
                    images = listOf(image3),
                    buttons = listOf(
                        ContentCardButton(
                            title = "Btn1",
                            style = "primary"
                        ),
                        ContentCardButton(
                            title = "Btn2",
                            style = "secondary"
                        ),
                    ),
                    link = "https://www.google.com/search?q=dream+theater",
                    eventParams = hashMapOf(
                        "title" to "Title 3",
                        "subtitle" to "Subtitle 3",
                        "body" to "Body body body 3",
                        "image" to image3,
                        "link" to "https://www.google.com/search?q=dream+theater",
                        "has_button" to true,
                        "parameter 1" to "value 3.1",
                        "parameter 2" to "value 3.2"
                    )
                )
            ),
            "",
            id.toLong()
        )
        messages.add(contentCard)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val quickReplyList: ChatMessage = VirtualAgentQuickReplyListChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Please select a menu item",
            object : ArrayList<QuickReplyButton>() {
                init {
                    add(QuickReplyTextButton("Menu 1"))
                    add(QuickReplyTextButton("Menu 2"))
                    add(QuickReplyTextButton("Multi line menu item \nhas more lines than one"))
                    add(EscalationButton("Menu 3"))
                    add(EscalationButton("Menu 4"))
                    add(EscalationButton("Menu 5"))
                    add(EscalationButton("I want to talk to a real human"))
                    add(QuickReplyTextButton("Last menu item "))
                }
            },
            virtualAgent,
            id.toLong()
        )
        messages.add(quickReplyList)

        id++
        timeNow.add(Calendar.MINUTE, 5)
        image1 = getRemoteImageUrl()
        image2 = getRemoteImageUrl()
        image3 = getRemoteImageUrl()
        contentCard = ContentCardChatMessage.createSent(
            id,
            id.toString(),
            timeNow.time,
            listOf(
                ContentCard(
                    title = "Title 1",
                    subtitle = "Subtitle 1",
                    body = "Body body body 1",
                    images = listOf(image1),
                    link = "https://www.google.com/search?q=frank+sinatra",
                    eventParams = hashMapOf(
                        "title" to "Title 1",
                        "subtitle" to "Subtitle 1",
                        "body" to "Body body body 1",
                        "image" to image1,
                        "has_button" to false,
                        "link" to "https://www.google.com/search?q=frank+sinatra",
                        "parameter 1" to "value 1.1",
                        "parameter 2" to "value 1.2"
                    )
                ),
                ContentCard(
                    title = "Title 2",
                    subtitle = "Subtitle 2",
                    body = "Body body body 2",
                    images = listOf(image2),
                    link = "https://www.google.com/search?q=ujet",
                    eventParams = hashMapOf(
                        "title" to "Title 2",
                        "subtitle" to "Subtitle 2",
                        "body" to "Body body body 2",
                        "image" to image2,
                        "has_button" to false,
                        "link" to "https://www.google.com/search?q=ujet",
                        "parameter 1" to "value 2.1",
                        "parameter 2" to "value 2.2"
                    )
                ),
                ContentCard(
                    title = "Title 3",
                    subtitle = "Subtitle 3",
                    body = "Body body body 3\nThere's no button here!",
                    images = listOf(image3),
                    link = "https://www.google.com/search?q=dream+theater",
                    eventParams = hashMapOf(
                        "title" to "Title 3",
                        "subtitle" to "Subtitle 3",
                        "body" to "Body body body 3",
                        "image" to image3,
                        "has_button" to false,
                        "link" to "https://www.google.com/search?q=dream+theater",
                        "parameter 1" to "value 3.1",
                        "parameter 2" to "value 3.2"
                    )
                )
            ),
            "",
            id.toLong()
        )
        messages.add(contentCard)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val humanAgentChatMessage: ChatMessage = HumanAgentChatMessage(id, id.toString(), timeNow.time,
            "Human agent message!", null, id.toLong(), null)
        messages.add(humanAgentChatMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val anotherHumanAgentChatMessage: ChatMessage =
            HumanAgentChatMessage(id, id.toString(), timeNow.time, "Another human agent message!",
                null, id.toLong(), null)
        messages.add(anotherHumanAgentChatMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val endUserChatMessage1: ChatMessage = createSent(id, id.toString(), timeNow.time, "testing", id.toLong())
        messages.add(endUserChatMessage1)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val endUserChatMessage2: ChatMessage = createSent(id, id.toString(), timeNow.time, "tests", id.toLong())
        messages.add(endUserChatMessage2)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val endUserChatMessage3: ChatMessage = createSent(id, id.toString(), timeNow.time, "test", id.toLong())
        messages.add(endUserChatMessage3)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val endUserChatMessage4: ChatMessage = createSent(id, id.toString(), timeNow.time, "test", id.toLong())
        messages.add(endUserChatMessage4)

        val fileDescriptors: MutableList<Triple<Type, String, String>> = LinkedList()
        fileDescriptors.add(Triple(Type.Audio, "AUD_1234.mp3", "https://filesamples.com/samples/audio/mp3/sample3.mp3"))
        fileDescriptors.add(Triple(Type.Doc, "MyShoppingList.doc", "http://www.iiswc.org/iiswc2012/sample.doc"))
        fileDescriptors.add(Triple(Type.Excel, "My spreadsheet.xls", "https://www.cmu.edu/blackboard/files/evaluate/tests-example.xls"))
        fileDescriptors.add(Triple(Type.PDF, "Confirmation_check_list.pdf", "http://www.africau.edu/images/default/sample.pdf"))
        fileDescriptors.add(Triple(Type.PPT, "Business presentation.ppt", "https://www.unm.edu/~unmvclib/powerpoint/pptexamples.ppt"))
        fileDescriptors.add(Triple(Type.Generic, "index.js", "https://cdn.snigelweb.com/adconsent/adconsent.js"))
        var message: ChatMessage
        // Add round-robin documents to see their placement
        for ((first, second, third) in fileDescriptors) {
            id++
            timeNow.add(Calendar.MINUTE, 1)
            val mediaFile = createRemote(id, first, Uploaded, second, third)
            message = when (id % 3) {
                0 -> EndUserDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, id.toLong())
                1 -> {
                    val agent = Agent(id, "Fake agent", null)
                    HumanAgentDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, agent, id.toLong(), null)
                }

                2 -> VirtualAgentDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, virtualAgent, id.toLong())
                else -> continue
            }
            messages.add(message)
        }

        // All end user documents
        for ((first, second, third) in fileDescriptors) {
            id++
            timeNow.add(Calendar.MINUTE, 1)
            val mediaFile = createRemote(id, first, Uploaded, second, third)
            message = EndUserDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, id.toLong())
            messages.add(message)
        }

        // All human agent documents
        for ((first, second, third) in fileDescriptors) {
            id++
            timeNow.add(Calendar.MINUTE, 1)
            val mediaFile = createRemote(id, first, Uploaded, second, third)
            val agent = Agent(1, "Fake agent", null)
            message = HumanAgentDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, agent, id.toLong(), null)
            messages.add(message)
        }

        // All virtual agent documents
        for ((first, second, third) in fileDescriptors) {
            id++
            timeNow.add(Calendar.MINUTE, 1)
            val mediaFile = createRemote(id, first, Uploaded, second, third)
            message = VirtualAgentDocumentChatMessage.createSent(id, id.toString(), timeNow.time, mediaFile, virtualAgent, id.toLong())
            messages.add(message)
        }

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val markdownMessage = """
               End User markdown message
               ${getString(R.string.markdown_input_0)}
               """.trimIndent()
        val endUserChatMarkdownMessage: ChatMessage = createSent(id, id.toString(), timeNow.time, markdownMessage, id.toLong())
        messages.add(endUserChatMarkdownMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        var sid = id.toString()
        var photoFile = getRandomLocalPhoto()
        val endUserPhotoMessage: ChatMessage = EndUserPhotoChatMessage.createSent(id, sid, timeNow.time, photoFile, id.toLong())
        messages.add(endUserPhotoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        var videoFile = getRandomLocalPhoto()
        val endUserVideoMessage: ChatMessage = VideoChatMessage.createSent(id, sid, timeNow.time, videoFile, id.toLong())
        messages.add(endUserVideoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        photoFile = gerRandomRemotePhoto()
        val agent = Agent(id, "Fake agent", null)
        val humanAgentPhotoMessage: ChatMessage = HumanAgentPhotoChatMessage.createSent(1, sid, timeNow.time,
            arrayListOf(photoFile), agent, id.toLong(), null, true)
        messages.add(humanAgentPhotoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        videoFile = getRandomLocalPhoto()
        val virtualAgentVideoMessage: ChatMessage = createSent(sid, timeNow.time, videoFile, virtualAgent, id.toLong())
        messages.add(virtualAgentVideoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        videoFile = getRandomLocalPhoto()
        val fakeAgent = Agent(id, "Fake agent", null)
        val humanAgentVideoMessage: ChatMessage = createSent(1, sid, timeNow.time, arrayListOf(videoFile),
            fakeAgent, id.toLong(), null, true)
        messages.add(humanAgentVideoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        photoFile = gerRandomRemotePhoto()
        val virtualAgentPhotoMessage: ChatMessage =
            VirtualAgentPhotoChatMessage.createSent(1, sid, timeNow.time, photoFile, virtualAgent, id.toLong())
        messages.add(virtualAgentPhotoMessage)

        id++
        timeNow.add(Calendar.MINUTE, 1)
        val quickReplyButtons: ChatMessage = VirtualAgentQuickReplyButtonsChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Please select a menu item",
            object : ArrayList<QuickReplyButton>() {
                init {
                    add(QuickReplyTextButton("Menu 1"))
                    add(QuickReplyTextButton("Menu 2"))
                    add(QuickReplyTextButton("Multi line menu item \nhas more lines than one"))
                    add(EscalationButton("I want to talk to a real human"))
                    add(QuickReplyTextButton("Last menu item "))
                }
            },
            virtualAgent,
            id.toLong()
        )
        messages.add(quickReplyButtons)


        id++
        timeNow.add(Calendar.MINUTE, 1)

        quickStickyReplyList = VirtualAgentQuickReplyButtonsChatMessage(
            id,
            id.toString(),
            timeNow.time,
            "Please select a menu item",
            object : ArrayList<QuickReplyButton>() {
                init {
                    add(QuickReplyTextButton("A"))
                    add(QuickReplyTextButton("Menu 1"))
                    add(QuickReplyTextButton("Menu 2"))
                    add(QuickReplyTextButton("Multi line menu item \nhas more lines than one"))
                    add(EscalationButton("I want to talk to a real human"))
                    add(QuickReplyTextButton("Last menu item "))
                    add(QuickReplyTextButton("Menu 3"))
                    add(QuickReplyTextButton("Menu 4"))
                    add(QuickReplyTextButton("Ujet"))
                    add(QuickReplyTextButton("1"))
                }
            },
            virtualAgent,
            id.toLong()
        )

        id++
        timeNow.add(Calendar.MINUTE, 1)
        sid = id.toString()
        val chatEndedMessage: ChatMessage =
            ChatEndedMessage(1, sid, timeNow.time, true, getString(co.ujet.android.R.string.ujet_chat_notification_ended), id.toLong(), false)
        messages.add(chatEndedMessage)


        return messages
    }

    private fun getRemoteImageUrl(): String {
        val random = Random(System.nanoTime())
        val w = random.nextInt(IMAGE_MAX_SIZE / 2) + IMAGE_MAX_SIZE * 2
        val h = random.nextInt(IMAGE_MAX_SIZE / 2) + IMAGE_MAX_SIZE
        return "https://picsum.photos/$w/$h/"
    }

    private fun gerRandomRemotePhoto(): MediaFile {
        val random = Random(System.nanoTime())
        return createRemote(random.nextInt(IMAGE_MAX_SIZE), Type.Photo, Uploaded, null, getRemoteImageUrl())
    }

    private fun getRandomLocalPhoto(): MediaFile {
        val random = Random(System.nanoTime())
        val url = getRemoteImageUrl()
        return create(random.nextInt(IMAGE_MAX_SIZE), Type.Photo, Uploaded, url, null, url)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(co.ujet.android.R.menu.ujet_menu_chat, menu)
        val menuItem = menu.getItem(0)
        val (_, fontStyle) = chatStyles.endChatButton ?: return false
        updateFontStyle(this, menuItem, fontStyle)
        return true
    }

    private class DummyMessagesDataSource(private val messages: List<ChatMessage>) : ChatMessageDataSource {
        override fun getMessage(index: Int) = messages.getOrNull(index)

        override fun getCount() = messages.size

        override fun shouldShowAgentNames() = false

        override fun clearChatMessages() {}

        override fun setChangeListener(listener: ChatMessageChangeListener?) {}
    }

    companion object {
        private const val IMAGE_MAX_SIZE = 300
    }
}
