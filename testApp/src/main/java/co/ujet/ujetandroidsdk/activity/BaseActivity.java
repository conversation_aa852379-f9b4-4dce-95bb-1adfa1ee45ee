package co.ujet.ujetandroidsdk.activity;

import android.content.Intent;
import android.os.SystemClock;
import androidx.appcompat.app.AppCompatActivity;
import co.ujet.android.Ujet;
import co.ujet.ujetandroidsdk.TestAppSettings;

/**
 * Created by mimu on 5/10/16.
 */
public abstract class BaseActivity extends AppCompatActivity {

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == SettingsActivity.REQUEST_FOR_SETTING && resultCode == SettingsActivity.RESULT_FOR_SETTING_UPDATED) {
            clearDataAndRestartApplication();
        }

        super.onActivityResult(requestCode, resultCode, data);
    }

    protected void clearDataAndRestartApplication() {
        if (TestAppSettings.isIsInitialized()) {
            Ujet.clearUserData(); //Clear the session data before switching the environment in test app
        }
        //Restart the app
        restartApplication();
    }

    protected void restartApplication() {
        SystemClock.sleep(500); //Few devices could not clear cache before restart but delaying restart did the trick.

        finish();
        Intent intent = new Intent(this, LoginActivity.class);
        startActivity(intent);

        android.os.Process.killProcess(android.os.Process.myPid());
        Runtime.getRuntime().exit(10);
    }
}
