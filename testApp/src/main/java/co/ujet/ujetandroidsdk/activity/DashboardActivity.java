package co.ujet.ujetandroidsdk.activity;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.FragmentManager;

import com.google.android.material.navigation.NavigationView;
import com.joanzapata.iconify.IconDrawable;
import com.joanzapata.iconify.fonts.FontAwesomeIcons;

import co.ujet.android.Ujet;
import co.ujet.android.UjetStatus;
import co.ujet.ujetandroidsdk.R;
import co.ujet.ujetandroidsdk.TestAppSettings;
import co.ujet.ujetandroidsdk.common.ShakeToSettings;
import co.ujet.ujetandroidsdk.fragment.HelpFragment;
import co.ujet.ujetandroidsdk.fragment.eventdata.EventDataFragment;

public class DashboardActivity extends BaseActivity
        implements NavigationView.OnNavigationItemSelectedListener {
    private ShakeToSettings shakeToSettings;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_dashboard);
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(
                this, drawer, toolbar, R.string.navigation_drawer_open, R.string.navigation_drawer_close);
        drawer.setDrawerListener(toggle);

        toggle.syncState();

        // Setup Navigation view
        NavigationView navigationView = findViewById(R.id.nav_view);
        navigationView.setNavigationItemSelectedListener(this);

        // Change drawer's icons
        navigationView.getMenu().findItem(R.id.nav_help).setIcon(new IconDrawable(this, FontAwesomeIcons.fa_question));
        navigationView.getMenu().findItem(R.id.nav_event_data).setIcon(new IconDrawable(this, FontAwesomeIcons.fa_dashboard));
        navigationView.getMenu().findItem(R.id.nav_clear_cache).setIcon(new IconDrawable(this, FontAwesomeIcons.fa_remove));
        navigationView.getMenu().findItem(R.id.nav_logout).setIcon(new IconDrawable(this, FontAwesomeIcons.fa_sign_out));

        // Update company name and logined user email on drawer header
        TextView companyNameTextView = navigationView.getHeaderView(0).findViewById(R.id.textViewCompanyName);
        companyNameTextView.setText(getCompanyName());

        TextView emailTextView = navigationView.getHeaderView(0).findViewById(R.id.textViewEmail);
        final SharedPreferences preferences = this.getSharedPreferences("UjetPreferences", Context.MODE_PRIVATE);
        emailTextView.setText(preferences.getString("userId", null));

        shakeToSettings = new ShakeToSettings(this);

        // Set default fragment
        if (findViewById(R.id.container) != null) {
            // However, if we're being restored from a previous state,
            // then we don't need to do anything and should return or else
            // we could end up with overlapping fragments.
            if (savedInstanceState != null) {
                return;
            }

            getSupportFragmentManager().beginTransaction()
                    .add(R.id.container, new HelpFragment()).commitAllowingStateLoss();
        }
    }

    @Override
    public void onBackPressed() {
        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        if (drawer.isDrawerOpen(GravityCompat.START)) {
            drawer.closeDrawer(GravityCompat.START);
        } else {
            finish();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.dashboard, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        if (item == null) {
            return super.onOptionsItemSelected(item);
        }
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            if (shakeToSettings != null) {
                shakeToSettings.launchSettingActivity();
                return true;
            }
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onNavigationItemSelected(MenuItem item) {
        // Handle navigation view item clicks here.
        int id = item.getItemId();

        if (id == R.id.nav_help) {
            displayHelpFragment();
        } else if (id == R.id.nav_event_data) {
            displayEventDataFragment();
        } else if (id == R.id.nav_clear_cache) {
            clearCache();
        } else if (id == R.id.nav_logout) {
            logout();
        }

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        drawer.closeDrawer(GravityCompat.START);
        return true;
    }

    private void displayEventDataFragment() {
        FragmentManager manager = getSupportFragmentManager();
        manager.beginTransaction()
                .replace(R.id.container, new EventDataFragment())
                .addToBackStack(null)
                .commitAllowingStateLoss();
        setTitle(R.string.dashboard_menu_item_event_data);
    }

    private void displayHelpFragment() {
        FragmentManager manager = getSupportFragmentManager();
        manager.beginTransaction()
                .replace(R.id.container, new HelpFragment())
                .addToBackStack(null)
                .commitAllowingStateLoss();
        setTitle(co.ujet.android.R.string.ujet_common_support);
    }

    private void clearCache() {
        Ujet.clearUserData();
        showLogoutMessage("Cleared the session data");
    }

    private void logout() {
        if (Ujet.getStatus() != UjetStatus.None) {
            Ujet.disconnect(() -> {
                Ujet.clearUserData();
                showLogoutMessage("Cleared the session data and user logged out successfully");
            });
        }
        // finish dashboard activity
        finish();
    }

    private void showLogoutMessage(@NonNull String message) {
        Toast.makeText(this.getApplicationContext(), message, Toast.LENGTH_LONG).show();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (shakeToSettings != null) {
            shakeToSettings.stop();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (shakeToSettings != null) {
            shakeToSettings.start();
        }
    }

    @Override
    protected void onDestroy() {
        if (shakeToSettings != null) {
            shakeToSettings.clear();
        }

        super.onDestroy();
    }

    private String getCompanyName() {
        String companyName = TestAppSettings.getTenant().getSubdomain();

        if (companyName.equals("local") || companyName.equals("sfco") || companyName.equals("zdco")) {
            companyName = "Generico";
        }

        return companyName;
    }
}
