package co.ujet.ujetandroidsdk.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import co.ujet.ujetandroidsdk.R;
import co.ujet.ujetandroidsdk.TestAppSettings;
import co.ujet.ujetandroidsdk.util.AssetsUtil;

public class ChatTranscriptActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chat_transcript);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        toolbar.setNavigationOnClickListener(v -> finish());

        EditText chatTranscriptInput = findViewById(R.id.chat_transcript_input);
        String cachedChatTranscript = TestAppSettings.getChatTranscriptExampleJson();
        if (TextUtils.isEmpty(cachedChatTranscript)) {
            String chatTranscriptJsonFromAssets = AssetsUtil.INSTANCE.getAssetsFileContent(this, "json/chat_transcript_example.json");
            chatTranscriptInput.setText(chatTranscriptJsonFromAssets);
        } else {
            chatTranscriptInput.setText(cachedChatTranscript);
        }

        findViewById(R.id.save_chat_transcript_button).setOnClickListener(v -> {
            String chatTranscript = chatTranscriptInput.getText().toString();
            TestAppSettings.setChatTranscriptExampleJson(chatTranscript);
            Toast.makeText(getApplicationContext(), "Transcript saved!", Toast.LENGTH_SHORT).show();
        });
        findViewById(R.id.load_chat_transcript_button).setOnClickListener(v -> {
            String chatTranscriptJsonFromAssets = AssetsUtil.INSTANCE.getAssetsFileContent(this, "json/chat_transcript_example.json");
            chatTranscriptInput.setText(chatTranscriptJsonFromAssets);
        });
        findViewById(R.id.clear_chat_transcript_button).setOnClickListener(v -> {
            chatTranscriptInput.setText(null);
        });
    }
}
