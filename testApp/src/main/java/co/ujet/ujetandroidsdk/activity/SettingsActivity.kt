package co.ujet.ujetandroidsdk.activity

import android.R.id
import co.ujet.ujetandroidsdk.fragment.SettingsFragment.Companion.newInstance
import co.ujet.ujetandroidsdk.fragment.SettingsFragment.SettingChangeListener
import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.OnBackPressedCallback
import co.ujet.ujetandroidsdk.fragment.SettingsFragment

class SettingsActivity : BaseActivity(), SettingChangeListener {
    private var isUpdated = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val settingsFragment = supportFragmentManager.findFragmentByTag(SettingsFragment.TAG)
        if (settingsFragment == null) {
            supportFragmentManager
                .beginTransaction()
                .add(id.content, newInstance(), SettingsFragment.TAG)
                .commitAllowingStateLoss()
        }
        handleOnBackPress()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            if (onBackPressedDispatcher.hasEnabledCallbacks()) {
                onBackPressedDispatcher.onBackPressed()
            }
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun handleOnBackPress() {
        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                close()
            }
        })
    }

    override fun onSettingChanged() {
        isUpdated = true
        val currentFragment = supportFragmentManager.findFragmentByTag(SettingsFragment.TAG)
        (currentFragment as? SettingsFragment)?.refresh()
    }

    override fun onClose() {
        close()
    }

    private fun close() {
        if (isUpdated) {
            setResult(RESULT_FOR_SETTING_UPDATED)
        }
        finish()
    }

    companion object {
        const val REQUEST_FOR_SETTING = 808
        const val RESULT_FOR_SETTING_UPDATED = 809
    }
}
