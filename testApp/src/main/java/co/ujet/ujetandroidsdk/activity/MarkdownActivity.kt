package co.ujet.ujetandroidsdk.activity

import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.Spinner
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.ujetandroidsdk.R
import cx.ujet.android.markdown.UjetMarkdown.parse
import cx.ujet.android.markdown.widgets.MarkdownTextView

class MarkdownActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_markdown)
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)
        toolbar.setNavigationOnClickListener { finish() }

        val markdownInputEditText = findViewById<View>(R.id.markdown_input) as EditText
        setTextViewColor(R.id.test_case_input_label)
        val spinnerAdapter = ArrayAdapter.createFromResource(applicationContext,
            R.array.spinner_markdown_test_cases, R.layout.spinner_list_item)
        spinnerAdapter.setDropDownViewResource(R.layout.spinner_list_item)
        findViewById<Spinner>(R.id.test_case_input_spinner).apply {
            background.colorFilter = getUjetStyle()?.textPrimaryColor?.let {
                BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                    it, BlendModeCompat.SRC_ATOP)
            }
            adapter = spinnerAdapter
            setSelection(0)
            onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>, view: View, position: Int, id: Long) {
                    // Get input string based on position and paste it into edit text box.
                    val updatedInputText = getUpdatedInputText(position)
                    markdownInputEditText.setText(updatedInputText)
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }
        }

        findViewById<View>(R.id.parse_markdown).setOnClickListener {
            val input = markdownInputEditText.text.toString()
            val resultView = findViewById<View>(R.id.markdown_result) as MarkdownTextView
            val markdownHtml = parse(input)
            resultView.setHtml(markdownHtml, "")
        }
    }
    
    private fun getUpdatedInputText(index: Int): String {
        val resourceName = "markdown_input_$index"
        val resourceId = getResourceId(resourceName)
        return getString(resourceId)
    }

    private fun getResourceId(resourceName: String) = resources.getIdentifier(resourceName, "string", packageName)

    private fun setTextViewColor(resId: Int) {
        findViewById<TextView>(resId)?.apply {
            getUjetStyle()?.let { UjetViewStyler.stylePrimaryText(it, this) }
        }
    }

    private fun getUjetStyle(): UjetStyle? {
        return applicationContext?.let { Injection.provideUjetStyle(it) }
    }
}
