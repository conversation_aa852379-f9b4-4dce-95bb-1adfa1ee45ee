package co.ujet.ujetandroidsdk.util

import android.content.Context
import android.graphics.drawable.StateListDrawable
import co.ujet.android.internal.Injection
import co.ujet.android.ui.util.DesignUtil

object StyleUtil {
    fun getRoundedRectangleDrawable(context: Context): StateListDrawable {
        val strokePx = context.resources.getDimensionPixelSize(co.ujet.android.ui.R.dimen.ujet_channel_button_stroke)
        val cornerRadiusPx = getUjetStyle(context).buttonRadius
        val normalDrawable = DesignUtil.createRoundedRectangleDrawable(
            getUjetStyle(context).primaryBackgroundColor, getUjetStyle(context).pickerSeparatorColor, strokePx, cornerRadiusPx
        )
        val pressedDrawable = DesignUtil.createRoundedRectangleDrawable(
            getUjetStyle(context).textFocusBackgroundColor, getUjetStyle(context).pickerSeparatorColor, strokePx, cornerRadiusPx
        )
        return DesignUtil.getStateListDrawable(normalDrawable, pressedDrawable)
    }

    private fun getUjetStyle(context: Context) = Injection.provideUjetStyle(context)
}
