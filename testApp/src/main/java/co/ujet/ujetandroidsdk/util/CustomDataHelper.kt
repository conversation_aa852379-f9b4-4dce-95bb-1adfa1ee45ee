package co.ujet.ujetandroidsdk.util

import android.content.Context
import android.os.Build
import co.ujet.android.UjetCustomData
import co.ujet.android.common.util.DeviceUtil
import co.ujet.ujetandroidsdk.Application
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.common.CustomData
import java.util.ArrayList
import java.util.Date

object CustomDataHelper {

    fun getSignedCustomData(context: Context): UjetCustomData {
        var cachedCustomSignedData = TestAppSettings.customSignedData
        // Generate default signed custom data and save it in cache for the first time
        if (cachedCustomSignedData.isNullOrEmpty()) {
            cachedCustomSignedData = generateSignedCustomData(context)
        }
        val data = generateCustomData(cachedCustomSignedData)

        // Need to send these params when customDataType is Signed or SignedAndUnsigned
        val cachedCustomData = TestAppSettings.customDataParams
        if (cachedCustomData.isReservedDataConfigEnabled && cachedCustomData.isSignedCustomDataEnabled) {
            data.put("reserved_verified_customer", "Verified Customer", cachedCustomData.isCustomerVerified)
            data.put("reserved_bad_actor", "Bad Actor", cachedCustomData.isCustomerBadActor)
            data.put("reserved_repeat_customer", "Repeat Customer", cachedCustomData.isCustomerRepeated)
        }
        return data
    }

    fun generateCustomData(customDataList: ArrayList<CustomData>): UjetCustomData {
        val data = UjetCustomData()
        for (customData in customDataList) {
            data.put(customData.key, customData.label, customData.type, customData.value, customData.invisibleToAgent)
        }
        return data
    }

    fun generateSignedCustomData(context: Context): ArrayList<CustomData> {
        var batteryLevel = 0
        if (context.applicationContext is Application) {
            batteryLevel = (context.applicationContext as Application).batteryLevel
        }
        val address = AddressUtil.getLastKnownLocation(context)
        val cachedCustomSignedData = ArrayList<CustomData>()
        cachedCustomSignedData.add(CustomData("os_version", "OS Version", "string",
            DeviceUtil.getDeviceVersion(), false))
        cachedCustomSignedData.add(CustomData("battery", "Battery", "string",
            "$batteryLevel%", false))
        cachedCustomSignedData.add(CustomData("model", "Model", "string",
            Build.MODEL, false))
        cachedCustomSignedData.add(CustomData("contact_date", "Contact Date", "date",
            Date(), false))
        cachedCustomSignedData.add(CustomData("location", "Location", "string",
            address, false))
        cachedCustomSignedData.add(CustomData("dashboard_url", "Dashboard URL", "url",
            "http://(company_name)/dashboard/device_user_ID", false))
        cachedCustomSignedData.add(CustomData("ssn", "Social Security Number", "string",
            "100-000-0000", true))

        TestAppSettings.customSignedData = cachedCustomSignedData
        return cachedCustomSignedData
    }
}
