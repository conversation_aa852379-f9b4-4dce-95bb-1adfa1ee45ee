package co.ujet.ujetandroidsdk.util;

import android.content.Context;

import androidx.annotation.NonNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class IOUtil {
    public static String read(@NonNull Context context, @NonNull String path) {
        InputStream inputStream = null;
        try {
            inputStream = context.getAssets().open(path);

        } catch (IOException e) {
            e.printStackTrace();
        }

        StringBuilder sb = new StringBuilder();
        try (Reader reader = new BufferedReader(new InputStreamReader
                (inputStream, Charset.forName(StandardCharsets.UTF_8.name())))) {
            int c;
            while ((c = reader.read()) != -1) {
                sb.append((char) c);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return sb.toString();
    }
}
