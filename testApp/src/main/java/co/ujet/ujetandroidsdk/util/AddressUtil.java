package co.ujet.ujetandroidsdk.util;

import android.content.Context;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.os.Build;
import android.text.TextUtils;

import java.io.IOException;
import java.util.List;
import java.util.Locale;

/**
 * Created by allen on 29/07/2017.
 */

public class AddressUtil {
    private static Location location;

    public static void setLocation(Location location) {
        AddressUtil.location = location;
    }

    public static String getLastKnownLocation(Context context) {
        Address address = null;

        if (location != null) {
            Locale locale = getCurrentLocale(context);
            Geocoder geocoder = new Geocoder(context, locale);

            try {
                List<Address> addresses = geocoder.getFromLocation(location.getLatitude(), location.getLongitude(), 1);
                if (addresses.size() > 0) {
                    address = addresses.get(0);
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        if (address == null)
            return "Location not available";

        boolean comma = false;
        StringBuilder sb = new StringBuilder();

        if (!TextUtils.isEmpty(address.getThoroughfare())) {
            if (!TextUtils.isEmpty(address.getSubThoroughfare())) {
                sb.append(address.getSubThoroughfare() + " ");
            }
            sb.append(address.getThoroughfare());
            comma = true;
        }

        if (!TextUtils.isEmpty(address.getLocality())) {
            if (comma) {
                sb.append(", ");
            }
            sb.append(address.getLocality());
            comma = true;
        }

        if (!TextUtils.isEmpty(address.getAdminArea())) {
            if (comma) {
                sb.append(", ");
            }
            sb.append(address.getAdminArea());
            comma = true;
        }

        if (!TextUtils.isEmpty(address.getCountryName())) {
            if (comma) {
                sb.append(", ");
            }
            sb.append(address.getCountryName());
        }

        return sb.toString();
    }

    private static Locale getCurrentLocale(Context context) {
        Locale currentLocale;
        if (Build.VERSION_CODES.N <= Build.VERSION.SDK_INT) {
            currentLocale = context.getResources().getConfiguration().getLocales().get(0);
        } else {
            currentLocale = context.getResources().getConfiguration().locale;
        }

        return currentLocale;
    }
}
