package co.ujet.ujetandroidsdk.util

import android.content.Context
import co.ujet.ujetandroidsdk.env.Env
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.IOException
import java.io.InputStream
import java.lang.reflect.Type

class EnvironmentUtil {
    companion object {
        lateinit var all: List<Env>
        lateinit var allNames: Array<CharSequence?>

        @JvmStatic
        fun load(context: Context) {
            val jsonFileString: String? = getJsonFromAssets(context)
            val listEnvType: Type = object : TypeToken<List<Env?>?>() {}.type
            all = Gson().fromJson(jsonFileString, listEnvType)
            if (all.isEmpty()) {
                throw RuntimeException("No environment")
            }

            for (env in all) {
                env.validate()
                for (tenant in env.tenants) {
                    tenant.validate()
                }
            }

            allNames = arrayOfNulls(all.size)
            all.forEachIndexed { index, env ->
                allNames[index] = env.name
            }
        }

        @JvmStatic
        fun pick(envName: String?): Env {
            for (env in all) {
                if (env.name == envName) {
                    return env
                }
            }

            return all[0]
        }

        private fun getJsonFromAssets(context: Context): String? {
            return try {
                val inputStream: InputStream = context.assets.open("env.json")
                val size: Int = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                inputStream.close()
                String(buffer, Charsets.UTF_8)
            } catch (e: IOException) {
                e.printStackTrace()
                return null
            }
        }
    }
}