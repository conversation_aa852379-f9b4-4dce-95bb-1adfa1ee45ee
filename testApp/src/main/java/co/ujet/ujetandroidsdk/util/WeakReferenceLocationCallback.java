package co.ujet.ujetandroidsdk.util;

import java.lang.ref.WeakReference;

import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationResult;

public class WeakReferenceLocationCallback extends LocationCallback {

    private final WeakReference<LocationCallback> locationCallbackRef;

    public WeakReferenceLocationCallback(LocationCallback locationCallback) {
        locationCallbackRef = new WeakReference<>(locationCallback);
    }

    @Override
    public void onLocationResult(LocationResult locationResult) {
        super.onLocationResult(locationResult);
        LocationCallback locationCallback = locationCallbackRef.get();
        if (locationCallback != null) {
            locationCallback.onLocationResult(locationResult);
        }
    }
}
