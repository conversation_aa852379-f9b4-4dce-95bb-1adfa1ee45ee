package co.ujet.ujetandroidsdk.util

import android.content.Context
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

object AssetsUtil {
    fun getAssetsFileContent(context: Context, filePath: String): String {
        var reader: BufferedReader? = null
        val stringBuilder = StringBuilder()
        try {
            reader = BufferedReader(InputStreamReader(context.assets.open(filePath), StandardCharsets.UTF_8))
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                stringBuilder.append("$line\n")
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            if (reader != null) {
                try {
                    reader.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return stringBuilder.toString()
    }
}
