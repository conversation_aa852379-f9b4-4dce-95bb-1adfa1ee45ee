package co.ujet.ujetandroidsdk.theme;

import android.content.Context;
import android.content.res.TypedArray;

import co.ujet.ujetandroidsdk.TestAppSettings;

/**
 * Created by mimu on 12/8/16.
 * Helper class for Ujet theme
 */
public class UjetTheme {
    public static TypedArray getThemeTypedArray(Context context) {
        String theme = TestAppSettings.getTheme();
        if (theme.equals("default")) {
            return null;
        }

        String attrName = "Ujet." + theme;
        int resId = context.getResources().getIdentifier(attrName, "style", context.getPackageName());
        if (resId == 0) {
            return null;
        }

        return context.obtainStyledAttributes(resId, co.ujet.android.ui.R.styleable.UjetAttrs);
    }
}
