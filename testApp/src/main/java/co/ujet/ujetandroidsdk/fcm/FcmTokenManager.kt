package co.ujet.ujetandroidsdk.fcm

import android.content.Context
import android.content.SharedPreferences
import androidx.preference.PreferenceManager
import co.ujet.android.libs.logger.Logger
import com.google.android.gms.tasks.Task
import com.google.firebase.messaging.FirebaseMessaging

class FcmTokenManager(context: Context?) {
    private val sharedPreferences: SharedPreferences? = context?.let {
        PreferenceManager.getDefaultSharedPreferences(it)
    }
    var token: String? = null
        get() {
            field = sharedPreferences?.getString("fcmToken", null)
            if (field == null) {
                FirebaseMessaging.getInstance().token
                        .addOnCompleteListener { task: Task<String?> ->
                            if (!task.isSuccessful || task.result == null) {
                                Logger.w("Couldn't get FCM token")
                                return@addOnCompleteListener
                            }
                            field = task.result
                            field?.let { updateToken(it) }
                        }
            }
            return field
        }
        private set

    fun updateToken(token: String) {
        sharedPreferences?.edit()
                ?.putString("fcmToken", token)
                ?.apply()
    }
}
