package co.ujet.ujetandroidsdk.fcm

import android.util.Log
import co.ujet.android.Ujet
import co.ujet.android.libs.logger.Logger
import com.google.android.gms.tasks.Task
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class MyFirebaseMessagingService : FirebaseMessagingService() {
    private var fcmTokenManager: FcmTokenManager? = null

    override fun onCreate() {
        super.onCreate()
        fcmTokenManager = FcmTokenManager(this)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Ujet.canHandlePush(remoteMessage.data)
    }

    /**
     * Called if InstanceID token is updated. This may occur if the security of
     * the previous token had been compromised. This call is initiated by the
     * InstanceID provider.
     */
    override fun onNewToken(token: String) {
        FirebaseMessaging.getInstance().token
                .addOnCompleteListener { task: Task<String?> ->
                    if (!task.isSuccessful || task.result == null) {
                        Logger.w("Couldn't get FCM token")
                        return@addOnCompleteListener
                    }
                    val refreshedToken = task.result
                    Log.i("FCM", "FCM token updated: $refreshedToken")
                    refreshedToken?.let { fcmTokenManager?.updateToken(it) }
                }
    }
}