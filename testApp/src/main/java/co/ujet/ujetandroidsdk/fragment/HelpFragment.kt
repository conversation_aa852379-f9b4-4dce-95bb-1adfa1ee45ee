package co.ujet.ujetandroidsdk.fragment

import android.content.*
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import android.widget.AdapterView.OnItemClickListener
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import co.ujet.android.Ujet
import co.ujet.android.UjetCustomData
import co.ujet.android.UjetStartOptions
import co.ujet.android.internal.Configuration
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.UjetInitializer
import co.ujet.ujetandroidsdk.activity.ChatBubblesActivity
import co.ujet.ujetandroidsdk.activity.ChatTranscriptActivity
import co.ujet.ujetandroidsdk.activity.MarkdownActivity
import co.ujet.ujetandroidsdk.help.HelpItem
import co.ujet.ujetandroidsdk.help.HelpListAdapter
import co.ujet.ujetandroidsdk.util.CustomDataHelper
import com.google.android.gms.tasks.Task
import com.google.firebase.messaging.FirebaseMessaging
import org.json.JSONObject
import java.util.*
import android.widget.Toast
import android.content.DialogInterface
import co.ujet.android.UjetPreferredChannel
import co.ujet.android.UjetWebFormCallback
import co.ujet.android.UjetWebFormListener
import co.ujet.android.app.survey.UjetSurveyActivity
import co.ujet.android.internal.Injection
import co.ujet.ujetandroidsdk.CoBrowseAlertProviderImpl
import co.ujet.ujetandroidsdk.TestAppSettings.isShowWebFormLoadingFailedScreenEnabled
import co.ujet.ujetandroidsdk.TestAppSettings.isShowWebFormTimedOutScreenEnabled
import co.ujet.ujetandroidsdk.activity.CustomDataActivity
import co.ujet.ujetandroidsdk.activity.UjetStyleOptionsActivity
import co.ujet.ujetandroidsdk.mock.FormDataRequest
import co.ujet.ujetandroidsdk.mock.WebFormMockServer.formApiService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class HelpFragment : Fragment() {
    private var preferences: SharedPreferences? = null
    private var textVersion: TextView? = null
    private var helpListView: ListView? = null

    override fun onCreateView(inflater: LayoutInflater,
                              container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        preferences = activity?.applicationContext?.getSharedPreferences("UjetPreferences", Context.MODE_PRIVATE)
        val result = inflater.inflate(R.layout.fragment_help, container, false)
        createHelpListView(result)
        return result
    }

    override fun onDestroyView() {
        super.onDestroyView()
        textVersion = null
        helpListView = null
    }

    private fun createHelpListView(fragment: View) {
        val context = activity?.applicationContext ?: return

        textVersion = fragment.findViewById(R.id.textVersion)
        helpListView = fragment.findViewById(R.id.helpListView)
        textVersion?.text = TestAppSettings.getBuildInfo(context)
        val adapter = HelpListAdapter(context, R.layout.help_list_item, helpItemsList)
        helpListView?.adapter = adapter
        helpListView?.onItemClickListener = OnItemClickListener { _: AdapterView<*>?, _: View?, i: Int, _: Long -> onItemClicked(adapter, i) }
    }

    private val helpItemsList: List<HelpItem>
        get() = object : ArrayList<HelpItem>() {
            init {
                add(HelpItem(getString(R.string.help_atrticles), null))
                add(HelpItem(getString(R.string.android_app_help_forum), null))
                add(HelpItem(getString(R.string.contact_customer_support), "ic_launcher", HelpItem.HELP_ITEM_TYPE_LAUNCH))
                add(HelpItem(getString(R.string.contact_customer_support_with_key), "ic_launcher", HelpItem.HELP_ITEM_TYPE_LAUNCH_WITH_KEY))
                add(
                    HelpItem(
                        getString(R.string.contact_customer_support_with_ivr_phone_number),
                        "ic_launcher",
                        HelpItem.HELP_ITEM_TYPE_LAUNCH_WITH_IVR_PHONE_NUMBER
                    )
                )
                add(
                    HelpItem(
                        getString(R.string.contact_customer_support_without_splash),
                        "ic_launcher",
                        HelpItem.HELP_ITEM_TYPE_LAUNCH_WITHOUT_SPLASH
                    )
                )
                add(HelpItem(getString(R.string.change_default_language), null, HelpItem.HELP_ITEM_CHANGE_DEFAULT_LANGAUGE))
                add(HelpItem(getString(R.string.clear_default_language), null, HelpItem.HELP_ITEM_CLEAR_DEFAULT_LANGUAGE))
                add(HelpItem(getString(R.string.clear_user_preferred_language), null, HelpItem.HELP_ITEM_CLEAR_USER_PREFERRED_LANGUAGE))
                add(HelpItem(getString(R.string.clear_device_uuid), null, HelpItem.HELP_ITEM_CLEAR_DEVICE_UUID))
                add(HelpItem(getString(R.string.set_signing_delay), null, HelpItem.HELP_ITEM_SET_SIGN_DELAY))
                add(HelpItem(getString(R.string.reset_signing_delay), null, HelpItem.HELP_ITEM_RESET_SIGN_DELAY))
                add(HelpItem(getString(R.string.fcm_key), null, HelpItem.HELP_ITEM_FCM_KEY))
                add(HelpItem(getString(R.string.configure_custom_data), null, HelpItem.HELP_ITEM_CONFIGURE_CUSTOM_DATA))
                add(HelpItem(getString(R.string.configure_ticket_id), null, HelpItem.HELP_ITEM_CONFIGURE_TICKET_ID))
                add(HelpItem(getString(R.string.configure_custom_cobrowse_license_key), null, HelpItem.HELP_ITEM_CUSTOM_COBROWSE_LICENSE_KEY))
                add(HelpItem(getString(R.string.configure_custom_cobrowse_url), null, HelpItem.HELP_ITEM_CUSTOM_COBROWSE_URL))
                add(HelpItem(getString(R.string.configure_preferred_channel), null, HelpItem.HELP_ITEM_CONFIGURE_PREFERRED_CHANNEL))
                add(HelpItem(getString(R.string.set_fallback_phone_number), null, HelpItem.HELP_ITEM_SET_FALLBACK_PHONE_NUMBER))
                add(HelpItem(getString(R.string.markdown_test), null, HelpItem.HELP_ITEM_MARKDOWN_TEST))
                add(HelpItem(getString(R.string.chat_bubbles_test), null, HelpItem.HELP_ITEM_CHAT_BUBBLES_TEST))
                add(HelpItem(getString(R.string.style_options_test), null, HelpItem.HELP_ITEM_STYLE_OPTIONS))
                add(HelpItem(getString(R.string.chat_transcript_input), null, HelpItem.HELP_ITEM_CHAT_TRANSCRIPT_EXAMPLE_JSON))
                add(HelpItem(getString(R.string.survey_page_test), null, HelpItem.HELP_ITEM_SURVEY_PAGE_TEST))
                add(HelpItem(getString(R.string.privacy_policy), null))
            }
        }

    private fun onItemClicked(adapter: HelpListAdapter, i: Int) {
        val item = adapter.getItem(i) ?: return
        when (item.type) {
            HelpItem.HELP_ITEM_TYPE_LAUNCH -> startUjet(null)
            HelpItem.HELP_ITEM_TYPE_LAUNCH_WITH_KEY -> showStartUjetWithKey()
            HelpItem.HELP_ITEM_TYPE_LAUNCH_WITH_IVR_PHONE_NUMBER -> startUjetWithIvr()
            HelpItem.HELP_ITEM_TYPE_LAUNCH_WITHOUT_SPLASH -> startWithoutSplash()
            HelpItem.HELP_ITEM_CHANGE_DEFAULT_LANGAUGE -> choiceDefaultLanguage()
            HelpItem.HELP_ITEM_CLEAR_USER_PREFERRED_LANGUAGE -> {
                val context = activity?.applicationContext ?: return
                val preferencesPostfix = TestAppSettings.companyKey
                val preferencesName = String.format("co.ujet.android.preferences.%s", preferencesPostfix)
                val ujetLocalRepositoryPrefs = context.getSharedPreferences(preferencesName, Context.MODE_PRIVATE)
                ujetLocalRepositoryPrefs.edit().remove("co.ujet.android.language").apply()
                context.getSharedPreferences("co.ujet.android.data.end_user", Context.MODE_PRIVATE)
                    .edit()
                    .remove("lang")
                    .apply()
                Toast.makeText(context, "Cleared user preferred language", Toast.LENGTH_SHORT).show()
            }

            HelpItem.HELP_ITEM_CLEAR_DEFAULT_LANGUAGE -> {
                TestAppSettings.language = null
                UjetInitializer.init()
                Toast.makeText(context, "Cleared default language", Toast.LENGTH_SHORT).show()
            }

            HelpItem.HELP_ITEM_CLEAR_DEVICE_UUID -> {
                val context = activity?.applicationContext ?: return
                val authenticator = Injection.provideAuthenticator(context)
                //We need to clear auth token so that device uuid is regenerated during authentication
                authenticator.invalidate()
                authenticator.clearUserData()
                authenticator.clearDeviceUUID()
                Toast.makeText(context, "Removed stored UUID", Toast.LENGTH_LONG).show()
            }

            HelpItem.HELP_ITEM_SET_SIGN_DELAY -> showDelayInputDialog()
            HelpItem.HELP_ITEM_RESET_SIGN_DELAY -> {
                TestAppSettings.resetSignDelay()
                Toast.makeText(activity, "", Toast.LENGTH_LONG).show()
            }

            HelpItem.HELP_ITEM_FCM_KEY -> showAndCopyFcmToken()
            HelpItem.HELP_ITEM_CONFIGURE_CUSTOM_DATA -> showCustomDataParamsConfig()
            HelpItem.HELP_ITEM_CONFIGURE_TICKET_ID -> showTicketIdConfig()
            HelpItem.HELP_ITEM_CONFIGURE_PREFERRED_CHANNEL -> showPreferredChannelConfig()
            HelpItem.HELP_ITEM_SET_FALLBACK_PHONE_NUMBER -> showFallbackPhoneNumberDialog()
            HelpItem.HELP_ITEM_MARKDOWN_TEST -> showMarkdownActivity()
            HelpItem.HELP_ITEM_CHAT_BUBBLES_TEST -> showChatBubblesActivity()
            HelpItem.HELP_ITEM_STYLE_OPTIONS -> showUjetStyleOptionsActivity()
            HelpItem.HELP_ITEM_SURVEY_PAGE_TEST -> showSurveyPageActivity()
            HelpItem.HELP_ITEM_CHAT_TRANSCRIPT_EXAMPLE_JSON -> showChatTranscriptActivity()
            HelpItem.HELP_ITEM_CUSTOM_COBROWSE_LICENSE_KEY -> showCustomCobrowseLicenseKeyConfig()
            HelpItem.HELP_ITEM_CUSTOM_COBROWSE_URL -> showCustomCobrowseUrlConfig()
        }
    }

    private fun startUjet(directAccessKey: String?) {
        setTestAppSettings()
        val ujetStartOptions: UjetStartOptions
        val ticketId = TestAppSettings.ticketId
        val preferredChannel = UjetPreferredChannel.getEnumFromValue(TestAppSettings.preferredChannel)

        val cachedCustomDataParams = TestAppSettings.customDataParams
        val cachedCustomUnsignedData = TestAppSettings.customUnsignedData
        var ujetCustomData: UjetCustomData? = if (cachedCustomDataParams.isUnsignedCustomDataEnabled &&
            !cachedCustomUnsignedData.isNullOrEmpty()
        ) {
            CustomDataHelper.generateCustomData(cachedCustomUnsignedData)
        } else {
            null
        }

        if (TestAppSettings.chatTranscriptExampleJson.isNotEmpty()) {
            if (ujetCustomData == null) {
                ujetCustomData = UjetCustomData()
            }
            try {
                val chatTranscript = JSONObject(TestAppSettings.chatTranscriptExampleJson)
                ujetCustomData.putJsonObject("external_chat_transfer", chatTranscript)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        ujetStartOptions = UjetStartOptions.Builder()
            .setMenuKey(directAccessKey)
            .setTicketId(ticketId)
            .setUnsignedCustomData(ujetCustomData)
            .setPreferredChannel(preferredChannel)
            .build()
        Ujet.start(ujetStartOptions)
        setUjetWebFormListener()
        setCoBrowseAlertProvider()
    }

    private fun showStartUjetWithKey() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Please input initial Menu Key")
        val key = preferences?.getString("directAccessKey", "key-1")
        val input = EditText(activity)
        input.setText(key)
        input.inputType = InputType.TYPE_CLASS_TEXT
        builder.setView(input)

        // Set up the buttons
        builder.setPositiveButton("OK") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val directAccessKey = input.text.toString()
            preferences?.edit()?.putString("directAccessKey", directAccessKey)?.apply()
            startUjet(directAccessKey)
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun startUjetWithIvr() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Please input initial IVR phone number")
        val key = preferences?.getString("ivrPhoneNumber", "")
        val input = EditText(activity)
        input.setText(key)
        input.inputType = InputType.TYPE_CLASS_TEXT
        builder.setView(input)

        // Set up the buttons
        builder.setPositiveButton("OK") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val ivrPhoneNumber = input.text.toString()
            preferences?.edit()?.putString("ivrPhoneNumber", ivrPhoneNumber)?.apply()
            setTestAppSettings()
            val ujetStartOptions: UjetStartOptions = if (ivrPhoneNumber.isEmpty()) {
                UjetStartOptions.Builder()
                    .setIvrMode(true)
                    .build()
            } else {
                UjetStartOptions.Builder()
                    .setIvrMode(true)
                    .setIvrPhoneNumber(ivrPhoneNumber)
                    .build()
            }
            Ujet.start(ujetStartOptions)
            setUjetWebFormListener()
            setCoBrowseAlertProvider()
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun startWithoutSplash() {
        val preferredChannel = UjetPreferredChannel.getEnumFromValue(TestAppSettings.preferredChannel)
        val ujetStartOptions = UjetStartOptions.Builder()
            .setSkipSplashEnabled(true)
            .setPreferredChannel(preferredChannel)
            .build()
        Ujet.start(ujetStartOptions)
        setUjetWebFormListener()
        setCoBrowseAlertProvider()
    }

    private fun setUjetWebFormListener() {
        Ujet.setUjetWebFormListener(object : UjetWebFormListener {
            override fun ujetWebFormDidReceive(event: Map<String, Any?>, callback: UjetWebFormCallback) {
                //This part will be done by customer, for testing purposes we are mocking it here
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        if (isShowWebFormLoadingFailedScreenEnabled()) {
                            callback.onError()
                            return@launch
                        }
                        val request = FormDataRequest(
                            externalFormId = event["external_form_id"].toString(),
                            smartActionId = event["smart_action_id"].toString().toInt()
                        )
                        val response = formApiService.fetchFormData(request)
                        when {
                            response.isSuccessful -> {
                                val formData = response.body()
                                val data = mapOf(
                                    "smart_action_id" to formData?.data?.smartActionId,
                                    "external_form_id" to formData?.data?.externalFormId,
                                    "uri" to formData?.data?.uri
                                )
                                val formDataEvent = mapOf(
                                    "type" to formData?.type,
                                    "signature" to formData?.signature,
                                    "data" to data
                                )
                                if (isShowWebFormTimedOutScreenEnabled()) {
                                    delay(70000) // giving delay 70s, It will cause timed out as the time limit is 60s in SDK
                                }
                                callback.onEvent(formDataEvent)
                            }

                            else -> {
                                callback.onError()
                            }
                        }
                    } catch (e: Throwable) {
                        callback.onError()
                    }
                }
            }
        })
    }

    private fun setTestAppSettings() {
        val identifier = preferences?.getString("identifier", null)
        val email = preferences?.getString("userId", "<EMAIL>")
        val name = preferences?.getString("name", "TestDevice")
        val phoneNumber = preferences?.getString("phoneNumber", null)
        TestAppSettings.identifier = identifier
        TestAppSettings.name = name
        TestAppSettings.email = email
        TestAppSettings.phoneNumber = phoneNumber
    }

    private fun choiceDefaultLanguage() {
        val activity = activity ?: return
        val context = activity.applicationContext
        val languages = context.resources.getStringArray(co.ujet.android.R.array.ujet_language_names)
        val languageCodes = context.resources.getStringArray(co.ujet.android.R.array.ujet_language_codes)
        var checkedIndex = 0
        val chooseLanguage = TestAppSettings.language
        if (chooseLanguage != null) {
            for (index in languageCodes.indices) {
                if (chooseLanguage == languageCodes[index]) {
                    checkedIndex = index
                }
            }
        }
        val builder = AlertDialog.Builder(activity)
        builder.setSingleChoiceItems(
            languages,
            checkedIndex
        ) { _: DialogInterface?, which: Int ->
            TestAppSettings.language = languageCodes[which]
            UjetInitializer.init()
            Toast.makeText(getContext(), "Changed default language to " + languages[which], Toast.LENGTH_SHORT).show()
        }
        builder.show()
    }

    private fun showDelayInputDialog() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Input delay milliseconds 0 ~ 5000 (5sec)")
        val input = EditText(activity)
        input.setText(String.format(Locale.US, "%d", TestAppSettings.getSignDelay()))
        input.inputType = InputType.TYPE_CLASS_NUMBER
        builder.setView(input)
        builder.setPositiveButton("OK") { _: DialogInterface?, _: Int ->
            val result = input.text.toString()
            TestAppSettings.setSignDelay(result.toInt())
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun showFallbackPhoneNumberDialog() {
        val activity = activity ?: return
        val input = EditText(activity)
        input.setText(String.format(Locale.US, "%s", Configuration.instance.fallbackPhoneNumber))
        input.inputType = InputType.TYPE_CLASS_PHONE
        AlertDialog.Builder(activity)
            .setTitle(R.string.set_fallback_phone_number)
            .setView(input)
            .setPositiveButton("OK") { _: DialogInterface?, _: Int -> UjetInitializer.setOptions(input.text.toString()) }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showAndCopyFcmToken() {
        val context = activity?.applicationContext ?: return
        FirebaseMessaging.getInstance().token
            .addOnCompleteListener { task: Task<String?> ->
                if (!task.isSuccessful || task.result == null) {
                    Toast.makeText(context, "Couldn't get FCM token", Toast.LENGTH_LONG).show()
                    return@addOnCompleteListener
                }
                val token = task.result
                Toast.makeText(context, String.format("Your token is %s\nCopied to clipboard", token), Toast.LENGTH_LONG).show()
                val clipboard = context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clip = ClipData.newPlainText("fcm_token", token)
                clipboard.setPrimaryClip(clip)
            }
    }

    private fun showCustomDataParamsConfig() {
        val activity = activity ?: return
        val intent = Intent(activity, CustomDataActivity::class.java)
        startActivity(intent)
    }

    private fun showTicketIdConfig() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Send Ticket Id to CRM")
        val input = EditText(activity)
        if (TestAppSettings.ticketId.isEmpty()) {
            input.hint = "Input the data to send"
        } else {
            input.setText(TestAppSettings.ticketId)
        }
        input.inputType = InputType.TYPE_CLASS_TEXT
        builder.setView(input)
        builder.setPositiveButton("OK") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val ticketId = input.text.toString()
            TestAppSettings.ticketId = ticketId
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun showCustomCobrowseLicenseKeyConfig() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Configure custom cobrowse license key")
        val input = EditText(activity)
        if (TestAppSettings.customCobrowseLicenseKey.isEmpty()) {
            input.hint = "Input custom cobrowse license key"
        } else {
            input.setText(TestAppSettings.customCobrowseLicenseKey)
        }
        input.inputType = InputType.TYPE_CLASS_TEXT
        builder.setView(input)
        builder.setPositiveButton("OK") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val cobrowseLicenseKey = input.text.toString()
            TestAppSettings.customCobrowseLicenseKey = cobrowseLicenseKey
            activity.finish() //restart required to reflect it
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun showCustomCobrowseUrlConfig() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Configure custom cobrowse url")
        val input = EditText(activity)
        if (TestAppSettings.customCobrowseUrl.isEmpty()) {
            input.hint = "Input custom cobrowse url"
        } else {
            input.setText(TestAppSettings.customCobrowseUrl)
        }
        input.inputType = InputType.TYPE_CLASS_TEXT
        builder.setView(input)
        builder.setPositiveButton("OK") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val cobrowseUrl = input.text.toString()
            TestAppSettings.customCobrowseUrl = cobrowseUrl
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun showPreferredChannelConfig() {
        val activity = activity ?: return
        val builder = AlertDialog.Builder(activity)
        builder.setTitle("Configure preferred channel")

        val preferredChannel = UjetPreferredChannel.getEnumFromValue(TestAppSettings.preferredChannel)
        var checkedItem = 0
        val items = ArrayList<String>()
        enumValues<UjetPreferredChannel>().forEachIndexed { index, element ->
            items.add(element.name)
            if (preferredChannel.toString() == element.name) {
                checkedItem = index
            }
        }

        builder.setSingleChoiceItems(items.toTypedArray(), checkedItem)
        { dialog: DialogInterface, selectedChannelIndex ->
            dialog.cancel()
            val selectedChannel = UjetPreferredChannel.values()[selectedChannelIndex].value
            Toast.makeText(activity, "Selected preferred channel is $selectedChannel", Toast.LENGTH_LONG).show()
            TestAppSettings.preferredChannel = selectedChannel
        }

        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun showMarkdownActivity() {
        val activity = activity ?: return
        val intent = Intent(activity, MarkdownActivity::class.java)
        startActivity(intent)
    }

    private fun showChatBubblesActivity() {
        val activity = activity ?: return
        val intent = Intent(activity, ChatBubblesActivity::class.java)
        startActivity(intent)
    }

    private fun showChatTranscriptActivity() {
        val activity = activity ?: return
        val intent = Intent(activity, ChatTranscriptActivity::class.java)
        startActivity(intent)
    }

    private fun showSurveyPageActivity() {
        val activity = activity ?: return
        UjetSurveyActivity.start(activity)
    }

    private fun showUjetStyleOptionsActivity() {
        val activity = activity ?: return
        val intent = Intent(activity, UjetStyleOptionsActivity::class.java)
        startActivity(intent)
    }

    private fun setCoBrowseAlertProvider() {
        if (TestAppSettings.isShowCustomCoBrowseAlertEnabled) {
            Ujet.setCoBrowseAlertProvider(CoBrowseAlertProviderImpl())
        }
    }

}
