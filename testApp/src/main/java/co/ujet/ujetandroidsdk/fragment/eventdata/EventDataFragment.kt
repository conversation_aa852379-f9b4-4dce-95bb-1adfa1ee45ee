package co.ujet.ujetandroidsdk.fragment.eventdata

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ListView
import android.widget.TextView
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.extensions.registerNavigationBarMenuProvider
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings.eventData
import co.ujet.ujetandroidsdk.TestAppSettings.isEventDataNotificationEnabled
import co.ujet.ujetandroidsdk.TestAppSettings.setEventData
import co.ujet.ujetandroidsdk.common.EventData

class EventDataFragment : BaseFragment() {
    private var eventDataListView: ListView? = null
    private var messageTextView: TextView? = null
    private var deleteButtonEnabled = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_event_data, container, false)
        eventDataListView = view.findViewById(R.id.event_list_view)
        eventDataListView?.adapter = EventDataListAdapter(activity, ArrayList())
        messageTextView = view.findViewById(R.id.message)
        registerNavigationBarMenuProvider(R.menu.menu_clear, { menuItem ->
            handleLongPressInMenuItem(menuItem.title.toString())
            menuItem.setEnabled(deleteButtonEnabled)
        }, { menuItemSelected ->
            when (menuItemSelected) {
                R.id.menu_item_delete -> {
                    clearEventData()
                    refreshView()
                    true
                }
                else -> false
            }
        })
        return view
    }

    override fun onResume() {
        super.onResume()
        refreshView()
    }

    override fun onDestroyView() {
        eventDataListView = null
        messageTextView = null
        super.onDestroyView()
    }

    private fun refreshView() {
        if (isEventDataNotificationEnabled) {
            if (eventData.isEmpty()) {
                setDeleteButtonEnabled(false)
                messageTextView?.visibility = View.VISIBLE
                messageTextView?.text = getString(R.string.event_data_default_message)
                eventDataListView?.visibility = View.GONE
            } else {
                setDeleteButtonEnabled(true)
                messageTextView?.visibility = View.GONE
                eventDataListView?.visibility = View.VISIBLE
                refreshEventDataList(eventData)
            }
        } else {
            setDeleteButtonEnabled(false)
            messageTextView?.visibility = View.VISIBLE
            messageTextView?.text = getString(R.string.event_data_warning_message)
            eventDataListView?.visibility = View.GONE
        }
    }

    private fun refreshEventDataList(eventData: List<EventData?>) {
        val eventDataListAdapter = eventDataListView?.adapter as EventDataListAdapter
        eventDataListAdapter.clear()
        eventDataListAdapter.addAll(eventData)
        eventDataListAdapter.notifyDataSetChanged()
    }

    private fun clearEventData() {
        setEventData(null)
    }

    private fun setDeleteButtonEnabled(deleteButtonEnabled: Boolean) {
        this.deleteButtonEnabled = deleteButtonEnabled
        activity?.invalidateOptionsMenu()
    }
}
