package co.ujet.ujetandroidsdk.fragment

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.ImageView
import android.widget.ListView
import android.widget.Spinner
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import co.ujet.android.app.common.BaseFragment
import co.ujet.ujetandroidsdk.R
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.activity.CustomDataActivity
import co.ujet.ujetandroidsdk.common.CustomData
import co.ujet.ujetandroidsdk.common.CustomDataListAdapter
import co.ujet.ujetandroidsdk.common.CustomDataType
import co.ujet.ujetandroidsdk.util.StyleUtil

class UnsignedCustomDataFragment: BaseFragment(), CustomDataListAdapter.OnClickListener {
    private var customDataListView: ListView? = null
    private var messageTextView: TextView?= null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val view = inflater.inflate(R.layout.fragment_unsigned_custom_data, container, false)
        messageTextView = view.findViewById(R.id.message)
        view.findViewById<View>(R.id.root_view).apply {
            this.setBackgroundColor(Injection.provideUjetStyle(context).primaryBackgroundColor)
        }
        view.findViewById<ImageView>(R.id.add_custom_data)?.apply {
            isClickable = true
            setOnClickListener {
                onAddButtonClicked()
            }
        }
        setTextViewColor(view, R.id.send_custom_data_label)
        customDataListView = view.findViewById<ListView?>(R.id.custom_data_list_view)?.apply {
            adapter = CustomDataListAdapter(context, CustomDataType.Unsigned, ArrayList())
        }
        setTextViewColor(view, R.id.tap_for_visibility_label)
        setActionBarTitle()
        return view
    }

    override fun onResume() {
        super.onResume()
        setActionBarTitle()
        refreshView()
    }

    override fun onDestroyView() {
        customDataListView = null
        messageTextView = null
        super.onDestroyView()
    }

    override fun onClick(position: Int) {
        val updatedCustomData = TestAppSettings.customUnsignedData
        updatedCustomData?.removeAt(position)
        TestAppSettings.customUnsignedData = updatedCustomData
        refreshView()
    }

    private fun setActionBarTitle() {
        val activity = activity as? CustomDataActivity? ?: return
        activity.supportActionBar?.title = getString(R.string.unsigned_custom_data)
    }

    private fun refreshView() {
        val cachedCustomUnsignedData = TestAppSettings.customUnsignedData
        if (cachedCustomUnsignedData != null) {
            messageTextView?.visibility = View.GONE
            customDataListView?.visibility = View.VISIBLE
            refreshCustomDataList(cachedCustomUnsignedData)
        } else {
            messageTextView?.visibility = View.VISIBLE
            messageTextView?.text = getString(co.ujet.ujetandroidsdk.R.string.event_data_default_message)
            customDataListView?.visibility = View.GONE
        }
    }

    private fun refreshCustomDataList(customData: List<CustomData>) {
        val customDataListAdapter = customDataListView?.adapter as? CustomDataListAdapter
        customDataListAdapter?.setOnClickListener(this)
        customDataListAdapter?.clear()
        customDataListAdapter?.addAll(customData)
        customDataListAdapter?.notifyDataSetChanged()
    }

    private fun onAddButtonClicked() {
        val context = context ?: return
        val builder = AlertDialog.Builder(context)
        builder.setTitle("Unsigned custom data to send")
        val dialogView: View = this.layoutInflater.inflate(R.layout.dialog_signed_custom_data_config, null)
        builder.setView(dialogView)
        dialogView.findViewById<View>(R.id.root_view).apply {
            getUjetStyle()?.primaryBackgroundColor?.let { this.setBackgroundColor(it) }
        }
        setTextViewColor(dialogView, R.id.custom_data_key_label)
        val customDataKeyEditText = dialogView.findViewById<EditText>(R.id.custom_data_key_edit_text)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            hint = "Enter here"
            getUjetStyle()?.textSecondaryColor?.let { setTextColor(it) }
            getUjetStyle()?.pickerTextNoCenterColor?.let { setHintTextColor(it) }
        }
        setTextViewColor(dialogView, R.id.custom_data_label_label)
        val customDataLabelEditText = dialogView.findViewById<EditText>(R.id.custom_data_label_edit_text)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            hint = "Enter here"
            getUjetStyle()?.textSecondaryColor?.let { setTextColor(it) }
            getUjetStyle()?.pickerTextNoCenterColor?.let { setHintTextColor(it) }
        }
        setTextViewColor(dialogView, R.id.custom_data_type_label)
        var customDataType = "string"
        val spinnerAdapter = ArrayAdapter.createFromResource(context,
            R.array.spinner_custom_data_type, R.layout.spinner_list_item)
        spinnerAdapter.setDropDownViewResource(R.layout.spinner_list_item)
        dialogView.findViewById<Spinner>(R.id.custom_data_type_spinner).apply {
            background.colorFilter = getUjetStyle()?.textPrimaryColor?.let {
                BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                    it, BlendModeCompat.SRC_ATOP)
            }
            adapter = spinnerAdapter
            setSelection(0)
            onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    customDataType = SpinnerCustomDataType.values()[position].value
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {}
            }
        }
        setTextViewColor(dialogView, R.id.custom_data_value_label)
        val customDataValueEditText = dialogView.findViewById<EditText>(R.id.custom_data_value_edit_text)?.apply {
            background = StyleUtil.getRoundedRectangleDrawable(context)
            hint = "Enter here"
            getUjetStyle()?.textSecondaryColor?.let { setTextColor(it) }
            getUjetStyle()?.pickerTextNoCenterColor?.let { setHintTextColor(it) }
        }

        builder.setPositiveButton("Save") { dialog: DialogInterface, _: Int ->
            dialog.cancel()
            val newCustomDataEntry = CustomData(customDataKeyEditText?.text.toString(), customDataLabelEditText?.text.toString(),
                customDataType, customDataValueEditText?.text.toString(), false)
            val cachedCustomUnsignedData = TestAppSettings.customUnsignedData
            cachedCustomUnsignedData?.add(newCustomDataEntry)
            TestAppSettings.customUnsignedData = cachedCustomUnsignedData
            refreshView()
        }
        builder.setNegativeButton("Cancel") { dialog: DialogInterface, _: Int -> dialog.cancel() }
        builder.show()
    }

    private fun getUjetStyle(): UjetStyle? {
        return context?.let { Injection.provideUjetStyle(it) }
    }

    private fun setTextViewColor(dialogView: View, resId: Int) {
        dialogView.findViewById<TextView>(resId)?.apply {
            getUjetStyle()?.let { UjetViewStyler.stylePrimaryText(it, this) }
        }
    }

    enum class SpinnerCustomDataType(val value: kotlin.String) {
        String("string"),
        Number("number"),
        Boolean("boolean"),
        Date("date");
    }

    companion object {
        const val TAG = "UnsignedCustomDataFragment"

        fun newInstance(): UnsignedCustomDataFragment {
            return UnsignedCustomDataFragment()
        }
    }
}
