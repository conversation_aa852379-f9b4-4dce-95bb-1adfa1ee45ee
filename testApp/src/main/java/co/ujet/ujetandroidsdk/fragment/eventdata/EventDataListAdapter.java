package co.ujet.ujetandroidsdk.fragment.eventdata;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import co.ujet.ujetandroidsdk.R;
import co.ujet.ujetandroidsdk.common.EventData;

public class EventDataListAdapter extends ArrayAdapter<EventData> {

    EventDataListAdapter(Context context, List<EventData> eventData) {
        super(context, 0, new ArrayList<>(eventData));
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return getEventDataView(getItem(position), convertView, parent);
    }

    private View getEventDataView(EventData eventData, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(getContext())
                    .inflate(R.layout.event_data_list_item, parent, false);
        }

        TextView eventNameTextView = convertView.findViewById(R.id.event_name);
        eventNameTextView.setText(eventData.getEventType());

        TextView eventDataTextView = convertView.findViewById(R.id.event_data);
        eventDataTextView.setText(eventData.getEventData());

        return convertView;
    }
}
