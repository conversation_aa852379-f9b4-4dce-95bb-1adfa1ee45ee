package co.ujet.ujetandroidsdk.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.core.view.MenuProvider
import co.ujet.android.app.FragmentHelper
import co.ujet.android.app.common.BaseFragment
import co.ujet.ujetandroidsdk.R
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetStyle
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.common.CustomDataParams

class CustomDataFragment: BaseFragment() {
    private var customDataParams: CustomDataParams? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val view = inflater.inflate(R.layout.fragment_custom_data_main, container, false)
        setupLayout(view)
        setupNavigationBarMenu()
        return view
    }

    private fun setupNavigationBarMenu() {
        activity?.addMenuProvider(
            object : MenuProvider {
                override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                    menu.clear()
                    menuInflater.inflate(R.menu.menu_save, menu)
                }

                override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                    return when (menuItem.itemId) {
                        R.id.menu_item_save -> {
                            saveCustomDataOptions()
                            true
                        }
                        else -> {
                            false
                        }
                    }
                }
            },
            viewLifecycleOwner
        )
    }

    private fun setupLayout(view: View) {
        view.findViewById<View>(R.id.root_view).apply {
            getUjetStyle()?.primaryBackgroundColor?.let { this.setBackgroundColor(it) }
        }

        val cachedCustomData = TestAppSettings.customDataParams
        customDataParams = CustomDataParams(cachedCustomData.isSignedCustomDataEnabled, cachedCustomData.isUnsignedCustomDataEnabled,
            cachedCustomData.isReservedDataConfigEnabled, cachedCustomData.isCustomerVerified,
            cachedCustomData.isCustomerBadActor, cachedCustomData.isCustomerRepeated)
        setTextViewColor(view, R.id.signed_custom_data_label)
        var isSignedCustomDataEnabled = customDataParams?.isSignedCustomDataEnabled ?: false
        view.findViewById<SwitchCompat?>(R.id.signed_custom_data_switch).apply {
            isChecked = isSignedCustomDataEnabled
            setOnClickListener {
                isSignedCustomDataEnabled = !isSignedCustomDataEnabled
                customDataParams?.isSignedCustomDataEnabled = isSignedCustomDataEnabled
            }
        }
        setTextViewColor(view, R.id.customer_verified_label)
        showCustomSwitchView(view, ReservedDataType.CUSTOMER_VERIFIED)
        setTextViewColor(view, R.id.customer_bad_actor_label)
        showCustomSwitchView(view, ReservedDataType.CUSTOMER_BAD_ACTOR)
        setTextViewColor(view, R.id.customer_repeated_label)
        showCustomSwitchView(view, ReservedDataType.CUSTOMER_REPEATED)

        setTextViewColor(view, R.id.reserved_data_config_label)
        var isReservedDataConfigEnabled = customDataParams?.isReservedDataConfigEnabled ?: false
        view.findViewById<SwitchCompat?>(R.id.reserved_data_config_switch).apply {
            isChecked = isReservedDataConfigEnabled
            setOnClickListener {
                isReservedDataConfigEnabled = !isReservedDataConfigEnabled
                customDataParams?.isReservedDataConfigEnabled = isReservedDataConfigEnabled
                showOrHideReservedDataView(view, isReservedDataConfigEnabled)
            }
        }
        showOrHideReservedDataView(view, isReservedDataConfigEnabled)
        setTextViewColor(view, R.id.signed_data_show_details_label)
        view.findViewById<TextView>(R.id.signed_data_show_details_label)?.apply {
            setOnClickListener {
                showSignedCustomDataFragment()
            }
        }
        setTextViewColor(view, R.id.unsigned_custom_data_label)
        var isUnsignedCustomDataEnabled = customDataParams?.isUnsignedCustomDataEnabled ?: false
        view.findViewById<SwitchCompat?>(R.id.unsigned_custom_data_switch).apply {
            isChecked = isUnsignedCustomDataEnabled
            setOnClickListener {
                isUnsignedCustomDataEnabled = !isUnsignedCustomDataEnabled
                customDataParams?.isUnsignedCustomDataEnabled = isUnsignedCustomDataEnabled
            }
        }
        setTextViewColor(view, R.id.unsigned_data_show_details_label)
        view.findViewById<TextView>(R.id.unsigned_data_show_details_label)?.apply {
            setOnClickListener {
                showUnsignedCustomDataFragment()
            }
        }
    }

    private fun showSignedCustomDataFragment() {
        FragmentHelper.show(
            this,
            SignedCustomDataFragment.newInstance(),
            SignedCustomDataFragment.TAG
        )
    }

    private fun showUnsignedCustomDataFragment() {
        FragmentHelper.show(
            this,
            UnsignedCustomDataFragment.newInstance(),
            UnsignedCustomDataFragment.TAG
        )
    }

    private fun showOrHideReservedDataView(view: View, isReservedDataConfigEnabled: Boolean) {
        view.findViewById<View>(R.id.reserved_keys_layout)?.apply {
            visibility = if (isReservedDataConfigEnabled) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    private fun getUjetStyle(): UjetStyle? {
        return context?.let { Injection.provideUjetStyle(it) }
    }

    private fun setTextViewColor(view: View, resId: Int) {
        view.findViewById<TextView>(resId)?.apply {
            getUjetStyle()?.let { UjetViewStyler.stylePrimaryText(it, this) }
        }
    }

    private fun showCustomSwitchView(view: View, reservedDataType: ReservedDataType) {
        val (resourceId, defaultValue) = when (reservedDataType) {
            ReservedDataType.CUSTOMER_VERIFIED -> Pair(R.id.customer_verified_switch, customDataParams?.isCustomerVerified ?: false)
            ReservedDataType.CUSTOMER_BAD_ACTOR -> Pair(R.id.customer_bad_actor_switch, customDataParams?.isCustomerBadActor ?: false)
            ReservedDataType.CUSTOMER_REPEATED -> Pair(R.id.customer_repeated_switch, customDataParams?.isCustomerRepeated ?: false)
        }
        view.findViewById<SwitchCompat?>(resourceId).apply {
            isChecked = defaultValue
            setOnClickListener {
                when (reservedDataType) {
                    ReservedDataType.CUSTOMER_VERIFIED -> customDataParams?.isCustomerVerified = !defaultValue
                    ReservedDataType.CUSTOMER_BAD_ACTOR -> customDataParams?.isCustomerBadActor = !defaultValue
                    ReservedDataType.CUSTOMER_REPEATED -> customDataParams?.isCustomerRepeated = !defaultValue
                }
            }
        }
    }

    private fun saveCustomDataOptions() {
        customDataParams?.let { TestAppSettings.customDataParams = it }
        activity?.finish()
    }

    enum class ReservedDataType {
        CUSTOMER_VERIFIED,
        CUSTOMER_BAD_ACTOR,
        CUSTOMER_REPEATED
    }

    companion object {
        const val TAG = "CustomDataFragment"

        fun newInstance(): CustomDataFragment {
            return CustomDataFragment()
        }
    }
}
