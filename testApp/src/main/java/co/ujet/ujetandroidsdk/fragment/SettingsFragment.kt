package co.ujet.ujetandroidsdk.fragment

import android.content.Context
import androidx.preference.PreferenceFragmentCompat
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import android.os.Bundle
import co.ujet.ujetandroidsdk.TestAppSettings
import android.content.SharedPreferences
import com.joanzapata.iconify.IconDrawable
import co.ujet.ujetandroidsdk.util.EnvironmentUtil
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.annotation.Keep
import androidx.preference.DropDownPreference
import androidx.preference.EditTextPreference
import androidx.preference.ListPreference
import androidx.preference.Preference
import androidx.preference.PreferenceCategory
import androidx.preference.SwitchPreference
import co.ujet.ujetandroidsdk.R
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_building
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_gear
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_globe
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_key
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_link
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_lock
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_magic
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_save
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_server
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_signal
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_tag
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_toggle_off
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_toggle_on
import com.joanzapata.iconify.fonts.FontAwesomeIcons.fa_spinner
import java.lang.ClassCastException

class SettingsFragment @Keep constructor() : PreferenceFragmentCompat(), OnSharedPreferenceChangeListener {
    interface SettingChangeListener {
        fun onSettingChanged()
        fun onClose()
    }

    private var settingChangeListener: SettingChangeListener? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        settingChangeListener = try {
            context as SettingChangeListener
        } catch (ex: ClassCastException) {
            throw ClassCastException("$context must implement SettingChangeListener")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        preferenceManager.sharedPreferencesName = TestAppSettings.TEST_APP_PREFERENCES_NAME
        preferenceManager.sharedPreferencesMode = Context.MODE_PRIVATE

        // Build information
        val buildInfo = findPreference<Preference>("build")
        buildInfo?.summary = TestAppSettings.getBuildInfo(requireContext())

        refresh()

        preferenceScreen.sharedPreferences?.registerOnSharedPreferenceChangeListener(this)
    }

    fun refresh() {
        val sharedPreferences = requireActivity().getSharedPreferences(TestAppSettings.TEST_APP_PREFERENCES_NAME, Context.MODE_PRIVATE)
        val category = findPreference<PreferenceCategory>("test_app_setting")
        val env = TestAppSettings.env
        val tenant = TestAppSettings.tenant

        // Env
        run {
            val pref = findPreference<ListPreference>("env") ?: return@run
            val icon = IconDrawable(activity, fa_server)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.entries = EnvironmentUtil.allNames
            pref.entryValues = EnvironmentUtil.allNames
            pref.setDefaultValue(TestAppSettings.env.name)
            pref.summary = env.name
            pref.value = env.name
            if (EnvironmentUtil.all.size == 1 && category != null) {
                category.removePreference(pref)
            }
        }

        // Tenant
        run {
            val pref = findPreference<ListPreference>("tenant") ?: return@run
            val icon = IconDrawable(activity, fa_building)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.entries = env.tenantNames
            pref.entryValues = env.tenantNames
            pref.setDefaultValue(TestAppSettings.tenant.subdomain)
            pref.summary = tenant.subdomain
            pref.value = tenant.subdomain
        }

        // Theme
        run {
            val pref = findPreference<ListPreference>("theme") ?: return@run
            val icon = IconDrawable(activity, fa_magic)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = sharedPreferences.getString("theme", "Default")
            pref.value = sharedPreferences.getString("theme", "Default")
            val entryKey = "themeListArray"
            val entryValuesKey = "themeValues"
            pref.entries = getStringArray(entryKey)
            pref.entryValues = getStringArray(entryValuesKey)
            pref.setDefaultValue(TestAppSettings.theme)
        }

        // Company Name
        run {
            val pref = findPreference<EditTextPreference>("company_name") ?: return@run
            val icon = IconDrawable(activity, fa_tag)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = sharedPreferences.getString("company_name", "")
            pref.text = sharedPreferences.getString("company_name", "")
            if (tenant.isNotCustom) {
                category?.removePreference(pref)
            }
        }

        // Company Key
        run {
            val pref = findPreference<EditTextPreference>("company_key") ?: return@run
            val icon = IconDrawable(activity, fa_key)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = tenant.key
            pref.text = tenant.key
            pref.isVisible = !tenant.isNotCustom
        }

        // Company Secret
        run {
            val pref = findPreference<EditTextPreference>("company_secret") ?: return@run
            val icon = IconDrawable(activity, fa_lock)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = tenant.secret
            pref.text = tenant.secret
            pref.isVisible = !tenant.isNotCustom
        }

        // Host
        run {
            val pref = findPreference<EditTextPreference>("host") ?: return@run
            val icon = IconDrawable(activity, fa_globe)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = tenant.host
            pref.text = tenant.host
            pref.isVisible = !tenant.isNotCustom
        }

        // Port
        run {
            val pref = findPreference<EditTextPreference>("port") ?: return@run
            val icon = IconDrawable(activity, fa_link)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = tenant.port.toString()
            pref.text = tenant.port.toString()
            pref.isVisible = tenant.isLocal
        }

        // Network sensitivity
        run {
            val pref = findPreference<EditTextPreference>("networkSensitivity") ?: return@run
            val icon = IconDrawable(activity, fa_signal)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.summary = sharedPreferences.getString("networkSensitivity", "0.0")
            pref.text = sharedPreferences.getString("networkSensitivity", "0.0")
        }

        // Dark Mode Enabled
        run {
            val pref = findPreference<SwitchPreference>("darkModeEnabled") ?: return@run
            val icon = getIconForState(sharedPreferences, "darkModeEnabled")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("darkModeEnabled", false)
        }
        // Show Custom CoBrowse Alert Enabled
        run {
            val pref = findPreference<SwitchPreference>("showCustomCoBrowseAlert") ?: return@run
            val icon = getIconForState(sharedPreferences, "showCustomCoBrowseAlert")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("showCustomCoBrowseAlert", false)
        }

        // Landscape Mode Disabled
        run {
            val pref = findPreference<SwitchPreference>("landscapeModeDisabled") ?: return@run
            val icon = getIconForState(sharedPreferences, "landscapeModeDisabled")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("landscapeModeDisabled", false)
        }

        // Show single channel Enabled
        run {
            val pref = findPreference<SwitchPreference>("showSingleChannelEnabled") ?: return@run
            val icon = getIconForState(sharedPreferences, "showSingleChannelEnabled")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("showSingleChannelEnabled", false)
        }

        // Auto Minimize Call View
        run {
            val pref = findPreference<SwitchPreference>("autoMinimizeCallView") ?: return@run
            val icon = getIconForState(sharedPreferences, "autoMinimizeCallView")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("autoMinimizeCallView", false)
        }

        // Remove Agent Icon Border Enabled
        run {
            val pref = findPreference<SwitchPreference>("removeAgentIconBorderEnabled") ?: return@run
            val icon = getIconForState(sharedPreferences, "removeAgentIconBorderEnabled")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("removeAgentIconBorderEnabled", false)
        }

        //Event Data Notification Enabled
        run {
            val pref = findPreference<SwitchPreference>("eventDataNotificationEnabled") ?: return@run
            val icon = getIconForState(sharedPreferences, "eventDataNotificationEnabled")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("eventDataNotificationEnabled", false)
        }

        // Static Font Size In Picker View
        run {
            val pref = findPreference<SwitchPreference>("staticFontSizeInPickerView") ?: return@run
            val icon = getIconForState(sharedPreferences, "staticFontSizeInPickerView")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("staticFontSizeInPickerView", false)
        }

        // Hide Media Attachment In Chat
        run {
            val pref = findPreference<SwitchPreference>("hideMediaAttachmentInChat") ?: return@run
            val icon = getIconForState(sharedPreferences, "hideMediaAttachmentInChat")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("hideMediaAttachmentInChat", false)
        }

        // Hide Skip Button In CSAT
        run {
            val pref = findPreference<SwitchPreference>("showCsatSkipButton") ?: return@run
            val icon = getIconForState(sharedPreferences, "showCsatSkipButton")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("showCsatSkipButton", false)
        }

        // Ignore Read Phone State Permission
        run {
            val pref = findPreference<SwitchPreference>("ignoreReadPhoneStatePermission") ?: return@run
            val icon = getIconForState(sharedPreferences, "ignoreReadPhoneStatePermission")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("ignoreReadPhoneStatePermission", false)
        }

        // Block End User ChatTermination Flow or allow it
        run {
            val pref = findPreference<SwitchPreference>("blockChatTerminationByEndUser") ?: return@run
            val icon = getIconForState(sharedPreferences, "blockChatTerminationByEndUser")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("blockChatTerminationByEndUser", false)
        }

        // Hide Status Bar
        run {
            val pref = findPreference<SwitchPreference>("hideStatusBar") ?: return@run
            val icon = getIconForState(sharedPreferences, "hideStatusBar")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("hideStatusBar", false)
        }

        // Hide Start New Conversation
        run {
            val pref = findPreference<SwitchPreference>("hideStartNewConversation") ?: return@run
            val icon = getIconForState(sharedPreferences, "hideStartNewConversation")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("hideStartNewConversation", false)
        }

        // Loading Spinner Drawable Res
        run {
            val pref = findPreference<DropDownPreference>("spinnerDrawableRes") ?: return@run
            val icon = IconDrawable(activity, fa_spinner)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.value = sharedPreferences.getString("spinnerDrawableRes", "default")
            pref.summary = pref.entry
        }

        // BEGIN OPTIONAL MODULES SECTION
        // Cobrowse
        run {
            val prefName = "enableCobrowse"
            val pref = findPreference<SwitchPreference>(prefName) ?: return@run
            val icon = getIconForState(sharedPreferences, prefName)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean(prefName, true)
        }
        // Nexmo Chat
        run {
            val prefName = "enableNexmoChat"
            val pref = findPreference<SwitchPreference>(prefName) ?: return@run
            val icon = getIconForState(sharedPreferences, prefName)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean(prefName, true)
        }
        // Nexmo Call
        run {
            val prefName = "enableNexmoCall"
            val pref = findPreference<SwitchPreference>(prefName) ?: return@run
            val icon = getIconForState(sharedPreferences, prefName)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean(prefName, true)
        }
        // Twilio Chat
        run {
            val prefName = "enableTwilioChat"
            val pref = findPreference<SwitchPreference>(prefName) ?: return@run
            val icon = getIconForState(sharedPreferences, prefName)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean(prefName, true)
        }
        // Twilio Call
        run {
            val prefName = "enableTwilioCall"
            val pref = findPreference<SwitchPreference>(prefName) ?: return@run
            val icon = getIconForState(sharedPreferences, prefName)
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean(prefName, true)
        }
        // END OPTIONAL MODULES SECTION

        // BEGIN WEB FORM SETTINGS SECTION
        // Show Web Form Loading Failed Screen
        run {
            val pref = findPreference<SwitchPreference>("showWebFormLoadingFailedScreen") ?: return@run
            val icon = getIconForState(sharedPreferences, "showWebFormLoadingFailedScreen")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("showWebFormLoadingFailedScreen", false)
        }

        // Show Web Form Timed Out Screen
        run {
            val pref = findPreference<SwitchPreference>("showWebFormTimedOutScreen") ?: return@run
            val icon = getIconForState(sharedPreferences, "showWebFormTimedOutScreen")
            icon.sizeDp(ICON_SIZE)
            pref.icon = icon
            pref.isChecked = sharedPreferences.getBoolean("showWebFormTimedOutScreen", false)
        }
        // END WEB FORM SETTINGS SECTION

        run {
            // App settings button
            val button = findPreference<Preference>("app_info") ?: return@run
            val closeIcon = IconDrawable(activity, fa_gear)
            closeIcon.sizeDp(ICON_SIZE)
            button.icon = closeIcon
            button.onPreferenceClickListener = Preference.OnPreferenceClickListener {
                val uri = Uri.fromParts("package", requireActivity().packageName, null)
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, uri)
                startActivity(intent)
                true
            }
        }
        run {
            // Close button
            val button = findPreference<Preference>("save") ?: return@run
            val closeIcon = IconDrawable(activity, fa_save)
            closeIcon.sizeDp(ICON_SIZE)
            button.icon = closeIcon
            button.onPreferenceClickListener = Preference.OnPreferenceClickListener {
                settingChangeListener?.onClose()
                true
            }
        }
    }

    private fun getIconForState(sharedPreferences: SharedPreferences, prefName: String): IconDrawable {
        val icon = if (sharedPreferences.getBoolean(prefName, false)) {
            fa_toggle_on
        } else {
            fa_toggle_off
        }
        return IconDrawable(activity, icon)
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.preferences)
    }

    override fun onDestroy() {
        preferenceScreen.sharedPreferences?.unregisterOnSharedPreferenceChangeListener(this)
        settingChangeListener = null
        super.onDestroy()
    }

    override fun onSharedPreferenceChanged(sharedPreferences: SharedPreferences?, key: String?) {
        settingChangeListener?.onSettingChanged()
    }

    private fun getStringArray(key: String): Array<String> {
        val resId = resources.getIdentifier(key, "array", requireActivity().packageName)
        return resources.getStringArray(resId)
    }

    companion object {
        const val TAG = "SettingsFragment"
        private const val ICON_SIZE = 25

        @JvmStatic
        fun newInstance() = SettingsFragment()
    }
}
