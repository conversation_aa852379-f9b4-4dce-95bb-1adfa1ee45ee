package co.ujet.ujetandroidsdk.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ListView
import android.widget.TextView
import co.ujet.android.app.common.BaseFragment
import co.ujet.android.internal.Injection
import co.ujet.android.ui.style.UjetViewStyler
import co.ujet.ujetandroidsdk.R
import co.ujet.ujetandroidsdk.TestAppSettings
import co.ujet.ujetandroidsdk.activity.CustomDataActivity
import co.ujet.ujetandroidsdk.common.CustomData
import co.ujet.ujetandroidsdk.common.CustomDataListAdapter
import co.ujet.ujetandroidsdk.common.CustomDataType
import co.ujet.ujetandroidsdk.util.CustomDataHelper

class SignedCustomDataFragment: BaseFragment() {
    private var customDataListView: ListView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val view = inflater.inflate(R.layout.fragment_signed_custom_data, container, false)
        view.findViewById<View>(R.id.root_view).apply {
            this.setBackgroundColor(Injection.provideUjetStyle(context).primaryBackgroundColor)
        }
        customDataListView = view.findViewById<ListView?>(R.id.custom_data_list_view)?.apply {
            adapter = CustomDataListAdapter(context, CustomDataType.Signed, ArrayList())
        }
        view.findViewById<TextView>(R.id.tap_for_visibility_label)?.apply {
            UjetViewStyler.stylePrimaryText(Injection.provideUjetStyle(context), this)
        }
        setActionBarTitle()
        return view
    }

    override fun onResume() {
        super.onResume()
        setActionBarTitle()
        refreshView()
    }

    override fun onDestroyView() {
        customDataListView = null
        super.onDestroyView()
    }

    private fun setActionBarTitle() {
        val activity = activity as? CustomDataActivity? ?: return
        activity.supportActionBar?.title = getString(R.string.signed_custom_data)
    }

    private fun refreshView() {
        var cachedCustomSignedData = TestAppSettings.customSignedData
        customDataListView?.visibility = View.VISIBLE
        // Generate default signed custom data and save it in cache for the first time
        if (cachedCustomSignedData.isNullOrEmpty()) {
            val context = context ?: return
            cachedCustomSignedData = CustomDataHelper.generateSignedCustomData(context)
        }
        refreshCustomDataList(cachedCustomSignedData)
    }

    private fun refreshCustomDataList(customData: List<CustomData>) {
        val customDataListAdapter = customDataListView?.adapter as? CustomDataListAdapter
        customDataListAdapter?.clear()
        customDataListAdapter?.addAll(customData)
        customDataListAdapter?.notifyDataSetChanged()
    }
    
    companion object {
        const val TAG = "SignedCustomDataFragment"

        fun newInstance(): SignedCustomDataFragment {
            return SignedCustomDataFragment()
        }
    }
}
