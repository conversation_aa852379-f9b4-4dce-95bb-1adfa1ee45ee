package co.ujet.ujetandroidsdk

import android.content.Context
import co.ujet.ujetandroidsdk.util.EnvironmentUtil.Companion.pick
import android.content.SharedPreferences
import co.ujet.ujetandroidsdk.env.Tenant
import android.content.pm.PackageManager.NameNotFoundException
import android.text.TextUtils
import co.ujet.android.UjetVersion
import co.ujet.android.modulemanager.common.ui.domain.ChatStyles
import co.ujet.ujetandroidsdk.common.CustomData
import co.ujet.ujetandroidsdk.common.EventData
import co.ujet.ujetandroidsdk.common.CustomDataParams
import co.ujet.ujetandroidsdk.env.Env
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.NumberFormatException
import java.util.ArrayList
import java.util.Locale

object TestAppSettings {
    const val TEST_APP_PREFERENCES_NAME = "co.ujet.demo.preferences"
    private const val KEY_NAME = "co.ujet.demo.preferences.name"
    private const val KEY_IDENTIFIER = "co.ujet.demo.preferences.identifier"
    private const val KEY_EMAIL = "co.ujet.demo.preferences.email"
    private const val KEY_END_USER_PHONE_NUMBER = "co.ujet.demo.preferences.endUserPhoneNumber"
    private const val KEY_DEFAULT_LANGUAGE = "co.ujet.demo.preferences.defaultLanguage"
    private const val KEY_SIGNED_CUSTOM_DATA = "co.ujet.demo.preferences.signedCustomData"
    private const val KEY_SIGNED_CUSTOM_DATA_PARAMS = "co.ujet.demo.preferences.signedCustomDataParams"
    private const val KEY_UNSIGNED_CUSTOM_DATA = "co.ujet.demo.preferences.unsignedCustomData"
    private const val KEY_CHAT_TRANSCRIPT_EXAMPLE_JSON = "co.ujet.demo.preferences.chatTranscriptExampleJson"
    private const val KEY_EVENT_DATA = "co.ujet.demo.preferences.event.data"
    private const val KEY_TICKET_ID = "co.ujet.demo.preferences.ticketId"
    private const val KEY_PREFERRED_CHANNEL = "co.ujet.demo.preferences.preferredChannel"
    private const val KEY_CUSTOM_COBROWSE_LICENSE_KEY = "co.ujet.demo.preferences.customCobrowseLicenseKey"
    private const val KEY_CUSTOM_COBROWSE_URL = "co.ujet.demo.preferences.customCobrowseUrl"
    private const val KEY_CUSTOM_CHAT_HEADER_TITLE = "co.ujet.demo.preferences.customChatHeaderTitle"
    private const val KEY_QUICK_REPLY_BUTTONS_GROUPED = "co.ujet.demo.preferences.quickReplyButtonsGrouped"
    private const val KEY_CUSTOM_CHAT_STYLES = "co.ujet.demo.preferences.customChatStyles"
    private const val ENDPOINT_URL_FORMAT = "https://%s/api/v2/"
    private const val ENDPOINT_HTTP_URL_FORMAT = "http://%s/api/v2/"
    private const val DEFAULT_COBROWSE_LICENSE_KEY = "0Blc4UGxQHHDNA"
    private const val DEFAULT_SIGN_DELAY_MILLISECONDS = 100
    private const val MAX_SIGN_DELAY_MILLISECONDS = 5000
    private const val DEFAULT_FALLBACK_SENSITIVITY = 0.85
    private lateinit var preferences: SharedPreferences
    private val gson = Gson()

    @JvmStatic
    var isIsInitialized = false
        private set
    private var signDelay = DEFAULT_SIGN_DELAY_MILLISECONDS

    var ticketId: String
        get() = preferences.getString(KEY_TICKET_ID, "") ?: ""
        set(ticketId) {
            preferences.edit().putString(KEY_TICKET_ID, ticketId).apply()
        }
    var preferredChannel: String
        get() = preferences.getString(KEY_PREFERRED_CHANNEL, "") ?: ""
        set(preferredChannel) {
            preferences.edit().putString(KEY_PREFERRED_CHANNEL, preferredChannel).apply()
        }
    var customCobrowseLicenseKey: String
        get() = preferences.getString(KEY_CUSTOM_COBROWSE_LICENSE_KEY, DEFAULT_COBROWSE_LICENSE_KEY) ?: DEFAULT_COBROWSE_LICENSE_KEY
        set(cobrowseLicenseKey) {
            preferences.edit().putString(KEY_CUSTOM_COBROWSE_LICENSE_KEY, cobrowseLicenseKey).apply()
        }
    var customCobrowseUrl: String
        get() = preferences.getString(KEY_CUSTOM_COBROWSE_URL, "") ?: ""
        set(cobrowseUrl) {
            preferences.edit().putString(KEY_CUSTOM_COBROWSE_URL, cobrowseUrl).apply()
        }

    @JvmStatic
    fun init(app: Application) {
        isIsInitialized = false
        preferences = app.getSharedPreferences(TEST_APP_PREFERENCES_NAME, Context.MODE_PRIVATE)
        isIsInitialized = true
        if (TextUtils.isEmpty(companyKey)) {
            isIsInitialized = false
        }
        if (TextUtils.isEmpty(companySecret)) {
            isIsInitialized = false
        }
    }

    val env: Env
        get() {
            val envName = preferences.getString("env", null)
            return pick(envName)
        }

    @JvmStatic
    val tenant: Tenant
        get() {
            val tenantName = preferences.getString("tenant", null)
            if ("custom" == tenantName) {
                return Tenant(
                    tenantName,
                    preferences.getString("company_key", "") ?: "",
                    preferences.getString("company_secret", "") ?: "",
                    preferences.getString("host", "") ?: "",
                    443
                )
            }
            return if ("local" == tenantName) {
                Tenant(
                    tenantName,
                    preferences.getString("company_key", "") ?: "",
                    preferences.getString("company_secret", "") ?: "",
                    preferences.getString("host", "") ?: "",
                    preferences.getString("port", "443")?.toInt() ?: 443
                )
            } else {
                env.pickTenant(tenantName)
            }
        }

    @JvmStatic
    val theme: String?
        get() = preferences.getString("theme", "Default")

    /**
     * For production, the endpoint url will be set with the subdomain in
     * [co.ujet.android.internal.Configuration] except of custom tenant.
     *
     *
     * For local, the endpoint url will start http and have owned port.
     *
     * @return
     */
    @JvmStatic
    val endpointUrl: String?
        get() {
            val tenant = tenant
            if ("production" == env.name && tenant.isNotCustom) return null
            if ("local" == tenant.subdomain) {
                val host = String.format(
                    Locale.US,
                    "%s:%d", tenant.host, tenant.port
                )
                return String.format(ENDPOINT_HTTP_URL_FORMAT, host)
            }
            return String.format(ENDPOINT_URL_FORMAT, tenant.host)
        }

    @JvmStatic
    val subdomain: String
        get() = tenant.subdomain

    @JvmStatic
    val companyKey: String
        get() = tenant.key

    @JvmStatic
    val companySecret: String
        get() = tenant.secret

    @JvmStatic
    val companyName: String
        get() = tenant.subdomain

    @JvmStatic
    val companyUrl: String
        get() = "https://ujet.co"

    @JvmStatic
    val networkSensitivity: Double
        get() = try {
            val value = preferences.getString("networkSensitivity", null)
            value?.toDouble() ?: DEFAULT_FALLBACK_SENSITIVITY
        } catch (ex: NumberFormatException) {
            DEFAULT_FALLBACK_SENSITIVITY
        }

    @JvmStatic
    val isDarkModeEnabled: Boolean
        get() = preferences.getBoolean("darkModeEnabled", false)

    @JvmStatic
    val isShowCustomCoBrowseAlertEnabled: Boolean
        get() = preferences.getBoolean("showCustomCoBrowseAlert", false)

    @JvmStatic
    val isLandscapeModeDisabled: Boolean
        get() = preferences.getBoolean("landscapeModeDisabled", false)

    @JvmStatic
    val isShowSingleChannelEnabled: Boolean
        get() = preferences.getBoolean("showSingleChannelEnabled", false)

    @JvmStatic
    fun autoMinimizeCallView() = preferences.getBoolean("autoMinimizeCallView", false)

    @JvmStatic
    val isRemoveAgentIconBorderEnabled: Boolean
        get() = preferences.getBoolean("removeAgentIconBorderEnabled", false)

    @JvmStatic
    val isEventDataNotificationEnabled: Boolean
        get() = preferences.getBoolean("eventDataNotificationEnabled", false)

    @JvmStatic
    fun staticFontSizeInPickerView() = preferences.getBoolean("staticFontSizeInPickerView", false)

    @JvmStatic
    fun hideMediaAttachmentInChat() = preferences.getBoolean("hideMediaAttachmentInChat", false)

    @JvmStatic
    fun showCsatSkipButton() = preferences.getBoolean("showCsatSkipButton", false)

    @JvmStatic
    fun ignoreReadPhoneStatePermission() = preferences.getBoolean("ignoreReadPhoneStatePermission", false)

    @JvmStatic
    fun isCobrowseEnabled() = preferences.getBoolean("enableCobrowse", true)

    @JvmStatic
    fun isNexmoChatEnabled() = preferences.getBoolean("enableNexmoChat", true)

    @JvmStatic
    fun isNexmoCallEnabled() = preferences.getBoolean("enableNexmoCall", true)

    @JvmStatic
    fun isTwilioChatEnabled() = preferences.getBoolean("enableTwilioChat", true)

    @JvmStatic
    fun isTwilioCallEnabled() = preferences.getBoolean("enableTwilioCall", true)

    fun getBuildInfo(context: Context): String {
        return if (BuildConfig.DEBUG) {
            String.format(
                Locale.US, "Build: %s v%s\nEnv: %s / %s", getApplicationName(context),
                getVersionName().first, env.name, tenant.subdomain
            )
        } else {
            String.format(
                Locale.US, "Build: %s v%s (%d)\nEnv: %s / %s", getApplicationName(context),
                getVersionName().first, getVersionName().second, env.name, tenant.subdomain
            )
        }
    }

    private fun getVersionName(): Pair<String, Int> {
        return if (BuildConfig.DEBUG) {
            if (BuildConfig.FLAVOR_env == "qa") {
                Pair(UjetVersion.SPRINT_NUMBER, 0)
            } else {
                Pair(UjetVersion.BUILD, 0)
            }
        } else {
            Pair(BuildConfig.VERSION_NAME, BuildConfig.VERSION_CODE)
        }
    }

    private fun getApplicationName(context: Context): String {
        return try {
            val applicationInfo = context.packageManager.getApplicationInfo(context.applicationInfo.packageName, 0)
            context.packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (ignore: NameNotFoundException) {
            "Unknown"
        }
    }

    @JvmStatic
    fun getSignDelay() = signDelay

    fun resetSignDelay() {
        signDelay = DEFAULT_SIGN_DELAY_MILLISECONDS
    }

    fun setSignDelay(signDelay: Int) {
        TestAppSettings.signDelay = if (signDelay < 0) {
            DEFAULT_SIGN_DELAY_MILLISECONDS
        } else if (signDelay > MAX_SIGN_DELAY_MILLISECONDS) {
            MAX_SIGN_DELAY_MILLISECONDS
        } else {
            signDelay
        }
    }

    @JvmStatic
    var phoneNumber: String?
        get() = preferences.getString(KEY_END_USER_PHONE_NUMBER, null)
        set(phoneNumber) {
            preferences.edit().putString(KEY_END_USER_PHONE_NUMBER, phoneNumber).apply()
        }

    @JvmStatic
    var identifier: String?
        get() = preferences.getString(KEY_IDENTIFIER, null)
        set(identifier) {
            preferences.edit().putString(KEY_IDENTIFIER, identifier).apply()
        }

    @JvmStatic
    var email: String?
        get() = preferences.getString(KEY_EMAIL, null)
        set(email) {
            preferences.edit().putString(KEY_EMAIL, email).apply()
        }

    @JvmStatic
    var name: String?
        get() = preferences.getString(KEY_NAME, null)
        set(name) {
            preferences.edit().putString(KEY_NAME, name).apply()
        }

    @JvmStatic
    var language: String?
        get() = preferences.getString(KEY_DEFAULT_LANGUAGE, null)
        set(language) {
            if (language == null) {
                preferences.edit().remove(KEY_DEFAULT_LANGUAGE).apply()
            } else {
                preferences.edit().putString(KEY_DEFAULT_LANGUAGE, language).apply()
            }
        }

    @JvmStatic
    var chatTranscriptExampleJson: String
        get() = preferences.getString(KEY_CHAT_TRANSCRIPT_EXAMPLE_JSON, "") ?: ""
        set(chatTranscriptJson) {
            preferences.edit().putString(KEY_CHAT_TRANSCRIPT_EXAMPLE_JSON, chatTranscriptJson).apply()
        }

    @JvmStatic
    val eventData: ArrayList<EventData?>
        get() {
            val json = preferences.getString(KEY_EVENT_DATA, null) ?: gson.toJson(ArrayList<EventData>())
            val type = object : TypeToken<ArrayList<EventData>>() {}.type
            return gson.fromJson(json, type)
        }

    @JvmStatic
    fun setEventData(list: ArrayList<EventData?>?) {
        if (list == null) {
            preferences.edit().remove(KEY_EVENT_DATA).apply()
        } else {
            val json = gson.toJson(list)
            preferences.edit().putString(KEY_EVENT_DATA, json).apply()
        }
    }

    @JvmStatic
    var customChatHeaderTitle: String?
        get() = preferences.getString(KEY_CUSTOM_CHAT_HEADER_TITLE, null)
        set(value) = preferences.edit().putString(KEY_CUSTOM_CHAT_HEADER_TITLE, value).apply()

    fun blockChatTerminationByEndUser() = preferences.getBoolean("blockChatTerminationByEndUser", false)

    @JvmStatic
    fun hideStatusBar() = preferences.getBoolean("hideStatusBar", false)

    @JvmStatic
    fun hideStartNewConversation() = preferences.getBoolean("hideStartNewConversation", false)

    @JvmStatic
    fun isShowWebFormLoadingFailedScreenEnabled() = preferences.getBoolean("showWebFormLoadingFailedScreen", false)

    @JvmStatic
    fun isShowWebFormTimedOutScreenEnabled() = preferences.getBoolean("showWebFormTimedOutScreen", false)

    @JvmStatic
    var quickReplyButtonsGrouped: Boolean
        get() = preferences.getBoolean(KEY_QUICK_REPLY_BUTTONS_GROUPED, true)
        set(value) {
            preferences.edit().putBoolean(KEY_QUICK_REPLY_BUTTONS_GROUPED, value).apply()
        }

    @JvmStatic
    var chatStyles: ChatStyles
        get() {
            val json = preferences.getString(KEY_CUSTOM_CHAT_STYLES, null) ?: gson.toJson(ChatStyles())
            val type = object : TypeToken<ChatStyles>() {}.type
            return gson.fromJson(json, type)
        }
        set(value) {
            val json = gson.toJson(value)
            preferences.edit().putString(KEY_CUSTOM_CHAT_STYLES, json).apply()
        }

    fun getSpinnerDrawableRes(context: Context): Int? {
        return preferences.getString("spinnerDrawableRes", null)?.let { drawableRes ->
            context.resources?.getIdentifier(drawableRes, "drawable", context.packageName)
        }
    }

    @JvmStatic
    var customDataParams: CustomDataParams
        get() {
            val json = preferences.getString(KEY_SIGNED_CUSTOM_DATA_PARAMS, null) ?: gson.toJson(CustomDataParams())
            val type = object : TypeToken<CustomDataParams>() {}.type
            return gson.fromJson(json, type)
        }
        set(value) {
            val json = gson.toJson(value)
            preferences.edit().putString(KEY_SIGNED_CUSTOM_DATA_PARAMS, json).apply()
        }

    var customSignedData: ArrayList<CustomData>?
        get() {
            val json = preferences.getString(KEY_SIGNED_CUSTOM_DATA, null) ?: gson.toJson(ArrayList<CustomData>())
            val type = object : TypeToken<ArrayList<CustomData>>() {}.type
            return gson.fromJson(json, type)
        }
        set(value) {
            val json = gson.toJson(value)
            preferences.edit().putString(KEY_SIGNED_CUSTOM_DATA, json).apply()
        }

    var customUnsignedData: ArrayList<CustomData>?
        get() {
            val json = preferences.getString(KEY_UNSIGNED_CUSTOM_DATA, null) ?: gson.toJson(ArrayList<CustomData>())
            val type = object : TypeToken<ArrayList<CustomData>>() {}.type
            return gson.fromJson(json, type)
        }
        set(value) {
            val json = gson.toJson(value)
            preferences.edit().putString(KEY_UNSIGNED_CUSTOM_DATA, json).apply()
        }
}
