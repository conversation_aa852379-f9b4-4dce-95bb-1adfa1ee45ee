<resources>
    <string name="ujet_company_key">3-xmSU-e3o17gkVPP-DeofyOPAc</string>
    <string name="ujet_subdomain">main</string>
    <string name="ujet_company_name">Ujet</string>

    <string name="action_example">Example action</string>
    <string name="action_settings">Settings</string>

    <string name="title_activity_settings">Settings</string>

    <!-- Dashboard activity strings -->
    <string name="title_activity_dashboard">Dashboard</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>

    <!-- Dashboard menu items -->
    <string name="dashboard_menu_item_main">Ujet Test App</string>
    <string name="dashboard_menu_item_event_data">Event Data</string>
    <string name="dashboard_menu_item_users">Users</string>
    <string name="dashboard_sub_menu_item_clear_cache">Clear Cache</string>
    <string name="dashboard_sub_menu_item_logout">Logout</string>

    <!-- Configure options in dashboard activity -->
    <string name="help_atrticles">Help Articles</string>
    <string name="android_app_help_forum">Android App Help Forum</string>
    <string name="contact_customer_support">Contact Customer Support</string>
    <string name="contact_customer_support_with_key">Contact Customer Support (with key)</string>
    <string name="contact_customer_support_with_ivr_phone_number">Contact Customer Support (with IVR)</string>
    <string name="contact_customer_support_without_splash">Contact Customer Support (without Splash)</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="menu_item_delete">Delete</string>
    <string name="menu_item_save">Save</string>
    <string name="event_data_default_message">No Data Available</string>
    <string name="event_data_warning_message">Please enable event data notification configuration to view the data. Go to Settings > Event Data Notification Enabled.</string>

    <string name="fcm_key">FCM Token</string>
    <string name="custom_data_type">Toggle custom data type</string>
    <string name="configure_custom_data">Configure custom data</string>
    <string name="signed_custom_data">Signed custom data</string>
    <string name="unsigned_custom_data">Unsigned custom data</string>
    <string name="configure_ticket_id">Configure Ticket Id</string>
    <string name="configure_preferred_channel">Configure Preferred Channel</string>
    <string name="configure_custom_cobrowse_license_key">Configure custom cobrowse license key</string>
    <string name="configure_custom_cobrowse_url">Configure custom cobrowse url</string>

    <string name="set_fallback_phone_number">Set fallback phone number</string>

    <string name="clear_user_preferred_language">Clear User Preferred Language</string>
    <string name="clear_default_language">Clear Default Language</string>
    <string name="change_default_language">Change Default Language</string>
    <string name="clear_device_uuid">Clear Device UUID</string>

    <string name="set_signing_delay">Set signing delay</string>
    <string name="reset_signing_delay">Reset signing delay</string>

    <string name="login">Log in</string>
    <string name="markdown_input_0">**Bold text** with _italics section_\n\n[click me!](https://www.google.com)\n\n--**_underline bold italic_**--\n\n**This is bold** and --this is underlined-- but this is not\n\n\n* first item\n* second item\n* third element\n\n***italic bold***\n\n- Another list 1\n- Another list 2\n\n1. first numbered item\n2. second numbered item\n👍</string>
    <string name="markdown_input_1">* level1\n* level1.1\n\t* level2\n\t* level2.1\n\t\t* level3\n\t\t* level3.1\n\t\t\t* level4\n\t\t\t* level4.1\n\t\t\t\t* level5\n\t\t\t\t* level5.1\n\t\t\t\t\t* level6\n\t\t\t\t\t* level6.1\n\t\t\t\t\t\t* level7\n\t\t\t\t\t\t* level7.1\n\t\t\t\t\t\t\t* level8\n\t\t\t\t\t\t\t* level8.1\n\t\t\t\t\t\t\t\t* level9\n\t\t\t\t\t\t\t\t* level9.1\n\n</string>
    <string name="markdown_input_2">- level1\n- level1.1\n\t- level2\n\t- level2.1\n\t\t- level3\n\t\t- level3.1\n\t\t\t- level4\n\t\t\t- level4.1\n\t\t\t\t- level5\n\t\t\t\t- level5.1\n\t\t\t\t\t- level6\n\t\t\t\t\t- level6.1\n\t\t\t\t\t\t- level7\n\t\t\t\t\t\t- level7.1\n\t\t\t\t\t\t\t- level8\n\t\t\t\t\t\t\t- level8.1\n\t\t\t\t\t\t\t\t- level9\n\t\t\t\t\t\t\t\t- level9.1\n\n</string>
    <string name="markdown_input_3">* level1\n* level1.1\n\t* level2\n\t* level2.1\n\t\t* level3\n\t\t* level3.1\n\t\t\t* level4\n\t\t\t* level4.1\n\t\t\t\t* level5\n\t\t\t\t* level5.1\n\t\t\t\t\t* level6\n\t\t\t\t\t* level6.1\n\t\t\t\t\t\t* level7\n\t\t\t\t\t\t* level7.1\n\t\t\t\t\t\t\t* level8\n\t\t\t\t\t\t\t* level8.1\n\t\t\t\t\t\t\t\t* level9\n\t\t\t\t\t\t\t\t* level9.1\n\n* level1\n* level1.1\n\t* level2\n\t* level2.1\n\t\t* level3\n\t\t* level3.1\n\t\t\t* level4\n\t\t\t* level4.1\n\t\t\t\t* level5\n\t\t\t\t* level5.1\n\t\t\t\t\t* level6\n\t\t\t\t\t* level6.1\n\t\t\t\t\t\t* level7\n\t\t\t\t\t\t* level7.1\n\t\t\t\t\t\t\t* level8\n\t\t\t\t\t\t\t* level8.1\n\t\t\t\t\t\t\t\t* level9\n\t\t\t\t\t\t\t\t* level9.1\n\n</string>
    <string name="markdown_input_4">- level1\n- level1.1\n\t- level2\n\t- level2.1\n\t\t- level3\n\t\t- level3.1\n\t\t\t- level4\n\t\t\t- level4.1\n\t\t\t\t- level5\n\t\t\t\t- level5.1\n\t\t\t\t\t- level6\n\t\t\t\t\t- level6.1\n\t\t\t\t\t\t- level7\n\t\t\t\t\t\t- level7.1\n\t\t\t\t\t\t\t- level8\n\t\t\t\t\t\t\t- level8.1\n\t\t\t\t\t\t\t\t- level9\n\t\t\t\t\t\t\t\t- level9.1\n\n- level1\n- level1.1\n\t- level2\n\t- level2.1\n\t\t- level3\n\t\t- level3.1\n\t\t\t- level4\n\t\t\t- level4.1\n\t\t\t\t- level5\n\t\t\t\t- level5.1\n\t\t\t\t\t- level6\n\t\t\t\t\t- level6.1\n\t\t\t\t\t\t- level7\n\t\t\t\t\t\t- level7.1\n\t\t\t\t\t\t\t- level8\n\t\t\t\t\t\t\t- level8.1\n\t\t\t\t\t\t\t\t- level9\n\t\t\t\t\t\t\t\t- level9.1\n\n</string>
    <string name="markdown_input_5">* level1\n* level1.1\n\t* level2\n\t* level2.1\n\t\t* level3\n\t\t* level3.1\n\t\t\t* level4\n\t\t\t* level4.1\n\t\t\t\t* level5\n\t\t\t\t* level5.1\n\t\t\t\t\t* level6\n\t\t\t\t\t* level6.1\n\t\t\t\t\t\t* level7\n\t\t\t\t\t\t* level7.1\n\t\t\t\t\t\t\t* level8\n\t\t\t\t\t\t\t* level8.1\n\t\t\t\t\t\t\t\t* level9\n\t\t\t\t\t\t\t\t* level9.1\n\n- level1\n- level1.1\n\t- level2\n\t- level2.1\n\t\t- level3\n\t\t- level3.1\n\t\t\t- level4\n\t\t\t- level4.1\n\t\t\t\t- level5\n\t\t\t\t- level5.1\n\t\t\t\t\t- level6\n\t\t\t\t\t- level6.1\n\t\t\t\t\t\t- level7\n\t\t\t\t\t\t- level7.1\n\t\t\t\t\t\t\t- level8\n\t\t\t\t\t\t\t- level8.1\n\t\t\t\t\t\t\t\t- level9\n\t\t\t\t\t\t\t\t- level9.1\n\n</string>
    <string name="markdown_input_6">1. first item\n2. second item\n3. third item\n4. fourth item\n5. fifth item\n6. sixth item\n7. seventh item\n8. eight item\n9. ninth item\n10. ten item\n11. eleven item\n12. twelve item\n13. thirteen item\n14. fourteen item\n15. fifteen item\n16. sixteen item\n\n</string>
    <string name="markdown_input_7">1. level1\n2. level1.1\n\t1. level2\n\t2. level2.1\n\t\t1. level3\n\t\t2. level3.1\n\t\t\t1. level4\n\t\t\t2. level4.1\n\t\t\t\t1. level5\n\t\t\t\t2. level5.1\n\t\t\t\t\t1. level6\n\t\t\t\t\t2. level6.1\n\t\t\t\t\t\t1. level7\n\t\t\t\t\t\t2. level7.1\n\n</string>
    <string name="markdown_input_8">1. level1\n2. level1.1\n\t1. level2\n\t2. level2.1\n\t\t1. level3\n\t\t2. level3.1\n\t\t\t1. level4\n\t\t\t2. level4.1\n\t\t\t\t1. level5\n\t\t\t\t2. level5.1\n\t\t\t\t\t1. level6\n\t\t\t\t\t2. level6.1\n\t\t\t\t\t\t1. level7\n\t\t\t\t\t\t2. level7.1\n\n1. level1\n2. level1.1\n\t1. level2\n\t2. level2.1\n\t\t1. level3\n\t\t2. level3.1\n\t\t\t1. level4\n\t\t\t2. level4.1\n\t\t\t\t1. level5\n\t\t\t\t2. level5.1\n\t\t\t\t\t1. level6\n\t\t\t\t\t2. level6.1\n\t\t\t\t\t\t1. level7\n\t\t\t\t\t\t2. level7.1\n\n</string>
    <string name="markdown_input_9">* level1\n* level1.1\n\t* level2\n\t* level2.1\n\t\t* level3\n\t\t* level3.1\n\t\t\t* level4\n\t\t\t* level4.1\n\t\t\t\t* level5\n\t\t\t\t* level5.1\n\t\t\t\t\t* level6\n\t\t\t\t\t* level6.1\n\t\t\t\t\t\t* level7\n\t\t\t\t\t\t* level7.1\n\t\t\t\t\t\t\t* level8\n\t\t\t\t\t\t\t* level8.1\n\t\t\t\t\t\t\t\t* level9\n\t\t\t\t\t\t\t\t* level9.1\n\n1. level1\n2. level1.1\n\t1. level2\n\t2. level2.1\n\t\t1. level3\n\t\t2. level3.1\n\t\t\t1. level4\n\t\t\t2. level4.1\n\t\t\t\t1. level5\n\t\t\t\t2. level5.1\n\t\t\t\t\t1. level6\n\t\t\t\t\t2. level6.1\n\t\t\t\t\t\t1. level7\n\t\t\t\t\t\t2. level7.1\n\n</string>
    <string name="markdown_test">Markdown test</string>
    <string name="chat_bubbles_test">Chat bubbles test</string>
    <string name="chat_transcript_input">Configure chat transcript Json</string>
    <string name="survey_page_test">Survey Page test</string>
    <string name="style_options_test">Configure style options</string>
    <string name="from_json">From JSON</string>
    <string name="clear_styles">Clear styles</string>

    <string-array name="font_style">
        <item>Normal</item>
        <item>Underlined</item>
    </string-array>
    <string-array name="font_family">
        <item>none</item>
        <item>sans-serif</item>
        <item>sans-serif-light</item>
        <item>sans-serif-medium</item>
        <item>sans-serif-black</item>
        <item>sans-serif-condensed-light</item>
        <item>sans-serif-condensed</item>
        <item>sans-serif-condensed-medium</item>
        <item>serif-monospace</item>
        <item>casual</item>
        <item>cursive</item>
        <item>sans-serif-smallcaps</item>
    </string-array>
    <string-array name="font_weight">
        <item>Normal</item>
        <item>Bold</item>
        <item>Italic</item>
    </string-array>
    <string-array name="back_button_icons">
        <item>Default</item>
        <item>ujet_ic_back</item>
    </string-array>
    <string-array name="logo_icons">
        <item>None</item>
        <item>Fitbit</item>
        <item>Netflix</item>
        <item>Ring</item>
        <item>Insta</item>
    </string-array>
    <string-array name="consumer_message_icon_position">
        <item>Right</item>
    </string-array>
    <string-array name="agent_message_icon_position">
        <item>Default</item>
        <item>Left</item>
    </string-array>
    <string-array name="spinner_drawable_res_entries">
        <item>Default</item>
        <item>Smiley</item>
        <item>Tom Green</item>
    </string-array>
    <string-array name="spinner_drawable_res_values">
        <item>default</item>
        <item>smiley</item>
        <item>tom_green</item>
    </string-array>
    <string-array name="spinner_custom_data_type">
        <item>string</item>
        <item>number</item>
        <item>boolean</item>
        <item>date</item>
    </string-array>
    <string-array name="spinner_markdown_test_cases">
        <item>Mix of bold italic and lists</item>
        <item>Nested bullet lists with *</item>
        <item>Nested bullet lists with -</item>
        <item>Two sets of nested bullet lists with *</item>
        <item>Two sets of nested bullet lists with -</item>
        <item>Two sets of mixed nested bullet lists with * and -</item>
        <item>Single numbered lists</item>
        <item>Nested numbered lists</item>
        <item>Two sets of nested numbered lists</item>
        <item>Mixed sets of nested bullet and numbered lists</item>
    </string-array>
</resources>
