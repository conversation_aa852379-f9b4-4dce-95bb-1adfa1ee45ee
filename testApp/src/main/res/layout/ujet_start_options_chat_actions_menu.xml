<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_actions_menu_camera_icon_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Camera icon visible: " />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_actions_menu_camera_icon_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_actions_menu_camera_icon_visible_label"
                android:checked="true" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_actions_menu_cobrowse_icon_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Cobrowse icon visible: " />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_actions_menu_cobrowse_icon_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_actions_menu_cobrowse_icon_visible_label"
                android:checked="true" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_actions_menu_photo_library_icon_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Photo library icon visible: " />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_actions_menu_photo_library_icon_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_actions_menu_photo_library_icon_visible_label"
                android:checked="true" />
        </RelativeLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/chat_actions_menu_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Chat actions menu style options"
        android:textStyle="bold" />
</RelativeLayout>
