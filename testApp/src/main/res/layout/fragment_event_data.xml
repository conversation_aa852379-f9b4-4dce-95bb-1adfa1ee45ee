<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="10dp"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:paddingTop="10dp">

    <TextView
        android:id="@+id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/event_data_default_message"
        android:layout_centerInParent="true"
        android:textSize="14sp" />

    <ListView
        android:id="@+id/event_list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:choiceMode="singleChoice"
        android:listSelector="@android:color/transparent"
        android:divider="@color/ujet_primary"
        android:dividerHeight="4px"
        android:scrollbarStyle="insideInset"
        android:scrollbars="vertical"/>

</RelativeLayout>