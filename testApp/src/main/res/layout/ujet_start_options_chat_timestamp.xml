<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/chat_time_stamp_font_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font color"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_time_stamp_font_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_time_stamp_font_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font size"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_time_stamp_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font size (Example: 14)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_time_stamp_font_style_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font style"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_time_stamp_font_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font style (Example: bold|italic)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_time_stamp_font_family_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Font family"/>

        <Spinner
            android:id="@+id/chat_time_stamp_font_family"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"/>

    </LinearLayout>

    <TextView
        android:id="@+id/chat_time_stamp_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Chat time stamp style options"
        android:textStyle="bold"/>
</RelativeLayout>
