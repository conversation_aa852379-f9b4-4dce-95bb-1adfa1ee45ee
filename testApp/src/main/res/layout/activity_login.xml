<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:fancy="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".activity.LoginActivity">

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageView3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:layout_margin="10dp"
            android:src="@drawable/logo_generico" />

        <EditText
            android:id="@+id/identifier"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:hint="Identifier"
            android:inputType="text"
            android:paddingTop="@dimen/login_form_edit_padding"
            android:paddingBottom="@dimen/login_form_edit_padding" />

        <EditText
            android:id="@+id/email"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:ems="8"
            android:hint="Email"
            android:inputType="textEmailAddress"
            android:paddingTop="@dimen/login_form_edit_padding"
            android:paddingBottom="@dimen/login_form_edit_padding" />

        <EditText
            android:id="@+id/name"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:ems="8"
            android:hint="Name"
            android:inputType="text"
            android:paddingTop="@dimen/login_form_edit_padding"
            android:paddingBottom="@dimen/login_form_edit_padding" />

        <EditText
            android:id="@+id/phone_number"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:ems="8"
            android:hint="Phone Number"
            android:inputType="phone"
            android:paddingTop="@dimen/login_form_edit_padding"
            android:paddingBottom="@dimen/login_form_edit_padding" />

        <EditText
            android:id="@+id/configuration_settings"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:ems="8"
            android:hint="Configuration Settings"
            android:inputType="text"
            android:paddingTop="@dimen/login_form_edit_padding"
            android:paddingBottom="@dimen/login_form_edit_padding" />

        <mehdi.sakout.fancybuttons.FancyButton
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:paddingBottom="10dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingTop="10dp"
            android:layout_marginEnd="20dp"
            android:layout_marginStart="20dp"
            android:layout_centerHorizontal="true"
            android:layout_margin="20dp"
            fancy:fb_defaultColor="@color/generico"
            fancy:fb_focusColor="@color/generico_dark"
            fancy:fb_radius="30dp"
            fancy:fb_text="@string/login"
            fancy:fb_textColor="#FFFFFF" />

    </LinearLayout>
    <View
        android:id="@+id/hiddenButton"
        android:layout_height="60dp"
        android:layout_width="60dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        />
</RelativeLayout>
