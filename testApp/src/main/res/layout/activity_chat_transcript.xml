<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fancy="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="@string/chat_transcript_input"/>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <mehdi.sakout.fancybuttons.FancyButton
            android:id="@+id/clear_chat_transcript_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:paddingStart="20dp"
            android:paddingTop="10dp"
            android:paddingEnd="20dp"
            android:paddingBottom="10dp"
            fancy:fb_defaultColor="@color/generico"
            fancy:fb_focusColor="@color/generico_dark"
            fancy:fb_radius="30dp"
            fancy:fb_text="Clear"
            fancy:fb_textColor="#FFFFFF"/>

        <EditText
            android:id="@+id/chat_transcript_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="20"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:orientation="horizontal">

            <mehdi.sakout.fancybuttons.FancyButton
                android:id="@+id/load_chat_transcript_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                fancy:fb_defaultColor="@color/generico"
                fancy:fb_focusColor="@color/generico_dark"
                fancy:fb_radius="30dp"
                fancy:fb_text="Load from assets"
                fancy:fb_textColor="#FFFFFF"/>

            <mehdi.sakout.fancybuttons.FancyButton
                android:id="@+id/save_chat_transcript_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_weight="1"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                fancy:fb_defaultColor="@color/generico"
                fancy:fb_focusColor="@color/generico_dark"
                fancy:fb_radius="30dp"
                fancy:fb_text="Save transcript"
                fancy:fb_textColor="#FFFFFF"/>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>
