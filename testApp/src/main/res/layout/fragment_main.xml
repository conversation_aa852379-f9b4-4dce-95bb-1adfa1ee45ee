<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent" android:paddingStart="@dimen/activity_horizontal_margin"
    android:paddingEnd="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    tools:context=".activity.MainActivity$PlaceholderFragment">

    <TextView android:id="@+id/section_label" android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</RelativeLayout>
