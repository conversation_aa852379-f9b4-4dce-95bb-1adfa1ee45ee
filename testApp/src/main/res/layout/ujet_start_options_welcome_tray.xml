<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_welcome_message_tray_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Show welcome message tray: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_welcome_message_tray_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_welcome_message_tray_visible_label"
                android:checked="true"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_agent_avatar_icon_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Show agent avatar icon: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_agent_avatar_icon_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_agent_avatar_icon_visible_label"
                android:checked="true"/>

        </RelativeLayout>

        <TextView
            android:id="@+id/chat_welcome_message_tray_divider_width_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Divider width"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_welcome_message_tray_divider_width"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Divider width (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_welcome_message_tray_divider_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Divider color"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_welcome_message_tray_divider_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Divider color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

    </LinearLayout>

    <TextView
        android:id="@+id/chat_welcome_message_tray_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Chat welcome message tray style options"
        android:textStyle="bold"/>
</RelativeLayout>
