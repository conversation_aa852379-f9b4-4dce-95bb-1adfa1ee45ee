<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_view"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/list_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp" >

        <TextView
            android:id="@+id/send_custom_data_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:text="Add custom unsigned custom data to send:"
            android:textSize="14sp"/>

        <ImageView
            android:id="@+id/add_custom_data"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:adjustViewBounds="true"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="10dp"
            android:layout_toEndOf="@+id/send_custom_data_label"
            android:src="@drawable/ujet_ic_plus" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@color/ujet_primary"/>

        <TextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/event_data_default_message"
            android:layout_centerInParent="true"
            android:textSize="14sp" />

        <ListView
            android:id="@+id/custom_data_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="40dp"
            android:choiceMode="singleChoice"
            android:listSelector="@android:color/transparent"
            android:divider="@color/ujet_primary"
            android:dividerHeight="2dp"
            android:scrollbarStyle="insideInset"
            android:scrollbars="vertical" />
    </LinearLayout>

    <TextView
        android:id="@+id/tap_for_visibility_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:text="Tap to toggle the visibility"
        android:textSize="14sp" />
</RelativeLayout>
