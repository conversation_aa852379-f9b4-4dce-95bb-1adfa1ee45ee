<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/activity_vertical_margin"
    tools:context="co.ujet.ujetandroidsdk.fragment.HelpFragment">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:id="@+id/linearLayout">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:id="@+id/imageView"
            android:src="?attr/icon_logo" />

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="Version"
                android:textColor="?android:attr/textColorPrimary"
                android:id="@+id/textVersion" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="© 2016"
                android:id="@+id/textCompanyInfo" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:text="All rights reserved"
                android:id="@+id/textView9" />
        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/linearLayout"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:id="@+id/frameLayout"
        android:background="#cccccc"/>

    <ListView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/helpListView"
        android:layout_below="@+id/frameLayout"
        android:layout_centerHorizontal="true" />
</RelativeLayout>
