<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="10dp"
    android:paddingEnd="10dp"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    android:paddingStart="10dp"
    android:paddingTop="10dp">

    <TextView
        android:id="@+id/event_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Session Created"
        android:layout_marginStart="10dp"
        android:textSize="14sp"
        android:textStyle="bold"
        android:gravity="start"/>

    <TextView
        android:id="@+id/event_data"
        android:layout_marginTop="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/event_name"
        android:text="data"
        android:layout_marginStart="10dp"
        android:textSize="12sp"
        android:gravity="start"/>

</RelativeLayout>