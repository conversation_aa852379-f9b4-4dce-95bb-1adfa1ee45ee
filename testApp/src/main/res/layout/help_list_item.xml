<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:gravity="center_vertical"
    android:layout_height="60dp">

    <ImageView
        android:id="@+id/icon"
        android:layout_gravity="start|center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/title"
        android:text="Cell Title"
        android:paddingStart="10dip"
        android:layout_weight="0.5"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorPrimary"/>
</LinearLayout>
