<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fancy="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activity.MarkdownActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="@string/markdown_test"/>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        tools:context=".activity.MarkdownActivity">

        <EditText
            android:id="@+id/markdown_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="15"
            android:text="@string/markdown_input_0"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/test_case_input_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_centerVertical="true"
                android:layout_marginTop="4dp"
                android:text="Select test cases to execute: "/>

            <Spinner
                android:id="@+id/test_case_input_spinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/test_case_input_label"
                android:padding="8dp"/>
        </RelativeLayout>

        <mehdi.sakout.fancybuttons.FancyButton
            android:id="@+id/parse_markdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            fancy:fb_defaultColor="@color/generico"
            fancy:fb_focusColor="@color/generico_dark"
            fancy:fb_radius="30dp"
            fancy:fb_text="Parse markdown"
            fancy:fb_textColor="#FFFFFF"/>

        <cx.ujet.android.markdown.widgets.MarkdownTextView
            android:id="@+id/markdown_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:lines="15"/>

    </LinearLayout>

</LinearLayout>
