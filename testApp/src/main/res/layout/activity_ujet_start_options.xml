<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:fancy="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ujet_white"
    tools:context=".activity.UjetStyleOptionsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/toolbar_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="Start options setup" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:id="@+id/main_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/save_options_button"
        android:layout_below="@+id/toolbar_container"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <include layout="@layout/ujet_start_options_quick_reply" />

            <include layout="@layout/ujet_start_options_input_bar" />

            <include layout="@layout/ujet_start_options_chat_actions_menu" />

            <include layout="@layout/ujet_start_options_header" />

            <include layout="@layout/ujet_start_options_back_button" />

            <include layout="@layout/ujet_start_options_end_chat" />

            <include layout="@layout/ujet_start_options_welcome_tray" />

            <include layout="@layout/ujet_start_options_system_messages" />

            <include layout="@layout/ujet_start_options_chat_timestamp" />

            <include layout="@layout/ujet_start_options_consumer_messages" />

            <include layout="@layout/ujet_start_options_agent_messages" />

            <include layout="@layout/ujet_start_options_content_cards" />

            <include layout="@layout/ujet_start_options_web_form_card" />

            <include layout="@layout/ujet_start_options_post_session" />
        </LinearLayout>

    </ScrollView>

    <mehdi.sakout.fancybuttons.FancyButton
        android:id="@+id/save_options_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="20dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        fancy:fb_defaultColor="@color/generico"
        fancy:fb_focusColor="@color/generico_dark"
        fancy:fb_radius="30dp"
        fancy:fb_text="Save options"
        fancy:fb_textColor="@color/ujet_white" />

</RelativeLayout>
