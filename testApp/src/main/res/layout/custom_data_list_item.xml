<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/list_view_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:paddingBottom="10dp"
        android:paddingEnd="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingStart="10dp"
        android:paddingTop="10dp"
        android:layout_toStartOf="@+id/remove_button_layout"
        android:orientation="vertical"
        android:focusable="true">

        <ImageView
            android:id="@+id/eye_slash_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerInParent="true"
            android:clickable="false"
            android:visibility="visible"
            tools:ignore="ContentDescription" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_toEndOf="@id/eye_slash_icon"
            android:orientation="vertical">

        <TextView
            android:id="@+id/custom_data_key"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Key"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="start"/>

        <TextView
            android:id="@+id/custom_data_label"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/custom_data_key"
            android:text="data"
            android:textSize="12sp"
            android:gravity="start"/>

        <TextView
            android:id="@+id/custom_data_type"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/custom_data_label"
            android:text="data"
            android:textSize="12sp"
            android:gravity="start"/>

        <TextView
            android:id="@+id/custom_data_value"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/custom_data_type"
            android:text="data"
            android:textSize="12sp"
            android:gravity="start"/>

        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/remove_button_layout"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:orientation="vertical">

    <ImageButton
        android:id="@+id/remove_button"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:clickable="true"
        android:focusable="true"
        android:visibility="visible"
        android:background="@android:color/transparent"
        android:contentDescription="@string/ujet_email_attachments_remove_alert_message" />
    </RelativeLayout>
</RelativeLayout>
