<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fancy="http://schemas.android.com/apk/res-auto"
    android:background="@color/ujet_green_dark"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="30dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Your desire title"
            android:textColor="@color/ujet_text_primary_light"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/message_body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="Your desire text"
            android:textColor="@color/ujet_text_primary_light"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <co.ujet.android.ui.button.FancyButton
                android:id="@+id/positive_button"
                style="@style/UjetButton.Default"
                android:layout_weight="1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:padding="8dp"
                fancy:ujet_fb_text="@string/ujet_common_done"
                fancy:ujet_fb_textAllCaps="false" />

            <co.ujet.android.ui.button.FancyButton
                android:layout_weight="1"
                android:id="@+id/negative_button"
                style="@style/UjetButton.Default"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:padding="8dp"
                fancy:ujet_fb_text="@string/ujet_common_done"
                fancy:ujet_fb_textAllCaps="false" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>
