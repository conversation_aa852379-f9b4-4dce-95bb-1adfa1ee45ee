<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_view"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp" >

        <ListView
            android:id="@+id/custom_data_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="40dp"
            android:choiceMode="singleChoice"
            android:listSelector="@android:color/transparent"
            android:divider="@color/ujet_primary"
            android:dividerHeight="2dp"
            android:scrollbarStyle="insideInset"
            android:scrollbars="vertical" />
    </LinearLayout>

    <TextView
        android:id="@+id/tap_for_visibility_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginBottom="10dp"
        android:text="Tap to toggle the visibility"
        android:textSize="14sp" />
</RelativeLayout>
