<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_view"
    android:background="@color/ujet_white"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp" >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:text="SIGNED"
            android:layout_gravity="center"
            android:textColor="@color/ujet_primary"
            android:textSize="18sp"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/signed_custom_data_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:textSize="16sp"
                android:text="Enable signed custom data: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/signed_custom_data_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_alignParentEnd="true"
                android:layout_toEndOf="@+id/signed_custom_data_label"
                android:checked="true"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/reserved_data_config_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/reserved_data_config_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:textSize="16sp"
                android:text="Include reserved data: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/reserved_data_config_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_alignParentEnd="true"
                android:layout_toEndOf="@+id/reserved_data_config_label"
                android:checked="true"/>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/reserved_keys_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="45dp"
            android:layout_marginEnd="30dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/customer_verified_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    android:textSize="16sp"
                    android:text="Verified customer: "/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/customer_verified_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_alignParentEnd="true"
                    android:layout_toEndOf="@+id/customer_verified_label"
                    android:checked="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/customer_bad_actor_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    android:textSize="16sp"
                    android:text="Bad actor: "/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/customer_bad_actor_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_alignParentEnd="true"
                    android:layout_toEndOf="@+id/customer_bad_actor_label"
                    android:checked="true"/>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/customer_repeated_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    android:textSize="16sp"
                    android:text="Repeat customer: "/>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/customer_repeated_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_alignParentEnd="true"
                    android:layout_toEndOf="@+id/customer_repeated_label"
                    android:checked="true"/>
            </RelativeLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/signed_data_show_details_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:text="Click here to view details >"
            android:textSize="16sp"
            android:focusable="true" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@color/ujet_primary"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:text="UNSIGNED"
            android:layout_gravity="center"
            android:textColor="@color/ujet_primary"
            android:textSize="18sp"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/unsigned_custom_data_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:textSize="16sp"
                android:text="Enable unsigned custom data: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/unsigned_custom_data_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_alignParentEnd="true"
                android:layout_toEndOf="@+id/unsigned_custom_data_label"
                android:checked="true"/>
        </RelativeLayout>

        <TextView
            android:id="@+id/unsigned_data_show_details_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_centerVertical="true"
            android:clickable="true"
            android:text="Click here to view details >"
            android:textSize="16sp"
            android:focusable="true" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@color/ujet_primary"/>
    </LinearLayout>

</RelativeLayout>
