<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_back_button_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Visible: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_back_button_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_back_button_visible_label"
                android:checked="true"/>
        </RelativeLayout>

        <TextView
            android:id="@+id/chat_back_button_image_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Back button image"/>

        <Spinner
            android:id="@+id/chat_back_button_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="40dp"
            android:padding="8dp"/>

    </LinearLayout>

    <TextView
        android:id="@+id/chat_back_button_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Chat back button style options"
        android:textStyle="bold"/>
</RelativeLayout>
