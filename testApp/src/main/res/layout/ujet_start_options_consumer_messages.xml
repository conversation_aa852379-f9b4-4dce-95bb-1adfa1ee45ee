<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/chat_consumer_message_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Text Styles"/>

        <TextView
            android:id="@+id/chat_consumer_message_font_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font color"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_font_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_font_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font size"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font size (Example: 14)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_font_style_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font style"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_font_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font style (Example: bold|italic)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_font_family_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Font family"/>

        <Spinner
            android:id="@+id/chat_consumer_message_font_family"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"/>

        <TextView
            android:id="@+id/chat_consumer_message_background_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background color"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_background_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_corner_radius_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background corner radius"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_corner_radius"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background corner radius (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_border_width_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background border width"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_border_width"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background border width (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/chat_consumer_message_border_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background border color"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_border_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background border color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <!-- consumer message icon styles -->
        <TextView
            android:id="@+id/chat_consumer_message_icon_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Icon Styles"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/chat_consumer_message_visible_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="4dp"
                android:text="Visible: "/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/chat_consumer_message_visible"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_toEndOf="@+id/chat_consumer_message_visible_label"
                android:checked="true"/>
        </RelativeLayout>

        <TextView
            android:id="@+id/chat_consumer_message_icon_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Icon size"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/chat_consumer_message_icon_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Icon size (Example: 50)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/chat_consumer_icon_options_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Icon options: "/>

                <Spinner
                    android:id="@+id/chat_consumer_icon_options"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="8dp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/chat_consumer_message_icon_position_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Icon position"/>

                <Spinner
                    android:id="@+id/chat_consumer_message_icon_position"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="8dp"/>
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/chat_consumer_message_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Chat consumer message style options"
        android:textStyle="bold"/>
</RelativeLayout>
