<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/root_view"
    android:background="@color/ujet_white"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp" >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/custom_data_key_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_centerVertical="true"
                android:layout_marginEnd="10dp"
                android:text="Key:"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/custom_data_key_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/custom_data_key_label"
                android:layout_marginTop="5dp"
                android:background="@drawable/ujet_white_rounded_rectangle"
                android:inputType="text"
                android:minHeight="40dp"
                android:padding="5dp"
                android:paddingBottom="10dp"
                android:textCursorDrawable="@null"
                android:textSelectHandle="@drawable/ujet_transparent_pixel"
                android:textSize="14sp" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/custom_data_label_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="10dp"
                android:text="Label:"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/custom_data_label_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/custom_data_label_label"
                android:layout_marginTop="5dp"
                android:background="@drawable/ujet_white_rounded_rectangle"
                android:inputType="text"
                android:minHeight="40dp"
                android:padding="5dp"
                android:paddingBottom="10dp"
                android:textCursorDrawable="@null"
                android:textSelectHandle="@drawable/ujet_transparent_pixel"
                android:textSize="14sp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/custom_data_type_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="10dp"
                android:text="Type:"
                android:textSize="14sp" />

            <Spinner
                android:id="@+id/custom_data_type_spinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/custom_data_type_label"
                android:padding="8dp"/>

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/custom_data_value_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="10dp"
                android:text="Value:"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/custom_data_value_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/custom_data_value_label"
                android:layout_marginTop="5dp"
                android:background="@drawable/ujet_white_rounded_rectangle"
                android:inputType="text"
                android:minHeight="40dp"
                android:padding="5dp"
                android:paddingBottom="10dp"
                android:textCursorDrawable="@null"
                android:textSelectHandle="@drawable/ujet_transparent_pixel"
                android:textSize="14sp" />

        </RelativeLayout>

    </LinearLayout>
</RelativeLayout>
