<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/chat_web_form_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Card Styles" />

        <TextView
            android:id="@+id/chat_web_form_card_background_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_background_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_corner_radius_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background corner radius"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_corner_radius"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background corner radius (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_border_width_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background border width"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_border_width"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background border width (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_border_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Background border color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_border_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Background border color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_font_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_font_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_font_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font size"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font size (Example: 14)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_font_style_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font style"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_font_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font style (Example: bold|italic)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_font_family_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Font family" />

        <Spinner
            android:id="@+id/chat_web_form_card_font_family"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp" />

        <!-- Title styles -->
        <TextView
            android:id="@+id/chat_web_form_card_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Title Styles (leave empty to use inherited values)" />

        <TextView
            android:id="@+id/chat_web_form_card_title_font_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_title_font_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_title_font_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font size"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_title_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font size (Example: 14)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_title_font_style_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font style"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_title_font_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font style (Example: bold|italic)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_title_font_family_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Font family" />

        <Spinner
            android:id="@+id/chat_web_form_card_title_font_family"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp" />

        <!-- Subtitle styles -->
        <TextView
            android:id="@+id/chat_web_form_card_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Subtitle Styles (leave empty to use inherited values)" />

        <TextView
            android:id="@+id/chat_web_form_card_subtitle_font_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_subtitle_font_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_subtitle_font_size_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font size"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_subtitle_font_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font size (Example: 14)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_subtitle_font_style_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Font style"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_subtitle_font_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Font style (Example: bold|italic)"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_web_form_card_subtitle_font_family_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Font family" />

        <Spinner
            android:id="@+id/chat_web_form_card_subtitle_font_family"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp" />

        <!-- Image styles -->
        <TextView
            android:id="@+id/chat_web_form_card_background_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Image Styles (leave empty to use inherited values)" />

        <TextView
            android:id="@+id/chat_web_form_card_background_image_height_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Image Height"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_web_form_card_background_image_height"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Image height (Example: 94)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/chat_web_form_card_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Web Form card style options"
        android:textStyle="bold" />
</RelativeLayout>
