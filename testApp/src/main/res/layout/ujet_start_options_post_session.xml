<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bordered_background"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/chat_post_session"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="8dp"
            android:text="Post-session Styles" />

        <TextView
            android:id="@+id/chat_post_session_background_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Container color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_post_session_background_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Container color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_post_session_border_width_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Border width"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_post_session_border_width"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Border width (Example: 4)"
            android:inputType="number"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/chat_post_session_border_color_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/ujet_dialog_margin"
            android:gravity="center_horizontal"
            android:text="Border color"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/chat_post_session_border_color"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@drawable/ujet_white_rounded_rectangle"
            android:hint="Border color resource name"
            android:inputType="text"
            android:minHeight="40dp"
            android:padding="5dp"
            android:paddingBottom="10dp"
            android:textCursorDrawable="@null"
            android:textSelectHandle="@drawable/ujet_transparent_pixel"
            android:textSize="14sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/chat_post_session_styles_box_label"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="Post-session style options"
        android:textStyle="bold" />
</RelativeLayout>
