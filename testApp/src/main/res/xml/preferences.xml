<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:allowDividerAbove="true"
    app:allowDividerBelow="true">

    <Preference
        android:key="build"
        android:summary="v0.0.0"
        android:title="Build"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true"
        app:iconSpaceReserved="false" />

    <PreferenceCategory
        android:key="test_app_setting"
        android:title="UJET Test App Settings"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true"
        app:iconSpaceReserved="false">

        <ListPreference
            android:hint="Select the environment"
            android:key="env"
            android:title="Environment"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <ListPreference
            android:hint="Select the company"
            android:key="tenant"
            android:title="Company"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <ListPreference
            android:entries="@array/themeListArray"
            android:entryValues="@array/themeValues"
            android:hint="Select the theme"
            android:key="theme"
            android:title="Theme"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:defaultValue="Generico"
            android:hint="Customize company display name"
            android:key="company_name"
            android:title="Company Name"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:hint="Input the company key"
            android:key="company_key"
            android:lines="1"
            android:title="Company Key"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:hint="Input the company secret"
            android:key="company_secret"
            android:lines="1"
            android:title="Company Secret"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:hint="ujetsfco.api.ujet.co"
            android:key="host"
            android:lines="1"
            android:title="Host"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:defaultValue="5001"
            android:hint="Input the port of your local server"
            android:inputType="number"
            android:key="port"
            android:lines="1"
            android:title="Port"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <EditTextPreference
            android:defaultValue="0"
            android:hint="Input the float number 0.0 to 1.0"
            android:inputType="numberDecimal"
            android:key="networkSensitivity"
            android:title="Network Sensitivity"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="darkModeEnabled"
            android:title="Dark Mode Enabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="showCustomCoBrowseAlert"
            android:title="Show Custom CoBrowse Alert Enabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="landscapeModeDisabled"
            android:title="Landscape Mode Disabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="eventDataNotificationEnabled"
            android:title="Event Data Notification Enabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="showSingleChannelEnabled"
            android:title="Show Single Channel Enabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="autoMinimizeCallView"
            android:title="Auto Minimize Call View"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="removeAgentIconBorderEnabled"
            android:title="Remove Agent Icon Border Enabled"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="staticFontSizeInPickerView"
            android:title="Static Font Size In Picker View"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="hideMediaAttachmentInChat"
            android:title="Hide Media Attachment In Chat"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="showCsatSkipButton"
            android:title="Show Skip Button In CSAT"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="ignoreReadPhoneStatePermission"
            android:title="Ignore Read Phone State Permission"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="blockChatTerminationByEndUser"
            android:title="Block Chat Termination By End User"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="hideStatusBar"
            android:title="Hide Status Bar"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="hideStartNewConversation"
            android:title="Hide Start New Conversation"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <DropDownPreference
            android:defaultValue="default"
            android:entries="@array/spinner_drawable_res_entries"
            android:entryValues="@array/spinner_drawable_res_values"
            android:key="spinnerDrawableRes"
            android:title="Custom loading spinner drawable"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true"
            app:defaultValue="Default" />

    </PreferenceCategory>


    <PreferenceCategory
        android:key="modules_setting"
        android:title="UJET Optional Modules Settings"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true"
        app:iconSpaceReserved="false">

        <SwitchPreference
            android:defaultValue="false"
            android:key="enableCobrowse"
            android:title="Enable Cobrowse"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="enableNexmoChat"
            android:title="Enable Nexmo Chat"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="enableNexmoCall"
            android:title="Enable Nexmo Call"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="enableTwilioChat"
            android:title="Enable Twilio Chat"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="enableTwilioCall"
            android:title="Enable Twilio Call"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />
    </PreferenceCategory>

    <PreferenceCategory
        android:key="web_form_setting"
        android:title="UJET Web Form Settings"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true"
        app:iconSpaceReserved="false">

        <SwitchPreference
            android:defaultValue="false"
            android:key="showWebFormLoadingFailedScreen"
            android:title="Show Web Form Loading Failed Screen"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="showWebFormTimedOutScreen"
            android:title="Show Web Form Timed out Screen"
            app:allowDividerAbove="true"
            app:allowDividerBelow="true" />

    </PreferenceCategory>

    <Preference
        android:key="app_info"
        android:title="App Info"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true" />

    <Preference
        android:key="save"
        android:title="Save"
        app:allowDividerAbove="true"
        app:allowDividerBelow="true" />

</androidx.preference.PreferenceScreen>
