<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="co.ujet.ujetandroidsdk">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

    <application
        android:name=".Application"
        android:allowBackup="true"
        android:authorities="${applicationId}.provider"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/GenericoTheme">
        <activity
            android:name=".activity.EventParametersActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data
                    android:host="contentCard"
                    android:scheme="ujetqa"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.CustomDataActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.UjetStyleOptionsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
        </activity>
        <activity
            android:name=".activity.MarkdownActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
        </activity>
        <activity
            android:name=".activity.ChatBubblesActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Ujet">
        </activity>
        <activity
            android:name=".activity.ChatTranscriptActivity"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
        </activity>
        <activity
            android:name=".activity.LoginActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.DashboardActivity"
            android:exported="true"
            android:label="@string/title_activity_dashboard"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme.NoActionBar">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.SettingsActivity"
            android:label="@string/title_activity_settings"
            android:screenOrientation="portrait"
            android:theme="@style/GenericoTheme" /> <!-- [UJET-6477] Deep linking for launch SDK from SMS -->
        <activity
            android:name="co.ujet.android.activity.UjetActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data
                    android:host="@string/app_open_host"
                    android:path="/open"
                    android:scheme="ujet"/>
            </intent-filter>
        </activity>

        <service
            android:name=".fcm.MyFirebaseMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <meta-data
            android:name="co.ujet.android.companyKey"
            android:value="@string/ujet_company_key"/>
        <meta-data
            android:name="co.ujet.android.subdomain"
            android:value="@string/ujet_subdomain"/>
        <meta-data
            android:name="co.ujet.android.companyName"
            android:value="@string/ujet_company_name"/>
        <meta-data
            android:name="co.ujet.android.subdomain"
            android:value="@string/ujet_subdomain"/>

        <receiver
            android:name=".PushTestBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="999">
                <action android:name="co.ujet.android.push_test"/>
            </intent-filter>
        </receiver>
    </application>

</manifest>
