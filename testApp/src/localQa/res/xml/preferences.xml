<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <Preference
        android:key="build"
        android:summary="v0.0.0"
        android:title="Information" />

    <PreferenceCategory
        android:key="categoryEnv"
        android:title="UJET Test App Settings">

        <ListPreference
            android:entries="@array/envNames"
            android:entryValues="@array/envValues"
            android:key="currentEnv"
            android:summary="Select the env of the Text app"
            android:title="Current Environment" />

        <ListPreference
            android:entries="@array/themeListArray"
            android:entryValues="@array/themeValues"
            android:key="currentTheme"
            android:summary="Select the theme of the Ujet"
            android:title="Theme" />

        <EditTextPreference
            android:defaultValue="Generico"
            android:key="companyName"
            android:summary="Customize company display name"
            android:title="Company Name" />

        <EditTextPreference
            android:key="companyKey"
            android:lines="1"
            android:summary="Input your company key"
            android:title="Company Key" />

        <EditTextPreference
            android:key="companySecret"
            android:lines="1"
            android:summary="Input your company secret"
            android:title="Company Secret" />

        <EditTextPreference
            android:hint="zdco.ujetqa.co"
            android:key="host"
            android:lines="1"
            android:summary="Input the host of API server"
            android:title="Host" />

        <EditTextPreference
            android:defaultValue="5001"
            android:inputType="number"
            android:key="port"
            android:lines="1"
            android:summary="Input your local server port"
            android:title="Port" />

        <EditTextPreference
            android:defaultValue="0"
            android:inputType="numberDecimal"
            android:key="networkSensitivity"
            android:summary="Input the network sensitivity for PSTN fallback. Float number 0 to 1. 1 is most sensitive."
            android:title="Network Sensitivity" />

    </PreferenceCategory>

    <Preference
        android:key="buttonAppSettings"
        android:summary="Show app settings"
        android:title="App Settings" />

    <Preference
        android:key="buttonClose"
        android:summary="Close settings view"
        android:title="Close" />

</PreferenceScreen>