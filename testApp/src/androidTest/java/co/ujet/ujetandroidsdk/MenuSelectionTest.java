package co.ujet.ujetandroidsdk;

import android.os.RemoteException;

import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.Until;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import static junit.framework.Assert.assertEquals;
import static junit.framework.Assert.assertTrue;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/19/17.
 */

public class MenuSelectionTest extends BaseTest {

    @Before
    public void launchUjet() throws Exception {
        clearUserPreferredLanguage();
        launchUjetDashBoard();
        checkUjet();

        UiObject2 nextButton = findButton("next");
        assertTrue(nextButton.isEnabled());
        nextButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
        changeToEnglish();
    }

    @After
    public void closeUjet() throws RemoteException, UiObjectNotFoundException {
        closeApp();
    }

    @Test
    public void openFirstMainMenu() {
        ScrollViewTestHelper listView = new ScrollViewTestHelper(findById("picker_ui_listview"));

        assertEquals("Main Menu", listView.getFocusedItem());
        pressNextButton();

        assertEquals("Sub Menu", listView.getFocusedItem());
        pressNextButton();

        assertEquals("Your current wait time is 24 hours", getInstantCallWaitTime());
    }

    @Test
    public void openSecondMainMenu() {
        ScrollViewTestHelper listView = new ScrollViewTestHelper(findById("picker_ui_listview"));

        assertEquals("Main Menu", listView.getFocusedItem());

        listView.moveDown();
        assertEquals("In-App Main Menu", listView.getFocusedItem());

        pressNextButton();
        assertEquals("In-App Sub Menu", listView.getFocusedItem());

        pressNextButton();
        assertEquals("Your current wait time is 24 hours", getInstantCallWaitTime());
    }

    private void pressNextButton() {
        UiObject2 nextButton = findButton("next_button");
        nextButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
    }

    /**
     * This method finds the CALL NOW button
     * and returns the wait time.
     *
     * @return "Your current wait time is 24 hours" if there's no agent.
     */
    private String getInstantCallWaitTime() {
        UiObject2 callNowButton = findById("channel_instant_call");
        return callNowButton.getChildren().get(1).getChildren().get(1).getText();
    }
}