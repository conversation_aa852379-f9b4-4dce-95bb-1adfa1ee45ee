package co.ujet.ujetandroidsdk;

import androidx.test.uiautomator.By;
import androidx.test.uiautomator.Direction;
import androidx.test.uiautomator.UiObject2;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON> on 4/17/17.
 */

public class ScrollViewTestHelper {
    private UiObject2 listView;
    private ArrayList<String> itemList = new ArrayList<>();
    private String lastItem, currentItem;

    ScrollViewTestHelper(UiObject2 listView) {
        this.listView = listView;
        this.listView.scroll(Direction.UP, 0.01f);

        for (int i = 0; i < this.listView.getChildCount(); i++) {
            String addingItem = this.listView.getChildren().get(i).getChildren().get(0).getText();
            if (addingItem != null) {
                itemList.add(addingItem);
            }
        }
    }

    public UiObject2 getListView() {
        return listView;
    }

    public void moveDown() {
        lastItem = itemList.get(itemList.size() - 1);
        currentItem = getFocusedItem();
        if (currentItem.equals(lastItem)) {
            moveToTopFromBottom();
        } else listView.scroll(Direction.DOWN, 0.2f, 1000);
    }

    public void moveToTopFromBottom() {
        String upperItem = listView.getChildren().get(1).getChildren().get(0).getText();
        while (upperItem != null) {
            moveToTop();
            upperItem = listView.getChildren().get(1).getChildren().get(0).getText();
        }
    }

    public void moveUp() {
        listView.scroll(Direction.UP, 0.2f, 10000);
    }

    public void moveToEnd() {
        listView.fling(Direction.DOWN);
    }

    public void moveToTop() {
        listView.fling(Direction.UP);
    }

    public int getChildCount() {
        return listView.getChildCount();
    }

    public boolean isScrollable() {
        return listView.isScrollable();
    }

    public List<UiObject2> getChildren() {
        return listView.findObjects(By.depth(1));
    }

    public String getResourceName() {
        return listView.getResourceName();
    }

    /**
     * This method returns the focused item in the list
     *
     * @return the focused item from current list
     */
    public String getFocusedItem() {
        int totalChildNum = listView.getChildCount();
        String focused = "";
        for (int i = 0; i < totalChildNum; i++) {
            if (listView.getChildren().get(i).getChildCount() == 3) { // Focused element of the list has three components . {tv_top_separator, tv_item, tv_bottom_separator}
                focused = listView.getChildren().get(i).getChildren().get(0).getText();
                return focused;
            }
        }
        return focused;
    }
}