package co.ujet.ujetandroidsdk;

import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.Until;

import org.junit.Before;
import org.junit.Test;

import static junit.framework.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * Created by mimu on 4/14/17.
 */

public class LanguageChangeTest extends BaseTest {

    private String defaultLanguage = "English";

    @Before
    public void launchUjet() throws Exception {
        launchUjetDashBoard();
        checkUjet();
    }

    /**
     * This test will test if there's plenty of language choices,
     * change language twice and check whether it has changed well.
     * Or, if language list is not available, check if the current language
     * is the same as default language.
     *
     * @throws Exception
     */
    @Test
    public void testLanguage() throws Exception {
        UiObject2 nextButton = findButton("next");
        UiObject2 languageButton = findClickableButton("language");

        assertTrue(nextButton.isEnabled());

        if (languageButton.isEnabled()) {
            assertTrue(languageButton.isClickable());

            languageButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
            changeLanguage();
            closeApp();

            launchUjetDashBoard();
            languageButton = findClickableButton("language");
            languageButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
            changeLanguage();
            closeApp();
        } else {
            nextButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
            String currentLanguage = checkCurrentLanguage();
            assertEquals(defaultLanguage, currentLanguage);
            closeApp();
        }
    }

    private void changeLanguage() throws InterruptedException, UiObjectNotFoundException {
        ScrollViewTestHelper listView = new ScrollViewTestHelper(findById("picker_ui_listview"));
        UiObject2 nextButton = findById("next_button");

        assertTrue(listView.isScrollable());
        assertTrue(nextButton.isClickable());

        listView.moveDown();

        String changedLanguage = getCurrentLanguage(listView);

        nextButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);

        assertTrue(changedLanguage.equals(checkChangedLanguage()));
    }

    /**
     * This method checks through the components of listView and
     * return the focused, that is, chosen language
     *
     * @param listView the list of languages
     * @return the chosen language from the list
     */
    private String getCurrentLanguage(ScrollViewTestHelper listView) {
        return listView.getFocusedItem();
    }

    private String checkCurrentLanguage() {
        UiObject2 nextButton = findById("next_button");
        String nextButtonText = nextButton.getChildren().get(0).getChildren().get(0).getText();
        if (nextButtonText.equals("Next")) return "English";
        else if (nextButtonText.equals("Weiter")) return "Deutsche";
        else if (nextButtonText.equals("Siguiente")) return "Español";
        else if (nextButtonText.equals("Suivant")) return "Français";
        return "";
    }

    private String checkChangedLanguage() {
        UiObject2 contactSupportButton = findById("start_ujet_button");
        if (contactSupportButton == null) {   //if FAQ is disabled
            return checkCurrentLanguage();
        } else {
            String contactSupportButtonText = contactSupportButton.getChildren().get(0).getChildren().get(0).getText();
            if (contactSupportButtonText.equals("Contact Support")) return "English";
            else if (contactSupportButtonText.equals("Support kontaktieren")) return "Deutsche";
            else if (contactSupportButtonText.equals("Ponte en contacto con el servicio al cliente"))
                return "Español";
            else if (contactSupportButtonText.equals("Contactez le service d'assistance clientèle"))
                return "Français";
            return "";
        }
    }
}