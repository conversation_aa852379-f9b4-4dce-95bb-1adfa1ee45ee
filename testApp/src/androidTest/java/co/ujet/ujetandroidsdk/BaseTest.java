package co.ujet.ujetandroidsdk;

import android.content.Context;
import android.content.Intent;
import android.os.Environment;
import android.os.RemoteException;

import androidx.test.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;
import androidx.test.uiautomator.By;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiObject2;
import androidx.test.uiautomator.UiObjectNotFoundException;
import androidx.test.uiautomator.Until;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;

import java.io.File;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Created by mimu on 4/13/17.
 */
@RunWith(AndroidJUnit4.class)
public abstract class BaseTest {

    protected UiDevice device;
    public final int LAUNCH_TIMEOUT = 10000;
    public final int DEFAULT_TIMEOUT = 3500;
    public final int EXECUTION_DEFAULT_TIMEOUT = 5000;

    @Before
    public void before() throws Exception {
        device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
        assertNotNull(device);
        openApp();
    }

    @After
    public void after() throws Exception {
        UiObject2 customerSupportButton = findByText("Contact Customer Support");
        if (customerSupportButton != null) customerSupportButton.clickAndWait(Until.newWindow(),DEFAULT_TIMEOUT);
        if (findById("welcome_image") != null) closeApp();
    }

    protected void takeScreenshot(String name) {
        String dir = String.format("%s/%s", Environment.getExternalStorageDirectory().getPath(), "test-screenshots");
        File theDir = new File(dir);
        if (!theDir.exists()) {
            theDir.mkdir();
        }
        device.takeScreenshot(new File(String.format("%s/%s", dir, name)));
    }

    protected void openApp() throws Exception {
        device.pressHome();

        Context context = InstrumentationRegistry.getInstrumentation().getContext();
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(BuildConfig.APPLICATION_ID);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        context.startActivity(intent);

        device.wait(Until.hasObject(By.pkg(BuildConfig.APPLICATION_ID).depth(0)), LAUNCH_TIMEOUT);

        clickHiddenButton();
        inputUserName();
    }

    protected void inputUserName() throws Exception {
        UiObject2 emailInput = findById("email");
        UiObject2 nameInput = findById("name");
        UiObject2 loginButton = findById("btn_login");

        assertTrue(emailInput.isEnabled());
        assertTrue(loginButton.isEnabled());
        assertTrue(loginButton.isClickable());

        emailInput.setText("<EMAIL>");
        nameInput.setText("Test");
        loginButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
    }

    /**
     * This method clicks on hidden button
     * for multiple times so that environment can
     * change into Ujetauto
     */
    private void clickHiddenButton() {
        UiObject2 hiddenButton = findButton("hiddenButton");
        if (hiddenButton != null) {
            assertTrue(hiddenButton.isClickable());
        }
        int count = 0;
        while (count < 10) {
            hiddenButton.click();
            count++;
        }
    }

    protected void launchUjetDashBoard() throws Exception {
        UiObject2 customerSupportButton = findByText("Contact Customer Support");
        if (customerSupportButton != null) {
            customerSupportButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
        }
        checkPermissions(); //check Permission for phone calls
        checkPermissions(); //check Permission for SMS messages
    }

    protected void checkPermissions() throws InterruptedException {
        UiObject2 permission = device.findObject(By.res("com.android.packageinstaller:id/permission_allow_button"));
        if (permission != null) permission.click();
    }

    protected void closeApp() throws UiObjectNotFoundException, RemoteException {
        UiObject2 exitButton = findButton("exit");
        if (exitButton != null) {
            exitButton.click();
        }
    }

    protected String getId(String id) {
        return String.format("%s:id/%s", BuildConfig.APPLICATION_ID, id);
    }

    protected UiObject2 findById(String id) {
        device.wait(Until.hasObject(By.res(getId(id)).enabled(true)), DEFAULT_TIMEOUT);
        return device.findObject(By.res(getId(id)).enabled(true));
    }

    protected UiObject2 findByText(String text) {
        device.wait(Until.hasObject(By.text(text).enabled(true)), DEFAULT_TIMEOUT);
        return device.findObject(By.text(text).enabled(true));
    }

    /**
     * This method finds an object which is not only
     * enabled but clickable by its id.
     *
     * @param id object's id
     * @return the object which is enabled and clickable
     */
    protected UiObject2 findButton(String id) {
        device.wait(Until.hasObject(By.res(getId(id)).clickable(true).enabled(true)), EXECUTION_DEFAULT_TIMEOUT);
        return device.findObject(By.res(getId(id)).clickable(true).enabled(true));
    }

    protected UiObject2 findClickableButton(String id) {
        device.wait(Until.hasObject(By.res(getId(id)).clickable(true)), DEFAULT_TIMEOUT);
        return device.findObject(By.res(getId(id)).clickable(true));
    }

    protected void clearUserPreferredLanguage() throws InterruptedException {
        UiObject2 clearButton = findByText("Clear User Preferred Language");
        clearButton.click();
    }

    protected void changeToEnglish() {
        ScrollViewTestHelper listView = new ScrollViewTestHelper(findById("picker_ui_listview"));
        UiObject2 nextButton = findById("next_button");
        while (true) {
            if (listView.getFocusedItem().equals("English")) break;
            listView.moveDown();
        }
        nextButton.clickAndWait(Until.newWindow(), DEFAULT_TIMEOUT);
    }

    protected void checkUjet() throws Exception {
        if (findById("welcome_image") == null) {
            closeApp();
            launchUjetDashBoard();
        }
    }
}