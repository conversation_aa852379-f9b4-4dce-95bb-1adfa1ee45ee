if ARGV.length != 3
	puts "usage: ruby version_checker.rb [check|write] [version.properties] [target_env]"
	exit 1
end

BasePath = '/Users/<USER>/ci/triggers'
command_type = ARGV[0]
version_filename = ARGV[1]
target_env = ARGV[2]

command_types = ["check", "write"]
target_envs = ["qa", "rc", "demo", "staging", "production"]

def exit_log(code)
	# Store exit code in the file and exit with 0
	# It's for teamcity build command step prevent build failure with optional job. 
	# Please check the output file contents whether previous command was success or not.
	
	f = File.open("#{BasePath}/ujet_android_build_exit_code", "w+")
	f.write(code)
	f.close
	exit 0
end

if !command_types.include?(command_type)
	puts "Please select command type one of these. #{command_types}"
	exit_log 1
end

if !File.file?(version_filename)
	puts "version.properties file not exists"
	exit_log 1
end

if !target_envs.include?(target_env)
	puts "Please select env one of these. #{target_envs}"
	exit_log 1
end

content = File.open(version_filename).read

key = "VERSION_NAME_#{target_env.upcase}"
match = content.scan(/#{key}=(\d+\.\d+\.\d+)/)
unless match and match.length == 1
	puts "Counldnt' find versionName string"
	exit_log 1
end

version = match[0][0]

filename = "#{BasePath}/ujet_android_test_app_version_#{target_env}.data"

if command_type == "check"
	unless File.file?(filename)
		# no file -> ok to distribute
		exit_log 0
	end

	old_version = File.open(filename).read

	if old_version == version
		# same version -> skip distribution
		exit_log 1
	end
	exit_log 0
else
	File.open(filename, 'w+').write(version)
end

exit_log 0