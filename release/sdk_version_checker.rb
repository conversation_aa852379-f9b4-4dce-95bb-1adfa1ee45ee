require 'net/http'
require 'uri'

def exit_log(code)
	# Store exit code in the file and exit with 0
	# It's for teamcity build command step prevent build failure with optional job. 
	# Please check the output file contents whether previous command was success or not.
	
	f = File.open("/Users/<USER>/ci/triggers/ujet_android_build_exit_code", "w+")
	f.write(code)
	f.close
	exit 0
end

def get_gradle_credential
	property_file = ENV['HOME'] + "/.gradle/gradle.properties" 
	if !File.file?(property_file)
		puts "Couldn't find #{property_file}"
		exit 1
	end
	text = File.open(property_file).read
	text.gsub!(/\r\n?/, "\n")

	artifactory_username = ""
	artifactory_password = ""

	text.each_line do |line|
		if line.include?("artifactory_username")
			artifactory_username = line.sub! 'artifactory_username=', ''
		end

		if line.include?("artifactory_password")
			artifactory_password = line.sub! 'artifactory_password=', ''
		end
	end

	return artifactory_username.strip!, artifactory_password.strip!
end

def get_latest_artifactory_version
	artifactory_username, artifactory_password = get_gradle_credential
	metadata_url = "http://artifactory.ujetqa.co/artifactory/libs-release-local/co/ujet/android/ujet/maven-metadata.xml"

	uri = URI.parse(metadata_url)
	http = Net::HTTP.new(uri.host, uri.port)
	req = Net::HTTP::Get.new(uri.request_uri)
	req.basic_auth artifactory_username, artifactory_password
	res = http.request(req)
	if res.code != "200"
		puts "Couldn't get metadata from artifactory"
		exit_log 1
	end
	version_match = res.body.match(/<version>(\d+\.\d+\.\d+)<\/version>/)
	
	if version_match.length != 2
		puts "Couldn't find version data from metadata"
		exit_log 1
	end
	version_match[1]
end

def get_current_sdk_version
	version_filename = File.expand_path(File.join(File.expand_path(File.dirname(__FILE__)), "..", "ujet", "src", "main", "java", "co", "ujet", "android", "UjetVersion.kt"))
	if !File.file?(version_filename)
		puts "Couldn't find UjetVersion.kt"
		exit_log 1
	end

	text = File.open(version_filename).read
	version_match = text.match(/BUILD = \"(\d+\.\d+\.\d+)\"/)
	if version_match.length != 2
		puts "Couldn't find version data from UjetVersion.kt"
		exit_log 1
	end

	version_match[1]
end

if get_latest_artifactory_version != get_current_sdk_version
	exit_log 0
else
	exit_log 1
end
