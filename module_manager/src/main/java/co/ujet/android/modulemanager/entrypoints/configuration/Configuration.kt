package co.ujet.android.modulemanager.entrypoints.configuration

import androidx.annotation.StyleRes
import co.ujet.android.modulemanager.common.ui.UjetStylesOptions

interface Configuration {
    fun getCompanyKey(): String

    fun getCompanyName(): String

    fun getEndpointUrl(): String

    fun getFallbackPhoneNumber(): String?

    fun getLogLevel(): Int

    fun getDefaultLanguage(): String?

    @StyleRes
    fun getUjetStyleId(): Int

    fun getAppVersionName(): String

    fun getAppVersion(): String

    fun isUncaughtExceptionHandlerEnabled(): Boolean

    fun getPstnFallbackSensitivity(): Double

    fun isDarkModeEnabled(): Boolean

    fun isShowSingleChannelEnabled(): Boolean

    fun isAutoMinimizeCallView(): Boolean

    fun isRemoveAgentIconBorderEnabled(): Boolean

    fun getStaticFontSizeInPickerView(): Boolean

    fun isHideMediaAttachmentInChat(): Boolean

    fun isShowCsatSkipButton(): Boolean

    fun isIgnoreReadPhoneStatePermission(): Boolean

    fun getAppIdentifier(): String

    fun getUjetStylesOptions(): UjetStylesOptions?

    fun getSpinnerDrawableRes(): Int?

    fun isLandscapeOrientationDisabled(): Boolean

    companion object {
        internal val defaultImplementation = object : Configuration {
            override fun getCompanyKey(): String = ""

            override fun getCompanyName(): String = ""

            override fun getEndpointUrl(): String = ""

            override fun getFallbackPhoneNumber(): String? = null

            override fun getLogLevel(): Int = 0

            override fun getDefaultLanguage(): String? = null

            @StyleRes
            override fun getUjetStyleId(): Int = 0

            override fun getAppVersionName(): String = ""

            override fun getAppVersion(): String = ""

            override fun isUncaughtExceptionHandlerEnabled(): Boolean = false

            override fun getPstnFallbackSensitivity(): Double = 0.0

            override fun isDarkModeEnabled(): Boolean = false

            override fun isShowSingleChannelEnabled(): Boolean = false

            override fun isAutoMinimizeCallView(): Boolean = false

            override fun isRemoveAgentIconBorderEnabled(): Boolean = false

            override fun getStaticFontSizeInPickerView(): Boolean = false

            override fun isHideMediaAttachmentInChat(): Boolean = false

            override fun isShowCsatSkipButton(): Boolean = false

            override fun isIgnoreReadPhoneStatePermission(): Boolean = false

            override fun getAppIdentifier(): String = ""

            override fun getUjetStylesOptions(): UjetStylesOptions? = null

            override fun getSpinnerDrawableRes(): Int? = null

            override fun isLandscapeOrientationDisabled(): Boolean = false
        }
    }
}
