package co.ujet.android.modulemanager.entrypoints.chat

import org.json.JSONObject

interface ChatMessage {
    /**
     * Returns a [JSONObject] representation of the chat message that can be used by [ChatTransport] to
     * transfer the message
     */
    fun toJson(): JSONObject

    /**
     * Returns a Map representation of the chat message that can be used by [ChatTransport] to transfer the message
     */
    fun toMap(): HashMap<String, Any> = hashMapOf()
}