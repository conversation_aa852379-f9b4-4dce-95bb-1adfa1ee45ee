package co.ujet.android.modulemanager.entrypoints.chat

import java.util.Date

interface ChatTransportListener<T : ChatMessage> {
    fun onMessageSent(chatMessage: T)
    fun onMessageSendFailed(message: T)
    fun onMessageReceived(
        id: String,
        body: String,
        authorIdentity: String,
        date: Date,
        messageIndex: Long
    )

    fun onMemberJoined(identity: String)
    fun onMemberLeft(identity: String)
    fun onTypingStarted(identity: String)
    fun onTypingEnded(identity: String)
    fun onTransportStateChanged(state: ChatTransportState)
}