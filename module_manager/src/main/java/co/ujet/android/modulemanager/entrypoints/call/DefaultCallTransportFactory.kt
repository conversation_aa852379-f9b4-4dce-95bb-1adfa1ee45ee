package co.ujet.android.modulemanager.entrypoints.call

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher

internal object DefaultCallTransportFactory : CallTransportFactory {

    override val transportType = "default_call"
    override val transportVersion = "N/A"

    override fun createCallTransport(
        context: Context,
        region: String?, //Only used in nexmo call
        callId: Int,
        participantId: Int, //Only used in nexmo call
        callTransportListener: CallTransportListener,
        callAccessTokenFetcher: CallAccessTokenFetcher,
        logger: Logger
    ) = DefaultCallTransport(logger)
}

internal class DefaultCallTransport(private val logger: Logger) : CallTransport {

    companion object {
        private const val LOG_MESSAGE =
            "This is a placeholder in case of a missing call transport dependency"
    }

    override fun connect() {
        logger.d(LOG_MESSAGE)
    }

    override fun getState(): CallTransportState {
        logger.d(LOG_MESSAGE)
        return CallTransportState.Disconnected
    }

    override fun mute() {
        logger.d(LOG_MESSAGE)
    }

    override fun unMute() {
        logger.d(LOG_MESSAGE)
    }

    override fun disconnect() {
        logger.d(LOG_MESSAGE)
    }
}