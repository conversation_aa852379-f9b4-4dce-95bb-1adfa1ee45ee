package co.ujet.android.modulemanager.entrypoints.cobrowse

import android.app.Application
import android.util.Log
import co.ujet.android.modulemanager.EntryPointFactory

interface Cobrowse {

    fun createSession(
        application: Application,
        customData: Map<String, String>,
        sessionStateListener: SessionStateListener,
        onSessionActivationRequest: () -> Unit = {},
        onSessionRemoteControlRequest: () -> Unit = {},
        onSessionCreated: (String) -> Unit = {},
        onSessionCreationError: (Error) -> Unit = {},
        onSessionFullDeviceRequest: () -> Unit = {},
    ) {
        Log.d(
            "CobrowsePlaceholder",
            "This is a placeholder in case cobrowse module is not added as dependency"
        )
    }

    fun stopSession(callback: (Error?) -> Unit) {
        Log.d(
            "CobrowsePlaceholder",
            "This is a placeholder in case cobrowse module is not added as dependency"
        )
    }

    fun isEnabled() = false

    fun getSessionState(): State = State.INACTIVE

    fun grantActivateSessionRequest(requestGranted: Boolean) {
        Log.d(
            "CobrowsePlaceholder",
            "This is a placeholder in case cobrowse module is not added as dependency"
        )
    }

    fun grantRemoteControlRequest(requestGranted: Boolean) {
        Log.d(
            "CobrowsePlaceholder",
            "This is a placeholder in case cobrowse module is not added as dependency"
        )
    }

    fun grantFullDeviceRequest(requestGranted: Boolean) {
        Log.d(
            "CobrowsePlaceholder",
            "This is a placeholder in case cobrowse module is not added as dependency"
        )
    }

    companion object : Cobrowse {
        private val instance by lazy { EntryPointFactory.provideEntryPoint(Cobrowse::class.java) }

        override fun createSession(
            application: Application,
            customData: Map<String, String>,
            sessionStateListener: SessionStateListener,
            onSessionActivationRequest: () -> Unit,
            onSessionRemoteControlRequest: () -> Unit,
            onSessionCreated: (String) -> Unit,
            onSessionCreationError: (Error) -> Unit,
            onSessionFullDeviceRequest: () -> Unit,
        ) = instance.createSession(
            application,
            customData,
            sessionStateListener,
            onSessionActivationRequest,
            onSessionRemoteControlRequest,
            onSessionCreated,
            onSessionCreationError,
            onSessionFullDeviceRequest
        )

        override fun stopSession(callback: (Error?) -> Unit) =
            instance.stopSession(callback)

        override fun isEnabled() = instance.isEnabled()

        override fun getSessionState() = instance.getSessionState()

        override fun grantActivateSessionRequest(requestGranted: Boolean) =
            instance.grantActivateSessionRequest(requestGranted)

        override fun grantRemoteControlRequest(requestGranted: Boolean) =
            instance.grantRemoteControlRequest(requestGranted)

        override fun grantFullDeviceRequest(requestGranted: Boolean) =
            instance.grantFullDeviceRequest(requestGranted)

        internal val defaultImplementation = object : Cobrowse {}
    }

    fun interface SessionStateListener {
        fun onSessionStateUpdated(state: State)
    }

    enum class State { INACTIVE, PENDING, ACTIVE }
}
