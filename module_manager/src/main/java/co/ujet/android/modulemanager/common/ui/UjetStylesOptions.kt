package co.ujet.android.modulemanager.common.ui

import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.common.ui.domain.ChatStyles
import co.ujet.android.modulemanager.entrypoints.ui.UjetUI

class UjetStylesOptions private constructor(builder: Builder) {
    val chatQuickReplyButtonsStyle = builder.chatQuickReplyButtonsStyle
    val chatStyles = builder.chatStyles

    class Builder {
        internal var chatQuickReplyButtonsStyle: QuickReplyButtonsStyle? = null
        internal var chatStyles: ChatStyles? = null

        /**
         * Sets the Quick reply buttons style (GROUPED, INDIVIDUAL)
         *
         * @param chatQuickReplyButtonsStyle Quick reply buttons style
         *
         * @return Builder
         */
        fun setChatQuickReplyButtonsStyle(chatQuickReplyButtonsStyle: QuickReplyButtonsStyle) = apply {
            this.chatQuickReplyButtonsStyle = chatQuickReplyButtonsStyle
        }

        /**
         * Sets the Chat Styles
         *
         * @param chatStyles Chat Styles
         *
         * @return Builder
         */
        fun setChatStyles(chatStyles: ChatStyles) = apply {
            this.chatStyles = chatStyles
        }

        /**
         * Sets the Chat Styles as JSON
         *
         * @param chatStyles Chat Styles JSON
         *
         * @return Builder
         */
        fun setChatStyles(chatStylesJson: String?) = apply {
            val ujetUi = EntryPointFactory.provideEntryPoint(UjetUI::class.java)
            this.chatStyles = ujetUi.buildChatStylesFromJson(chatStylesJson)
        }

        fun build() = UjetStylesOptions(this)
    }

    companion object {
        enum class QuickReplyButtonsStyle { GROUPED, INDIVIDUAL }
    }
}
