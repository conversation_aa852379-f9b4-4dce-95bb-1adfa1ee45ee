package co.ujet.android.modulemanager

import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import co.ujet.android.modulemanager.entrypoints.call.DefaultCallTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.DefaultChatTransportFactory
import co.ujet.android.modulemanager.entrypoints.cobrowse.Cobrowse
import co.ujet.android.modulemanager.entrypoints.configuration.Configuration
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.entrypoints.ui.UjetUI
import java.util.ServiceLoader

object ModuleManager {

    var initialized = false

    init {
        // Register all default entry points
        EntryPointFactory.registerDefaultPoint(Cobrowse::class.java, Cobrowse.defaultImplementation)
        EntryPointFactory.registerDefaultPoint(ChatTransportFactory::class.java, DefaultChatTransportFactory)
        EntryPointFactory.registerDefaultPoint(CallTransportFactory::class.java, DefaultCallTransportFactory)
        EntryPointFactory.registerDefaultPoint(Logger::class.java, Logger)
        EntryPointFactory.registerDefaultPoint(UjetUI::class.java, UjetUI.defaultImplementation)
        EntryPointFactory.registerDefaultPoint(Configuration::class.java, Configuration.defaultImplementation)
    }

    fun configure(configurationsMap: Map<String, Any?>) {
        // Invoke service loader to call all configurators
        ServiceLoader
                .load(Configurable::class.java)
                .forEach {
                    it.configure(configurationsMap)
                }
        initialized = true
    }
}
