package co.ujet.android.modulemanager.common.chat

import org.json.JSONObject

object ChatTransportUtil {
    fun handleTaskVaMessage(fetcher: TaskVaMessageFetcher, messageId: Int, onSuccess: (String) -> Unit, onFailure: (String?) -> Unit) {
        fetcher.fetch(
            messageId,
            onSuccess = { body ->
                try {
                    val content = JSONObject(body).getString("content")
                    onSuccess(content)
                } catch (e: Exception) {
                    onFailure(e.stackTraceToString())
                }
            },
            onFailure = {
                // Error Message is Already logged in APIManager, nothing to handle here
            }
        )
    }
}
