package co.ujet.android.modulemanager.entrypoints.chat

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher

interface ChatTransportFactory {

    val transportType: String

    val transportVersion: String

    fun <T : ChatMessage> createChatTransport(
        context: Context,
        channelId: String,
        region: String?,
        chatTransportListener: ChatTransportListener<T>,
        chatAccessTokenFetcher: ChatAccessTokenFetcher,
        taskVaMessageFetcher: TaskVaMessageFetcher,
        logger: Logger
    ): ChatTransport<T>
}
