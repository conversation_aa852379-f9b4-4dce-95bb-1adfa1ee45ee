package co.ujet.android.modulemanager

import co.ujet.android.modulemanager.entrypoints.call.CallTransportFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatTransportFactory

@Suppress("UNCHECKED_CAST")
object EntryPointFactory {
    private val registeredEntryPoints = mutableMapOf<Class<*>, List<Any>>()
    private val defaultEntryPoints = mutableMapOf<Class<*>, Any>()

    init {
        if (ModuleManager.initialized.not()) {
            ModuleManager.configure(HashMap())
        }
    }

    internal fun registerDefaultPoint(clazz: Class<*>, entryPoint: Any) {
        defaultEntryPoints[clazz] = entryPoint
    }

    fun <T : Any> registerEntryPoint(clazz: Class<T>, entryPoint: T) {
        registeredEntryPoints[clazz] = (registeredEntryPoints[clazz] ?: mutableListOf()) + entryPoint
    }

    fun <T : Any> provideEntryPoint(clazz: Class<T>): T {
        return (registeredEntryPoints[clazz] as? List<T>)?.firstOrNull() ?: defaultEntryPoints[clazz] as T
    }

    fun <T : Any> provideEntryPoints(clazz: Class<T>): List<T> {
        val registeredEntryPoints =  registeredEntryPoints[clazz] as? List<T>
        return if (registeredEntryPoints.isNullOrEmpty()) {
            listOfNotNull(defaultEntryPoints[clazz] as? T?)
        } else {
            registeredEntryPoints
        }
    }

    private fun hasEntryPointForType(clazz: Class<*>) = provideEntryPoints(clazz).firstOrNull() != defaultEntryPoints[clazz]

    fun hasChatEntryPoint() = hasEntryPointForType(ChatTransportFactory::class.java)

    fun hasCallEntryPoint() = hasEntryPointForType(CallTransportFactory::class.java)
}
