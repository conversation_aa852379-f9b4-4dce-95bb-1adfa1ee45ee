package co.ujet.android.modulemanager.entrypoints.log

import android.util.Log
import co.ujet.android.modulemanager.EntryPointFactory

interface Logger {
    fun v(message: String, vararg args: Any?)
    fun d(message: String, vararg args: Any?)
    fun i(message: String, vararg args: Any?)
    fun w(message: String, vararg args: Any?)
    fun w(exception: Exception, message: String?, vararg args: Any?)
    fun e(message: String, vararg args: Any?)
    fun e(exception: Exception, message: String?, vararg args: Any?)

    companion object : Logger {
        private const val TAG = "Logger"

        override fun v(message: String, vararg args: Any?) {
            Log.v(TAG, message)
        }

        override fun d(message: String, vararg args: Any?) {
            Log.d(TAG, message)
        }

        override fun i(message: String, vararg args: Any?) {
            Log.i(TAG, message)
        }

        override fun w(message: String, vararg args: Any?) {
            Log.w(TAG, message)
        }

        override fun w(exception: Exception, message: String?, vararg args: Any?) {
            Log.w(TAG, message, exception)
        }

        override fun e(message: String, vararg args: Any?) {
            Log.e(TAG, message)
        }

        override fun e(exception: Exception, message: String?, vararg args: Any?) {
            Log.w(TAG, message, exception)
        }
    }
}
