package co.ujet.android.modulemanager.entrypoints.chat

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.chat.ChatAccessTokenFetcher
import co.ujet.android.modulemanager.common.chat.TaskVaMessageFetcher

internal object DefaultChatTransportFactory : ChatTransportFactory {
    override val transportType = "default_chat"
    override val transportVersion = "N/A"

    override fun <T : ChatMessage> createChatTransport(
        context: Context,
        channelId: String,
        region: String?,
        chatTransportListener: ChatTransportListener<T>,
        chatAccessTokenFetcher: ChatAccessTokenFetcher,
        taskVaMessageFetcher: TaskVaMessageFetcher,
        logger: Logger
    ) = DefaultChatTransport<T>(logger)
}

internal class DefaultChatTransport<T : ChatMessage>(private val logger: Logger) : ChatTransport<T> {

    companion object {
        private const val LOG_MESSAGE =
            "This is a placeholder in case of a missing chat transport dependency"
    }

    override fun connect() {
        logger.d(LOG_MESSAGE)
    }

    override fun getState(): ChatTransportState {
        logger.d(LOG_MESSAGE)
        return ChatTransportState.DISCONNECTED
    }

    override fun send(chatMessage: T) {
        logger.d(LOG_MESSAGE)
    }

    override fun onTypingStarted() {
        logger.d(LOG_MESSAGE)
    }

    override fun onTypingStopped() {
        logger.d(LOG_MESSAGE)
    }

    override fun sendMessagePreview(chatMessage: ChatMessage) {
        logger.d(LOG_MESSAGE)
    }

    override fun disconnect() {
        logger.d(LOG_MESSAGE)
    }
}
