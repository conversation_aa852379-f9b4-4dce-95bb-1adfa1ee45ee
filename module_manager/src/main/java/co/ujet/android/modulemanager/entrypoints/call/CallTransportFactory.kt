package co.ujet.android.modulemanager.entrypoints.call

import android.content.Context
import co.ujet.android.modulemanager.entrypoints.log.Logger
import co.ujet.android.modulemanager.common.call.CallAccessTokenFetcher

interface CallTransportFactory {

    val transportType: String

    val transportVersion: String

    fun createCallTransport(
        context: Context,
        region: String?,
        callId: Int,
        participantId: Int,
        callTransportListener: CallTransportListener,
        callAccessTokenFetcher: CallAccessTokenFetcher,
        logger: Logger
    ): CallTransport
}