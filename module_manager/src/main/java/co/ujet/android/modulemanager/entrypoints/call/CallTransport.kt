package co.ujet.android.modulemanager.entrypoints.call

interface CallTransport {
    fun connect()
    fun getState(): CallTransportState
    fun mute()
    fun unMute()
    fun disconnect()
}

sealed class CallTransportState {
    object Disconnected : CallTransportState()
    object Connecting : CallTransportState()
    object Connected : CallTransportState()
    data class Error(val failureReason: FailureReason) : CallTransportState()
    data class Failed(val failureReason: FailureReason) : CallTransportState()
}

/**
 * The call failure reason. This is a leaky abstraction as the UI layer relies on these specific failure definitions that are really Twilio specific.
 * TODO: Find out if these are all actually valid reasons from <PERSON><PERSON><PERSON>'s point-of-view and in which use-cases these are actually triggered.
 *   There might be a disconnect here between the reality of when these errors are triggered and the messaging tha the UI uses for these error values
 */
enum class FailureReason {
    /**
     * Copied hardcoded values from [UjetCallService]. Not sure these are in use anymore at all by <PERSON><PERSON><PERSON> as they are not defined in [CallException].
     * Leaving them here for now, but we should remove them when we are sure that <PERSON><PERSON><PERSON> isn't using them anymore.
     */
    GENERIC_FAILURE,

    /**
     * Connection error. A connection error occurred during the call.
     */
    CONNECTION_FAILURE,

    /**
     * The Twilio call establishment flow failed. Might be due to a network issue or a problem with the Twilio backend.
     */
    TRANSPORT_FAILURE,

    /**
     * Catch-all for all other reasons.
     */
    UNKNOWN_FAILURE
}
