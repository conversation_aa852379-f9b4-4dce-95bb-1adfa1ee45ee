publishing {
    publications {
        android.libraryVariants.all { variant ->
            if (variant.buildType.name != 'release')
                return

            def packageName = "module_manager"

            basic(MavenPublication) {
                groupId 'co.ujet.android'
                artifactId packageName
                version getUjetSDKVersion()

                artifact("$buildDir/outputs/aar/${packageName}-release.aar")
                artifact("$buildDir/intermediates/aar_main_jar/release/syncReleaseLibJars/classes.jar") {
                    classifier "sources"
                }

                pom.withXml {
                    def dependenciesNode = asNode().appendNode('dependencies')
                    def dependencies = []
                    dependencies.addAll(configurations.implementation.allDependencies)
                    dependencies.each {
                        if (it.name != 'unspecified') {
                            def dependencyNode = dependenciesNode.appendNode('dependency')
                            dependencyNode.appendNode('groupId', it.group)
                            dependencyNode.appendNode('artifactId', it.name)
                            dependencyNode.appendNode('version', it.version)
                        }
                    }
                }
            }
        }
    }
}
