package cx.ujet.android.markdown.span

import android.graphics.Canvas
import android.graphics.Paint
import android.text.Layout
import android.text.Spanned
import android.text.style.LeadingMarginSpan
import cx.ujet.android.markdown.UjetMarkdown
import cx.ujet.android.markdown.UjetMarkdown.centerPositionAdditionalWidth

/**
 * A version of [LeadingMarginSpan] that shows text inside the margin.
 *
 * @param indentation The indentation level of this item.
 * @param string String to show inside the margin.
 */
class TextLeadingMarginSpan(private val indentation: Int, private val string: String,
                            private val isTextCentered: Boolean) : LeadingMarginSpan {
    private var leadingMargin = 0f

    override fun drawLeadingMargin(
        canvas: Canvas,
        paint: Paint,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence,
        start: Int,
        end: Int,
        first: Boolean,
        l: Layout,
    ) {
        val startCharOfSpan = (text as? Spanned)?.getSpanStart(this)
        val isFirstCharacter = startCharOfSpan == start
        val singleCharWidth = paint.measureText("•")
        val markNumber = string.filter { it.isDigit() }
        val isOrderedList = markNumber.isNotEmpty()
        val marginWidth = (singleCharWidth * UjetMarkdown.WHITE_SPACES_COUNT_FOR_TAB).toInt()

        if (isFirstCharacter) {
            val currBaseline = baseline.toFloat()
            val marginWidthWithIndentation = marginWidth.toFloat() * indentation
            // Calculate X position of • based on indentation level
            var xPosition = if (indentation > 0) {
                (marginWidthWithIndentation) + (singleCharWidth * indentation)
            } else {
                (marginWidthWithIndentation)
            }
            leadingMargin = if (isOrderedList) {
                (marginWidthWithIndentation) + (singleCharWidth * ((2 * markNumber.length) + 1))
            } else {
                (marginWidthWithIndentation) + (singleCharWidth * 2)
            }
            if (isTextCentered) {
                // Align first item in the list to centered horizontally and use the same width to position
                // first and remaining items
                val listItemContent = (text as? Spanned)?.subSequence(start, end)
                val additionalWidth = singleCharWidth * (string.length + (listItemContent?.length ?: 0))
                if (centerPositionAdditionalWidth == 0f) {
                    centerPositionAdditionalWidth = additionalWidth
                }
                val canvasCenterPosition = (canvas.width - centerPositionAdditionalWidth) /2
                leadingMargin += canvasCenterPosition
                xPosition += canvasCenterPosition
            } else {
                centerPositionAdditionalWidth = 0f // Reset
            }
            canvas.drawText(string, xPosition, currBaseline, paint)
        }
    }

    // Start margin to first / leading character of the list item
    override fun getLeadingMargin(first: Boolean): Int {
        return leadingMargin.toInt()
    }
}
