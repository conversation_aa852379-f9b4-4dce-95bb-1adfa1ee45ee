package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.UjetMarkdown.findAndReplace

object Underline {
    private val UNDERLINE_REGEX = "(--)(.+?)(--)".toRegex()
    private const val UNDERLINE_HTML_OPEN = "<u>"
    private const val UNDERLINE_HTML_CLOSE = "</u>"

    internal fun String.parseUnderlines(): String {
        return findAndReplace(UNDERLINE_REGEX, UNDERLINE_HTML_OPEN, UNDERLINE_HTML_CLOSE)
    }
}
