package cx.ujet.android.markdown.types

object Link {
    val URL_REGEX = "(http|https)?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_+.~#?&\\/=]*)"
    private val LINK_REGEX = "\\[(.+?)]\\((.+?)\\)".toRegex()
    private const val LINK_HTML_FORMAT = "<a href=\"%s\">%s</a>"

    internal fun String.parseLinks(): String {
        val matchedUrls = mutableListOf<String?>()
        var result = LINK_REGEX.replace(this) {
            val linkText = it.groups[1]?.value
            val linkAddress = it.groups[2]?.value
            matchedUrls.add(linkAddress)
            return@replace String.format(LINK_HTML_FORMAT, linkAddress, linkText)
        }
        result = URL_REGEX.toRegex().replace(result) {
            val matchedUrl = it.value
            if (matchedUrls.contains(matchedUrl)) {
                return@replace matchedUrl
            }
            val escapedUrl = escapeSpecialChars(matchedUrl)
            return@replace String.format(LINK_HTML_FORMAT, escapedUrl, escapedUrl)
        }
        return result
    }

    private fun escapeSpecialChars(input: String): String {
        return input
            .replace("_", "&#95;")
            .replace("*", "&#42;")
            .replace("-", "&#45;")
    }
}
