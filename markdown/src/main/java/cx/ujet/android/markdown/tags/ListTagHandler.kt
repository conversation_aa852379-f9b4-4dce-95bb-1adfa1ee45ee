package cx.ujet.android.markdown.tags

import android.text.Editable
import android.text.Html
import cx.ujet.android.markdown.UjetMarkdown.DEFAULT_INDENTATION
import cx.ujet.android.markdown.UjetMarkdown.ORDERED_LIST_START_INDEX
import cx.ujet.android.markdown.tags.ol.NumberListItem
import cx.ujet.android.markdown.types.Lists.OL_TAG
import cx.ujet.android.markdown.types.Lists.UL_TAG
import cx.ujet.android.markdown.types.Lists.LI_TAG
import cx.ujet.android.markdown.tags.ol.Ol
import cx.ujet.android.markdown.tags.ul.BulletListItem
import cx.ujet.android.markdown.tags.ul.Ul
import org.xml.sax.XMLReader
import java.util.Stack

class ListTagHandler(private val isTextCentered: Boolean) : Html.TagHandler {

    private val lists = Stack<ListTag>()
    private var orderedListIndexMap = mutableMapOf<Int, Int>()
    private var orderedListCurrentIndentation: Int

    init {
        orderedListCurrentIndentation = DEFAULT_INDENTATION
        orderedListIndexMap[DEFAULT_INDENTATION] = ORDERED_LIST_START_INDEX
    }

    /**
     * Called when the HTML parser reaches an opening or closing tag.
     * We only handle list tags and ignore other tags.
     *
     * <ul> and <ol> tags are pushed to the [lists] stack and popped when the closing tag is reached.
     *
     * <li> tags are handled by the [ListTag] instance corresponding to the parent tag.
     */
    override fun handleTag(opening: Boolean, tag: String, output: Editable, xmlReader: XMLReader) {
        when (tag) {
            UL_TAG -> if (opening) { // handle <ul>
                lists.push(Ul())
            } else { // handle </ul>
                lists.pop()
            }
            OL_TAG -> if (opening) { // handle <ol>
                lists.push(Ol())
            } else { // handle </ol>
                lists.pop()
            }
            LI_TAG -> if (opening) { // handle <li>
                // Pass item index to ordered list item and ignore it in other lists
                orderedListIndexMap[orderedListCurrentIndentation]?.let { lists.peek().openItem(output, it) }
            } else { // handle </li>
                handleCloseItem(output)
            }
        }
    }

    private fun handleCloseItem(output: Editable) {
        var updatedIndentation = DEFAULT_INDENTATION
        var leadingTextContent = ""
        when (lists.peek()) {
            is Ol -> {
                updatedIndentation = lists.peek().getUpdatedIndentation(output, lists.peek().getLast<NumberListItem>(output))
                val lastOrderedListItem = lists.peek().getLast<NumberListItem>(output)
                // When indentation is changed, update mark number so that ordered list starts with start index (i.e, 1)
                // and reset the 'index' to next index value. Next level ordered lists starts with new index.
                if (orderedListCurrentIndentation < updatedIndentation) {
                    lastOrderedListItem?.let { mark ->
                        mark.number = ORDERED_LIST_START_INDEX
                        orderedListIndexMap[updatedIndentation] = ORDERED_LIST_START_INDEX + 1
                    }
                } else if (orderedListCurrentIndentation > updatedIndentation) {
                    lastOrderedListItem?.let { mark ->
                        val currentMarkNumber = (orderedListIndexMap[updatedIndentation] ?: 0)
                        mark.number = currentMarkNumber
                        orderedListIndexMap[updatedIndentation] = currentMarkNumber + 1
                    }
                } else {
                    orderedListIndexMap[updatedIndentation] = (orderedListIndexMap[updatedIndentation] ?: 0) + 1
                }
                orderedListCurrentIndentation = updatedIndentation
                lastOrderedListItem?.let { mark ->
                    leadingTextContent = "${mark.number}."
                }
            }
            is Ul -> {
                updatedIndentation = lists.peek().getUpdatedIndentation(output, lists.peek().getLast<BulletListItem>(output))
                val lastOrderedListItem = lists.peek().getLast<BulletListItem>(output)
                // When indentation is changed, update mark number so that ordered list starts with start index (i.e, 1)
                // and reset the 'index' to next index value. Next level ordered lists starts with new index.
                if (orderedListCurrentIndentation < updatedIndentation) {
                    lastOrderedListItem?.let {
                        orderedListIndexMap[updatedIndentation] = ORDERED_LIST_START_INDEX + 1
                    }
                } else if (orderedListCurrentIndentation > updatedIndentation) {
                    lastOrderedListItem?.let {
                        val currentMapValue = (orderedListIndexMap[updatedIndentation] ?: 0)
                        orderedListIndexMap[updatedIndentation] = currentMapValue + 1
                    }
                } else {
                    orderedListIndexMap[updatedIndentation] = (orderedListIndexMap[updatedIndentation] ?: 0) + 1
                }
                orderedListCurrentIndentation = updatedIndentation
                leadingTextContent = when (updatedIndentation) {
                    0 -> {
                        "•"
                    }
                    1 -> {
                        "◦"
                    }
                    else -> {
                        "▪"
                    }
                }
            }
        }
        lists.peek().closeItem(output, updatedIndentation, leadingTextContent, isTextCentered)
    }
}
