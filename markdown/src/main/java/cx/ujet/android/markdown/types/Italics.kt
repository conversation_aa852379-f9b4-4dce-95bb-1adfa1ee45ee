package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.UjetMarkdown.findAndReplace

object Italics {
    private val V1_REGEX = "(\\*)(.+?)(\\*)".toRegex()
    private val V2_REGEX = "(_)(.+?)(_)".toRegex()
    private val V3_REGEX = "(__)(.+?)(__)".toRegex()
    private const val HTML_OPEN = "<i>"
    private const val HTML_CLOSE = "</i>"

    internal fun String.parseItalics(): String {
        return findAndReplace(V1_REGEX, HTML_OPEN, HTML_CLOSE)
                .findAndReplace(V2_REGEX, HTML_OPEN, HTML_CLOSE)
                .findAndReplace(V3_REGEX, HTML_OPEN, HTML_CLOSE)
    }
}
