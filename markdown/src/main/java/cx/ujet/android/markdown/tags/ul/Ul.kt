package cx.ujet.android.markdown.tags.ul

import android.text.Editable
import cx.ujet.android.markdown.tags.ListTag
import cx.ujet.android.markdown.span.TextLeadingMarginSpan

/**
 * Subclass of [ListTag] for unordered lists.
 */
class Ul : ListTag() {

    override fun openItem(text: Editable, index: Int) {
        appendNewLine(text)
        start(text, BulletListItem())
    }

    override fun closeItem(text: Editable, currentIndentation: Int, leadingTextContent: String,
                           isTextCentered: Boolean) {
        appendNewLine(text)

        getLast<BulletListItem>(text)?.let { mark ->
            setSpanFromMark(text, mark, TextLeadingMarginSpan(currentIndentation, leadingTextContent, isTextCentered))
        }
    }
}
