package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.UjetMarkdown.findAndReplace

object Numbered {
    private val NESTED_LIST_VERIFICATION_REGEX_V1 = "((\n|^)[0-9]+.\\s+)(.*)".toRegex()
    private val NUMBERED_LIST_REGEX_V1 = "((\n|\\s+|^)[0-9]+\\.\\s+)(.*)".toRegex()

    private const val OPEN = "<${Lists.OL_TAG}>"
    private const val CLOSE = "</${Lists.OL_TAG}>"
    private val HTML_INTERNAL_REGEX = "$OPEN.*?$CLOSE".toRegex()
    private const val HTML_OPEN = "<ol>"
    private const val HTML_CLOSE = "</ol>"
    private const val CAPTURING_GROUP = 3

    internal fun String.parseNumberedLists(): String {
        return findAndReplace(NUMBERED_LIST_REGEX_V1, OPEN, CLOS<PERSON>, CAPTURING_GROUP, true, NESTED_LIST_VERIFICATION_REGEX_V1)
    }

    internal fun String.addWrappingNumberedListHtml(): String {
        return findAndReplace(HTML_INTERNAL_REGEX, HTML_OPEN, HTML_CLOSE, 0)
                .replace(OPEN, Lists.HTML_OPEN)
                .replace(CLOSE, Lists.HTML_CLOSE)
    }

    internal fun removeLineBreaks(input: String): String {
        return input.replace("$CLOSE${Lists.BR_HTML}$OPEN", "$CLOSE$OPEN")
    }
}
