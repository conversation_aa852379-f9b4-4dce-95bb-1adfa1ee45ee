package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.UjetMarkdown.findAndReplace

object Bold {
    private val V1_REGEX = "(\\*\\*)(.+?)(\\*\\*)(?!\\*)".toRegex()
    private val V2_REGEX = "(__)(.+?)(__)".toRegex()
    private const val HTML_OPEN = "<b>"
    private const val HTML_CLOSE = "</b>"
    
    internal fun String.parseBold(): String {
        return findAndReplace(V1_REGEX, HTML_OPEN, HTML_CLOSE)
                .findAndReplace(V2_REGEX, HTML_OPEN, HTML_CLOSE)
    }
}
