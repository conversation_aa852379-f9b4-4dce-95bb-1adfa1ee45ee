package cx.ujet.android.markdown.tags

import android.text.Editable
import android.text.Spannable
import android.text.Spanned
import android.text.style.LeadingMarginSpan
import cx.ujet.android.markdown.UjetMarkdown.DEFAULT_INDENTATION
import cx.ujet.android.markdown.UjetMarkdown.INDENTATION_CHARACTER
import kotlin.math.max

/**
 * <PERSON><PERSON> for <li> tags. Subclasses set the bullet appearance.
 */
abstract class ListTag {
    /**
     * Called when an opening <li> tag is encountered.
     *
     * Inserts an invisible [Mark] span that doesn't do any styling.
     * Instead, [closeItem] will later find the location of this span so it knows where the opening tag was.
     */
    abstract fun openItem(text: Editable, index: Int)

    /**
     * Called when a closing </li> tag is encountered.
     *
     * Pops out the invisible [Mark] span and uses it to get the opening tag location.
     * Then, sets a [LeadingMarginSpan] from the opening tag position to closing tag position.
     */
    abstract fun closeItem(text: Editable, currentIndentation: Int, leadingTextContent: String,
                           isTextCentered: Boolean)

    /**
     * Returns the most recently added span of type [T] in [text].
     *
     * Invisible marking spans are inserted to record the location of opening HTML tags in the text.
     * We do this rather than using a stack in case text is inserted and the relative location shifts around.
     *
     * The last span corresponds to the top of the "stack".
     */
    inline fun <reified T : Mark> getLast(text: Spanned) = text.getSpans(0, text.length, T::class.java).lastOrNull()

    /**
     * Appends a new line to [text] if it doesn't already end in a new line
     */
    fun appendNewLine(text: Editable) {
        if (text.isNotEmpty() && text.last() != '\n') {
            text.append("\n")
        }
    }

    /**
     * Pops out the invisible [mark] span and uses it to get the opening tag location.
     * Then, sets a span from the opening tag position to closing tag position.
     */
    fun setSpanFromMark(text: Spannable, mark: Mark, styleSpan: Any) {
        // Find the location where the mark is inserted in the string.
        val markerLocation = text.getSpanStart(mark)
        // Remove the mark now that the location is saved
        text.removeSpan(mark)

        val end = text.length
        if (markerLocation != end) {
            text.setSpan(styleSpan, markerLocation, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    /**
     * Inserts an invisible [mark] span that doesn't do any styling.
     * Instead, [setSpanFromMark] will later find the location of this span so it knows where the opening tag was.
     */
    fun start(text: Spannable, mark: Mark) {
        val currentPosition = text.length
        text.setSpan(mark, currentPosition, currentPosition, Spanned.SPAN_MARK_MARK)
    }

    fun getUpdatedIndentation(text: Editable, lastItem: Any?): Int {
        val firstCharacterIndex = text.getSpanStart(lastItem)
        val updatedIndentation = if (firstCharacterIndex > 0) {
            // Extract current item text content and get tab spaces count from it
            val currentItemText = text.substring(firstCharacterIndex, text.length)
            max(DEFAULT_INDENTATION, currentItemText.count { it == INDENTATION_CHARACTER })
        } else {
            DEFAULT_INDENTATION
        }

        return updatedIndentation
    }
}
