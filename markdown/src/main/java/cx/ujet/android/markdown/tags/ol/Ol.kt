package cx.ujet.android.markdown.tags.ol

import android.text.Editable
import cx.ujet.android.markdown.tags.ListTag
import cx.ujet.android.markdown.span.TextLeadingMarginSpan

/**
 * Subclass of [ListTag] for ordered lists.
 */
class Ol : ListTag() {

    override fun openItem(text: Editable, index: Int) {
        appendNewLine(text)
        start(text, NumberListItem(index))
    }

    override fun closeItem(text: Editable, currentIndentation: Int, leadingTextContent: String,
                           isTextCentered: Boolean) {
        appendNewLine(text)

        getLast<NumberListItem>(text)?.let { mark ->
            setSpanFromMark(text, mark, TextLeadingMarginSpan(currentIndentation, leadingTextContent, isTextCentered))
        }
    }
}
