package cx.ujet.android.markdown.widgets

import android.content.Context
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.TextView
import cx.ujet.android.markdown.UjetMarkdown
import cx.ujet.android.markdown.UjetMarkdown.INDENTATION_CHARACTER
import cx.ujet.android.markdown.UjetMarkdown.LIST_LEADING_MARKER
import cx.ujet.android.markdown.UjetMarkdown.normalizeNumberWithEscapedDot
import java.util.regex.Matcher
import java.util.regex.Pattern

class MarkdownTextView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    init {
        movementMethod = LinkMovementMethod.getInstance()
    }

    fun setHtml(content: String, defaultText: String, isTextCentered: Boolean = false) {
        val htmlConvertedString = UjetMarkdown.getHtmlConvertedString(content, isTextCentered)
        // htmlConvertedString returns blank string when content string contains unsupported html tags
        // and use default string in that case.
        // If defaultText contains <script>, it will return as default.
        // Allow the Numbers with dot example: ##. without breaking conditions
        val scriptPatternMatcher = getPatternMatcher(defaultText, "<script[^>]*>(.|\\s)*?</script>")
        text = if (scriptPatternMatcher.find()) {
            defaultText
        } else {
            val numberWithDotPatternMatcher = getPatternMatcher(defaultText, "\\d+\\.?")
            // Find number with dot pattern to filter numbered list and ignore string with numbers with dot.
            if (numberWithDotPatternMatcher.find()) {
                // Below pattern only recognizes numbered lists but ignores string with numbers with dot.
                val ignoringNumberWithDotPatternMatcher = getPatternMatcher(defaultText, "(?m)^(\\d+)\\.\\s+")
                if (ignoringNumberWithDotPatternMatcher.find()) {
                    htmlConvertedString.ifBlank {
                        defaultText
                    }
                } else {
                    // Strings with numbers and dot will reach here.
                    defaultText.normalizeNumberWithEscapedDot()
                }
            } else {
                htmlConvertedString.ifBlank {
                    defaultText
                }
            }
        }

        // Adjust text into view when string contains html supported tags only, otherwise ignore it.
        if (htmlConvertedString.isNotEmpty()) {
            adjustTextIntoTextView(htmlConvertedString)
        }
    }

    private fun getPatternMatcher(defaultText: String, regex: String): Matcher {
        return Pattern.compile(regex).matcher(defaultText)
    }
    
    private fun adjustTextIntoTextView(htmlConvertedString: Spanned) {
        /* Sometimes, when text contains list with indentation, text is not fully visible and cut
         * at the end so adjust text width accordingly in that case. Find longest string in htmlConvertedString
         * to accommodate entire text in message bubble. There is an edge case, when input strings are "77\t89",
         * "\t\t\tn5", longest string will be "77\t89" instead of "\t\t\tn5". To handle it, we need to replace
         * tab space with 4 characters so that correct longest string is found and then replace it back with "\t" to find the width.
         */
        val inputString = htmlConvertedString.toString().replace(INDENTATION_CHARACTER.toString(), "    ")
        var longestText = inputString.split("\n").maxByOrNull { it.length } ?: return
        longestText = longestText.replace("    ", INDENTATION_CHARACTER.toString())
        // Find the longest character width in longestText to determine longest text width. When longestText
        // is empty, maxOf can throw exception so to address it, check it only when text is not empty.
        if (longestText.isNotEmpty()) {
            val maxCharWidth = longestText.maxOf { paint.measureText(it.toString()) }
            this.width = getUpdatedWidth(longestText, maxCharWidth)
        }
    }

    private fun getUpdatedWidth(longestText: String, maxCharWidth: Float): Int {
        val indentationCount = longestText.count { it == INDENTATION_CHARACTER }
        val singleCharWidth = paint.measureText(LIST_LEADING_MARKER)
        // apply indentation spaces width when indentation exists otherwise ignore it
        val indentationSpacesWidth = if (indentationCount >= 1) {
            (indentationCount * singleCharWidth * UjetMarkdown.WHITE_SPACES_COUNT_FOR_TAB).toInt()
        } else {
            // We are adding 60dp margin at start and end margin for chat message bubble, so append that
            // additional width when indentation does not exist, to accommodate single level (non-nested) lists.
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 60.toFloat(),
                context.resources.displayMetrics).toInt()
        }
        val longerTextWithoutSpacesLength = longestText.trim().length
        val longerTextWidth = (maxCharWidth * longerTextWithoutSpacesLength).toInt()
        return indentationSpacesWidth + longerTextWidth
    }
}
