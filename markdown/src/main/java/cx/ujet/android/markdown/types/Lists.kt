package cx.ujet.android.markdown.types

object Lists {
    const val HTML_OPEN = "<li>"
    const val HTML_CLOSE = "</li>"
    const val BR_HTML = "<br/>"

    // Custom list tags
    const val OL_TAG = "ordered"
    const val UL_TAG = "unordered"
    const val LI_TAG = "listitem"

    internal fun String.adjustListLineBreaks(): String {
        var result = Bullets.removeLineBreaks(this)
        result = Numbered.removeLineBreaks(result)
        return result
    }

    internal fun String.applyCustomListFormat(): String {
        return this
                .replace("(?i)<ul[^>]*>".toRegex(), "<$UL_TAG>")
                .replace("(?i)</ul>".toRegex(), "</$UL_TAG>")
                .replace("(?i)<ol[^>]*>".toRegex(), "<$OL_TAG>")
                .replace("(?i)</ol>".toRegex(), "</$OL_TAG>")
                .replace("(?i)<li[^>]*>".toRegex(), "<$LI_TAG>")
                .replace("(?i)</li>".toRegex(), "</$LI_TAG>")
    }
}
