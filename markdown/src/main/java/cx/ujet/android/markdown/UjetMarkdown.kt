package cx.ujet.android.markdown

import android.text.Spanned
import android.util.Patterns
import androidx.core.text.HtmlCompat
import cx.ujet.android.markdown.tags.ListTagHandler
import cx.ujet.android.markdown.types.Bold.parseBold
import cx.ujet.android.markdown.types.Bullets.addWrappingBulletListHtml
import cx.ujet.android.markdown.types.Bullets.parseBullets
import cx.ujet.android.markdown.types.Italics.parseItalics
import cx.ujet.android.markdown.types.LineBreak.parseLineBreaks
import cx.ujet.android.markdown.types.Link.parseLinks
import cx.ujet.android.markdown.types.Lists
import cx.ujet.android.markdown.types.Lists.adjustListLineBreaks
import cx.ujet.android.markdown.types.Lists.applyCustomListFormat
import cx.ujet.android.markdown.types.Numbered.addWrappingNumberedListHtml
import cx.ujet.android.markdown.types.Numbered.parseNumberedLists
import cx.ujet.android.markdown.types.Underline.parseUnderlines
import java.util.regex.Pattern

object UjetMarkdown {
    const val LIST_LEADING_MARKER = "1."
    const val INDENTATION_CHARACTER = '\t'
    const val DEFAULT_INDENTATION = 0
    const val ORDERED_LIST_START_INDEX = 1
    const val WHITE_SPACES_COUNT_FOR_TAB = 4

    private const val TEXT_TO_BE_REPLACED = "@#$%^&"
    var centerPositionAdditionalWidth = 0f

    fun parse(markdown: String): String {
        try {
            return markdown
                .trimMarkdownText()
                .parsePhoneNumber()
                .parseRegularNumbers()
                .parseLinks()
                .parseBold()
                .parseNumberedLists()
                .parseUnderlines()
                .parseItalics()
                .parseBullets()

                // Convert line breaks to HTML
                .parseLineBreaks()

                // Adjust line breaks between list items
                .adjustListLineBreaks()

                // Bullets list
                // Add wrapping <ul></ul>
                .addWrappingBulletListHtml()

                // Numbered list
                // Add wrapping <ol></ol> for
                .addWrappingNumberedListHtml()

                // This is to handle numbered lists that aren't supported by Android
                .applyCustomListFormat()
                // This is to replace TEXT_TO_BE_REPLACED with spaces
                .applyPhoneNumberFormat()
        } catch (e: Exception) {
            e.printStackTrace()
            // Fallback to the raw markdown if anything goes wrong
            return markdown
        }
    }

    internal fun String.findAndReplace(
        needle: Regex, openReplacement: String, closeReplacement: String,
        captureGroup: Int = 2, isNumberedOrBulletedList: Boolean = false,
        nestedListVerificationRegex: Regex? = null,
    ): String {
        var updatedCaptureGroup = captureGroup
        return needle.replace(this) {
            // To avoid array index out of bounds exception, use last group item as capture group
            val groupSize = it.groups.size
            if (updatedCaptureGroup >= groupSize) {
                updatedCaptureGroup = groupSize - 1
            }
            var text = it.groups[updatedCaptureGroup]?.value
            val result = "$openReplacement$text$closeReplacement"
            // Check group size again to avoid array index out of bounds exception
            if (groupSize < 2) {
                return@replace result
            }
            val firstGroupContent = it.groups[1]?.value
            text = getTextWithIndentation(text, firstGroupContent, isNumberedOrBulletedList, nestedListVerificationRegex)
            return@replace getTextWithMixedListContent(text, openReplacement, closeReplacement)
        }
    }

    fun getHtmlConvertedString(content: String, isTextCentered: Boolean): Spanned {
        /*If string contains 4 white spaces instead of tab space, then replace them with tab space
        * as our markdown module works with tab space to apply indentation to bullet lists. Below regex
        * filters string content with 4 white spaces and ignore tab spaces.
        */
        val stringWithFourWhiteSpaces = "[^\\S\\t]{${WHITE_SPACES_COUNT_FOR_TAB}}".toRegex()
        val updatedContent = if (content.contains(stringWithFourWhiteSpaces)) {
            content.replace(stringWithFourWhiteSpaces, "\t")
        } else {
            content
        }
        return HtmlCompat.fromHtml(updatedContent, HtmlCompat.FROM_HTML_MODE_LEGACY, null, ListTagHandler(isTextCentered))
    }

    private fun String.trimMarkdownText(): String {
        return this.replace("\\", "") // to remove escaping character
                .replace("  \n", "\n") // remove redundant whitespace before new line
    }

    private fun String.parsePhoneNumber(): String {
        /* When phone number contains spaces (****** 567 8901), it will be filtered as numbered list,
        *  to avoid it, replace spaces with TEXT_TO_BE_REPLACED so that it does not match with numbered
        *  lists regex.
        * */
        val phoneMatcher = Patterns.PHONE.matcher(this)
        var updatedString = this
        while (phoneMatcher.find()) {
            val phoneNumberString = this.substring(phoneMatcher.start(), phoneMatcher.end())
            val replacedPhoneNumberString = phoneNumberString.replace(" ", TEXT_TO_BE_REPLACED)
            updatedString = updatedString.replace(phoneNumberString, replacedPhoneNumberString)
        }
        return updatedString
    }

    private fun String.parseRegularNumbers(): String {
        val pattern = Pattern.compile("^([0-9]+(?:\\s|[^A-Za-z0-9.])+)(?=[^0-9])") // Allow special characters within numbers
        val numbersMatcher = pattern.matcher(this)
        var updatedString = this
        while (numbersMatcher.find()) {
            val numbersString = this.substring(numbersMatcher.start(), numbersMatcher.end())
            var replacedNumbersString = numbersString.replace("\\h".toRegex(), " ")
            replacedNumbersString = replacedNumbersString.replace(" ", TEXT_TO_BE_REPLACED)
            updatedString = if (numbersString == replacedNumbersString) {
                val lineBreakIndex = replacedNumbersString.indexOf("\n")
                val updatedNumbersString = if (lineBreakIndex != -1 && lineBreakIndex < replacedNumbersString.length) {
                    val contentAfterLineBreak = replacedNumbersString.substring(lineBreakIndex)
                    val textWithoutSpaces = replacedNumbersString.substring(0, lineBreakIndex).trim()
                    "$textWithoutSpaces$TEXT_TO_BE_REPLACED$contentAfterLineBreak"
                } else {
                    replacedNumbersString
                }
                updatedString.replace(numbersString, updatedNumbersString)
            } else {
                updatedString.replace(numbersString, replacedNumbersString)
            }
        }
        return updatedString
    }

    private fun String.applyPhoneNumberFormat(): String {
        // once string is parsed, replace back TEXT_TO_BE_REPLACED with spaces.
        return this.replace(TEXT_TO_BE_REPLACED, " ")
    }

    private fun getTextWithIndentation(text: String?, firstGroupContent: String?, isNumberedOrBulletedList: Boolean,
                                       nestedListVerificationRegex: Regex?): String? {
        var textWithIndentation = text
        // First level list items does not contain indentation but other levels will have it,
        // nestedListVerificationRegex matches first level items only and if first group content
        // does not matches with it then items have indentation, otherwise they do not.
        val isListContainsIndentation = firstGroupContent?.let { firstGroup ->
            nestedListVerificationRegex?.let {
                !it.containsMatchIn(firstGroup)
            } ?: run {
                false
            }
        } ?: run {
            false
        }
        if (isNumberedOrBulletedList && isListContainsIndentation) {
            // Extract indentation from first group content by replacing remaining text content
            // with empty string. For example: ordered list first group content will be "\n\t 1."
            // and we extract sub string starting with "\n" index to
            // leading content ("1.") and replace it and "\n" with "" to get indentation value.
            val textWithoutSpaces =
                (firstGroupContent)?.replace("[^a-zA-Z0-9_-]", "")?.trim() ?: ""
            val startIndex = (firstGroupContent)?.indexOf("\n") ?: 0
            val endIndex = (firstGroupContent)?.indexOf(textWithoutSpaces) ?: 0
            val indentation = (firstGroupContent)?.subSequence(startIndex + 1, endIndex)
            textWithIndentation = "$indentation$text"
        }
        
        return textWithIndentation
    }

    private fun getTextWithMixedListContent(text: String?, openReplacement: String, closeReplacement: String): String {
        // Check if text contains mixed order and unordered list, if it does then append closeReplacement
        // before another lists starts. For example, if text is B2<ordered>N3</ordered> and once we append 
        // open and close tags, it will be <unordered>B2<ordered>N3</ordered></unordered>. So instead 
        // we modify text to yield <unordered>B2</unordered><ordered>N3</ordered>.
        val patternToVerifyTextContainsMixedList = when (openReplacement) {
            "<${Lists.UL_TAG}>" -> {
                "<${Lists.OL_TAG}>"
            }
            "<${Lists.OL_TAG}>" -> {
                "<${Lists.UL_TAG}>"
            }
            else -> {
                ""
            }
        }
        text?.indexOf(patternToVerifyTextContainsMixedList)?.let { index ->
            // index will be 0 when text matches with pattern but does not contain mixed list so to ignore that case
            // we are checking for index greater than 0
            if (index > 0) {
                val currentItemContent = text.subSequence(0, index)
                val remainingItemContent = text.subSequence(index, text.length)
                return "$openReplacement$currentItemContent$closeReplacement$remainingItemContent"
            }
        }
        return "$openReplacement$text$closeReplacement"
    }

    fun String.normalizeNumberWithEscapedDot(): String {
        // Only apply logic if the string starts with digits and ends with a backslash followed by a dot
        return this.replace(Regex("""(\d+)(\\+)\.""")) { matchResult ->
            val originalText = matchResult.value
            val number = matchResult.groupValues[1]
            val backslashes = matchResult.groupValues[2]

            // Check if the backslash appears after a number and before a dot
            if (number.isNotEmpty() && backslashes.isNotEmpty()) {
                // Count the backslashes and divide by 2
                val backslashesCount = backslashes.count { it == '\\' }
                val reducedBackslashes = backslashesCount / 2
                // Rebuild the result with reduced backslashes and a dot
                number + "\\".repeat(reducedBackslashes) + "."
            } else {
                originalText // If condition doesn't match, return the original matched value
            }
        }
    }
}
