package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.UjetMarkdown.findAndReplace

object Bullets {
    private val BULLET_LIST_REGEX_V1 = "((\n|\\s+|^)\\* )(.*)".toRegex()
    private val BULLET_LIST_REGEX_V2 = "((\n|\\s+|^)\\- )(.*)".toRegex()
    private val NESTED_LIST_VERIFICATION_REGEX_V1 = "((\n|^)\\* )(.*)".toRegex()
    private val NESTED_LIST_VERIFICATION_REGEX_V2 = "((\n|^)- )(.*)".toRegex()
    private const val OPEN = "<${Lists.UL_TAG}>"
    private const val CLOSE = "</${Lists.UL_TAG}>"
    private val HTML_INTERNAL_REGEX = "$OPEN.*?$CLOSE".toRegex()
    private const val HTML_OPEN = "<ul>"
    private const val HTML_CLOSE = "</ul>"
    private const val CAPTURING_GROUP = 3

    internal fun String.parseBullets(): String {
        return findAndReplace(BULLET_LIST_REGEX_V1, OPEN, CLOSE, CAPTURING_GROUP, true, NESTED_LIST_VERIFICATION_REGEX_V1)
            .findAndReplace(BULLET_LIST_REGEX_V2, OPEN, CLOSE, CAPTURING_GROUP, true, NESTED_LIST_VERIFICATION_REGEX_V2)
    }

    internal fun String.addWrappingBulletListHtml(): String {
        return findAndReplace(HTML_INTERNAL_REGEX, HTML_OPEN, HTML_CLOSE, 0)
                .replace(OPEN, Lists.HTML_OPEN)
                .replace(CLOSE, Lists.HTML_CLOSE)
    }

    internal fun removeLineBreaks(input: String): String {
        return input.replace("$CLOSE${Lists.BR_HTML}$OPEN", "$CLOSE$OPEN")
    }
}
