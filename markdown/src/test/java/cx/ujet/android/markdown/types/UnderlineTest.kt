package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.Underline.parseUnderlines
import org.junit.Assert.assertEquals
import org.junit.Test

class UnderlineTest {
    @Test
    fun `it should parse underlined text with asterisks`() {
        val input = "--this is an underlined text--"
        val result = input.parseUnderlines()
        assertEquals("<u>this is an underlined text</u>", result)
    }

    @Test
    fun `it should parse underlined text with underscores`() {
        val input = "this is an inline --underlined text--"
        val result = input.parseUnderlines()
        assertEquals("this is an inline <u>underlined text</u>", result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v1`() {
        val input = "=this is a wrong formatted bold text--"
        val result = input.parseUnderlines()
        assertEquals(input, result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v2`() {
        val input = "--this is a wrong underlined text_"
        val result = input.parseUnderlines()
        assertEquals(input, result)
    }
}
