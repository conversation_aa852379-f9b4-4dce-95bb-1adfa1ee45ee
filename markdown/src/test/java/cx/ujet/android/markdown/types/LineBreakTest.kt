package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.LineBreak.parseLineBreaks
import org.junit.Assert.assertEquals
import org.junit.Test

class LineBreakTest {
    @Test
    fun `it should parse line break text multi line`() {
        val input = "this is a\nmultiline\ntext"
        val result = input.parseLineBreaks()
        assertEquals("this is a<br/>multiline<br/>text", result)
    }

    @Test
    fun `it should parse line break text single line`() {
        val input = "this is a single line text"
        val result = input.parseLineBreaks()
        assertEquals(input, result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v1`() {
        val input = "this is a wrong formatted/n multi line text"
        val result = input.parseLineBreaks()
        assertEquals(input, result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v2`() {
        val input = "**this is a wrong formatted italics text_"
        val result = input.parseLineBreaks()
        assertEquals(input, result)
    }
}
