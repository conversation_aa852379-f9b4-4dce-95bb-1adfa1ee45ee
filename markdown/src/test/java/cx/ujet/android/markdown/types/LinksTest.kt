package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.Link.parseLinks
import org.junit.Assert.assertEquals
import org.junit.Test

class LinksTest {
    @Test
    fun `it should parse line break text multi line`() {
        val input = "[link text](https://link.url/path)"
        val result = input.parseLinks()
        assertEquals("<a href=\"https://link.url/path\">link text</a>", result)
    }

    @Test
    fun `it should parse line break text single line`() {
        val input = "this is an example text with [a link](https://link.url/path)"
        val result = input.parseLinks()
        assertEquals("this is an example text with <a href=\"https://link.url/path\">a link</a>", result)
    }

    @Test
    fun `it should parse links with underscores`() {
        val input = "this is an example text with [a link with underscores](https://link.url/with_some_underscores_inside/path)"
        val result = input.parseLinks()
        assertEquals("this is an example text with <a href=\"https://link.url/with_some_underscores_inside/path\">a link with underscores</a>", result)
    }

    @Test
    fun `it should parse links with hyphens`() {
        val input = "this is an example text with [a link with hypyens](https://link.url/with-some-hypyens-inside/path)"
        val result = input.parseLinks()
        assertEquals("this is an example text with <a href=\"https://link.url/with-some-hypyens-inside/path\">a link with hypyens</a>", result)
    }

    @Test
    fun `it should parse if the format is not markdown but it's a valid URL inside it - v1`() {
        val input = "this is a wrong formatted markdown link text](https://link.url/path)"
        val result = input.parseLinks()
        assertEquals("this is a wrong formatted markdown link text](<a href=\"https://link.url/path)\">https://link.url/path)</a>", result)
    }

    @Test
    fun `it should parse if the format is not markdown but it's a valid URL inside it - v2`() {
        val input = "**this is a wrong formatted [markdown link text (https://link.url/path)"
        val result = input.parseLinks()
        assertEquals("**this is a wrong formatted [markdown link text (<a href=\"https://link.url/path)\">https://link.url/path)</a>", result)
    }
}
