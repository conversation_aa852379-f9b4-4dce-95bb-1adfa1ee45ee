package cx.ujet.android.markdown

import org.junit.Assert.*
import org.junit.Test

class UjetMarkdownTest {
    
    @Test
    fun `it should convert a well formatted markdown to internal tag system`() {
        val input = "**bold** with _italics_\n" +
                    "\n" +
                    "[click me!](https://www.google.com)\n" +
                    "\n\n" +
                    "* first item\n" +
                    "* second item\n" +
                    "* third element\n" +
                    "\n" +
                    "***italic bold***\n" +
                    "\n\n" +
                    "- another list 1\n" +
                    "- another list 2\n" +
                    "\n\n" +
                    "1. first numbered\n" +
                    "2. second numbered\n" +
                    "\n" +
                    "**this is bold** and --this is underlined-- but this is not"
        val convertedHtml = UjetMarkdown.parse(input)
        val expectedInternalTagSystem = "<b>bold</b> with <i>italics</i><br/>" +
                                        "<br/>" +
                                        "<a href=\"https://www.google.com\">click me!</a><br/>" +
                                        "<br/>" +
                                        "<unordered><listitem>first item</listitem><listitem>second item</listitem><listitem>third element</listitem></unordered><br/>" +
                                        "<br/>" +
                                        "<b><i>italic bold</i></b><br/>" +
                                        "<br/>" +
                                        "<unordered><listitem>another list 1</listitem><listitem>another list 2</listitem></unordered><br/>" +
                                        "<br/>" +
                                        "<ordered><listitem>first numbered</listitem><listitem>second numbered</listitem></ordered><br/>" +
                                        "<br/>" +
                                        "<b>this is bold</b> and <u>this is underlined</u> but this is not"
        assertEquals(expectedInternalTagSystem, convertedHtml)
    }
}
