package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.LineBreak.parseLineBreaks
import cx.ujet.android.markdown.types.Lists.adjustListLineBreaks
import cx.ujet.android.markdown.types.Numbered.addWrappingNumberedListHtml
import cx.ujet.android.markdown.types.Numbered.parseNumberedLists
import org.junit.Assert.assertEquals
import org.junit.Test

class NumberedListTest {
    @Test
    fun `it should convert to internal tag system numbered text with consecutive`() {
        val input = "1. first item\n2. second item"
        val result = input.parseNumberedLists()
        assertEquals("${OPEN}first item${CLOSE}${OPEN}second item${CLOSE}", result)
    }

    @Test
    fun `it should convert to internal tag system numbered text with constant numbers`() {
        val input = "1. first item\n1. second item"
        val result = input.parseNumberedLists()
        assertEquals("${OPEN}first item${CLOSE}${OPEN}second item${CLOSE}", result)
    }
    
    @Test
    fun `it should parse numbered text with random numbers`() {
        val input = "7. first item\n114. second item\n\n"
        val result = input
                .parseNumberedLists()
                .parseLineBreaks()
                .adjustListLineBreaks()
                .addWrappingNumberedListHtml()
        assertEquals("<ol><li>first item</li></ol><ol><li>second item</li></ol><br/><br/>", result)
    }

    @Test
    fun `it should return the original text if the format is not correct`() {
        val input = ". wrong formatted\n. numbered list"
        val result = input.parseNumberedLists()
        assertEquals(input, result)
    }

    @Test
    fun `it should parse nested numbered lists`() {
        val input = "1. level1\n2. level1.1\n\t1. level2\n\t2. level2.1\n\n"
        val result = input
            .parseNumberedLists()
            .parseLineBreaks()
            .adjustListLineBreaks()
            .addWrappingNumberedListHtml()
        assertEquals("<ol><li>level1</li></ol><ol><li>level1.1</li></ol><ol><li>\tlevel2</li></ol><ol><li>\tlevel2.1</li></ol><br/><br/>", result)
    }

    companion object {
        private const val OPEN = "<${Lists.OL_TAG}>"
        private const val CLOSE = "</${Lists.OL_TAG}>"
    }
}
