package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.Italics.parseItalics
import org.junit.Assert.assertEquals
import org.junit.Test

class ItalicsTest {
    @Test
    fun `it should parse italics text with asterisks`() {
        val input = "*this is a bold text*"
        val result = input.parseItalics()
        assertEquals("<i>this is a bold text</i>", result)
    }

    @Test
    fun `it should parse italics text with underscores`() {
        val input = "_this is a bold text_"
        val result = input.parseItalics()
        assertEquals("<i>this is a bold text</i>", result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v1`() {
        val input = "this is a wrong formatted italics text_"
        val result = input.parseItalics()
        assertEquals(input, result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v2`() {
        val input = "=this is a wrong formatted italics text_"
        val result = input.parseItalics()
        assertEquals(input, result)
    }
}
