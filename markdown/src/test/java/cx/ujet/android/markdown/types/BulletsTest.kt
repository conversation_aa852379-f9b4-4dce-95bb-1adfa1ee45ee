package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.Bullets.addWrappingBulletListHtml
import cx.ujet.android.markdown.types.Bullets.parseBullets
import cx.ujet.android.markdown.types.LineBreak.parseLineBreaks
import cx.ujet.android.markdown.types.Lists.adjustListLineBreaks
import org.junit.Assert.assertEquals
import org.junit.Test

class BulletsTest {

    @Test
    fun `it should convert to internal tag system bullets text with asterisks`() {
        val input = "* first item\n* second item"
        val result = input.parseBullets()
        assertEquals("${OPEN}first item${CLOSE}${OPEN}second item${CLOSE}", result)
    }

    @Test
    fun `it should convert to internal tag system bullets text with hyphen`() {
        val input = "- first item\n- second item"
        val result = input.parseBullets()
        assertEquals("${OPEN}first item${CLOSE}${OPEN}second item${CLOSE}", result)
    }

    @Test
    fun `it should parse bullets text with asterisks`() {
        val input = "* first item\n* second item"
        val result = input
                .parseBullets()
                .parseLineBreaks()
                .adjustListLineBreaks()
                .addWrappingBulletListHtml()
        assertEquals("<ul><li>first item</li><li>second item</li></ul>", result)
    }

    @Test
    fun `it should parse bullets text with hyphen`() {
        val input = "- first item\n- second item"
        val result = input
                .parseBullets()
                .parseLineBreaks()
                .adjustListLineBreaks()
                .addWrappingBulletListHtml()
        assertEquals("<ul><li>first item</li><li>second item</li></ul>", result)
    }

    @Test
    fun `it should return the original text if the format is not correct`() {
        val input = ". wrong formatted\n. bullets list"
        val result = input.parseBullets()
        assertEquals(input, result)
    }

    @Test
    fun `it should parse nested bullet lists`() {
        val input = "* level1\n* level1.1\n\t* level2\n\t* level2.1\n\n"
        val result = input
            .parseBullets()
            .parseLineBreaks()
            .adjustListLineBreaks()
            .addWrappingBulletListHtml()
        assertEquals("<ul><li>level1</li><li>level1.1</li><li>\tlevel2</li><li>\tlevel2.1</li></ul><br/><br/>", result)
    }

    @Test
    fun `it should parse nested bullet lists with -`() {
        val input = "- level1\n- level1.1\n\t- level2\n\t- level2.1\n\n"
        val result = input
            .parseBullets()
            .parseLineBreaks()
            .adjustListLineBreaks()
            .addWrappingBulletListHtml()
        assertEquals("<ul><li>level1</li><li>level1.1</li><li>\tlevel2</li><li>\tlevel2.1</li></ul><br/><br/>", result)
    }

    @Test
    fun `it should parse bullet lists with numbered list`() {
        val input = "* first item\n* second item\n* third element\n\n1. first numbered item\n2. second numbered item\n3. third numbered item\n\n"
        val result = input
            .parseBullets()
            .parseLineBreaks()
            .adjustListLineBreaks()
            .addWrappingBulletListHtml()
        assertEquals("<ul><li>first item</li><li>second item</li><li>third element</li></ul><br/><br/>1. first numbered item<br/>2. second numbered item<br/>3. third numbered item<br/><br/>", result)
    }
    
    companion object {
        private const val OPEN = "<${Lists.UL_TAG}>"
        private const val CLOSE = "</${Lists.UL_TAG}>"
    }
}
