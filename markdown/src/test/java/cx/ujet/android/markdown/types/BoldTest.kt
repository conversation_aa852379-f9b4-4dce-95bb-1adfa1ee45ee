package cx.ujet.android.markdown.types

import cx.ujet.android.markdown.types.Bold.parseBold
import org.junit.Assert.assertEquals
import org.junit.Test

class BoldTest {
    @Test
    fun `it should parse bold text with asterisks`() {
        val input = "**this is a bold text**"
        val result = input.parseBold()
        assertEquals("<b>this is a bold text</b>", result)
    }

    @Test
    fun `it should parse bold text with underscores`() {
        val input = "__this is a bold text__"
        val result = input.parseBold()
        assertEquals("<b>this is a bold text</b>", result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v1`() {
        val input = "_this is a wrong formatted bold text__"
        val result = input.parseBold()
        assertEquals(input, result)
    }

    @Test
    fun `it should return the original text if the format is not correct - v2`() {
        val input = "__this is a wrong formatted bold text_"
        val result = input.parseBold()
        assertEquals(input, result)
    }
}
