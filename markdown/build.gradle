plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace = "cx.ujet.android.markdown"
    defaultConfig {
        buildToolsVersion = rootProject.ext.buildToolsVersion
        compileSdk rootProject.ext.compileSdkVersion
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    api "androidx.core:core-ktx:$androidXCoreKtxVersion"
    testImplementation "junit:junit:$junitVersion"
}

apply from: 'publish.gradle'
