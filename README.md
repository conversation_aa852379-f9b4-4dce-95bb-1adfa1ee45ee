# Setup

* Install fastlane for distribution.
```bash
bundle install
```

# Run the Test App

To build the project, you need to have the Android Studio installed. To do this, follow these steps:
1. Download and install [Android Studio](https://developer.android.com/studio).
2. Open Android Studio and import the project by selecting the `build.gradle` file in the root directory of the project.
3. Wait for Android Studio to sync the project and download the necessary dependencies.
4. Once the project is synced, you can add an emulator or connect a physical device to run the app.
   - If you want to use an emulator, you can create one by going to `Tools` > `AVD Manager` and following the instructions to set up a virtual device.
   - If you want to use a physical device, make sure USB debugging is enabled on your device and it is connected to your computer.
5. Now, you can run the project by clicking on the `Run` button in Android Studio and selecting `testApp`.

# Configuring the Test App

You can configure the test app by following https://ujetcs.atlassian.net/wiki/x/KYDfpg.

# Push notifications

To enable push notifications, you need to ensure the service account key and app identifier are set up correctly
in the portal under `Settings > Developer Settings > Mobile Apps`. You can refer to https://ujetcs.atlassian.net/wiki/x/N4CNEw to get them.

# Distribution

App distribution is automated using CI and fastlane, leveraging [firebase with fastlane](https://firebase.google.com/docs/app-distribution/android/distribute-fastlane).
It generates the apps for QA, RC, and Production environments and uploads them to Firebase App Distribution.
To distribute the app, you can use the following fastlane commands:

```bash
bundle exec fastlane firebase_qa # For QA distribution
bundle exec fastlane firebase_rc # For RC distribution
bundle exec fastlane firebase_production # For Production distribution
```

## Distribute with custom builds (optional modules)
* Create a new commit to the `custom_build`  branch specifying the modules to be included
* The commit message should be of the form `ujetModule1^excludeGroup1;excludeGroup2,ujetModule2`
For instance, if you want to include `ujet`, `markdown` but not `cobrowse` you can do
```bash
# on branch custom_build
git commit --allow-empty -m "ujet,markdown" # note that cobrowse is not in the modules to be added
git push origin custom_build
```

You can also use the `custom_build.sh` script for this. The script will always include `ujet` as a dependency 🙂
For instance, if you want to include `ujet`, `markdown` but not `cobrowse` you can do
```bash
./custom_build.sh markdown
```

Distribute with fastlane. `bundle exec fastlane` `beta_qa` or `fastlane staging` or you can do it all at once by `fastlane _beta_all`

# Publish SDK to public repository.

When you push to the `RC` or `production` branch, the CI pipeline will automatically publish the new SDK.

1. Ensure the SDK's version is up to date in `UjetVersion.kt`.
2. Execute the following gradle tasks to publish to local maven repository.

 ```bash
./gradlew :ujet:clean
./gradlew :ujet:assembleRelease

# publishing the artifacts
./gradlew publish -PbucketName=${bucket_name} -Paws.s3.access=${access_key} -Paws.s3.secret=${secret_key}
```

# Release process

We're using Github Actions to release the app and SDKs. Go to [release.yml](https://github.com/UJET/ujet-android-sdk/actions/workflows/release.yml) and select `Run workflow` dropdown menu.
More information about the release process can be found in the [build documentation](https://ujetcs.atlassian.net/wiki/x/CgDWig) 
and [release documentation](https://ujetcs.atlassian.net/wiki/x/EIBzdw).

# Shipment process

We're using Github Actions to release the latest ship-ready SDK along with example app in the `ujet-android-example` repository.
Go to [upload_to_drive.yml](https://github.com/UJET/ujet-android-sdk-example/actions/workflows/upload_to_drive.yml) and select `Run workflow` dropdown menu.
It will upload to [here](https://drive.google.com/open?id=0B0K-Kcvw-AaSV0NXY1JoRWtBLWM).

## Install the example app

You can download the latest example app from the [link](https://externals.ujet.co/packages/ujet-android-sdk-example.zip) 
and follow the [instructions](https://ujetcs.atlassian.net/wiki/x/5anWAg) to configure it.

# How to add new languages 

To add new languages to the SDK, you can refer to the [Localization documentation](https://ujetcs.atlassian.net/wiki/x/cwCKdw).

# Code style

* http://google.github.io/styleguide/javaguide.html
