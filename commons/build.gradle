plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace = "co.ujet.android.commons"
    defaultConfig {
        buildToolsVersion = rootProject.ext.buildToolsVersion
        compileSdk rootProject.ext.compileSdkVersion
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
        buildConfig = true

    }
}

dependencies {
    implementation project(":module_manager")
    api "com.google.android.material:material:$googleMaterialVersion"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutinesVersion"
}

apply from: 'publish.gradle'
