package co.ujet.android.commons.domain

import android.webkit.MimeTypeMap
import android.webkit.URLUtil
import co.ujet.android.commons.domain.MediaFile.Type
import co.ujet.android.commons.libs.uson.SerializedName
import java.lang.IllegalArgumentException
import java.util.Locale

data class Document(
    @SerializedName("text")
    val text: String?,

    @SerializedName("file_name")
    private val fileName: String?,

    @SerializedName("type")
    val type: String?,

    @SerializedName("url")
    val url: String
) {
    constructor() : this(null, null, null, "")

    // Used for virtual and end user agent messages only
    fun getFileType(): Type {
        return if (type == null) {
            when (MimeTypeMap.getFileExtensionFromUrl(url).toLowerCase(Locale.getDefault())) {
                "mp3" -> Type.Audio
                "doc", "docx" -> Type.Doc
                "xls", "xlsx" -> Type.Excel
                "pdf" -> Type.PDF
                "ppt", "pptx" -> Type.PPT
                else -> Type.Generic
            }
        } else {
            try {
                Type.valueOf(type)
            } catch (e: IllegalArgumentException) {
                Type.Generic
            }
        }
    }

    fun getFileName(): String = fileName ?: URLUtil.guessFileName(url, null, null)
}
