package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.VirtualAgent
import java.util.Date

class VirtualAgentVideoChatMessage private constructor(
    localId: Int,
    sid: String,
    timestamp: Date,
    uploadMedia: MediaFile,
    private val virtualAgent: VirtualAgent?,
    messageIndex: Long
) : DocumentChatMessage(localId, sid, timestamp, uploadMedia, messageIndex) {

    fun getAgentAvatarUrl() = virtualAgent?.avatarUrl

    var agentName = virtualAgent?.displayName

    override fun getSenderAgent() = virtualAgent

    companion object {
        fun createSent(
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            virtualAgent: VirtualAgent?,
            messageIndex: Long
        ): VirtualAgentVideoChatMessage {
            return VirtualAgentVideoChatMessage(
                0,
                sid,
                timestamp,
                mediaFile,
                virtualAgent,
                messageIndex
            )
        }
    }
}
