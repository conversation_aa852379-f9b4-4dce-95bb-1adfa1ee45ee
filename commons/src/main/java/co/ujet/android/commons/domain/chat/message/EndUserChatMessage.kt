package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import org.json.JSONException
import org.json.JSONObject
import java.util.*

class EndUserChatMessage : SendableChatMessage {
    var message: String
        private set

    private constructor(@IntRange(from = 1) localId: Int,
                        timestamp: Date,
                        message: String) : super(localId, timestamp) {
        this.message = message
    }

    private constructor(@IntRange(from = 1) localId: Int,
                        sid: String,
                        timestamp: Date,
                        message: String,
                        messageIndex: Long) : super(localId, sid, timestamp, messageIndex) {
        this.message = message
    }

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "text")
                put("local_id", localId)
                put("content", message)
            } catch (e: JSONException) {
                logger.w(e, "failed convert end user message to json")
            }
        }
    }

    override fun messageByEndUser() = true

    companion object {
        @JvmStatic
        fun createPending(localId: Int,
                          timestamp: Date,
                          message: String): EndUserChatMessage {
            return EndUserChatMessage(localId, timestamp, message)
        }

        @JvmStatic
        fun createSent(localId: Int,
                       sid: String,
                       timestamp: Date,
                       message: String,
                       messageIndex: Long): EndUserChatMessage {
            return EndUserChatMessage(localId, sid, timestamp, message, messageIndex)
        }
    }
}
