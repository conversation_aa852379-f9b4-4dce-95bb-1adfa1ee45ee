package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.agent.VirtualAgent
import java.util.*

sealed class VirtualAgentQuickReplyChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    body: String?,
    val quickReplyButtons: List<QuickReplyButton>,
    private val virtualAgent: VirtualAgent?,
    messageIndex: Long
) : VirtualAgentChatMessage(localId, sid, timestamp, body, virtualAgent, messageIndex) {
    /**
     * Flag indicating if quick reply buttons should be displayed
     */
    var quickReplyButtonsVisible = true

    override fun getSenderAgent() = virtualAgent
}

class VirtualAgentQuickReplyListChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    body: String?,
    quickReplyButtons: List<QuickReplyButton>,
    virtualAgent: VirtualAgent?,
    messageIndex: Long
) : VirtualAgentQuickReplyChatMessage(localId, sid, timestamp, body, quickReplyButtons, virtualAgent, messageIndex)

class VirtualAgentQuickReplyButtonsChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    body: String?,
    quickReplyButtons: List<QuickReplyButton>,
    virtualAgent: VirtualAgent?,
    messageIndex: Long
) : VirtualAgentQuickReplyChatMessage(localId, sid, timestamp, body, quickReplyButtons, virtualAgent, messageIndex)
