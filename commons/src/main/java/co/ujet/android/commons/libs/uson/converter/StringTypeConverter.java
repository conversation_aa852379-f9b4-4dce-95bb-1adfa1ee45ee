package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONStringer;

public class StringTypeConverter implements TypeConverter<String> {
    @Override
    public String convert(Object value) {
        if (JSONObject.NULL.equals(value))
            return null;

        String str = String.valueOf(value);
        if ("null".equals(str))
            return null;

        return str;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null) {
                stringer.value(JSONObject.NULL);

            } else if (value instanceof String) {
                stringer.value(value);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
