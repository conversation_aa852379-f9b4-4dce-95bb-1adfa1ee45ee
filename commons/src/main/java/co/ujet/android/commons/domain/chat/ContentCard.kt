package co.ujet.android.commons.domain.chat

import co.ujet.android.commons.libs.uson.SerializedName
import java.io.Serializable
import java.util.HashMap

data class ContentCard(
    @SerializedName("title")
    val title: String? = null,

    @SerializedName("subtitle")
    val subtitle: String? = null,

    @SerializedName("body")
    val body: String? = null,

    @SerializedName("images")
    val images: List<String>? = null,

    @SerializedName("buttons")
    val buttons: List<ContentCardButton>? = null,

    @SerializedName("link")
    val link: String? = null,

    @SerializedName("event_params")
    val eventParams: HashMap<String, Any>? = null
) : Serializable
