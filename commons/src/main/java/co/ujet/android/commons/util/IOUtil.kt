package co.ujet.android.commons.util

import java.io.BufferedReader
import java.io.Closeable
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader

object IOUtil {
    @JvmStatic
    fun closeQuietly(closeable: Closeable?) {
        try {
            closeable?.close()
        } catch (ioe: IOException) {
            // ignore
        }
    }

    @JvmStatic
    fun readString(inputStream: InputStream?): String? {
        if (inputStream == null) return null
        val sb = StringBuilder()
        val reader = BufferedReader(InputStreamReader(inputStream))
        try {
            while (true) {
                val line = reader.readLine() ?: break
                sb.append(line).append("\n")
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            closeQuietly(reader)
        }
        return sb.toString()
    }
}
