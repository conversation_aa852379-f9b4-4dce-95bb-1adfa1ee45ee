package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class BooleanTypeConverter implements TypeConverter<Boolean> {
    private Boolean defaultValue;

    public BooleanTypeConverter(Boolean defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Boolean convert(Object value) {
        if (value instanceof Boolean)
            return (Boolean) value;

        if (value instanceof String) {
            try {
                return Boolean.valueOf((String) value);
            } catch (NumberFormatException ignore) {
            }
        }

        return defaultValue;
    }

    @Override
    public void toJson(Object value, J<PERSON><PERSON>tringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof String) {
                stringer.value(Boolean.valueOf((String) value));

            } else if (value instanceof Boolean) {
                stringer.value(value);

            } else if (value.getClass() == boolean.class) {
                stringer.value((boolean) value);

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
