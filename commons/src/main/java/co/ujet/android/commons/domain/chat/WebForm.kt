package co.ujet.android.commons.domain.chat

import co.ujet.android.commons.libs.uson.SerializedName
import java.io.Serializable

data class WebForm(
    @SerializedName("name")
    val name: String? = null,

    @SerializedName("title")
    val title: String? = null,

    @SerializedName("subtitle")
    val subtitle: String? = null,

    @SerializedName("image")
    val image: String? = null,

    @SerializedName("external_form_id")
    val externalFormId: String? = null,

    @SerializedName("smart_action_id")
    val smartActionId: Int? = null
) : Serializable {
    var status = ""
    var signature: String? = null
    var isCompleted = false
}
