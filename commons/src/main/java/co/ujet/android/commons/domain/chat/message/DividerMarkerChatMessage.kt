package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class DividerMarkerChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    private val message: String?,
    messageIndex: Long
) : ChatMessage(localId, sid, timestamp, messageIndex) {
    fun getMessage() = message ?: "null"
}
