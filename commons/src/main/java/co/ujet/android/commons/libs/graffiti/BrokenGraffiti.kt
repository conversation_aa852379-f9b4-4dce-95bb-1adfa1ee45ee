package co.ujet.android.commons.libs.graffiti

import android.net.Uri
import android.widget.ImageView

/**
 * When Activity is finished and try to use Graffiti, Graffiti doesn't work and cause a crash.
 * For preventing a crash, return BrokenGraffiti in [Graffiti.with].
 */
class BrokenGraffiti : Graffiti() {
    override fun from(path: String?) = this

    override fun from(uri: Uri?) = this

    override fun fallback(resId: Int) = this

    override fun resize(width: Int, height: Int) = this

    override fun listener(listener: BitmapListener?) = this

    override fun memCache(use: Boolean) = this

    override fun diskCache(use: <PERSON>olean) = this

    override fun radius(radius: Int) = this

    override fun into(imageView: ImageView) {}
    override fun get(listener: BitmapListener) {}
}
