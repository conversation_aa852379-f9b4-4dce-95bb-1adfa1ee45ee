package co.ujet.android.commons.domain.chat.message.base

import androidx.annotation.IntRange
import androidx.annotation.Keep
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.chat.message.ChatMessageStatus
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.entrypoints.log.Logger
import java.util.Date

abstract class ChatMessage : Comparable<ChatMessage> {
    var localId = 0
    var sid: String? = null
    var messageIndex: Long? = null

    // When a message belongs to a list of messages (text + image, text + document, etc) they'll be numbered as
    // x.0, x.1, x.2 where x is the message index of the original message that originated the list
    var messageSubIndex: Double? = null

    // The timestamp if the message from <PERSON><PERSON><PERSON> or the message creation time of the
    // local device time when there's no remote time
    private var timestamp: Date

    // The message creation time of the local device. Only set for locally created messages
    var localTimestamp: Date? = null
    var messageStatus = ChatMessageStatus.Sending
    protected val logger = EntryPointFactory.provideEntryPoint(Logger::class.java)

    var groupMessageId: Int? = null

    @Keep
    constructor() {
        timestamp = Date()
    }

    constructor(@IntRange(from = 1) localId: Int,
                sid: String,
                timestamp: Date,
                messageIndex: Long,
                groupMessageId: Int?) {
        this.localId = localId
        this.timestamp = timestamp
        this.sid = sid
        this.messageIndex = messageIndex
        this.groupMessageId = groupMessageId
    }

    constructor(@IntRange(from = 1) localId: Int,
                sid: String,
                timestamp: Date,
                messageIndex: Long) {
        this.localId = localId
        this.timestamp = timestamp
        this.sid = sid
        this.messageIndex = messageIndex
    }

    constructor(@IntRange(from = 1) localId: Int,
                localTimestamp: Date) {
        this.localId = localId
        this.localTimestamp = localTimestamp
        timestamp = localTimestamp
        this.messageIndex = localTimestamp.time
    }

    /**
     * @return the local message creation timestamp for locally generated messages and the remote
     * timestamp for messages received from the chat provider
     */
    fun getTimestamp() : Date {
        return localTimestamp ?: timestamp
    }

    fun setTimestamp(timestamp: Date) {
        this.timestamp = timestamp
    }

    override fun compareTo(other: ChatMessage): Int {
        // Messages without a messageIndex are sorted after messages with an index. If both messages
        // have (or don't have) a messageIndex, then the timestamp of them is compared
        return when {
            messageIndex != null && other.messageIndex == null -> {
                -1
            }
            messageIndex == null -> {
                1
            }
            // in case of human agent attachments, we need to sort them by the messageSubIndex instead of timestamp.
            groupMessageId != null && groupMessageId == other.groupMessageId -> {
                messageSubIndex?.compareTo(other.messageSubIndex ?: 0.0) ?: 0
            }
            else -> {
                timestamp.compareTo(other.timestamp)
            }
        }
    }

    // To be implemented in subclasses
    open fun getSenderAgent(): Agent? {
        return null
    }

    // To be implemented in subclasses
    open fun messageByEndUser() = false

    fun assignSubIndex(index: Int) {
        messageSubIndex = messageIndex?.toDouble()?.plus(index / 10.0)
    }
}
