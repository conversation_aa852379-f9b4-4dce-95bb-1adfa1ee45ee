package co.ujet.android.commons.libs.graffiti

import android.graphics.Bitmap
import co.ujet.android.commons.libs.graffiti.cache.BitmapMemCache
import co.ujet.android.commons.util.MainLooper
import java.io.File
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.Executors

internal object Worker {
    private val jobs: Queue<Job> = LinkedList()
    private val processors = Runtime.getRuntime().availableProcessors()
    private val executorService = Executors.newFixedThreadPool(
        if (processors > 1) {
            processors / 2
        } else {
            1
        }
    )

    fun enqueue(job: Job) {
        synchronized(jobs) {
            jobs.add(job)
            doNext()
        }
    }

    private operator fun next() {
        synchronized(jobs) {
            doNext()
        }
    }

    private fun doNext() {
        if (jobs.isNotEmpty()) {
            val job = jobs.poll() ?: return
            val bitmap = BitmapMemCache.getCache(job.cacheKey ?: return)
            if (bitmap != null) {
                MainLooper.post {
                    job.callback?.onReady(bitmap)
                    next()
                }
            } else {
                executorService.execute(DoJob(job))
            }
        }
    }

    private class DoJob(private val job: Job) : Runnable {
        override fun run() {
            try {
                var bitmap = BitmapMemCache.getCache(job.cacheKey ?: return)
                if (bitmap != null) {
                    notifyOnReady(bitmap)
                    return
                }
                val decoder = job.decoder
                if (decoder == null) {
                    notifyOnFailed()
                    return
                }
                var file: File? = null
                for (loader in job.imageLoaders) {
                    file = loader.load()
                    if (file != null) {
                        break
                    }
                }
                if (file == null) {
                    notifyOnFailed()
                    return
                }
                bitmap = job.decoder?.decode(file)
                if (bitmap == null) {
                    notifyOnFailed()
                    return
                }
                job.decoder?.getRoundedCornerBitmap(bitmap, job.radius)?.let {
                    bitmap = it
                    notifyOnReady(it)
                }
                job.bitmapCaches.forEach { cache ->
                    // Ignore exception during caching
                    try {
                        cache.cache(bitmap ?: return@forEach)
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                }
            } catch (ex: Exception) {
                notifyOnFailed()
            } finally {
                next()
            }
        }

        private fun notifyOnReady(bitmap: Bitmap) {
            MainLooper.post {
                job.callback?.onReady(bitmap)
                next()
            }
        }

        private fun notifyOnFailed() {
            MainLooper.post {
                job.callback?.onFailed()
                next()
            }
        }
    }
}
