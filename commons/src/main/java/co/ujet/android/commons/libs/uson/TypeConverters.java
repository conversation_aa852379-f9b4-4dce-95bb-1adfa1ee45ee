package co.ujet.android.commons.libs.uson;

import androidx.annotation.NonNull;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import co.ujet.android.commons.libs.uson.converter.ArrayTypeConverter;
import co.ujet.android.commons.libs.uson.converter.BooleanTypeConverter;
import co.ujet.android.commons.libs.uson.converter.CharacterTypeConverter;
import co.ujet.android.commons.libs.uson.converter.CustomTypeConverter;
import co.ujet.android.commons.libs.uson.converter.DateTypeConverter;
import co.ujet.android.commons.libs.uson.converter.DoubleTypeConverter;
import co.ujet.android.commons.libs.uson.converter.EnumTypeConverter;
import co.ujet.android.commons.libs.uson.converter.FloatTypeConverter;
import co.ujet.android.commons.libs.uson.converter.IntegerTypeConverter;
import co.ujet.android.commons.libs.uson.converter.ListTypeConverter;
import co.ujet.android.commons.libs.uson.converter.LongTypeConverter;
import co.ujet.android.commons.libs.uson.converter.MapTypeConverter;
import co.ujet.android.commons.libs.uson.converter.ObjectTypeConverter;
import co.ujet.android.commons.libs.uson.converter.StringTypeConverter;
import co.ujet.android.commons.libs.uson.converter.TypeConverter;

public class TypeConverters {
    private ClassMetadataCache classMetadataCache;
    private HashMap<Class<?>, TypeConverter> converters;

    public TypeConverters(ClassMetadataCache classMetadataCache) {
        this.classMetadataCache = classMetadataCache;

        converters = new HashMap<>();
        converters.put(Object.class, new ObjectTypeConverter());

        converters.put(int.class, new IntegerTypeConverter(0));
        converters.put(long.class, new LongTypeConverter(0L));
        converters.put(float.class, new FloatTypeConverter(0.f));
        converters.put(double.class, new DoubleTypeConverter(0.d));
        converters.put(boolean.class, new BooleanTypeConverter(false));

        converters.put(Integer.class, new IntegerTypeConverter(null));
        converters.put(Long.class, new LongTypeConverter(null));
        converters.put(Float.class, new FloatTypeConverter(null));
        converters.put(Double.class, new DoubleTypeConverter(null));
        converters.put(Boolean.class, new BooleanTypeConverter(null));

        converters.put(char.class, new CharacterTypeConverter('\u0000'));
        converters.put(Character.class, new CharacterTypeConverter(null));

        converters.put(String.class, new StringTypeConverter());

        converters.put(Date.class, new DateTypeConverter());
    }

    @NonNull
    public <T> TypeConverter<T> getConverter(Class<T> rawType) {
        return getConverter(new TypeToken<T>(rawType));
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public <T> TypeConverter<T> getConverter(TypeToken<T> typeToken) {
        if (converters.containsKey(typeToken.getRawType()))
            return converters.get(typeToken.getRawType());

        if (List.class.isAssignableFrom(typeToken.getRawType()))
            return new ListTypeConverter(typeToken, this);

        if (Enum.class.isAssignableFrom(typeToken.getRawType()))
            return new EnumTypeConverter(typeToken);

        if (typeToken.getRawType().isArray())
            return new ArrayTypeConverter(typeToken, this);

        if (Map.class.isAssignableFrom(typeToken.getRawType()))
            return new MapTypeConverter(typeToken, this);

        return new CustomTypeConverter(typeToken, this, classMetadataCache);
    }
}
