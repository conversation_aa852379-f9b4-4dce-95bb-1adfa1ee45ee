package co.ujet.android.commons.domain.chat

import co.ujet.android.commons.libs.uson.SerializedName
import java.io.Serializable
import java.util.HashMap

data class ContentCardButton(
    @SerializedName("title")
    val title: String? = null,

    @SerializedName("style")
    val style: String? = null,

    @SerializedName("auto_reply")
    val autoReply: Boolean? = null,

    @SerializedName("link")
    val link: String? = null,

    @SerializedName("event_params")
    val eventParams: HashMap<String, Any>? = null
) : Serializable
