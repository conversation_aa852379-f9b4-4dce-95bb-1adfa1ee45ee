package co.ujet.android.commons.domain.chat.message

sealed class QuickReplyButton(val title: String, val link: String? = null, val eventParams: HashMap<String, Any>? = null) {

    companion object {

        @JvmStatic
        fun fromChatButtonMessage(chatButtonMessage: ChatButtonMessage): QuickReplyButton? {
            return when (chatButtonMessage.action) {
                "quick_reply" -> chatButtonMessage.title?.let { QuickReplyTextButton(it) }
                "escalation" -> chatButtonMessage.title?.let { EscalationButton(it) }
                "quick_reply_link" -> {
                    chatButtonMessage.title?.let { title ->
                        chatButtonMessage.link?.let { link ->
                            QuickReplyLinkButton(
                                title, link,
                                chatButtonMessage.eventParams
                            )
                        }
                    }
                }

                else -> null
            }
        }
    }
}

class QuickReplyLinkButton(title: String, link: String, eventParams: HashMap<String, Any>?) : QuickReply<PERSON>utton(title, link, eventParams)
class QuickReplyTextButton(title: String) : QuickReplyButton(title)
class EscalationButton(title: String) : QuickReplyButton(title)
class DeflectToEmailButton(
    title: String,
    val deflectionType: String,
    val menuPath: String?,
    val escalationId: Int
) : QuickReplyButton(title)

class DeflectToVirtualAgentButton(title: String, val escalationId: Int) : QuickReplyButton(title)

class DeflectToEDLButton(
    title: String,
    val deflectionType: String,
    val url: String,
) : QuickReplyButton(title)
