package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.*

class ChatEndedMessage : ChatMessage {
    val message: String
    val isTimeout: Boolean
    val hideEndChatButtonView: Boolean
    var isDownloadTranscriptButtonVisible = true

    constructor(localId: Int,
                timestamp: Date,
                timeout: Boolean,
                message: String,
                hideEndChatButtonView: Boolean) : super(localId, timestamp) {
        isTimeout = timeout
        this.message = message
        this.hideEndChatButtonView = hideEndChatButtonView
    }

    constructor(localId: Int,
                sid: String,
                timestamp: Date,
                timeout: Boolean,
                message: String,
                messageIndex: Long,
                hideEndChatButtonView: Boolean) : super(localId, sid, timestamp, messageIndex) {
        isTimeout = timeout
        this.message = message
        this.hideEndChatButtonView = hideEndChatButtonView
    }
}
