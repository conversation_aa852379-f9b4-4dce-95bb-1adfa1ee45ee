package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONStringer;

import java.util.List;

import co.ujet.android.commons.libs.uson.TypeConverters;
import co.ujet.android.commons.libs.uson.TypeToken;
import co.ujet.android.commons.libs.uson.UsonUtil;

public class ListTypeConverter<T extends List> implements TypeConverter<T> {
    private TypeToken<T> typeToken;
    private TypeConverters typeConverters;

    public ListTypeConverter(TypeToken<T> typeToken, TypeConverters typeConverters) {
        this.typeToken = typeToken;
        this.typeConverters = typeConverters;
    }

    @Override
    public T convert(Object value) {
        if (value instanceof List)
            return (T) value;

        JSONArray jsonArray = UsonUtil.toJsonArray(value);
        if (jsonArray == null)
            return null;

        T list = UsonUtil.newInstance(typeToken);
        if (list == null)
            return null;

        Class<?> componentType = UsonUtil.getListComponentRawType(typeToken);
        TypeToken componentTypeToken = new TypeToken(componentType);

        for (int index = 0; index < jsonArray.length(); ++index) {
            Object element = jsonArray.opt(index);

            TypeConverter typeConverter = typeConverters.getConverter(componentTypeToken);
            list.add(typeConverter.convert(element));
        }

        return list;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null || !typeToken.getRawType().isInstance(value)) {
                stringer.value(null);

            } else {
                Class componentRawType = UsonUtil.getListComponentRawType(typeToken);
                TypeToken componentTypeToken = new TypeToken(componentRawType);

                stringer.array();
                for (Object element : ((List) value)) {
                    typeConverters.getConverter(componentTypeToken)
                            .toJson(element, stringer);
                }
                stringer.endArray();
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
