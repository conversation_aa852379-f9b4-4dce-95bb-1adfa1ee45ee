package co.ujet.android.commons.util

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

object TimeUtil {
    private val ujetDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale.ENGLISH)
    private val dateFormats = arrayOf(
        SimpleDateFormat("y-M-d H:m Z", Locale.ENGLISH),  // Ujet
        SimpleDateFormat("y-M-d'T'H:m:s.S'Z'", Locale.ENGLISH),  // Ujet
        SimpleDateFormat("y-M-d'T'H:m:s'Z'", Locale.ENGLISH),  // Twilio
        ujetDateFormat
    )

    fun parseTime(timeString: String?): Date? {
        for (format in dateFormats) {
            tryParse(format, timeString)?.let {
                return it
            }
        }
        return null
    }

    private fun tryParse(dateFormat: SimpleDateFormat, timeString: String?): Date? {
        return try {
            dateFormat.timeZone = TimeZone.getTimeZone("UTC")
            dateFormat.parse(timeString ?: return null)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Format date to 2016-10-10T19:47:49.788-07:00
     */
    fun getUjetServerFormat(time: Date?): String? {
        ujetDateFormat.timeZone = TimeZone.getTimeZone("UTC")
        return ujetDateFormat.format(time ?: return null)
    }
}
