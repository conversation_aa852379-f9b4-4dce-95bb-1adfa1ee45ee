package co.ujet.android.commons.libs.graffiti

import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.net.Uri
import android.widget.ImageView
import androidx.core.content.res.ResourcesCompat
import co.ujet.android.commons.libs.graffiti.cache.BitmapCache
import co.ujet.android.commons.libs.graffiti.cache.BitmapDiskCache
import co.ujet.android.commons.libs.graffiti.cache.BitmapDiskCacheRemover
import co.ujet.android.commons.libs.graffiti.cache.BitmapMemCache
import co.ujet.android.commons.libs.graffiti.cache.BitmapMemCacheRemover
import co.ujet.android.commons.libs.graffiti.decoder.BitmapDecoder
import co.ujet.android.commons.libs.graffiti.decoder.DecodeOptions
import co.ujet.android.commons.libs.graffiti.loader.DiskCacheLoader
import co.ujet.android.commons.libs.graffiti.loader.FileLoader
import co.ujet.android.commons.libs.graffiti.loader.ImageLoader
import co.ujet.android.commons.libs.graffiti.loader.UriLoader
import co.ujet.android.commons.libs.graffiti.loader.UrlLoader
import co.ujet.android.commons.libs.graffiti.misc.GraffitiChaining
import co.ujet.android.commons.libs.graffiti.misc.GraffitiSource

/**
 * Load and cache images from URL
 * Alternative of Glide (https://github.com/bumptech/glide)
 *
 * Loader -> Decoder -> (Transform) -> Cache
 * 1. Loader creates InputStream from the source
 * 2. Decoder resize and creates Bitmap
 * 3. Transform not exists yet
 * 4. Cache write the file of final bitmap
 */
open class Graffiti : GraffitiSource, GraffitiChaining {
    private val context: Context?
    private val resources: Resources?

    // Options
    private var source: String? = null
    private var sourceUri: Uri? = null
    private val decodeOptions = DecodeOptions()
    private var fallbackResId = 0
    private var useMemCache = true
    private var useDiskCache = true
    private var radius = 0
    private var listener: BitmapListener? = null

    internal constructor() {
        context = null
        resources = null
    }

    private constructor(context: Context) {
        this.context = context
        resources = context.resources
    }

    /**
     * Set URL or file path
     *
     * @param path url or file path
     * @return this
     */
    override fun from(path: String?): GraffitiChaining {
        source = path
        return this
    }

    /**
     * Set URI
     *
     * @param uri uri
     * @return this
     */
    override fun from(uri: Uri?): GraffitiChaining {
        sourceUri = uri
        return this
    }

    /**
     * Fallback image when failed to load the image
     *
     * @param resId drawable resource id
     * @return this
     */
    override fun fallback(resId: Int): GraffitiChaining {
        fallbackResId = resId
        return this
    }

    /**
     * Resize option
     *
     * @param width  width (px)
     * @param height height (px)
     * @return this
     */
    override fun resize(width: Int, height: Int): GraffitiChaining {
        decodeOptions.setResize(width, height)
        return this
    }

    /**
     * Event listener
     *
     * @param listener Listener
     * @return this
     */
    override fun listener(listener: BitmapListener?): GraffitiChaining {
        this.listener = listener
        return this
    }

    /**
     * Flag whether use mem cache or not
     *
     * @param use true if use, false if not
     * @return this
     */
    override fun memCache(use: Boolean): GraffitiChaining {
        useMemCache = use
        return this
    }

    /**
     * Flag whether use disk cache or not
     *
     * @param use true if use, false if not
     * @return this
     */
    override fun diskCache(use: Boolean): GraffitiChaining {
        useDiskCache = use
        return this
    }

    /**
     * Image border radius value
     *
     * @param radius radius (px)
     * @return this
     */
    override fun radius(radius: Int): GraffitiChaining {
        this.radius = radius
        return this
    }

    /**
     * Load an image and set drawable to ImageView
     *
     * @param imageView ImageView
     */
    override fun into(imageView: ImageView) {
        enqueue(ImageViewBitmapSetter(imageView))
    }

    /**
     * Load an image and pass the drawable to listener
     *
     * @param listener callback
     */
    override fun get(listener: BitmapListener) {
        enqueue(listener)
    }

    /**
     * Enqueue to worker
     */
    private fun enqueue(doneListener: BitmapListener) {
        val cacheKey = getCacheKey()
        BitmapMemCache.getCache(cacheKey)?.let {
            BitmapListenerCaller(listOfNotNull(listener, doneListener)).onReady(it)
            return
        }
        val imageLoader = getImageLoader(cacheKey) ?: return
        val bitmapMemCache = getMemCache(cacheKey)
        val bitmapDiskCache = getDiskCache(cacheKey)
        val job = Job()
            .cacheKey(cacheKey)
            .loader(DiskCacheLoader(context, source, cacheKey))
            .loader(imageLoader)
            .decoder(BitmapDecoder(decodeOptions))
            .cache(bitmapMemCache)
            .cache(bitmapDiskCache)
            .radius(radius)
            .done(BitmapListenerCaller(listOfNotNull(listener, doneListener)))
        Worker.enqueue(job)
    }

    private fun getCacheKey(): String {
        val resizeWidth = decodeOptions.resizeWidth
        val resizeHeight = decodeOptions.resizeHeight
        val path = source ?: sourceUri.toString()
        return if (resizeHeight <= 0 || resizeWidth <= 0) {
            GraffitiUtil.getCacheKey(path)
        } else {
            GraffitiUtil.getCacheKey(path, resizeWidth, resizeHeight)
        }
    }

    private fun getImageLoader(cacheKey: String?): ImageLoader? {
        val sourceUri = sourceUri
        return when {
            sourceUri != null -> UriLoader(context, sourceUri, cacheKey)
            source?.startsWith("http") == true -> UrlLoader(context, source, cacheKey)
            else -> FileLoader(source ?: return null)
        }
    }

    private fun getMemCache(cacheKey: String): BitmapCache {
        return if (useMemCache) {
            BitmapMemCache(cacheKey)
        } else {
            BitmapMemCacheRemover(cacheKey)
        }
    }

    private fun getDiskCache(cacheKey: String?): BitmapCache {
        return if (useDiskCache) {
            BitmapDiskCache(context, source, cacheKey)
        } else {
            BitmapDiskCacheRemover(context, source, cacheKey)
        }
    }

    private class BitmapListenerCaller(private val listeners: List<BitmapListener>) :
        BitmapListener {
        override fun onReady(bitmap: Bitmap) {
            listeners.forEach {
                it.onReady(bitmap)
            }
        }

        override fun onFailed() {
            listeners.forEach {
                it.onFailed()
            }
        }
    }

    private inner class ImageViewBitmapSetter(private val imageView: ImageView) :
        BitmapListener {
        override fun onReady(bitmap: Bitmap) {
            imageView.setImageBitmap(bitmap)
        }

        override fun onFailed() {
            if (fallbackResId != 0 && resources != null && context != null) {
                val fallbackDrawable = ResourcesCompat.getDrawable(resources, fallbackResId, context.theme)
                imageView.setImageDrawable(fallbackDrawable)
            }
        }
    }

    companion object {
        @JvmStatic
        fun with(context: Context?): GraffitiSource {
            return context?.let {
                Graffiti(it)
            } ?: BrokenGraffiti()
        }
    }
}
