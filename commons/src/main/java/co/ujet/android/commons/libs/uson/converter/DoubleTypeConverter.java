package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class DoubleTypeConverter implements TypeConverter<Double> {
    private Double defaultValue;

    public DoubleTypeConverter(Double defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Double convert(Object value) {
        if (value instanceof Double)
            return (Double) value;

        if (value instanceof Number)
            return ((Number) value).doubleValue();

        if (value instanceof String) {
            try {
                return Double.valueOf((String) value);
            } catch (NumberFormatException ignore) {
            }
        }

        return defaultValue;
    }

    @Override
    public void toJson(Object value, J<PERSON>NStringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof Number) {
                stringer.value(((Number) value).doubleValue());

            } else if (value instanceof String) {
                stringer.value(Double.parseDouble((String) value));

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
