package co.ujet.android.commons.util

import android.content.Context.ACCESSIBILITY_SERVICE
import android.os.Build
import android.view.KeyEvent
import android.view.View
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityManager
import android.widget.TextView
import androidx.core.view.AccessibilityDelegateCompat
import androidx.core.view.ViewCompat
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import androidx.core.view.isVisible
import com.google.android.material.button.MaterialButton
import java.util.concurrent.TimeUnit

object AccessibilityUtil {
    const val BUTTON_ROLE = "Button"
    const val IMAGE_ROLE = "Image"
    const val CUSTOM_PICKER_VIEW_ROLE = "picker item adjustable"
    const val CUSTOM_PICKER_VIEW_ACTION = "use two fingers to swipe up or down to adjust"
    val ACCESSIBILITY_WAIT_TIMER = TimeUnit.MILLISECONDS.toMillis(900)
    private val announcementMap = hashMapOf<Int, Boolean>()
    var isBackButtonClicked: Boolean = false
    var ignoreNextButtonFocus: Boolean = false
    var isPsaActionClicked: Boolean = false
    var isCountryCodeClicked: Boolean = false

    // Custom views like FancyButton are not recognized as buttons and adding button role explicitly
    // so that talkback will read custom views as buttons to user.
    fun addButtonRole(view: View) {
        updateAccessibilityInfo(view, AccessibilityRequestType.ADD_BUTTON_ROLE)
    }

    // Custom views like image view inside GridView is not recognized as image and adding image role
    // explicitly so that talkback will read custom views as images to user.
    fun addImageRole(view: View) {
        updateAccessibilityInfo(view, AccessibilityRequestType.ADD_IMAGE_ROLE)
    }

    // Add User Role (i.e. mobile user, human agent etc.) string for every end user text in chat and
    // also Checking if the message has "." at the end of the sentence if not then adding "." manually so that
    // talkback gives a pause before announcing timestamp for better user experience
    fun addChatUserRole(userRole: String,
                        mainContainer: View,
                        message: TextView? = null,
                        timestamp: TextView,
                        resend: TextView? = null,
                        documentType: String = "",
                        videoType: String = "",
                        imageType: String = "",
                        isImage: Boolean = false,
                        isVideo: Boolean = false,
                        isDocument: Boolean = false,
                        isClickable: Boolean = false,
                        adapterPosition: Int
    ) {
        mainContainer.isFocusable = true
        if (!isClickable) {
            updateAccessibilityAction(mainContainer, true, null)
        }
        val timestampText = if (timestamp.isVisible) {
            timestamp.text
        } else {
            ""
        }
        val resendText = if (resend == null || !resend.isVisible) {
            ""
        } else {
            resend.text
        }
        val messageType = when {
            isImage -> imageType
            isDocument -> documentType
            isVideo -> videoType
            else -> ""
        }
        val text = if (message == null) {
            ""
        } else if (messageEndsWithPeriod(message.text.toString())) {
            message.text.toString()
        } else {
            "${message.text}."
        }
        // For Image and video ImageView has been used, and talkback read role description `image` after contentDescription which is being
        // duplicated with `messageType`.To prevent this role description has been updated for image and video.
        if (isImage || isVideo) {
            mainContainer.contentDescription = userRole
            updateRoleDescription(mainContainer, "$messageType $text $resendText $timestampText")
        } else {
            mainContainer.contentDescription = "$userRole $messageType $text $resendText $timestampText"
        }
        invokeAnnouncementOnce(
            mainContainer.context.getSystemService(ACCESSIBILITY_SERVICE) as AccessibilityManager,
            adapterPosition,
            "$userRole $messageType $text $resendText $timestampText"
        )
    }

    // Check if the message has "." at the end of the sentence
    private fun messageEndsWithPeriod(text: String): Boolean {
        return text.isNotEmpty() && text.last() == '.'
    }

    // when button is disabled while loading/fetching remote data, ignore button click event so that
    // talkback does not say "double tap to activate", otherwise it should say it."roleDescription" will
    // announce the role(i.e. button,image), keep it null if you don't want talkback to announce role.
    fun updateAccessibilityAction(view: View, ignoreAction: Boolean, roleDescription: String? = BUTTON_ROLE) {
        if (ignoreAction) {
            updateAccessibilityInfo(view, AccessibilityRequestType.IGNORE_ACTION, roleDescription)
        } else {
            updateAccessibilityInfo(view, AccessibilityRequestType.ADD_ACTION, roleDescription)
        }
    }

    // Override talkback content/text announced to user
    fun overrideContentDescription(view: View, text: String) {
        updateAccessibilityInfo(view, AccessibilityRequestType.OVERRIDE_TEXT, text)
    }

    // This method forces initial accessibility focus (talkback) to first UI view (back button at top of the screen).
    fun setupInitialFocus(accessibilityManager: AccessibilityManager, decorView: View) {
        if (accessibilityManager.isEnabled) {
            decorView.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED)
        }
    }

    fun setHeading(view: View) {
        updateAccessibilityInfo(view, AccessibilityRequestType.SET_VIEW_AS_HEADING)
    }

    // Talkback will announce the "announcement" string when this method is called
    fun invokeTalkbackAnnouncementEvent(accessibilityManager: AccessibilityManager, announcement: String) {
        if (accessibilityManager.isEnabled) {
            val event = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                AccessibilityEvent(AccessibilityEvent.TYPE_ANNOUNCEMENT)
            } else {
                AccessibilityEvent.obtain(AccessibilityEvent.TYPE_ANNOUNCEMENT)
            }
            event.text.add(announcement)
            accessibilityManager.sendAccessibilityEvent(event)
        }
    }

    private fun updateAccessibilityInfo(view: View, accessibilityType: AccessibilityRequestType, overrideText: String? = null) {
        getAccessibilityInfo(view, accessibilityType) { host, info, type ->
            when (type) {
                AccessibilityRequestType.ADD_BUTTON_ROLE -> info?.roleDescription = BUTTON_ROLE
                AccessibilityRequestType.ADD_IMAGE_ROLE -> info?.roleDescription = IMAGE_ROLE
                AccessibilityRequestType.OVERRIDE_TEXT -> {
                    if (overrideText?.isNotEmpty() == true) {
                        info?.text = overrideText
                    }
                }

                AccessibilityRequestType.IGNORE_ACTION -> {
                    if (!overrideText.isNullOrEmpty())
                        info?.roleDescription = BUTTON_ROLE
                    info?.removeAction(AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_CLICK)
                    info?.removeAction(AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_LONG_CLICK)
                    info?.isClickable = false
                    host.isClickable = false
                    info?.isLongClickable = false
                }

                AccessibilityRequestType.ADD_ACTION -> {
                    if (!overrideText.isNullOrEmpty())
                        info?.roleDescription = BUTTON_ROLE
                    info?.addAction(AccessibilityNodeInfoCompat.AccessibilityActionCompat.ACTION_CLICK)
                    info?.isClickable = true
                    host.isClickable = true
                }

                AccessibilityRequestType.SET_VIEW_AS_HEADING -> info?.isHeading = true
                AccessibilityRequestType.CHANGE_CLICK_ACTION_LABEL -> {
                    info?.addAction(
                        AccessibilityNodeInfoCompat.AccessibilityActionCompat(
                            AccessibilityNodeInfoCompat.ACTION_CLICK, overrideText
                        )
                    )
                }

                AccessibilityRequestType.ADD_BUTTON_ROLE_TO_TOGGLE_BUTTON -> {
                    info?.stateDescription = info?.text
                    info?.roleDescription = BUTTON_ROLE
                    info?.addAction(
                        AccessibilityNodeInfoCompat.AccessibilityActionCompat(
                            AccessibilityNodeInfoCompat.ACTION_CLICK, overrideText
                        )
                    )
                }

                AccessibilityRequestType.UPDATE_ROLE_DESCRIPTION -> {
                    info?.roleDescription = overrideText
                }
            }
        }
    }

    private fun getAccessibilityInfo(view: View,
                                     accessibilityType: AccessibilityRequestType,
                                     callback: (View, AccessibilityNodeInfoCompat?, AccessibilityRequestType) -> Unit) {
        ViewCompat.setAccessibilityDelegate(view, object : AccessibilityDelegateCompat() {
            override fun onInitializeAccessibilityNodeInfo(host: View, info: AccessibilityNodeInfoCompat) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                callback(host, info, accessibilityType)
            }
        })
    }

    fun changeAccessibilityClickDescription(view: View, actionText: String) {
        updateAccessibilityInfo(view, AccessibilityRequestType.CHANGE_CLICK_ACTION_LABEL, actionText)
    }

    // 'actionText' will be added after 'double tap to'
    fun addButtonRoleToToggleButton(button: MaterialButton, actionText: String) {
        updateAccessibilityInfo(button, AccessibilityRequestType.ADD_BUTTON_ROLE_TO_TOGGLE_BUTTON, actionText)
    }

    fun invokeAnnouncementOnce(accessibilityManager: AccessibilityManager, adapterPosition: Int, announcement: String) {
        if (announcementMap[adapterPosition] == null) {
            invokeTalkbackAnnouncementEvent(accessibilityManager, announcement)
            announcementMap[adapterPosition] = true
        }
    }

    private fun updateRoleDescription(view: View, text: String) {
        updateAccessibilityInfo(view, AccessibilityRequestType.UPDATE_ROLE_DESCRIPTION, text)
    }

    fun clearAnnouncementMap() {
        announcementMap.clear()
    }

    fun setupKeyboardAccessibility(
        view: View?,
        onDpadDown: ((View) -> Boolean)? = null,
        onTab: ((View) -> Boolean)? = null,
        onTabOrDpadDown: ((View) -> Boolean)? = null,
        onDpadUp: ((View) -> Boolean)? = null,
        onEnter: ((View) -> Boolean)? = null,
        onDpadRight: ((View) -> Boolean)? = null,
        onDpadLeft: ((View) -> Boolean)? = null
    ) {
        view?.setOnKeyListener { v, keyCode, event ->
            if (event.action != KeyEvent.ACTION_DOWN) return@setOnKeyListener false
            return@setOnKeyListener when (keyCode) {
                KeyEvent.KEYCODE_TAB -> onTab?.invoke(v) == true || onTabOrDpadDown?.invoke(v) == true
                KeyEvent.KEYCODE_DPAD_DOWN -> onDpadDown?.invoke(v) == true || onTabOrDpadDown?.invoke(v) == true
                KeyEvent.KEYCODE_DPAD_UP -> onDpadUp?.invoke(v) == true
                KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_DPAD_CENTER -> onEnter?.invoke(v) == true
                KeyEvent.KEYCODE_DPAD_RIGHT -> onDpadRight?.invoke(v) == true
                KeyEvent.KEYCODE_DPAD_LEFT -> onDpadLeft?.invoke(v) == true
                else -> false
            }
        }
    }

    enum class AccessibilityRequestType {
        ADD_BUTTON_ROLE, ADD_IMAGE_ROLE, ADD_ACTION, IGNORE_ACTION, OVERRIDE_TEXT, SET_VIEW_AS_HEADING,
        CHANGE_CLICK_ACTION_LABEL, ADD_BUTTON_ROLE_TO_TOGGLE_BUTTON, UPDATE_ROLE_DESCRIPTION
    }
}
