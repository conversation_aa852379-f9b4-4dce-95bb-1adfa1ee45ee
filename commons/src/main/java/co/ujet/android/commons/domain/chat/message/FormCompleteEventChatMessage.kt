package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import org.json.JSONException
import org.json.JSONObject
import java.util.Date

class FormCompleteEventChatMessage(private val signature: String?,
                                   val status: String?,
                                   val smartActionId: Int?,
                                   private val errorCode: String?,
                                   private val errorMessage: String?,
                                   private val eventTimestamp: String?,
                                   localId: Int,
                                   timestamp: Date
) : SendableChatMessage(localId, timestamp) {
    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "form_complete")
                put("local_id", localId)
                put("signature", signature)
                put("data", JSONObject().apply {
                    put("status", status)
                    put("smart_action_id", smartActionId)
                    if (status?.trim()?.lowercase() != "success") {
                        put("details", JSONObject().apply {
                            put("error_code", errorCode)
                            put("message", errorMessage)
                        })
                    }
                    put("timestamp", eventTimestamp)
                })
            } catch (e: JSONException) {
                logger.w(e, "failed to convert form complete event message to json")
            }
        }
    }

    companion object {
        @JvmStatic
        fun createPending(signature: String?,
                          status: String?,
                          smartActionId: Int?,
                          errorCode: String?,
                          errorMessage: String?,
                          eventTimestamp: String?,
                          localId: Int,
                          timestamp: Date): FormCompleteEventChatMessage {
            return FormCompleteEventChatMessage(signature, status, smartActionId, errorCode, errorMessage, eventTimestamp, localId, timestamp)
        }

    }
}
