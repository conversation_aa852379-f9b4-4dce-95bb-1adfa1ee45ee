package co.ujet.android.commons.extensions

import java.util.Timer
import java.util.TimerTask

fun <A, B, R> Any.let(a: A?, b: B?, block: (A, B) -> R?): R? {
    return if (a != null && b != null) {
        block(a, b)
    } else {
        null
    }
}

fun Any.fixedRateTimer(period: Long, timeout: Long, action: TimerTask.() -> Unit, onTimeout: () -> Unit): Timer {
    val maxLoops = timeout / period
    var loopCount = 0
    val timer = kotlin.concurrent.fixedRateTimer(period = period, action = timerTask@{
        if (loopCount++ >= maxLoops) {
            onTimeout()
            cancel()
            return@timerTask
        }
        action()
    })
    return timer
}

fun <T> Any.getValueOrDefault(condition: Boolean, value: T, default: T) = if (condition) {
    value
} else {
    default
}
