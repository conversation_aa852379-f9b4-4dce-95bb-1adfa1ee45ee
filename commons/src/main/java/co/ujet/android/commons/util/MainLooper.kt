package co.ujet.android.commons.util

import android.os.Handler
import android.os.Looper
import androidx.annotation.AnyThread

/**
 * To post runnable on main looper(UI thread) easily
 * [android.app.Activity.runOnUiThread]
 */
object MainLooper {
    /**
     * Post runnable on main looper
     *
     * @param runnable The Runnable that will be executed.
     */
    @JvmStatic
    @AnyThread
    fun post(runnable: Runnable) {
        val mainLooper = Looper.getMainLooper()
        if (Looper.myLooper() == mainLooper) {
            runnable.run()
        } else {
            Handler(mainLooper).post(runnable)
        }
    }

    /**
     * Post runnable on main looper
     *
     * @param runnable    The Runnable that will be executed.
     * @param delayMillis The delay (in milliseconds) until the Runnable
     * will be executed.
     */
    @JvmStatic
    @AnyThread
    fun postDelayed(runnable: Runnable, delayMillis: Long) {
        val mainLooper = Looper.getMainLooper()
        <PERSON><PERSON>(mainLooper).postDelayed(runnable, delayMillis)
    }
}
