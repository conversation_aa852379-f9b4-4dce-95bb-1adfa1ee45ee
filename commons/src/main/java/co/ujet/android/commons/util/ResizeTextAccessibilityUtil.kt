package co.ujet.android.commons.util

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.DisplayMetrics
import kotlin.math.round
import kotlin.math.sqrt

object ResizeTextAccessibilityUtil {
    private const val SHARED_PREFERENCE_KEY = "co.ujet.android.preferences.app.internal_data"
    private const val RESIZE_FONT_SIZE_KEY = "resize_font_size"
    private const val RESIZE_DISPLAY_SIZE_KEY = "resize_display_size"
    private const val FONT_SIZE_SUFFIX = 0.0001f

    //font size
    const val MAX_FONT_SIZE = 2f
    private const val MIN_FONT_SIZE = 1f
    private const val DEFAULT_FONT_SIZE = 0f

    //display size
    private const val MAX_DISPLAY_SIZE = 1.2f
    private const val MEDIUM_DISPLAY_SIZE = 1.1f
    private const val MIN_DISPLAY_SIZE = 1f

    //conditional font size
    private const val FONT_SIZE_WITH_MAX_DISPLAY_SIZE = 1.5f
    private const val FONT_SIZE_WITH_MEDIUM_DISPLAY_SIZE = 1.6f
    private const val FONT_SIZE_WITH_MIN_DISPLAY_SIZE = 1.8f
    const val FONT_DISPLAY_SIZE_TO_HIDE_ICON = 2.75f

    var isFontSizeAdjustedAfterExceeding: Boolean = false
    var isFontSizeAdjustedAfterShrunken: Boolean = false
    var chatTimestampFontSize = 0.0f

    fun isLargeTextAccessibilityEnabled(context: Context): Boolean {
        return isFontSizeIncreased(context) || isDisplaySizeIncreased(context)
    }

    fun isFontSizeExceededLimit(context: Context): Boolean {
        val fontSize = getFontScale(context)
        val displaySize = getDisplayDensityScaleFactor(context)
        // When font size exceeded maximum limit, we are adjusting font size to fit the text and there
        // onwards getFontScale() returns adjusted font scale so if isFontSizeExceededAdjusted is true then
        // it means font size exceeded already. Otherwise, it will in following conditions.
        return isFontSizeAdjustedAfterExceeding || (fontSize >= MAX_FONT_SIZE && displaySize > MIN_DISPLAY_SIZE) ||
                (fontSize > MIN_FONT_SIZE && displaySize > MAX_DISPLAY_SIZE)
    }

    fun isFontSizeShrunkenBelowLimit(context: Context): Boolean {
        val fontSize = getFontScale(context)
        val displaySize = getDisplayDensityScaleFactor(context)
        return isFontSizeAdjustedAfterShrunken || fontSize < MIN_FONT_SIZE || displaySize < MIN_DISPLAY_SIZE
    }

    fun getMaxFontSize(context: Context): Float {
        val fontSize = getFontScale(context)
        val displaySize = getDisplayDensityScaleFactor(context)
        return when {
            /* Increasing display text will increase the font size along with magnifying the view components
            and there is no work around to restrict magnification or zoom level just like how we restricted
            font size to up to 200%, so instead reduced font size when displaySize is increased along with font size.
            * */
            displaySize >= MAX_DISPLAY_SIZE -> getMaxFontSize(fontSize, FONT_SIZE_WITH_MAX_DISPLAY_SIZE)
            displaySize > MEDIUM_DISPLAY_SIZE -> getMaxFontSize(fontSize, FONT_SIZE_WITH_MEDIUM_DISPLAY_SIZE)
            displaySize > MIN_DISPLAY_SIZE -> getMaxFontSize(fontSize, FONT_SIZE_WITH_MIN_DISPLAY_SIZE)
            else -> getMaxFontSize(fontSize, MAX_FONT_SIZE)
        }
    }

    fun isFontAndDisplaySizeMaxedOut(context: Context): Boolean {
        val fontSize = getFontScale(context)
        val displaySize = getDisplayDensityScaleFactor(context)
        // When font size exceeded maximum limit (when isFontSizeExceededLimit() is true), we are adjusting
        // font size to fit the text and there onwards getFontScale() returns adjusted font scale so
        // if isFontSizeAdjusted is true then font and display size will be maxed out when fontSize >= 1.5f
        // and displaySize >= 1.2f, checkout getMaxFontSize() implementation above, otherwise we can
        // replay on font size and display size to compare.
        return if (isFontSizeAdjustedAfterExceeding) {
            fontSize >= FONT_SIZE_WITH_MAX_DISPLAY_SIZE && displaySize >= MAX_DISPLAY_SIZE
        } else {
            fontSize >= MAX_FONT_SIZE && displaySize > MAX_DISPLAY_SIZE
        }
    }

    fun getFontAndDisplaySize(context: Context) = getDisplayDensityScaleFactor(context) + getFontScale(context)

    fun getDisplayDensityScaleFactor(context: Context): Float {
        val metrics = context.resources.displayMetrics
        val currentDeviceDensity = metrics.densityDpi

        // DENSITY_DEVICE_STABLE is introduced in API level 24 so we explicitly calculate density scale
        // factor using screen measurements for earlier API level versions (between 21 and 24)
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            currentDeviceDensity.toFloat() / DisplayMetrics.DENSITY_DEVICE_STABLE
        } else {
            val widthInPixels = metrics.widthPixels
            val heightInPixels = metrics.heightPixels
            val exactScreenWidth = round(widthInPixels.toDouble() / metrics.xdpi.toDouble())
            val exactScreenHeight = round(heightInPixels.toDouble() / metrics.ydpi.toDouble())
            val screenInches = round(sqrt((exactScreenWidth * exactScreenWidth) + (exactScreenHeight * exactScreenHeight)))
            val dens = round(sqrt(((widthInPixels * widthInPixels) + (heightInPixels * heightInPixels)).toDouble()) / screenInches)
            (currentDeviceDensity.toFloat() / dens).toFloat()
        }
    }

    fun isResizeAccessibilityConfigUpdated(context: Context): Boolean {
        val cachedFontSize = getCachedFontSize(context)
        val cachedDisplaySize = getCachedDisplaySize(context)
        val fontSize = getFontScale(context)
        val displaySize = getDisplayDensityScaleFactor(context)
        return cachedFontSize != fontSize || cachedDisplaySize != displaySize
    }

    fun saveResizeAccessibilityConfig(context: Context, fontSize: Float, displaySize: Float,
                                      appendFontSuffix: Boolean = true) {
        val sharedPreferences: SharedPreferences = getCachedPreferences(context)
        // We are appending "001" to actual font size so that when user changed font size through
        // settings, isResizeAccessibilityConfigUpdated returns correct value. For example: when
        // font size = 2f and display size is 1.09, we will adjust the font size to 1.8001 and
        // subsequent calls to getMaxFontSize will return 1.8001 though actual setting size is 2f
        // and when user changes the font size to 1.8 from settings then we can detect user updated
        // font size, if we use exact font size 1.8 then our code will fail to detect font size change in this case.
        //val updatedFontSize = fontSize.toString().plus(FONT_SIZE_SUFFIX).toFloat()
        val updatedFontSize = if (appendFontSuffix) {
            fontSize + FONT_SIZE_SUFFIX
        } else {
            fontSize
        }
        sharedPreferences.edit().putFloat(RESIZE_FONT_SIZE_KEY, updatedFontSize)
            .putFloat(RESIZE_DISPLAY_SIZE_KEY, displaySize).apply()
    }

    private fun getCachedFontSize(context: Context): Float {
        return getCachedPreferences(context).getFloat(RESIZE_FONT_SIZE_KEY, DEFAULT_FONT_SIZE)
    }

    private fun getCachedDisplaySize(context: Context): Float {
        return getCachedPreferences(context).getFloat(RESIZE_DISPLAY_SIZE_KEY, DEFAULT_FONT_SIZE)
    }

    private fun getCachedPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(SHARED_PREFERENCE_KEY, Context.MODE_PRIVATE)
    }

    private fun isDisplaySizeIncreased(context: Context): Boolean {
        val displayDensityScaleFactor = getDisplayDensityScaleFactor(context)
        // If display size under accessibility option is increased then density scale factor will be greater than 1
        return displayDensityScaleFactor > MIN_DISPLAY_SIZE
    }

    private fun isFontSizeIncreased(context: Context): Boolean {
        val fontScale = getFontScale(context)
        // If font size under accessibility option is increased then font scale will be greater than 1
        return fontScale > MIN_FONT_SIZE
    }

    private fun getFontScale(context: Context): Float {
        val configuration: android.content.res.Configuration? = context.resources.configuration
        return configuration?.fontScale ?: DEFAULT_FONT_SIZE
    }

    private fun getMaxFontSize(fontSize: Float, condition: Float): Float {
        return if (fontSize >= condition) {
            condition
        } else {
            fontSize
        }
    }
}
