package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

import co.ujet.android.commons.libs.uson.TypeToken;

public class EnumTypeConverter<T extends Enum> implements TypeConverter<T> {
    private TypeToken<T> typeToken;

    public EnumTypeConverter(TypeToken<T> typeToken) {
        this.typeToken = typeToken;
    }

    @Override
    public T convert(Object value) {
        if (value instanceof Enum)
            return (T) value;

        if (value instanceof String) {
            return (T) Enum.valueOf(typeToken.getRawType(), (String) value);
        }

        return null;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (!typeToken.getRawType().isInstance(value)) {
                if (value instanceof String) {
                    Enum[] enumConstants = typeToken.getRawType().getEnumConstants();
                    if (enumConstants == null) {
                        stringer.value(null);

                    } else {
                        boolean found = false;
                        for (Enum enumConstant : enumConstants) {
                            if (enumConstant.name().equals(value)) {
                                stringer.value(value);
                                found = true;
                                break;
                            }
                        }

                        if (!found) {
                            stringer.value(null);
                        }
                    }

                } else {
                    stringer.value(null);
                }

            } else {
                stringer.value(((Enum) value).name());
            }

        } catch (JSONException e) {
            e.printStackTrace();

        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }
}
