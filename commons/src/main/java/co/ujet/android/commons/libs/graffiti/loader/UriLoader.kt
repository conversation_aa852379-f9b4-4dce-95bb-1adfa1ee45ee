package co.ujet.android.commons.libs.graffiti.loader

import android.content.Context
import android.net.Uri
import co.ujet.android.commons.libs.graffiti.GraffitiUtil
import co.ujet.android.commons.util.FileUtil
import java.io.File
import java.io.IOException

class UriLoader(private val context: Context?, private val uri: Uri, private val cacheKey: String?) : ImageLoader {
    override fun load(): File? {
        val compressFormat = GraffitiUtil.getCompressFormat(context, uri)
        val outFile = GraffitiUtil.resolveFile(context?.cacheDir, cacheKey, compressFormat)
        try {
            context?.contentResolver?.openInputStream(uri)?.use {
                FileUtil.copyInputStreamToFile(it, outFile)
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return if (outFile.exists()) {
            outFile
        } else {
            null
        }
    }
}
