package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class IntegerTypeConverter implements TypeConverter<Integer> {
    private Integer defaultValue;

    public IntegerTypeConverter(Integer defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Integer convert(Object value) {
        if (value instanceof Integer)
            return (Integer) value;

        if (value instanceof Number)
            return ((Number) value).intValue();

        if (value instanceof String) {
            try {
                return Integer.valueOf((String) value);
            } catch (NumberFormatException ignore) {
            }
        }

        return null;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof Number) {
                stringer.value(((Number) value).intValue());

            }  else if (value instanceof String) {
                stringer.value(Integer.parseInt((String) value));

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
