package co.ujet.android.commons.libs.uson;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;

public class ClassMetadataCache {
    private HashMap<Class, ClassMetadata> classMetadataMap = new HashMap<>();

    public synchronized ClassMetadata get(Class cls) {
        ClassMetadata classMetadata = classMetadataMap.get(cls);
        if (classMetadata == null) {
            classMetadata = new ClassMetadata();

            for (Field field : cls.getDeclaredFields()) {
                int modifiers = field.getModifiers();
                if (Modifier.isStatic(modifiers) || Modifier.isTransient(modifiers))
                    continue;

                String serializedName = getSerializedName(field);
                if (serializedName == null)
                    continue;

                if (!field.isAccessible()) {
                    try {
                        field.setAccessible(true);
                    } catch (SecurityException ex) {
                        continue;
                    }
                }

                classMetadata.add(field, serializedName);
            }

            classMetadataMap.put(cls, classMetadata);
        }

        return classMetadata;
    }

    private String getSerializedName(Field field) {
        SerializedName serializedName = field.getAnnotation(SerializedName.class);
        if (serializedName != null)
            return serializedName.value();

        return null;
    }
}
