package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONStringer;

import java.lang.reflect.Field;
import java.util.Stack;

import androidx.annotation.NonNull;
import co.ujet.android.commons.BuildConfig;
import co.ujet.android.commons.libs.uson.ClassMetadata;
import co.ujet.android.commons.libs.uson.ClassMetadataCache;
import co.ujet.android.commons.libs.uson.TypeConverters;
import co.ujet.android.commons.libs.uson.TypeToken;
import co.ujet.android.commons.libs.uson.UsonUtil;
import co.ujet.android.modulemanager.EntryPointFactory;
import co.ujet.android.modulemanager.entrypoints.log.Logger;

public class CustomTypeConverter<T> implements TypeConverter<T> {
    private static final String CLASS_NAME_KEY = "__clazz__";

    private final TypeConverters typeConverters;
    private final ClassMetadataCache classMetadataCache;
    private final TypeToken<T> typeToken;
    private final Logger logger = EntryPointFactory.INSTANCE.provideEntryPoint(Logger.class);

    public CustomTypeConverter(TypeToken<T> typeToken, TypeConverters typeConverters, ClassMetadataCache classMetadataCache) {
        this.typeConverters = typeConverters;
        this.classMetadataCache = classMetadataCache;
        this.typeToken = typeToken;
    }

    @Override
    public T convert(Object object) {
        JSONObject jsonObject = UsonUtil.toJsonObject(object);
        if (jsonObject == null)
            return null;

        if (jsonObject.has(CLASS_NAME_KEY)) {
            String clsName = jsonObject.optString(CLASS_NAME_KEY);
            if (!clsName.equals(typeToken.getRawType().getName())) {
                try {
                    Class<? extends T> newClass = (Class<? extends T>) Class.forName(clsName);
                    return typeConverters.getConverter(newClass).convert(object);
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }

        T instance = UsonUtil.newInstance(typeToken);
        Stack<TypeToken> typeTokens = getTypeHierarchy(typeToken);

        while (!typeTokens.empty()) {
            TypeToken typeToken = typeTokens.pop();
            ClassMetadata classMetadata = classMetadataCache.get(typeToken.getRawType());

            for (int index = 0; index < classMetadata.size(); ++index) {
                String name = classMetadata.getSerializedName(index);
                Object value = jsonObject.opt(name);
                if (value == null)
                    continue;

                Field field = classMetadata.getField(index);

                TypeToken fieldTypeToken = new TypeToken(field.getGenericType());
                TypeConverter typeConverter = typeConverters.getConverter(fieldTypeToken);
                if (typeConverter != null) {
                    UsonUtil.setField(field, instance, typeConverter.convert(value));
                }
            }
        }

        return instance;
    }

    @Override
    public void toJson(Object object, JSONStringer stringer) {
        try {
            if (object == null) {
                stringer.value(JSONObject.NULL);

            } else if (!typeToken.getRawType().isInstance(object)) {
                stringer.value(JSONObject.NULL);
                logger.w("Invalid instance type: " + object + ", Expected type: " + typeToken.getRawType());

            } else {
                objectToJson(object, stringer);
            }

        } catch (JSONException e) {
            if (BuildConfig.DEBUG) {
                e.printStackTrace();
            }
        }
    }

    private void objectToJson(Object object, JSONStringer stringer) throws JSONException {
        stringer.object();

        Stack<TypeToken> typeTokens;
        // Raw type is parent of real class of object
        if (typeToken.getRawType() != object.getClass() &&
                typeToken.getRawType().isAssignableFrom(object.getClass())) {
            stringer.key(CLASS_NAME_KEY);
            stringer.value(object.getClass().getName());

            typeTokens = getTypeHierarchy(new TypeToken(object.getClass()));

        } else {
            typeTokens = getTypeHierarchy(typeToken);
        }

        while (!typeTokens.empty()) {
            TypeToken typeToken = typeTokens.pop();
            ClassMetadata classMetadata = classMetadataCache.get(typeToken.getRawType());
            for (int index = 0; index < classMetadata.size(); ++index) {
                Field field = classMetadata.getField(index);
                Object value = UsonUtil.get(field, object);
                if (value == null)
                    continue;

                TypeConverter typeConverter = typeConverters.getConverter(new TypeToken<>(field.getGenericType()));
                stringer.key(classMetadata.getSerializedName(index));
                typeConverter.toJson(value, stringer);
            }
        }

        stringer.endObject();
    }

    @NonNull
    private Stack<TypeToken> getTypeHierarchy(TypeToken typeToken) {
        TypeToken _typeToken = typeToken;
        Stack<TypeToken> typeTokens = new Stack<>();
        typeTokens.add(_typeToken);
        while (_typeToken.getRawType().getSuperclass() != Object.class) {
            _typeToken = new TypeToken<>(_typeToken.getRawType().getGenericSuperclass());
            typeTokens.add(_typeToken);
        }
        return typeTokens;
    }
}
