package co.ujet.android.commons.libs.graffiti

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.Bitmap.CompressFormat.JPEG
import android.graphics.Bitmap.CompressFormat.PNG
import android.graphics.Bitmap.CompressFormat.WEBP
import android.net.Uri
import android.webkit.MimeTypeMap
import co.ujet.android.commons.BuildConfig
import co.ujet.android.modulemanager.entrypoints.log.Logger
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

object GraffitiUtil {
    fun getCompressFormat(path: String): CompressFormat {
        return if (MimeTypeMap.getFileExtensionFromUrl(path).endsWith("jpg")) {
            JPEG
        } else {
            PNG
        }
    }

    fun getCompressFormat(context: Context?, uri: Uri): CompressFormat {
        return when {
            uri.scheme == ContentResolver.SCHEME_CONTENT -> {
                val type = context?.contentResolver?.getType(uri)
                if (MimeTypeMap.getSingleton().getExtensionFromMimeType(type)?.endsWith("jpg") == true) {
                    JPEG
                } else {
                    PNG
                }
            }
            uri.scheme?.startsWith("http") == true || uri.scheme == ContentResolver.SCHEME_FILE -> {
                getCompressFormat(uri.toString())
            }
            else -> PNG
        }
    }

    fun resolveFile(dir: File?, cacheKey: String?, compressFormat: CompressFormat): File {
        val filename = if (compressFormat == PNG) {
            "$cacheKey.png"
        } else {
            "$cacheKey.jpg"
        }
        return File(dir, filename)
    }

    /**
     * @param bitmap         Bitmap to be written as a file
     * @param file           Cache file
     * @param compressFormat [android.graphics.Bitmap.CompressFormat]
     * @param quality        Compress quality
     * @return true if success, false if fail
     */
    fun writeFile(bitmap: Bitmap, file: File, compressFormat: CompressFormat, quality: Int): Boolean {
        if (compressFormat == WEBP && BuildConfig.DEBUG) {
            throw RuntimeException("Unsupported compress format: $WEBP")
        }
        return try {
            FileOutputStream(file).use {
                bitmap.compress(compressFormat, quality, it)
            }
            Logger.d("Succeed to write a bitmap to file: %s", file.absolutePath)
            true
        } catch (e: IOException) {
            Logger.w(e, "saveTempFile failed")
            false
        }
    }

    fun getCacheKey(path: String, width: Int, height: Int) = getCacheKey(path + "_" + width + "x" + height)

    fun getCacheKey(path: String): String {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            digest.update(path.toByteArray())
            digest.digest().map {
                (0xFF and it.toInt()).toString(16).padStart(2, '0')
            }.reduce { acc, string -> acc + string }
        } catch (e: NoSuchAlgorithmException) {
            path.hashCode().toString()
        }
    }
}
