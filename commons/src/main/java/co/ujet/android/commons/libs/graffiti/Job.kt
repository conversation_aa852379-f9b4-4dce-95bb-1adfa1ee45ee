package co.ujet.android.commons.libs.graffiti

import co.ujet.android.commons.libs.graffiti.cache.BitmapCache
import co.ujet.android.commons.libs.graffiti.decoder.BitmapDecoder
import co.ujet.android.commons.libs.graffiti.loader.ImageLoader
import java.util.ArrayList

/**
 * Single bitmap processing
 */
internal class Job {
    val imageLoaders: ArrayList<ImageLoader> = ArrayList()
    val bitmapCaches: ArrayList<BitmapCache> = ArrayList()

    var decoder: BitmapDecoder? = null
        private set
    var callback: BitmapListener? = null
        private set
    var cacheKey: String? = null
        private set
    var radius = 0
        private set

    fun loader(imageLoader: ImageLoader): Job {
        imageLoaders.add(imageLoader)
        return this
    }

    fun decoder(decoder: BitmapDecoder): Job {
        this.decoder = decoder
        return this
    }

    fun cache(bitmapCache: BitmapCache): Job {
        bitmapCaches.add(bitmapCache)
        return this
    }

    fun cacheKey(cacheKey: String): Job {
        this.cacheKey = cacheKey
        return this
    }

    fun done(listener: BitmapListener): Job {
        callback = listener
        return this
    }

    fun radius(radius: Int): Job {
        this.radius = radius
        return this
    }
}
