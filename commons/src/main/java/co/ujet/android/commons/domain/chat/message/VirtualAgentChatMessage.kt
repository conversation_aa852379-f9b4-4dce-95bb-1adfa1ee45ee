package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.agent.VirtualAgent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

open class VirtualAgentChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    private val message: String?,
    private val virtualAgent: VirtualAgent?,
    messageIndex: Long,
    val isMarkDownSupported: Boolean = false
) : ChatMessage(localId, sid, timestamp, messageIndex) {

    fun getMessage() = message ?: ""

    var agentName = virtualAgent?.displayName

    fun getAgentAvatarUrl() = virtualAgent?.avatarUrl

    override fun getSenderAgent() = virtualAgent
}
