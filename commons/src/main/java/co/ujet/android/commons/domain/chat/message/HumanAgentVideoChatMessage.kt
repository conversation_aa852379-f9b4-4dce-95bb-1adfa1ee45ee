package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class HumanAgentVideoChatMessage private constructor(
    localId: Int,
    sid: String,
    timestamp: Date,
    var mediaFiles: ArrayList<MediaFile>,
    private val agent: Agent?,
    messageIndex: Long,
    groupMessageId: Int?,
    var isVisible: Boolean
) : ChatMessage(localId, sid, timestamp, messageIndex, groupMessageId) {

    var agentAvatarUrl = agent?.avatarUrl

    var agentName = agent?.displayName

    override fun getSenderAgent() = agent

    companion object {
        fun createSent(
            @IntRange(from = 1) localId: Int,
            sid: String,
            timestamp: Date,
            mediaFiles: ArrayList<MediaFile>,
            agent: Agent?,
            messageIndex: Long,
            groupMessageId: Int?,
            isVisible: Boolean
        ): HumanAgentVideoChatMessage {
            return HumanAgentVideoChatMessage(localId, sid, timestamp, mediaFiles, agent, messageIndex,
                groupMessageId, isVisible)
        }
    }
}
