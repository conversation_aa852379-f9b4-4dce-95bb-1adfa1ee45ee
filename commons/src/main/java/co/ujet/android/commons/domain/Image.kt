package co.ujet.android.commons.domain

import android.webkit.URLUtil
import co.ujet.android.commons.libs.uson.SerializedName

data class Image(
    @SerializedName("text")
    val text: String?,

    @SerializedName("file_name")
    private val fileName: String?,

    @SerializedName("url")
    val url: String
) {
    constructor() : this(null, null, "")

    fun getFileName(): String = fileName ?: URLUtil.guessFileName(url, null, null)
}
