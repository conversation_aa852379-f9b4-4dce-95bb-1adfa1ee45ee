package co.ujet.android.commons.extensions

import android.graphics.Bitmap
import android.view.View
import android.widget.ImageView
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat.SRC_IN
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import co.ujet.android.commons.libs.graffiti.BitmapListener
import co.ujet.android.commons.libs.graffiti.Graffiti

fun ImageView.loadOrSetDefault(path: String?, @DrawableRes fallback: Int, @ColorInt fallbackTint: Int, radius: Int = 0) {
    visibility = View.VISIBLE
    if (path.isNullOrEmpty()) {
        setImageResource(fallback)
        colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(fallbackTint, SRC_IN)
    } else {
        Graffiti
            .with(context)
            .from(path)
            .radius(radius)
            .into(this)
    }
}

fun ImageView.loadOrSetVisibility(path: String?, visibility: Int, radius: Int = 0) {
    if (path == null) {
        this.visibility = visibility
    } else {
        Graffiti
            .with(context)
            .from(path)
            .radius(radius)
            .into(this)
    }
}

fun ImageView.loadRemoteImage(path: String?, radius: Float = 0f, onSuccess: (Bitmap) -> Unit = {}, onError: () -> Unit = {}) {
    if (path == null) {
        onError()
    } else {
        Graffiti
            .with(context)
            .from(path)
            .listener(object : BitmapListener {
                override fun onReady(bitmap: Bitmap) {
                    val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.resources, bitmap)
                    roundedBitmapDrawable.cornerRadius = radius
                    roundedBitmapDrawable.setAntiAlias(true)
                    roundedBitmapDrawable.isFilterBitmap = true
                    setImageDrawable(roundedBitmapDrawable)
                    onSuccess(bitmap)
                }

                override fun onFailed() {
                    onError()
                }
            })
            .into(this)
    }
}

fun ImageView.loadOrSetInvisible(path: String?, radius: Int = 0) {
    loadOrSetVisibility(path, View.INVISIBLE, radius)
}
