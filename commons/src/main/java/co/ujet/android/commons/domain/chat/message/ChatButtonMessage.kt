package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.libs.uson.SerializedName
import java.io.Serializable
import java.util.HashMap

data class ChatButtonMessage(
    @SerializedName("title")
    val title: String? = null,

    @SerializedName("action")
    val action: String? = null,

    @SerializedName("link")
    val link: String? = null,

    @SerializedName("event_params")
    val eventParams: HashMap<String, Any>? = null
) : Serializable
