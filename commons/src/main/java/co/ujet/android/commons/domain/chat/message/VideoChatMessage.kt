package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import org.json.JSONException
import org.json.JSONObject
import java.util.*

class VideoChatMessage : MediaChatMessage {
    private constructor(localId: Int,
                        timestamp: Date,
                        mediaFile: MediaFile
    ) : super(localId, timestamp, mediaFile)

    private constructor(localId: Int,
                        sid: String,
                        timestamp: Date,
                        mediaFile: MediaFile,
                        messageIndex: Long) : super(localId, sid, timestamp, mediaFile, messageIndex)

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "video")
                put("local_id", localId)
                put("media_id", mediaFile.mediaId)
            } catch (e: JSONException) {
                logger.w(e, "failed to convert video message to json")
            }
        }
    }

    override fun messageByEndUser() = true

    val thumbnailFilename: String?
        get() = mediaFile.thumbnailFilename

    companion object {
        fun createPending(localId: Int,
                          timestamp: Date,
                          mediaFile: MediaFile): VideoChatMessage {
            return VideoChatMessage(localId, timestamp, mediaFile)
        }

        fun createSent(localId: Int,
                       sid: String,
                       timestamp: Date,
                       mediaFile: MediaFile,
                       messageIndex: Long): VideoChatMessage {
            return VideoChatMessage(localId, sid, timestamp, mediaFile, messageIndex)
        }
    }
}