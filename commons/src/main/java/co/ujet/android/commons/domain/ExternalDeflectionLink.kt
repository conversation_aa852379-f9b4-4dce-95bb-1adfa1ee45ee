package co.ujet.android.commons.domain

import androidx.annotation.Keep
import co.ujet.android.commons.libs.uson.SerializedName
import java.io.Serializable

data class ExternalDeflectionLink @Keep constructor(
    @SerializedName("name")
    val name: String? = null,

    @SerializedName("enabled")
    val enabled: Boolean = false,

    @SerializedName("deflection_type")
    val deflectionType: String? = null,

    @SerializedName("display_name")
    val displayName: String? = null,

    @SerializedName("url")
    val url: String? = null,

    @SerializedName("call_to_action")
    val callToAction: String? = null,

    @SerializedName("mobile_icon_id")
    val mobileIconId: String? = null,

    @SerializedName("web_icon")
    val webIcon: String? = null
) : Serializable