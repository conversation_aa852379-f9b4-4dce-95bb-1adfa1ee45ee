package co.ujet.android.commons.libs.graffiti.decoder

import android.graphics.Bitmap
import android.graphics.Bitmap.Config.ARGB_8888
import android.graphics.BitmapFactory
import android.graphics.BitmapFactory.Options
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff.Mode.SRC_IN
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.media.ExifInterface
import co.ujet.android.commons.BuildConfig
import co.ujet.android.commons.util.IOUtil
import java.io.File
import java.io.FileInputStream
import java.io.IOException

/**
 * https://developer.android.com/topic/performance/graphics/load-bitmap.html
 */
class BitmapDecoder(private val decodeOptions: DecodeOptions) {
    fun decode(file: File): Bitmap? {
        try {
            val fileInputStream = FileInputStream(file)
            val fileDescriptor = fileInputStream.fd
            val options = Options()
            if (decodeOptions.resizeWidth > 0 && decodeOptions.resizeHeight > 0) {
                options.inJustDecodeBounds = true
                BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)
                options.inSampleSize = calculateInSampleSize(options)
            }
            options.inJustDecodeBounds = false
            val bitmap = BitmapFactory.decodeFileDescriptor(fileDescriptor, null, options)
            IOUtil.closeQuietly(fileInputStream)
            return rotate(file, bitmap)
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    fun rotate(file: File, bitmap: Bitmap): Bitmap {
        val orientation = getOrientation(file)
        if (orientation == 0) {
            return bitmap
        }
        val matrix = Matrix()
        matrix.postRotate(orientation.toFloat())
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }

    private fun getOrientation(file: File): Int {
        val orientation = try {
            val exif = ExifInterface(file.path)
            val orientString = exif.getAttribute(ExifInterface.TAG_ORIENTATION)
            orientString?.toInt() ?: ExifInterface.ORIENTATION_NORMAL
        } catch (e: IOException) {
            if (BuildConfig.DEBUG) {
                e.printStackTrace()
            }
            return 0
        }
        return when (orientation) {
            ExifInterface.ORIENTATION_ROTATE_90 -> 90
            ExifInterface.ORIENTATION_ROTATE_180 -> 180
            ExifInterface.ORIENTATION_ROTATE_270 -> 270
            else -> 0
        }
    }

    private fun calculateInSampleSize(options: Options): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        if (height > decodeOptions.resizeHeight || width > decodeOptions.resizeWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            while (halfHeight / inSampleSize >= decodeOptions.resizeHeight && halfWidth / inSampleSize >= decodeOptions.resizeWidth) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    fun getRoundedCornerBitmap(bitmap: Bitmap, radius: Int): Bitmap {
        val output = Bitmap.createBitmap(bitmap.width, bitmap.height, ARGB_8888)
        val canvas = Canvas(output)

        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        canvas.drawRoundRect(rectF, radius.toFloat(), radius.toFloat(), paint)
        paint.xfermode = PorterDuffXfermode(SRC_IN)
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    fun getBitmapWithLimitedRoundedCorners(
        sourceBitmap: Bitmap,
        radius: Float
    ): Bitmap {
        val width = sourceBitmap.width
        val height = sourceBitmap.height

        val resultBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(resultBitmap)

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        val shader = BitmapShader(sourceBitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.shader = shader

        val path = Path()
        val rect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        // Leaves left / start part and applies rounded corners to rest of the rect canvas
        path.addRoundRect(rect, floatArrayOf(0f, 0f, radius, radius, radius, radius, radius, radius), Path.Direction.CW)
        canvas.drawPath(path, paint)
        return resultBitmap
    }
}
