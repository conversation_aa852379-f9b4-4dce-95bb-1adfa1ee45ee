package co.ujet.android.commons.libs.uson;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;
import org.json.JSONStringer;

import java.lang.ref.SoftReference;
import java.lang.reflect.Type;

import androidx.annotation.Nullable;
import co.ujet.android.commons.libs.uson.converter.TypeConverter;

/**
 * <p>
 * Supported features
 * - Serialize instance to json string
 * - Deserialize json string to instance
 * - Easy to use with annotation for field name
 * - Use TypeToken to specify Generic types (see below for the details)
 * <p>
 * Supported types
 * - int, Integer
 * - long, Long
 * - float, Float
 * - double, Double
 * - boolean, Boolean
 * - String
 * - Array
 * - Date (see below for the details)
 * - Enum
 * - List
 * - Map
 * - Custom classes
 * <p>
 * Important
 * - Set null to boxed type if value not exists, doesn't set default value
 * - Doesn't support to use non static inner class
 * - All classes should have zero parameter constructor
 * <p>
 * Field name annotation
 *
 * @see SerializedName
 * <p>
 * TypeToken
 * - To specify generic types
 * - Must pass as instantiated TypeToken likes new TypeToken<List<Integer>>() {}
 * @see TypeToken
 * <p>
 * Transform between String and Date
 */

public class Uson implements Serializer {

    private static SoftReference<Uson> reference;

    public static Uson getInstance() {
        Uson instance = reference != null ? reference.get() : null;
        if (instance == null) {
            instance = new Uson();
            reference = new SoftReference<>(instance);
        }
        return instance;
    }

    private final TypeConverters typeConverters;

    public Uson() {
        typeConverters = new TypeConverters(new ClassMetadataCache());
    }

    @Override
    public String serialize(Object obj) {
        return toJson(obj);
    }

    @Override
    public <T> String serialize(Object obj, Class<T> cls) {
        return toJson(obj, cls);
    }

    @Override
    public <T> String serialize(T obj, TypeToken<T> typeToken) {
        return toJson(obj, typeToken);
    }

    @Nullable
    @Override
    public <T> T deserialize(String str, Class<T> cls) {
        return fromJson(str, cls);
    }

    @Nullable
    @Override
    public <T> T deserialize(String json, TypeToken<T> typeToken) {
        return fromJson(json, typeToken);
    }

    @Nullable
    public <T> T fromJson(String json, Class<T> cls) throws UsonException {
        return fromJson(json, new TypeToken<T>(cls));
    }

    @Nullable
    public <T> T fromJson(String json, TypeToken<T> typeToken) {
        return fromJson((Object) json, typeToken);
    }

    @Nullable
    private <T> T fromJson(Object object, TypeToken<T> typeToken) throws UsonException {
        if (object == null)
            return null;

        TypeConverter<T> typeConverter = typeConverters.getConverter(typeToken);
        return typeConverter.convert(object);
    }

    public String toJson(Object obj) throws UsonException {
        return toJson(obj, new TypeToken<>(obj.getClass()));
    }

    public <T> String toJson(Object obj, Class<T> cls) throws UsonException {
        return toJson(obj, new TypeToken<>(cls));
    }

    public <T> String toJson(T obj, TypeToken<T> typeToken) throws UsonException {
        if (obj == null)
            return JSONObject.NULL.toString();

        JSONStringer stringer = new JSONStringer();

        TypeConverter<T> typeConverter = typeConverters.getConverter(typeToken);
        typeConverter.toJson(obj, stringer);

        return stringer.toString();
    }

    @NotNull
    public <T> Object fromJson(@NotNull String json, Type type) {
        TypeToken<T> typeToken = new TypeToken<>(type);
        TypeConverter<T> typeConverter = typeConverters.getConverter(typeToken);
        return typeConverter.convert(json);
    }
}
