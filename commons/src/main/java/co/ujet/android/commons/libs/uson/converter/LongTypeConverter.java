package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class LongTypeConverter implements TypeConverter<Long> {
    private Long defaultValue;

    public  LongTypeConverter(Long defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Long convert(Object value) {
        if (value instanceof Long)
            return (Long) value;

        if (value instanceof Number)
            return ((Number) value).longValue();

        if (value instanceof String) {
            try {
                return Long.valueOf((String) value);
            } catch (NumberFormatException ignore) {
            }
        }

        return defaultValue;
    }

    @Override
    public void toJson(Object value, J<PERSON>NStringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof Number) {
                stringer.value(((Number) value).longValue());

            }  else if (value instanceof String) {
                stringer.value(Long.parseLong((String) value));

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
