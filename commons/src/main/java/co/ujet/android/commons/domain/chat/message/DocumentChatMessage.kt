package co.ujet.android.commons.domain.chat.message

import androidx.annotation.DrawableRes
import co.ujet.android.commons.R
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.MediaFile.Type
import org.json.JSONException
import org.json.JSONObject
import java.util.Date

abstract class DocumentChatMessage : MediaChatMessage {
    constructor(localId: Int, timestamp: Date, uploadMedia: MediaFile) : super(
        localId,
        timestamp,
        uploadMedia
    )

    constructor(
        localId: Int,
        sid: String,
        timestamp: Date,
        uploadMedia: MediaFile,
        messageIndex: Long
    ) : super(localId, sid, timestamp, uploadMedia, messageIndex)

    constructor(
        localId: Int,
        sid: String,
        timestamp: Date,
        uploadMedia: MediaFile,
        messageIndex: Long,
        groupMessageId: Int?
    ) : super(localId, sid, timestamp, uploadMedia, messageIndex, groupMessageId)

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", mediaFile.type)
                put("local_id", localId)
                put("media_id", mediaFile.mediaId)
            } catch (e: JSONException) {
                logger.w(e, "failed convert document message to json")
            }
        }
    }

    @DrawableRes
    fun getThumbnail(): Int {
        return when (mediaFile.type) {
            Type.Audio -> R.drawable.ujet_file_audio
            Type.Doc -> R.drawable.ujet_file_doc
            Type.Excel -> R.drawable.ujet_file_excel
            Type.PDF -> R.drawable.ujet_file_pdf
            Type.PPT -> R.drawable.ujet_file_ppt
            Type.Video -> R.drawable.ujet_file_video
            Type.TXT, Type.CSV -> R.drawable.ujet_file_txt
            else -> R.drawable.ujet_file_generic
        }
    }
}
