package co.ujet.android.commons.libs.graffiti.loader

import android.content.Context
import co.ujet.android.commons.libs.graffiti.GraffitiUtil
import co.ujet.android.commons.util.FileUtil
import java.io.File
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL

class UrlLoader(private val context: Context?, private val urlString: String?, private val cacheKey: String?) : ImageLoader {
    override fun load(): File? {
        val compressFormat = GraffitiUtil.getCompressFormat(urlString ?: return null)
        val outFile = GraffitiUtil.resolveFile(context?.cacheDir, cacheKey, compressFormat)
        var urlConnection: HttpURLConnection? = null
        try {
            val url = URL(urlString)
            urlConnection = url.openConnection() as HttpURLConnection
            urlConnection.inputStream.use {
                FileUtil.copyInputStreamToFile(it, outFile)
            }
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        } finally {
            urlConnection?.disconnect()
        }
        return if (outFile.exists()) {
            outFile
        } else {
            null
        }
    }
}
