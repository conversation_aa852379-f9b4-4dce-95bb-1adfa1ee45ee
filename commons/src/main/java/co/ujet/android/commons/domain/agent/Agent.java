package co.ujet.android.commons.domain.agent;

import androidx.annotation.VisibleForTesting;
import co.ujet.android.commons.libs.uson.SerializedName;

public class Agent {
    @SerializedName("id")
    private int id;

    @SerializedName("name")
    private String name;

    @SerializedName("avatar_url")
    private String avatarUrl;

    @SerializedName("first_name")
    private String firstName;

    @SerializedName("joined_timestamp")
    private long joinTime;

    public Agent() {
    }

    public Agent(int id, String name, String avatarUrl) {
        this.id = id;
        this.name = name;
        this.firstName = name;
        this.avatarUrl = avatarUrl;
    }

    public int getId() {
        return id;
    }

    @VisibleForTesting
    public String getName() {
        return name;
    }

    @VisibleForTesting
    public String getFirstName() {
        return firstName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public String getDisplayName() {
        return firstName != null ? firstName : name;
    }

    public long getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(long joinTime) {
        this.joinTime = joinTime;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Agent))
            return false;

        Agent that = (Agent) obj;
        return id == that.id &&
                (name == null && that.name == null || name != null && name.equals(that.name)) &&
                (firstName == null && that.firstName == null || firstName != null && firstName.equals(that.firstName)) &&
                (avatarUrl == null && that.avatarUrl == null || avatarUrl != null && avatarUrl.equals(that.avatarUrl));
    }
}
