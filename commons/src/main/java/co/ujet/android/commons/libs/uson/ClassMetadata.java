package co.ujet.android.commons.libs.uson;

import java.lang.reflect.Field;
import java.util.ArrayList;

public class ClassMetadata {
    private ArrayList<Field> fields;
    private ArrayList<String> serializedNames;

    void add(Field field, String serializedName) {
        if (fields == null) {
            fields = new ArrayList<>();
            serializedNames = new ArrayList<>();
        }

        fields.add(field);
        serializedNames.add(serializedName);
    }

    public Field getField(int index) {
        return fields.get(index);
    }

    public String getSerializedName(int index) {
        return serializedNames.get(index);
    }

    public int size() {
        return fields == null ? 0 : fields.size();
    }
}
