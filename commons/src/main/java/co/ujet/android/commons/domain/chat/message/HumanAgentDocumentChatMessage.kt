package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.Agent
import java.util.Date

class HumanAgentDocumentChatMessage private constructor(
    localId: Int,
    sid: String,
    timestamp: Date,
    uploadMedia: MediaFile,
    private val agent: Agent?,
    messageIndex: Long,
    groupMessageId: Int?
) : DocumentChatMessage(localId, sid, timestamp, uploadMedia, messageIndex, groupMessageId) {

    var agentAvatarUrl = agent?.avatarUrl

    var agentName = agent?.displayName

    override fun getSenderAgent() = agent

    companion object {
        fun createSent(
            localId: Int,
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            agent: Agent?,
            messageIndex: Long,
            groupMessageId: Int?
        ): HumanAgentDocumentChatMessage {
            return HumanAgentDocumentChatMessage(
                localId,
                sid,
                timestamp,
                mediaFile,
                agent,
                messageIndex,
                groupMessageId
            )
        }
    }
}
