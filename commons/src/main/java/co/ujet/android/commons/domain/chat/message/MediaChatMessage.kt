package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import co.ujet.android.commons.util.Preconditions
import java.util.*

abstract class MediaChatMessage : SendableChatMessage {
    val mediaFile: MediaFile

    internal constructor(localId: Int,
                         timestamp: Date,
                         mediaFile: MediaFile) : super(localId, timestamp) {
        this.mediaFile = Preconditions.checkNotNull(mediaFile)
    }

    internal constructor(localId: Int,
                         sid: String,
                         timestamp: Date,
                         mediaFile: MediaFile,
                         messageIndex: Long) : super(localId, sid, timestamp, messageIndex) {
        this.mediaFile = Preconditions.checkNotNull(mediaFile)
    }

    internal constructor(localId: Int,
                         sid: String,
                         timestamp: Date,
                         mediaFile: MediaFile,
                         messageIndex: Long,
                         groupMessageId: Int?) : super(localId, sid, timestamp, messageIndex, groupMessageId) {
        this.mediaFile = Preconditions.checkNotNull(mediaFile)
    }
}
