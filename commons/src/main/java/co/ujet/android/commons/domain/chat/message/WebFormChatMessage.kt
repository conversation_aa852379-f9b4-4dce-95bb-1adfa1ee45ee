package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.chat.WebForm
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class WebFormChatMessage private constructor(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    val webForm: WebForm,
    val memberIdentity: String,
    messageIndex: Long
) : ChatMessage(localId, sid, timestamp, messageIndex) {

    companion object {
        fun createSent(
            @IntRange(from = 1) localId: Int,
            sid: String,
            timestamp: Date,
            webForm: WebForm,
            memberIdentity: String,
            messageIndex: Long
        ): WebFormChatMessage {
            return WebFormChatMessage(
                localId,
                sid,
                timestamp,
                webForm,
                memberIdentity,
                messageIndex
            )
        }
    }
}
