package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import org.json.JSONException
import org.json.JSONObject
import java.util.*

class SendableNotificationMessage(
    localId: Int,
    timestamp: Date,
    private val event: String,
    private val detail: JSONObject? = null
) : SendableChatMessage(localId, timestamp) {

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "noti")
                put("event", event)
                if (detail != null) {
                    put("detail", detail)
                }
            } catch (e: JSONException) {
                logger.w(e, "failed convert notification message to json")
            }
        }
    }
}
