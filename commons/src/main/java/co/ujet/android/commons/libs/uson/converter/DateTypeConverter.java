package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONStringer;

import java.util.Date;

import co.ujet.android.commons.util.TimeUtil;

public class DateTypeConverter implements TypeConverter<Date> {
    private static final String DATE_KEY_NAME = "date";

    @Override
    public Date convert(Object value) {
        if (value instanceof Date)
            return (Date) value;

        JSONObject jsonObject = null;
        if (value instanceof String) {
            Date date = TimeUtil.INSTANCE.parseTime((String) value);
            if (date != null)
                return date;

            try {
                jsonObject = new JSONObject((String) value);
            } catch (JSONException e) {
                e.printStackTrace();
            }

        } else if (value instanceof JSONObject) {
            jsonObject = (JSONObject) value;
        }

        if (jsonObject == null)
            return null;

        return TimeUtil.INSTANCE.parseTime(jsonObject.optString(DATE_KEY_NAME));
    }

    @Override
    public void toJson(Object value, JSO<PERSON>tringer stringer) {
        try {
            stringer.object();
            stringer.key(DATE_KEY_NAME);

            if (value == null) {
                stringer.value(JSONObject.NULL);

            } else if (value instanceof Date) {
                stringer.value(TimeUtil.INSTANCE.getUjetServerFormat((Date) value));

            } else if (value instanceof String) {
                stringer.value(value);

            } else {
                stringer.value(JSONObject.NULL);
            }

            stringer.endObject();

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
