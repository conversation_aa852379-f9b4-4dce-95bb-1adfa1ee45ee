package co.ujet.android.commons.domain

import android.os.Parcel
import android.os.Parcelable
import co.ujet.android.commons.libs.uson.SerializedName
import java.io.File

class MediaFile() : Parcelable {
    enum class Type {
        Photo, Video, Screenshot, Audio, Doc, Excel, PDF, PPT, TXT, CSV, Generic
    }

    enum class Status {
        Selected, Pending, Uploading, Uploaded, Failed
    }

    @SerializedName("type")
    var type: Type? = null
        private set

    @SerializedName("filename")
    var filename: String? = null
        private set

    // When media (photos/videos) attached from library in email screen, original file name is not
    // pronounced by talkback because we convert them to temp files with random temp file name so
    // saving original file name here and to be used for accessibility purpose only in email screen.
    @SerializedName("original_filename")
    var originalFilename: String? = null
        private set

    @SerializedName("url")
    var url: String? = null
        private set

    @SerializedName("thumbnail_filename")
    var thumbnailFilename: String? = null
    private set

    @SerializedName("thumbnail_url")
    var thumbnailUrl: String? = null
    private set

    @SerializedName("status")
    private var status: Status? = null

    @SerializedName("media_id")
    var mediaId: Int? = null

    @SerializedName("local_id")
    var localId: Int? = null
        private set

    @SerializedName("duration")
    var duration: String? = null

    @SerializedName("cache_file_path")
    var cacheFilePath: String? = null

    val file: File?
        get() {
            val file = File(filename ?: return null)
            return if (file.isFile) {
                file
            } else {
                null
            }
        }

    constructor(parcel: Parcel) : this() {
        filename = parcel.readString()
        mediaId = parcel.readValue(Int::class.java.classLoader) as? Int
        duration = parcel.readString()
    }

    fun getStatus(): Status? {
        return status
    }

    fun setStatus(status: Status?) {
        this.status = status
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<MediaFile> = object : Parcelable.Creator<MediaFile> {
            override fun createFromParcel(parcel: Parcel): MediaFile {
                return MediaFile(parcel)
            }

            override fun newArray(size: Int): Array<MediaFile?> {
                return arrayOfNulls(size)
            }
        }

        @JvmStatic
        fun create(localId: Int, type: Type, status: Status, filename: String, originalFilename: String?,
                   thumbnailFilename: String?): MediaFile {
            return MediaFile().apply {
                this.localId = localId
                this.type = type
                this.status = status
                this.filename = filename
                this.originalFilename = originalFilename
                this.thumbnailFilename = thumbnailFilename
            }
        }

        @JvmStatic
        fun createRemote(localId: Int, type: Type, status: Status, filename: String?, url: String? = null): MediaFile {
            return MediaFile().apply {
                this.localId = localId
                this.type = type
                this.status = status
                this.filename = filename
                this.url = url
            }
        }
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(filename)
        parcel.writeValue(mediaId)
        parcel.writeString(duration)
    }

    override fun describeContents(): Int {
        return 0
    }
}
