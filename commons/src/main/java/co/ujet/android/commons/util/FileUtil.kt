package co.ujet.android.commons.util

import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat.JPEG
import co.ujet.android.modulemanager.entrypoints.log.Logger
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.text.DecimalFormat

object FileUtil {

    private const val BYTES_FILE_SIZE = 1024

    @JvmStatic
    fun saveTempImage(outputDir: File?, filename: String?, bitmap: Bitmap): String? {
        var fOut: OutputStream? = null
        try {
            val cacheFile = File.createTempFile(filename, ".jpg", outputDir)
            fOut = FileOutputStream(cacheFile)
            bitmap.compress(JPEG, 85, fOut)
            fOut.flush()
            return cacheFile.absolutePath
        } catch (e: IOException) {
            Logger.w(e, "Failed to save a temp image file")
        } catch (e: IllegalStateException) {
            Logger.w(e, "Failed to save a temp image file")
        } finally {
            IOUtil.closeQuietly(fOut)
        }
        return null
    }

    @JvmStatic
    @Throws(IOException::class)
    fun copyInputStreamToFile(`in`: InputStream, file: File) {
        val out: OutputStream = FileOutputStream(file)
        try {
            val buf = ByteArray(BYTES_FILE_SIZE)
            var len: Int
            while (`in`.read(buf).also { len = it } > 0) {
                out.write(buf, 0, len)
            }
            out.close()
        } catch (ex: IOException) {
            if (!file.delete()) {
                Logger.d("Failed to delete %s", file.absolutePath)
            }
            throw ex
        }
        `in`.close()
    }

    @JvmStatic
    fun removeFile(filename: String?): Boolean {
        if (filename == null) {
            return false
        }
        val file = File(filename)
        if (file.isFile) {
            Logger.d("Delete file %s", file.absolutePath)
            return file.delete()
        }
        return false
    }

    @JvmStatic
    fun getFileSize(filename: String?): Long {
        if (filename == null) {
            return -1
        }
        val file = File(filename)
        if (file.isFile) {
            Logger.d("delete [%s]", file.absolutePath)
            return file.length()
        }
        return -1
    }

    @JvmStatic
    fun exists(filename: String?): Boolean {
        return filename != null && File(filename).isFile
    }

    @JvmStatic
    fun isFileInDir(filename: String?, dir: File?): Boolean {
        if (filename == null || dir == null) return false
        val file = File(filename)
        return dir.isDirectory &&
                file.exists() && file.isFile &&
                file.absolutePath.startsWith(dir.absolutePath)
    }

    @JvmStatic
    fun getFileSizeInMB(fileSize: Long): Double {
        return Math.round(fileSize.toDouble() / (1000 * 1000) * 10.0) / 10.0
    }

    fun formatFileSize(file: File): String {
        val fileSizeInBytes = file.length().toDouble()
        val fileSizeInKB = fileSizeInBytes / BYTES_FILE_SIZE
        val fileSizeInMB = fileSizeInKB / BYTES_FILE_SIZE

        val decimalFormat = DecimalFormat("#.#")

        return when {
            fileSizeInMB >= 1 -> "${decimalFormat.format(fileSizeInMB)} MB"
            fileSizeInKB >= 1 -> "${decimalFormat.format(fileSizeInKB)} KB"
            else -> "$fileSizeInBytes Bytes"
        }
    }
}
