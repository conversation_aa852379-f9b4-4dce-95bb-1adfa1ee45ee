package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class HumanAgentChatMessage(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    private val message: String?,
    private val agent: Agent?,
    messageIndex: Long,
    groupMessageId: Int?,
    val isMarkDownSupported: Boolean = false,
) : ChatMessage(localId, sid, timestamp, messageIndex, groupMessageId) {

    var agentId = agent?.id ?: 0
    var agentName = agent?.displayName
    var agentAvatarUrl = agent?.avatarUrl

    fun getMessage() = message ?: ""

    override fun getSenderAgent() = agent
}
