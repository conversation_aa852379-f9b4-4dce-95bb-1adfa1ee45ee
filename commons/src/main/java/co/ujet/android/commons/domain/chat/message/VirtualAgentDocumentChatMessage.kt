package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.VirtualAgent
import java.util.Date

class VirtualAgentDocumentChatMessage private constructor(
    localId: Int,
    sid: String,
    timestamp: Date,
    uploadMedia: MediaFile,
    private val virtualAgent: VirtualAgent?,
    messageIndex: Long
) : DocumentChatMessage(localId, sid, timestamp, uploadMedia, messageIndex) {

    fun getAgentAvatarUrl() = virtualAgent?.avatarUrl

    var agentName = virtualAgent?.displayName

    override fun getSenderAgent() = virtualAgent

    companion object {
        fun createSent(
            localId: Int,
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            virtualAgent: VirtualAgent?,
            messageIndex: Long
        ): VirtualAgentDocumentChatMessage {
            return VirtualAgentDocumentChatMessage(
                localId,
                sid,
                timestamp,
                mediaFile,
                virtualAgent,
                messageIndex
            )
        }
    }
}
