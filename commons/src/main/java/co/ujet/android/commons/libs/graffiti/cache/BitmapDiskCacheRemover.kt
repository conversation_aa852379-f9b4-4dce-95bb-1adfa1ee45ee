package co.ujet.android.commons.libs.graffiti.cache

import android.content.Context
import android.graphics.Bitmap
import co.ujet.android.commons.libs.graffiti.GraffitiUtil

class BitmapDiskCacheRemover(private val context: Context?, private val source: String?, private val cacheKey: String?) : BitmapCache {
    override fun cache(bitmap: Bitmap): <PERSON>olean {
        val compressFormat = GraffitiUtil.getCompressFormat(source ?: return false)
        val file = GraffitiUtil.resolveFile(context?.cacheDir, cacheKey, compressFormat)
        return file.exists() && file.delete()
    }
}
