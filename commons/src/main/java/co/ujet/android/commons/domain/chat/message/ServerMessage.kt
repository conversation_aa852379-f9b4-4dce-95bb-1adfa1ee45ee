package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.chat.message.base.SendableChatMessage
import org.json.JSONException
import org.json.JSONObject
import java.util.Date

class ServerMessage(messageId: Int, localId: Int, timestamp: Date) : SendableChatMessage(localId, timestamp){
    private var messageId: Int? = messageId

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "server_message")
                put("visibility", "task_virtual_agent")
                put("message_id", messageId)
            } catch (e: JSONException) {
                logger.w(e, "failed convert task va message to json")
            }
        }
    }
}
