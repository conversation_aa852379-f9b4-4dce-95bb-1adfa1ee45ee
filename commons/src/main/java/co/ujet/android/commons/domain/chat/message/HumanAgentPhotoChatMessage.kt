package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class HumanAgentPhotoChatMessage private constructor(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    var mediaFiles: ArrayList<MediaFile>,
    private val agent: Agent?,
    messageIndex: Long,
    groupMessageId: Int?,
    var isVisible: Boolean
) : ChatMessage(localId, sid, timestamp, messageIndex, groupMessageId) {

    var agentName = agent?.displayName
    var agentAvatarUrl = agent?.avatarUrl

    override fun getSenderAgent() = agent

    companion object {
        fun createSent(
            @IntRange(from = 1) localId: Int,
            sid: String,
            timestamp: Date,
            mediaFiles: ArrayList<MediaFile>,
            agent: Agent?,
            messageIndex: Long,
            groupMessageId: Int?,
            isVisible: Boolean
        ): HumanAgentPhotoChatMessage {
            return HumanAgentPhotoChatMessage(
                localId,
                sid,
                timestamp,
                mediaFiles,
                agent,
                messageIndex,
                groupMessageId,
                isVisible
            )
        }
    }
}
