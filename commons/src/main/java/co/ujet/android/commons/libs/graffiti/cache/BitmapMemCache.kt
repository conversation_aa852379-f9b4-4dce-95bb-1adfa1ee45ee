package co.ujet.android.commons.libs.graffiti.cache

import android.graphics.Bitmap
import android.util.LruCache
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * https://developer.android.com/topic/performance/graphics/cache-bitmap.html
 */
class BitmapMemCache(private val cacheKey: String) : BitmapCache {
    companion object {
        private val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        private val cacheSize: Int = maxMemory / 8
        private val LRU_CACHE = object : LruCache<String, Bitmap>(cacheSize) {
            override fun sizeOf(key: String?, bitmap: Bitmap): Int {
                return bitmap.byteCount / 1024
            }
        }
        private val lock = ReentrantReadWriteLock()

        fun getCache(cacheKey: String): Bitmap? {
            return lock.read {
                LRU_CACHE[cacheKey]
            }
        }

        fun remove(cacheKey: String): Bitmap? {
            return lock.write {
                LRU_CACHE.remove(cacheKey)
            }
        }

        fun clear() {
            lock.write {
                LRU_CACHE.evictAll()
            }
        }
    }

    override fun cache(bitmap: Bitmap): Boolean {
        lock.write {
            LRU_CACHE.put(cacheKey, bitmap)
        }
        return true
    }
}
