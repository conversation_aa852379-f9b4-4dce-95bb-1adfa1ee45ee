package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class CharacterTypeConverter implements TypeConverter<Character> {
    private Character defaultValue;

    public CharacterTypeConverter(Character defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Character convert(Object value) {
        if (value instanceof Character)
            return (Character) value;

        if (value instanceof String) {
            String str = (String) value;
            if (str.length() == 1)
                return str.charAt(0);
        }

        return defaultValue;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof Character) {
                stringer.value(value.toString());

            } else if (value.getClass() == char.class) {
                stringer.value(String.valueOf(value));

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
