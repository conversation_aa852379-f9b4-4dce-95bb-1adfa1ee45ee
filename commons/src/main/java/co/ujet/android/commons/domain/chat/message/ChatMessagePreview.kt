package co.ujet.android.commons.domain.chat.message

import android.annotation.SuppressLint
import co.ujet.android.modulemanager.EntryPointFactory
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage
import co.ujet.android.modulemanager.entrypoints.log.Logger
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

data class ChatMessagePreview(val message: String, val timestamp: String) : ChatMessage {
    private val logger = EntryPointFactory.provideEntryPoint(Logger::class.java)

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("chat_preview", message)
                put("chat_preview_updated_at", timestamp)
            } catch (e: JSONException) {
                logger.w(e, "failed convert message preview to json")
            }
        }
    }

    override fun toMap(): HashMap<String, Any> {
        return hashMapOf(
            "chat_preview" to message,
            "chat_preview_updated_at" to timestamp
        )
    }

    companion object {
        fun from(message: String) = ChatMessagePreview(message, dateFormat.format(Calendar.getInstance().time))

        @SuppressLint("SimpleDateFormat")
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.sss'Z'").apply {
            timeZone = TimeZone.getTimeZone("GMT")
        }
    }
}
