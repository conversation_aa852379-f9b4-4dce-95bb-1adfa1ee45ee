package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.MediaFile
import co.ujet.android.commons.domain.agent.VirtualAgent
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class VirtualAgentPhotoChatMessage private constructor(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    private var mediaFile: MediaFile,
    private val virtualAgent: VirtualAgent?,
    messageIndex: Long
) : ChatMessage(localId, sid, timestamp, messageIndex) {

    val photoUrl: String?
        get() = mediaFile.url

    fun getAgentAvatarUrl() = virtualAgent?.avatarUrl

    var agentName = virtualAgent?.displayName

    override fun getSenderAgent() = virtualAgent

    companion object {
        fun createSent(
            @IntRange(from = 1) localId: Int,
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            virtualAgent: VirtualAgent?,
            messageIndex: Long
        ): VirtualAgentPhotoChatMessage {
            return VirtualAgentPhotoChatMessage(
                localId,
                sid,
                timestamp,
                mediaFile,
                virtualAgent,
                messageIndex
            )
        }
    }
}
