package co.ujet.android.commons.libs.graffiti.misc

import android.widget.ImageView
import androidx.annotation.DrawableRes
import co.ujet.android.commons.libs.graffiti.BitmapListener

interface GraffitiChaining {
    fun fallback(@DrawableRes resId: Int): GraffitiChaining
    fun resize(width: Int, height: Int): GraffitiChaining
    fun listener(listener: BitmapListener?): GraffitiChaining
    fun memCache(use: <PERSON><PERSON>an): GraffitiChaining
    fun diskCache(use: Boolean): GraffitiChaining
    fun radius(radius: Int): GraffitiChaining
    fun into(imageView: ImageView)
    operator fun get(listener: BitmapListener)
}
