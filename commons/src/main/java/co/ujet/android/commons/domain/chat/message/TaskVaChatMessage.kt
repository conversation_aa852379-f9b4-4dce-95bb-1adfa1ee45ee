package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.libs.uson.SerializedName

class TaskVaChatMessage {

    @SerializedName("task_va_id")
    val taskVaId: Int? = null

    @SerializedName("participant_id")
    val participantId: Int? = null

    @SerializedName("id")
    val id: Int? = null

    @SerializedName("content")
    val taskVaMessageContent: ChatMessageContent? = null

}
