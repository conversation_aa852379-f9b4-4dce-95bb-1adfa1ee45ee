package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class FloatTypeConverter implements TypeConverter<Float> {
    private Float defaultValue;

    public FloatTypeConverter(Float defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public Float convert(Object value) {
        if (value instanceof Float)
            return (Float) value;

        if (value instanceof Number)
            return ((Number) value).floatValue();

        if (value instanceof String) {
            try {
                return Float.valueOf((String) value);
            } catch (NumberFormatException ignore) {
            }
        }

        return null;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null) {
                stringer.value(defaultValue);

            } else if (value instanceof Number) {
                stringer.value(((Number) value).floatValue());

            } else {
                stringer.value(defaultValue);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
