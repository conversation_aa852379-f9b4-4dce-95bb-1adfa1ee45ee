package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import java.util.Date

class EndUserDocumentChatMessage : DocumentChatMessage {
    private constructor(localId: Int, timestamp: Date, uploadMedia: MediaFile) : super(
        localId,
        timestamp,
        uploadMedia
    )

    private constructor(
        localId: Int,
        sid: String,
        timestamp: Date,
        uploadMedia: MediaFile,
        messageIndex: Long
    ) : super(localId, sid, timestamp, uploadMedia, messageIndex)

    override fun messageByEndUser() = true

    companion object {
        fun createPending(localId: Int, timestamp: Date, mediaFile: MediaFile) =
            EndUserDocumentChatMessage(localId, timestamp, mediaFile)

        fun createSent(
            localId: Int,
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            messageIndex: Long
        ): EndUserDocumentChatMessage {
            return EndUserDocumentChatMessage(localId, sid, timestamp, mediaFile, messageIndex)
        }
    }
}
