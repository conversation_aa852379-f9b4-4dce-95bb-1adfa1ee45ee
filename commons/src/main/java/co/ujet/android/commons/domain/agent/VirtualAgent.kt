package co.ujet.android.commons.domain.agent

import co.ujet.android.commons.libs.uson.SerializedName

// An empty marker class for virtual agents
class VirtualAgent : Agent {
    @SerializedName("status")
    var status: String = ""
    constructor()
    constructor(id: Int, name: String, avatarUrl: String, status: String = "") : super(
        id,
        name,
        avatarUrl
    ) {
        this.status = status
    }
}
