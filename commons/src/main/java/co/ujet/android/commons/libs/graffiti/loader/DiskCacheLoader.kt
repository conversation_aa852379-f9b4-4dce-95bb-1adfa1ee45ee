package co.ujet.android.commons.libs.graffiti.loader

import android.content.Context
import co.ujet.android.commons.libs.graffiti.GraffitiUtil
import java.io.File

class DiskCacheLoader(private val context: Context?, private val source: String?, private val cacheKey: String?) : ImageLoader {
    override fun load(): File? {
        val compressFormat = GraffitiUtil.getCompressFormat(source ?: return null)
        val file = GraffitiUtil.resolveFile(context?.cacheDir, cacheKey, compressFormat)
        return if (file.exists()) {
            file
        } else {
            null
        }
    }
}
