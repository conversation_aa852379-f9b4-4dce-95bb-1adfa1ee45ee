package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONStringer;

public class ObjectTypeConverter implements TypeConverter<Object> {
    @Override
    public Object convert(Object value) {
        return value;
    }

    @Override
    public void toJson(Object value, J<PERSON><PERSON>tringer stringer) {
        try {
            if (value == null) {
                stringer.value(null);

            } else {
                stringer.value(value);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
