package co.ujet.android.commons.domain.chat.message.base

import java.util.*
import co.ujet.android.modulemanager.entrypoints.chat.ChatMessage as IChatMessage

abstract class SendableChatMessage : ChatMessage, IChatMessage {
    constructor()
    constructor(localId: Int, sid: String, timestamp: Date, messageIndex: Long) :
            super(localId, sid, timestamp, messageIndex)
    constructor(localId: Int, sid: String, timestamp: Date, messageIndex: Long, groupMessageId: Int?) :
            super(localId, sid, timestamp, messageIndex, groupMessageId)
    constructor(localId: Int, timestamp: Date) : super(localId, timestamp)
}
