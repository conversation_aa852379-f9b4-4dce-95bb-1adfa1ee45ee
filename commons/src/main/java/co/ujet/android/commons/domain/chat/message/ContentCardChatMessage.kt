package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.Date

class ContentCardChatMessage private constructor(
    @IntRange(from = 1) localId: Int,
    sid: String,
    timestamp: Date,
    val contentCards: List<ContentCard>,
    val memberIdentity: String,
    messageIndex: Long
) : ChatMessage(localId, sid, timestamp, messageIndex) {

    companion object {
        fun createSent(
            @IntRange(from = 1) localId: Int,
            sid: String,
            timestamp: Date,
            contentCards: List<ContentCard>,
            memberIdentity: String,
            messageIndex: Long
        ): ContentCardChatMessage {
            return ContentCardChatMessage(
                localId,
                sid,
                timestamp,
                contentCards,
                memberIdentity,
                messageIndex
            )
        }
    }
}
