package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONStringer;

import java.lang.reflect.Array;

import co.ujet.android.commons.libs.uson.TypeConverters;
import co.ujet.android.commons.libs.uson.TypeToken;
import co.ujet.android.commons.libs.uson.UsonUtil;

public class ArrayTypeConverter<T> implements TypeConverter<T> {
    private TypeToken<T> typeToken;
    private TypeConverters typeConverters;

    public ArrayTypeConverter(TypeToken<T> typeToken, TypeConverters typeConverters) {
        this.typeToken = typeToken;
        this.typeConverters = typeConverters;
    }

    @Override
    public T convert(Object value) {
        JSONArray jsonArray = UsonUtil.toJsonArray(value);
        if (jsonArray == null)
            return null;

        Class<?> componentType = UsonUtil.getArrayComponentRawType(typeToken);
        TypeToken componentTypeToken = new TypeToken(componentType);

        T array = (T) Array.newInstance(componentType, jsonArray.length());
        for (int index = 0; index < jsonArray.length(); ++index) {
            Object element = jsonArray.opt(index);

            TypeConverter typeConverter = typeConverters.getConverter(componentTypeToken);
            if (typeConverter != null) {
                Array.set(array, index, typeConverter.convert(element));

            } else {
                Array.set(array, index, element);
            }
        }

        return array;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            TypeToken componentTypeToken = new TypeToken<>(UsonUtil.getArrayComponentType(typeToken));

            stringer.array();
            int length = Array.getLength(value);
            for (int index = 0; index < length; ++index) {
                typeConverters.getConverter(componentTypeToken)
                        .toJson(Array.get(value, index), stringer);
            }
            stringer.endArray();

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
