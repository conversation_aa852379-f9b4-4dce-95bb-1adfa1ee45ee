package co.ujet.android.commons.libs.uson.converter;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONStringer;

import java.lang.reflect.Type;
import java.util.Iterator;
import java.util.Map;

import co.ujet.android.commons.libs.uson.TypeConverters;
import co.ujet.android.commons.libs.uson.TypeToken;
import co.ujet.android.commons.libs.uson.UsonUtil;

public class MapTypeConverter<T extends Map> implements TypeConverter<T> {
    private TypeToken<T> typeToken;
    private TypeConverters typeConverters;

    public MapTypeConverter(TypeToken<T> typeToken, TypeConverters typeConverters) {
        this.typeToken = typeToken;
        this.typeConverters = typeConverters;
    }

    @Override
    public T convert(Object value) {
        JSONObject jsonObject = UsonUtil.toJsonObject(value);
        if (jsonObject == null)
            return null;

        T map = UsonUtil.newInstance(typeToken);
        if (map == null)
            return null;

        Type keyType = UsonUtil.getMapKeyType(typeToken);
        Type valueType = UsonUtil.getMapValueType(typeToken);

        Class keyRawType = UsonUtil.getRawType(keyType);
        Class valueRawType = UsonUtil.getRawType(valueType);

        Iterator<String> keyIterator = jsonObject.keys();
        while (keyIterator.hasNext()) {
            Object _key = keyIterator.next();
            Object _value = jsonObject.opt((String) _key);

            _key = typeConverters.getConverter(keyRawType).convert(_key);
            _value = typeConverters.getConverter(valueRawType).convert(_value);

            map.put(_key, _value);
        }

        return map;
    }

    @Override
    public void toJson(Object value, JSONStringer stringer) {
        try {
            if (value == null) {
                stringer.value(null);

            } else if (typeToken.getRawType().isInstance(value)) {
                TypeToken keyTypeToken = new TypeToken(UsonUtil.getMapKeyType(typeToken));
                TypeToken valueTypeToken = new TypeToken(UsonUtil.getMapValueType(typeToken));

                mapToJson((Map) value, keyTypeToken, valueTypeToken, stringer);
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private <K, V> void mapToJson(Map<K, V> map, TypeToken<K> keyTypeToken, TypeToken<V> valueTypeToken, JSONStringer stringer) throws JSONException {
        stringer.object();
        for (Map.Entry<K, V> entry : map.entrySet()) {
            stringer.key(entry.getKey().toString());
            typeConverters.getConverter(valueTypeToken)
                    .toJson(entry.getValue(), stringer);
        }
        stringer.endObject();
    }
}
