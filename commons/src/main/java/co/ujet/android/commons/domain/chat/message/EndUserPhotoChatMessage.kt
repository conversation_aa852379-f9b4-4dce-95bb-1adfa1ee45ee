package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.MediaFile
import org.json.JSONException
import org.json.JSONObject
import java.util.Date

class EndUserPhotoChatMessage : MediaChatMessage {
    private constructor(localId: Int, timestamp: Date, uploadMedia: MediaFile) : super(
        localId,
        timestamp,
        uploadMedia
    )

    private constructor(
        localId: Int,
        sid: String,
        timestamp: Date,
        uploadMedia: MediaFile,
        messageIndex: Long
    ) : super(localId, sid, timestamp, uploadMedia, messageIndex)

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            try {
                put("type", "photo")
                put("local_id", localId)
                put("media_id", mediaFile.mediaId)
            } catch (e: JSONException) {
                logger.w(e, "failed convert photo message to json")
            }
        }
    }

    override fun messageByEndUser() = true

    val thumbnailFilename: String?
        get() = mediaFile.thumbnailFilename

    companion object {
        @JvmStatic
        fun createPending(
            localId: Int,
            timestamp: Date,
            mediaFile: MediaFile
        ): EndUserPhotoChatMessage {
            return EndUserPhotoChatMessage(localId, timestamp, mediaFile)
        }

        fun createSent(
            localId: Int,
            sid: String,
            timestamp: Date,
            mediaFile: MediaFile,
            messageIndex: Long
        ): EndUserPhotoChatMessage {
            return EndUserPhotoChatMessage(localId, sid, timestamp, mediaFile, messageIndex)
        }
    }
}
