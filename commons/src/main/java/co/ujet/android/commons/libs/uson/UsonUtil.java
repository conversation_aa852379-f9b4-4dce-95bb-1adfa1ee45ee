package co.ujet.android.commons.libs.uson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.WildcardType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UsonUtil {
    public static Type getMapKeyType(TypeToken typeToken) {
        Type type = typeToken.getType();
        if (!(type instanceof ParameterizedType))
            return String.class;

        return ((ParameterizedType) type).getActualTypeArguments()[0];
    }

    public static Type getMapValueType(TypeToken typeToken) {
        Type type = typeToken.getType();
        if (!(type instanceof ParameterizedType))
            return Object.class;

        Type valueType = ((ParameterizedType) type).getActualTypeArguments()[1];
        // If the type argument is a WildcardType then use the first upper bound type as the return type
        // (this is rather crude and doesn't cover all possible type arguments but it's at least
        // solving the specific use-case for "? extends java.util.Map<java.lang.String, ?>")
        if (valueType instanceof WildcardType) {
            Type[] upperBounds = ((WildcardType)valueType).getUpperBounds();
            if (upperBounds.length > 0) {
                valueType = upperBounds[0];
                if (!(valueType instanceof ParameterizedType)) {
                    return Object.class;
                }
                return ((ParameterizedType) valueType).getRawType();
            } else {
                return Object.class;
            }
        }
        return ((ParameterizedType) type).getActualTypeArguments()[1];
    }

    public static Type getArrayComponentType(TypeToken typeToken) {
        if (typeToken.getType() instanceof GenericArrayType)
            return ((GenericArrayType) typeToken.getType()).getGenericComponentType();

        return typeToken.getRawType().getComponentType();
    }

    public static Class<?> getArrayComponentRawType(TypeToken typeToken) {
        return getRawType(getArrayComponentType(typeToken));
    }

    public static Type getListComponentType(TypeToken typeToken) {
        if (typeToken.getType() instanceof ParameterizedType)
            return ((ParameterizedType) typeToken.getType()).getActualTypeArguments()[0];

        return Object.class;
    }

    public static Class<?> getListComponentRawType(TypeToken typeToken) {
        return getRawType(getListComponentType(typeToken));
    }

    public static Class<?> getRawType(Type type) {
        if (type instanceof ParameterizedType) {
            return (Class<?>) ((ParameterizedType) type).getRawType();

        } else if (type instanceof Class) {
            return (Class<?>) type;

        } else if (type instanceof GenericArrayType) {
            return Array.newInstance(getRawType(((GenericArrayType) type).getGenericComponentType()), 0).getClass();
        } else if (type instanceof WildcardType) {
            Type[] bounds = ((WildcardType) type).getUpperBounds();
            return bounds.length == 0 ? Object.class : getRawType(bounds[0]);
        }

        throw new UsonException("Can't get the raw type of " + type);
    }

    public static boolean isPrimitiveType(Class<?> type) {
        return type == int.class ||
                type == float.class ||
                type == double.class ||
                type == boolean.class ||
                type == long.class;
    }

    public static boolean isBoxedPrimitiveType(Class<?> type) {
        return type == Integer.class ||
                type == Float.class ||
                type == Double.class ||
                type == Boolean.class ||
                type == Long.class;
    }

    public static JSONArray toJsonArray(Object value) {
        if (value == null)
            return null;

        if (value instanceof JSONArray)
            return (JSONArray) value;

        if (value instanceof String) {
            try {
                return new JSONArray((String) value);
            } catch (JSONException ignore) {
            }
        }

        return null;
    }

    public static JSONObject toJsonObject(Object value) {
        if (value == null)
            return null;

        if (value instanceof JSONObject)
            return (JSONObject) value;

        if (value instanceof String) {
            try {
                return new JSONObject((String) value);
            } catch (JSONException ignore) {
            }
        }

        return null;
    }

    public static <T> T newInstance(TypeToken<T> typeToken) {
        try {
            if (typeToken.getRawType() == List.class)
                return (T) new ArrayList<>();

            if (typeToken.getRawType() == Map.class)
                return (T) new HashMap<>();

            return typeToken.getRawType().newInstance();

        } catch (InstantiationException e) {
            throw new UsonException(e);

        } catch (IllegalAccessException e) {
            throw new UsonException(e);
        }
    }

    public static void setField(Field field, Object instance, Object value) {
        try {
            field.set(instance, value);

        } catch (IllegalAccessException e) {
            throw new UsonException(e);
        }
    }

    public static Object get(Field field, Object instance) {
        try {
            return field.get(instance);

        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }

        return null;
    }
}
