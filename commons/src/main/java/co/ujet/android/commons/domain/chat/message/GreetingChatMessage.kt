package co.ujet.android.commons.domain.chat.message

import androidx.annotation.IntRange
import co.ujet.android.commons.domain.chat.message.base.ChatMessage
import java.util.*

class GreetingChatMessage(@IntRange(from = 1) localId: Int,
                          sid: String,
                          timestamp: Date,
                          val message: String,
                          messageIndex: Long,
                          val userRole: String = "",
                          val isMarkDownSupported: Boolean = false) : ChatMessage(localId, sid, timestamp, messageIndex)
