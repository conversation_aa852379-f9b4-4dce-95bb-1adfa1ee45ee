package co.ujet.android.commons.libs.uson;

import java.lang.reflect.Array;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public class TypeToken<T> {
    private static Class<?> getRawType(Type type) {
        if (type instanceof ParameterizedType) {
            return (Class<?>) ((ParameterizedType) type).getRawType();

        } else if (type instanceof Class) {
            return (Class<?>) type;

        } else if (type instanceof GenericArrayType) {
            return Array.newInstance(getRawType(((GenericArrayType) type).getGenericComponentType()), 0).getClass();
        }

        throw new UnsupportedOperationException("Can't get the raw type of " + type);
    }

    private Type type;
    private Class<? extends T> rawType;

    public TypeToken() {
        Type superType = getClass().getGenericSuperclass();
        if (!(superType instanceof ParameterizedType))
            throw new UnsupportedOperationException("Invalid type token");

        this.type = ((ParameterizedType) superType).getActualTypeArguments()[0];
        this.rawType = (Class<? extends T>) getRawType(type);
    }

    public TypeToken(Type type) {
        this.type = type;
        this.rawType = (Class<? extends T>) getRawType(type);
    }

    public Type getType() {
        return type;
    }

    public Class<? extends T> getRawType() {
        return rawType;
    }
}
