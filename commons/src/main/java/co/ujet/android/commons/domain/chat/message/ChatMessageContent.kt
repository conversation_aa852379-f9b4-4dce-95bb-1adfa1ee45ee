package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.domain.Deflection
import co.ujet.android.commons.domain.Document
import co.ujet.android.commons.domain.Image
import co.ujet.android.commons.domain.Video
import co.ujet.android.commons.domain.agent.Agent
import co.ujet.android.commons.domain.agent.VirtualAgent
import co.ujet.android.commons.domain.chat.ContentCard
import co.ujet.android.commons.domain.chat.WebForm
import co.ujet.android.commons.libs.uson.SerializedName

class ChatMessageContent {
    @SerializedName("type")
    val type: String? = null

    @SerializedName("signature")
    val signature: String? = null

    @SerializedName("event")
    val event: String? = null

    @SerializedName("content")
    val content: String = ""

    @SerializedName("local_id")
    val localId = 0

    @SerializedName("media_id")
    val mediaId: Int? = null

    @SerializedName("agent")
    val agent: Agent? = null

    @SerializedName("to_agent")
    val toAgent: Agent? = null

    @SerializedName("to_virtual_agent")
    val toVirtualAgent: VirtualAgent? = null

    @SerializedName("memberName")
    val memberName: String? = null

    @SerializedName("name")
    val name: String? = null

    @SerializedName("timeout")
    val timeout = false

    @SerializedName("memberIdentity")
    private val memberIdentity: String? = null

    @SerializedName("title")
    val title: String? = null

    @SerializedName("buttons")
    val buttons: List<ChatButtonMessage>? = null

    @SerializedName("escalation_id")
    val escalationId: Int? = null

    @SerializedName("reason")
    val reason: String? = null

    @SerializedName("escalation_reason")
    val escalationReason: String? = null

    @SerializedName("deflection")
    val deflection: Deflection? = null

    @SerializedName("document")
    val document: Document? = null

    @SerializedName("image")
    val image: Image? = null

    @SerializedName("video")
    val video: Video? = null

    /** Present for messages with "type":"noti","event":"chatDismissed" **/
    @SerializedName("status")
    val status: String? = null

    @SerializedName("cards")
    val cards: List<ContentCard>? = null

    @SerializedName("form")
    var form: WebForm? = null

    @SerializedName("data")
    val data: FormCompleteEventData? = null

    @SerializedName("form_title")
    val formTitle: String? = null

    @SerializedName("groupMessageId")
    val groupMessageId: Int? = null

    fun getMemberIdentity() = memberIdentity ?: ""
}
