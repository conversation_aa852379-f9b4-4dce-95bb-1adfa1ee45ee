package co.ujet.android.commons.domain.chat.message

import co.ujet.android.commons.libs.uson.SerializedName

data class FormCompleteEvent(
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("signature")
    val signature: String? = null,
    @SerializedName("data")
    val data: FormCompleteEventData? = null
)

data class FormCompleteEventData(
    @SerializedName("status")
    val status: String? = null,
    @SerializedName("timestamp")
    val timestamp: String? = null,
    @SerializedName("smart_action_id")
    val smartActionId: Int? = null,
    @SerializedName("details")
    val details: FormCompleteEventDetails? = null
)

data class FormCompleteEventDetails(
    @SerializedName("error_code")
    val errorCode: String? = null,
    @SerializedName("message")
    val message: String? = null
)
